/**
 * 统一配色系统 - Unified Color System
 * 为整个八字分析系统提供统一的配色方案
 * 包含浅色模式和深色模式的完整配色体系
 */

:root {
    /* ===== 主色调系统 ===== */
    /* 主品牌色 - 优雅的蓝紫色系 */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;  /* 主色 */
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    /* 辅助色 - 紫色系 */
    --secondary-50: #faf5ff;
    --secondary-100: #f3e8ff;
    --secondary-200: #e9d5ff;
    --secondary-300: #d8b4fe;
    --secondary-400: #c084fc;
    --secondary-500: #a855f7;
    --secondary-600: #9333ea;
    --secondary-700: #7c3aed;
    --secondary-800: #6b21a8;
    --secondary-900: #581c87;
    
    /* 强调色 - 橙色系（用于八字相关元素） */
    --accent-50: #fff7ed;
    --accent-100: #ffedd5;
    --accent-200: #fed7aa;
    --accent-300: #fdba74;
    --accent-400: #fb923c;
    --accent-500: #f97316;  /* 强调色 */
    --accent-600: #ea580c;
    --accent-700: #c2410c;
    --accent-800: #9a3412;
    --accent-900: #7c2d12;
    
    /* ===== 语义化颜色 ===== */
    /* 成功色 - 绿色系 */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;
    
    /* 警告色 - 黄色系 */
    --warning-50: #fefce8;
    --warning-100: #fef9c3;
    --warning-200: #fef08a;
    --warning-300: #fde047;
    --warning-400: #facc15;
    --warning-500: #eab308;
    --warning-600: #ca8a04;
    --warning-700: #a16207;
    --warning-800: #854d0e;
    --warning-900: #713f12;
    
    /* 错误色 - 红色系 */
    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;
    
    /* 信息色 - 蓝色系 */
    --info-50: #eff6ff;
    --info-100: #dbeafe;
    --info-200: #bfdbfe;
    --info-300: #93c5fd;
    --info-400: #60a5fa;
    --info-500: #3b82f6;
    --info-600: #2563eb;
    --info-700: #1d4ed8;
    --info-800: #1e40af;
    --info-900: #1e3a8a;
    
    /* ===== 中性色系统 ===== */
    /* 灰色系 - 用于文本、边框、背景 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* ===== 浅色模式配色 ===== */
    /* 主要颜色 */
    --color-primary: var(--primary-500);
    --color-primary-hover: var(--primary-600);
    --color-primary-active: var(--primary-700);
    --color-secondary: var(--secondary-500);
    --color-accent: var(--accent-500);
    
    /* 文本颜色 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-tertiary: var(--gray-500);
    --text-muted: var(--gray-400);
    --text-inverse: var(--gray-50);
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --bg-card: #ffffff;
    --bg-overlay: rgba(15, 23, 42, 0.5);
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-hover: var(--gray-50);
    --bg-active: var(--gray-100);
    
    /* 边框颜色 */
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-300);
    --border-focus: var(--primary-500);
    --border-error: var(--error-500);
    --border-success: var(--success-500);
    
    /* ===== 渐变色系统 ===== */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 30%, var(--primary-200) 70%, var(--primary-300) 100%);
    --gradient-card: linear-gradient(145deg, #ffffff 0%, var(--gray-50) 100%);
    --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    --gradient-error: linear-gradient(135deg, var(--error-500) 0%, var(--error-600) 100%);
    --gradient-info: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
    
    /* 八字专用渐变 */
    --gradient-bazi: linear-gradient(135deg, #fff8e1 0%, #f3e5ab 100%);
    --gradient-bazi-button: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
    
    /* ===== 阴影系统 ===== */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* 特殊阴影 */
    --shadow-primary: 0 10px 25px -5px rgba(14, 165, 233, 0.3);
    --shadow-accent: 0 4px 15px rgba(249, 115, 22, 0.2);
    --shadow-card-hover: 0 20px 40px rgba(14, 165, 233, 0.15);
    
    /* 深色模式变量 */
    --bg-primary-dark: var(--gray-900);
    --bg-secondary-dark: var(--gray-800);
    --bg-tertiary-dark: var(--gray-700);
    --bg-card-dark: var(--gray-800);
    --bg-glass-dark: rgba(31, 41, 55, 0.8);
    --bg-input-dark: #2d3748;
    
    --text-primary-dark: var(--gray-100);
    --text-secondary-dark: var(--gray-300);
    --text-tertiary-dark: var(--gray-400);
    
    --border-primary-dark: var(--gray-600);
    --border-secondary-dark: var(--gray-700);
    
    --color-primary-dark: var(--primary-400);
    --color-accent-dark: var(--accent-400);
    
    /* 深色模式渐变 */
    --gradient-card-dark: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    --gradient-bazi-button-dark: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --gradient-bazi-button-dark-hover: linear-gradient(135deg, #764ba2 0%, #667eea 50%, #f093fb 100%);
    
    /* 深色模式阴影 */
    --shadow-primary-dark: 0 10px 30px rgba(102, 126, 234, 0.4);
    --shadow-primary-hover: 0 15px 40px rgba(102, 126, 234, 0.6);
    --shadow-focus-dark: rgba(99, 179, 237, 0.2);
    
    /* ===== 圆角系统 ===== */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 20px;
    --radius-3xl: 24px;
    --radius-full: 9999px;
    
    /* ===== 动画和过渡 ===== */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --transition-bounce: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    
    /* ===== 字体系统 ===== */
    --font-family-primary: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* ===== 间距系统 ===== */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
}

/* ===== 深色模式配色 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        /* 主要颜色 - 深色模式下稍微调亮 */
        --color-primary: var(--primary-400);
        --color-primary-hover: var(--primary-300);
        --color-primary-active: var(--primary-200);
        --color-secondary: var(--secondary-400);
        --color-accent: var(--accent-400);
        
        /* 文本颜色 - 深色模式 */
        --text-primary: var(--gray-50);
        --text-secondary: var(--gray-200);
        --text-tertiary: var(--gray-400);
        --text-muted: var(--gray-500);
        --text-inverse: var(--gray-900);
        
        /* 背景颜色 - 深色蓝绿色系 */
        --bg-primary: var(--gray-900);
        --bg-secondary: var(--gray-800);
        --bg-tertiary: var(--gray-700);
        --bg-card: var(--gray-800);
        --bg-overlay: rgba(15, 23, 42, 0.8);
        --bg-glass: rgba(30, 41, 59, 0.8);
        --bg-hover: var(--gray-700);
        --bg-active: var(--gray-600);
        
        /* 边框颜色 - 深色模式 */
        --border-primary: var(--gray-700);
        --border-secondary: var(--gray-600);
        --border-focus: var(--primary-400);
        --border-error: var(--error-400);
        --border-success: var(--success-400);
        
        /* 渐变色 - 深色模式调整 */
        --gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-500) 100%);
        --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-500) 100%);
        --gradient-accent: linear-gradient(135deg, var(--accent-400) 0%, var(--accent-500) 100%);
        --gradient-hero: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 30%, var(--gray-700) 70%, var(--gray-600) 100%);
        --gradient-card: linear-gradient(145deg, var(--gray-800) 0%, var(--gray-900) 100%);
        
        /* 八字专用渐变 - 深色模式 */
        --gradient-bazi: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-700) 100%);
        --gradient-bazi-button: linear-gradient(135deg, var(--accent-400) 0%, var(--accent-500) 50%, var(--secondary-400) 100%);
        
        /* 阴影 - 深色模式增强 */
        --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.5);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.7), 0 10px 10px -5px rgba(0, 0, 0, 0.6);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
        
        /* 特殊阴影 - 深色模式 */
        --shadow-primary: 0 10px 25px -5px rgba(56, 189, 248, 0.4);
        --shadow-accent: 0 10px 25px -5px rgba(251, 146, 60, 0.4);
        --shadow-card-hover: 0 20px 40px rgba(56, 189, 248, 0.2);
    }
}

/* ===== 强制深色模式类 ===== */
.dark {
    /* 当使用 .dark 类时，强制应用深色模式样式 */
    --color-primary: var(--primary-400);
    --color-primary-hover: var(--primary-300);
    --color-primary-active: var(--primary-200);
    --color-secondary: var(--secondary-400);
    --color-accent: var(--accent-400);
    
    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-200);
    --text-tertiary: var(--gray-400);
    --text-muted: var(--gray-500);
    --text-inverse: var(--gray-900);
    
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --bg-card: var(--gray-800);
    --bg-overlay: rgba(15, 23, 42, 0.8);
    --bg-glass: rgba(30, 41, 59, 0.8);
    --bg-hover: var(--gray-700);
    --bg-active: var(--gray-600);
    
    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);
    --border-focus: var(--primary-400);
    --border-error: var(--error-400);
    --border-success: var(--success-400);
}

/* ===== 工具类 ===== */
/* 快速应用主要颜色的工具类 */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-accent { color: var(--color-accent) !important; }
.text-success { color: var(--success-500) !important; }
.text-warning { color: var(--warning-500) !important; }
.text-error { color: var(--error-500) !important; }
.text-info { color: var(--info-500) !important; }

.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-accent { background-color: var(--color-accent) !important; }
.bg-success { background-color: var(--success-500) !important; }
.bg-warning { background-color: var(--warning-500) !important; }
.bg-error { background-color: var(--error-500) !important; }
.bg-info { background-color: var(--info-500) !important; }

.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-accent { border-color: var(--color-accent) !important; }
.border-success { border-color: var(--success-500) !important; }
.border-warning { border-color: var(--warning-500) !important; }
.border-error { border-color: var(--error-500) !important; }
.border-info { border-color: var(--info-500) !important; }

/* 渐变背景工具类 */
.gradient-primary { background: var(--gradient-primary) !important; }
.gradient-secondary { background: var(--gradient-secondary) !important; }
.gradient-accent { background: var(--gradient-accent) !important; }
.gradient-hero { background: var(--gradient-hero) !important; }
.gradient-card { background: var(--gradient-card) !important; }
.gradient-bazi { background: var(--gradient-bazi) !important; }
.gradient-bazi-button { background: var(--gradient-bazi-button) !important; }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-primary { box-shadow: var(--shadow-primary) !important; }
.shadow-accent { box-shadow: var(--shadow-accent) !important; }
.shadow-card-hover { box-shadow: var(--shadow-card-hover) !important; }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* 过渡动画工具类 */
.transition-fast { transition: var(--transition-fast) !important; }
.transition-normal { transition: var(--transition-normal) !important; }
.transition-slow { transition: var(--transition-slow) !important; }
.transition-bounce { transition: var(--transition-bounce) !important; }