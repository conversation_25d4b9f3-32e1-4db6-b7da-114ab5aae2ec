#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from card_manager import CardManager
from datetime import datetime
import json

print("=== 直接测试 CardManager 卡密验证 ===")
print()

# 创建 CardManager 实例
card_manager = CardManager()
print(f"CardManager 实例创建完成，ID: {id(card_manager)}")
print(f"加载的卡密总数: {len(card_manager.cards_data)}")
print()

# 测试卡密
test_card = "DAnrKv2ffcbvfh0C"
print(f"测试卡密: {test_card}")
print("-" * 50)

# 检查卡密是否存在
exists = test_card in card_manager.cards_data
print(f"1. 卡密是否存在于数据中: {exists}")

if exists:
    card_info = card_manager.cards_data[test_card]
    print(f"2. 卡密原始数据: {card_info}")
    
    # 检查各个验证条件
    print("\n3. 验证条件检查:")
    print(f"   - valid 字段: {card_info.get('valid', False)}")
    print(f"   - usage_count: {card_info.get('usage_count', 0)}")
    print(f"   - max_usage: {card_info.get('max_usage', 0)}")
    print(f"   - expire_time: {card_info.get('expire_time')}")
    
    # 检查过期时间
    expire_time_str = card_info.get('expire_time')
    if expire_time_str:
        try:
            expire_time = datetime.fromisoformat(expire_time_str.replace('T', ' '))
            current_time = datetime.now()
            is_expired = current_time > expire_time
            print(f"   - 当前时间: {current_time}")
            print(f"   - 过期时间: {expire_time}")
            print(f"   - 是否过期: {is_expired}")
        except Exception as e:
            print(f"   - 解析过期时间出错: {e}")
    
    # 检查使用次数
    usage_count = card_info.get('usage_count', 0)
    max_usage = card_info.get('max_usage', 0)
    usage_exceeded = usage_count >= max_usage
    print(f"   - 使用次数是否超限: {usage_exceeded}")
    
    print("\n4. 调用 validate_card 方法:")
    try:
        result = card_manager.validate_card(test_card)
        print(f"   - validate_card() 返回: {result}")
    except Exception as e:
        print(f"   - validate_card() 出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n5. 调用 get_card_info 方法:")
    try:
        info = card_manager.get_card_info(test_card)
        print(f"   - get_card_info() 返回: {info}")
    except Exception as e:
        print(f"   - get_card_info() 出错: {e}")
else:
    print("❌ 卡密不存在于数据中")
    print("\n当前所有卡密:")
    for i, key in enumerate(card_manager.cards_data.keys(), 1):
        print(f"   {i}. {key}")

print("\n=== 测试完成 ===")