<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试函数加载</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 函数加载测试</h1>
        
        <div class="section">
            <h3>📋 关键函数检查</h3>
            <button onclick="checkFunctions()">检查函数</button>
            <div id="functionStatus"></div>
        </div>
        
        <div class="section">
            <h3>🧪 测试渐进式加载</h3>
            <button onclick="testProgressiveLoader()">测试加载器</button>
            <div id="loaderStatus"></div>
        </div>
        
        <div class="section">
            <h3>📊 详细结果</h3>
            <pre id="detailResults">等待测试...</pre>
        </div>
    </div>

    <!-- 加载所有必要的JavaScript文件 -->
    <script src="js/ui_detailed_helpers.js"></script>
    <script src="js/ui_detailed_states.js"></script>
    <script src="js/detailed_result/detailed_result_bazi_info.js"></script>
    <script src="js/detailed_result/detailed_result_utils.js"></script>
    <script src="js/detailed_result/progressive_loader.js"></script>
    <script src="js/detailed_result/image_exporter.js"></script>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkFunctions() {
            const results = [];
            results.push('=== 关键函数检查 ===');
            
            // 检查关键函数
            const functions = [
                'generateBaziInfoSection',
                'initializeToggleFeatures',
                'toggleSection',
                'downloadReport',
                'editTitle',
                'saveTitle',
                'loadCustomTitle'
            ];
            
            let allGood = true;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`✅ ${funcName}: 存在`);
                } else {
                    results.push(`❌ ${funcName}: 不存在`);
                    allGood = false;
                }
            });
            
            // 检查类
            results.push('\n=== 类检查 ===');
            if (typeof window.ProgressiveLoader === 'function') {
                results.push('✅ ProgressiveLoader: 存在');
            } else {
                results.push('❌ ProgressiveLoader: 不存在');
                allGood = false;
            }
            
            if (typeof window.ImageExporter === 'function') {
                results.push('✅ ImageExporter: 存在');
            } else {
                results.push('❌ ImageExporter: 不存在');
                allGood = false;
            }
            
            // 检查实例
            results.push('\n=== 实例检查 ===');
            if (window.progressiveLoader) {
                results.push('✅ window.progressiveLoader: 存在');
            } else {
                results.push('❌ window.progressiveLoader: 不存在');
                allGood = false;
            }
            
            document.getElementById('detailResults').textContent = results.join('\n');
            
            if (allGood) {
                showStatus('functionStatus', '✅ 所有函数都正确加载', 'success');
            } else {
                showStatus('functionStatus', '❌ 部分函数缺失', 'error');
            }
        }
        
        async function testProgressiveLoader() {
            try {
                if (!window.progressiveLoader) {
                    showStatus('loaderStatus', '❌ progressiveLoader 不存在', 'error');
                    return;
                }
                
                showStatus('loaderStatus', '🧪 测试中...', 'info');
                
                // 测试 loadBasicData 方法
                const basicData = await window.progressiveLoader.loadBasicData('Xs1MR9iVx9RNZOk6', null);
                
                if (basicData) {
                    showStatus('loaderStatus', '✅ loadBasicData 返回了数据', 'success');
                    
                    // 测试 displayBasicInfo 方法
                    if (typeof window.progressiveLoader.displayBasicInfo === 'function') {
                        showStatus('loaderStatus', '✅ displayBasicInfo 方法存在，准备测试...', 'success');
                        
                        // 这里不实际调用 displayBasicInfo，因为页面结构不同
                        // window.progressiveLoader.displayBasicInfo(basicData);
                    } else {
                        showStatus('loaderStatus', '❌ displayBasicInfo 方法不存在', 'error');
                    }
                } else {
                    showStatus('loaderStatus', '❌ loadBasicData 返回 null', 'error');
                }
                
            } catch (error) {
                showStatus('loaderStatus', `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkFunctions, 1000);
        });
    </script>
</body>
</html>
