/**
 * 报告UI组件生成
 * 处理各种UI组件的生成
 */

// 初始化模块日志记录器
const componentsLogger = window.BaziLogger ? window.BaziLogger.forModule('ReportsComponents') : {
    debug: console.log,
    info: console.log,
    warn: console.warn,
    error: console.error
};

// 生成运行中分析的HTML - 增强版本
HistoryReportsManager.prototype.generateRunningAnalysisHtml = function(analysis) {
    // 检查是否真的在进行分析 - 更宽松的检查
    const hasRealProgress = analysis && (
        // 检查是否有分析状态
        (analysis.status && (
            analysis.status.status === 'processing' ||
            analysis.status.status === 'llm_analyzing' ||
            analysis.status.status === 'queued' ||
            analysis.status.llm_progress ||
            (analysis.status.progress && analysis.status.progress > 0)
        )) ||
        // 或者检查是否有分析ID和开始时间（说明分析已启动）
        (analysis.requestId && analysis.startTime)
    );

    return `
        <div class="running-analysis-section">
            <div class="running-analysis-card">
                <div class="running-header">
                    <div class="running-icon">⚡</div>
                    <h3>正在进行分析</h3>
                    <div class="running-status" id="runningStatus" style="display: ${hasRealProgress ? 'block' : 'none'};">分析中...</div>
                    <div class="progress-percent" id="progressPercent" style="display: ${hasRealProgress ? 'block' : 'none'};">0%</div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text" id="progressText">正在处理您的八字信息...</div>
                </div>

                <!-- 详细进度步骤 -->
                <div class="progress-steps-container" id="progressSteps">
                    <div class="progress-steps">
                        <div class="progress-step current">
                            <div class="step-indicator">⚡</div>
                            <div class="step-name">性格深度分析</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">事业运势分析</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">感情婚姻分析</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">健康状况分析</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">运势趋势分析</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">AI深度整合</div>
                        </div>

                        <div class="progress-step">
                            <div class="step-indicator">○</div>
                            <div class="step-name">生成报告</div>
                        </div>
                    </div>
                </div>

                <div class="analysis-time">
                    <span><i class="time-icon">🕒</i> 开始时间：${new Date(analysis.startTime).toLocaleTimeString()}</span>
                    <span id="elapsedTime"><i class="time-icon">⏱️</i> 已用时：0秒</span>
                </div>

                <div class="debug-info" id="debugInfo" style="display: none;">
                    <div class="debug-title">调试信息 <span class="debug-toggle" onclick="toggleDebugInfo()">收起</span></div>
                    <pre id="debugContent">等待状态检查...</pre>
                </div>

                <div class="analysis-actions">
                    <button onclick="viewCurrentResults()" class="action-btn primary view-results-btn">
                        📊 查看排盘结果
                    </button>
                    <button onclick="forceCompleteAnalysis()" class="action-btn secondary force-complete-btn">
                        强制完成 ⚠️
                    </button>
                    <button onclick="toggleDebugInfo()" class="action-btn outline debug-btn">
                        显示调试信息 🔧
                    </button>
                </div>
            </div>
        </div>
        <div class="section-divider"></div>
    `;
};

// 生成报告列表HTML
HistoryReportsManager.prototype.generateReportsHtml = function(reports) {
    let reportsHtml = '<div class="reports-section"><h3>📋 历史报告</h3><div class="reports-grid">';
    
    reports.forEach(report => {
        const date = new Date(report.timestamp);
        const formattedDate = date.toLocaleString('zh-CN');
        const summary = this.generateReportSummary(report.data);
        
        // 优先尝试提取原始出生日期和性别
        let originalBirthDate = this.extractOriginalBirthDate(report);
        let title = '';
        
        if (originalBirthDate) {
            // 尝试从原始请求数据获取性别
            let gender = '';
            if (report.data && report.data.requestData && report.data.requestData.gender) {
                gender = report.data.requestData.gender;
            } else if (report.analysisData && report.analysisData.requestData && report.analysisData.requestData.gender) {
                gender = report.analysisData.requestData.gender;
            }
            
            // 格式化性别文本
            let genderText = '';
            if (gender === '1' || gender === 1 || gender === '男' || gender.toLowerCase() === 'male') {
                genderText = '男';
            } else if (gender === '2' || gender === 2 || gender === '女' || gender.toLowerCase() === 'female') {
                genderText = '女';
            }
            
            // 组装标题
            if (genderText) {
                title = `${originalBirthDate} ${genderText}性八字分析`;
            } else {
                title = `${originalBirthDate} 八字分析`;
            }
            
            componentsLogger.debug('使用原始出生日期生成标题:', title);
        } else {
            // 回退到常规标题生成
            title = this.generateReportTitle(report);
        }
        
        reportsHtml += `
            <div class="report-card" onclick="viewReport('${report.id}')">
                <div class="report-header">
                    <h3 class="report-title">${title}</h3>
                    <span class="report-source ${report.source}">${report.source === 'local' ? '本地' : '云端'}</span>
                </div>
                <div class="report-meta">
                    <span class="report-date">📅 ${formattedDate}</span>
                </div>
                <div class="report-summary">
                    ${summary}
                </div>
                <div class="report-actions">
                    <button class="view-btn" onclick="event.stopPropagation(); viewReport('${report.id}')">📖 查看详情</button>
                    <button class="delete-btn" onclick="event.stopPropagation(); deleteReport('${report.id}')">🗑️ 删除</button>
                </div>
            </div>
        `;
    });
    
    reportsHtml += '</div></div>';
    return reportsHtml;
};

// 生成空状态HTML
HistoryReportsManager.prototype.generateEmptyStateHtml = function() {
    return `
        <div class="empty-state">
            <div class="empty-icon">📋</div>
            <h3>暂无历史报告</h3>
            <p>您还没有进行过八字分析，点击下方按钮开始您的第一次分析</p>
            <a href="up.html" class="action-btn primary">☯ 开始分析</a>
        </div>
    `;
}; 