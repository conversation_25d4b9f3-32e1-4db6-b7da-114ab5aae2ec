/**
 * 日志工具
 * 提供统一的日志记录机制，支持开发和生产环境
 */

// 日志级别
const LogLevel = {
    DEBUG: 0,   // 调试信息，仅在开发环境显示
    INFO: 1,    // 一般信息
    WARN: 2,    // 警告信息
    ERROR: 3,   // 错误信息
    NONE: 4     // 不显示任何日志
};

// 当前日志级别，可以通过URL参数或localStorage设置
let currentLogLevel = LogLevel.ERROR;

// 初始化日志级别
function initLogLevel() {
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('debug')) {
        currentLogLevel = LogLevel.DEBUG;
        localStorage.setItem('baziLogLevel', LogLevel.DEBUG.toString());
        return;
    }
    
    // 检查localStorage
    const savedLevel = localStorage.getItem('baziLogLevel');
    if (savedLevel !== null) {
        currentLogLevel = parseInt(savedLevel);
    } else {
        // 默认设置：生产环境只显示错误，开发环境显示所有信息
        currentLogLevel = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1' ? 
                          LogLevel.DEBUG : LogLevel.ERROR;
    }
}

// 设置日志级别
function setLogLevel(level) {
    if (level >= LogLevel.DEBUG && level <= LogLevel.NONE) {
        currentLogLevel = level;
        localStorage.setItem('baziLogLevel', level.toString());
    }
}

// 获取日志前缀（时间和模块名）
function getLogPrefix(module) {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    return module ? `[${timeString}][${module}]` : `[${timeString}]`;
}

// 日志函数
const Logger = {
    debug: function(message, ...args) {
        if (currentLogLevel <= LogLevel.DEBUG) {
            console.log(getLogPrefix('DEBUG'), message, ...args);
        }
    },
    
    info: function(message, ...args) {
        if (currentLogLevel <= LogLevel.INFO) {
            console.log(getLogPrefix('INFO'), message, ...args);
        }
    },
    
    warn: function(message, ...args) {
        if (currentLogLevel <= LogLevel.WARN) {
            console.warn(getLogPrefix('WARN'), message, ...args);
        }
    },
    
    error: function(message, ...args) {
        if (currentLogLevel <= LogLevel.ERROR) {
            console.error(getLogPrefix('ERROR'), message, ...args);
        }
    },
    
    // 创建特定模块的日志记录器
    forModule: function(moduleName) {
        return {
            debug: (message, ...args) => {
                if (currentLogLevel <= LogLevel.DEBUG) {
                    console.log(getLogPrefix(moduleName), message, ...args);
                }
            },
            info: (message, ...args) => {
                if (currentLogLevel <= LogLevel.INFO) {
                    console.log(getLogPrefix(moduleName), message, ...args);
                }
            },
            warn: (message, ...args) => {
                if (currentLogLevel <= LogLevel.WARN) {
                    console.warn(getLogPrefix(moduleName), message, ...args);
                }
            },
            error: (message, ...args) => {
                if (currentLogLevel <= LogLevel.ERROR) {
                    console.error(getLogPrefix(moduleName), message, ...args);
                }
            }
        };
    }
};

// 初始化日志级别
initLogLevel();

// 暴露全局接口
window.BaziLogger = Logger;
window.BaziLogLevel = LogLevel;
window.setLogLevel = setLogLevel; 