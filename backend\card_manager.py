import json
import os
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class CardManager:
    """卡密管理器"""
    
    def __init__(self, cards_file='cards.json'):
        # 使用绝对路径构建卡密文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.cards_file = os.path.join(current_dir, cards_file)
        logger.info(f"加载卡密文件: {self.cards_file}")
        self.cards_data = self._load_cards()
        self.processing_cards = {}  # 正在处理的卡密
        logger.info(f"已加载卡密数量: {len(self.cards_data)}")
    
    def _load_cards(self):
        """加载卡密数据"""
        if os.path.exists(self.cards_file):
            try:
                with open(self.cards_file, 'r', encoding='utf-8') as f:
                    cards_data = json.load(f)
                    logger.info(f"成功从{self.cards_file}加载了{len(cards_data)}个卡密")
                    # 打印所有卡密信息，方便调试
                    for key in cards_data:
                        logger.info(f"卡密: {key}, 有效: {cards_data[key].get('valid')}, 过期时间: {cards_data[key].get('expire_time')}")
                    return cards_data
            except Exception as e:
                logger.error(f"加载卡密文件失败: {str(e)}")
                return self._create_default_cards()
        else:
            logger.warning(f"卡密文件不存在: {self.cards_file}, 创建默认卡密")
            return self._create_default_cards()
    
    def _create_default_cards(self):
        """创建默认卡密数据"""
        default_cards = {
            'test123': {
                'valid': True,
                'created_time': datetime.now().isoformat(),
                'expire_time': (datetime.now() + timedelta(days=30)).isoformat(),
                'usage_count': 0,
                'max_usage': 100
            },
            'demo456': {
                'valid': True,
                'created_time': datetime.now().isoformat(),
                'expire_time': (datetime.now() + timedelta(days=7)).isoformat(),
                'usage_count': 0,
                'max_usage': 10
            }
        }
        
        self._save_cards(default_cards)
        return default_cards
    
    def _save_cards(self, cards_data=None):
        """保存卡密数据"""
        if cards_data is None:
            cards_data = self.cards_data
        
        try:
            with open(self.cards_file, 'w', encoding='utf-8') as f:
                json.dump(cards_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存卡密文件失败: {str(e)}")
    
    def validate_card(self, card_key):
        """验证卡密"""
        if not card_key or card_key not in self.cards_data:
            return False
        
        card_info = self.cards_data[card_key]
        
        # 检查卡密是否有效
        if not card_info.get('valid', False):
            return False
        
        # 检查是否过期
        expire_time = datetime.fromisoformat(card_info['expire_time'])
        if datetime.now() > expire_time:
            return False
        
        # 检查使用次数
        usage_count = card_info.get('usage_count', 0)
        max_usage = card_info.get('max_usage', 0)
        if usage_count >= max_usage:
            return False
        
        return True
    
    def mark_processing(self, card_key):
        """标记卡密为处理中"""
        if card_key in self.cards_data:
            self.processing_cards[card_key] = {
                'start_time': datetime.now(),
                'status': 'processing'
            }
    
    def mark_completed(self, card_key, success=True):
        """标记卡密处理完成"""
        if card_key in self.cards_data:
            # 增加使用次数
            self.cards_data[card_key]['usage_count'] += 1
            self._save_cards()
            
            # 更新处理状态
            if card_key in self.processing_cards:
                self.processing_cards[card_key].update({
                    'status': 'completed' if success else 'failed',
                    'end_time': datetime.now(),
                    'completed': True
                })
    
    def get_status(self, card_key):
        """获取卡密状态"""
        if card_key in self.processing_cards:
            return self.processing_cards[card_key]
        else:
            return {
                'status': 'not_found',
                'completed': False
            }
    
    def add_card(self, card_key, expire_days=30, max_usage=100):
        """添加新卡密"""
        self.cards_data[card_key] = {
            'valid': True,
            'created_time': datetime.now().isoformat(),
            'expire_time': (datetime.now() + timedelta(days=expire_days)).isoformat(),
            'usage_count': 0,
            'max_usage': max_usage
        }
        self._save_cards()
        logger.info(f"添加新卡密: {card_key}")
    
    def disable_card(self, card_key):
        """禁用卡密"""
        if card_key in self.cards_data:
            self.cards_data[card_key]['valid'] = False
            self._save_cards()
            logger.info(f"禁用卡密: {card_key}")
    
    def remove_card(self, card_key):
        """完全删除卡密"""
        if card_key in self.cards_data:
            # 从数据中删除卡密
            del self.cards_data[card_key]
            # 保存更改
            self._save_cards()
            logger.info(f"删除卡密: {card_key}")
            return True
        return False
    
    def get_card_info(self, card_key):
        """获取卡密信息"""
        return self.cards_data.get(card_key, None)
        
    def get_all_cards(self):
        """获取所有卡密信息"""
        cards_with_status = {}
        
        for card_key, card_info in self.cards_data.items():
            # 复制一份卡密信息
            card_data = dict(card_info)
            
            # 添加计算后的状态信息
            expire_time = datetime.fromisoformat(card_info['expire_time'])
            usage_count = card_info.get('usage_count', 0)
            max_usage = card_info.get('max_usage', 1)
            
            card_data['expired'] = datetime.now() > expire_time
            card_data['usage_percent'] = round((usage_count / max_usage) * 100, 1) if max_usage > 0 else 100
            card_data['days_remaining'] = (expire_time - datetime.now()).days if expire_time > datetime.now() else 0
            
            # 获取处理状态
            if card_key in self.processing_cards:
                card_data['processing_status'] = self.processing_cards[card_key].get('status', 'unknown')
            else:
                card_data['processing_status'] = 'not_processing'
                
            cards_with_status[card_key] = card_data
            
        return cards_with_status