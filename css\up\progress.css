/* 进度指示器样式 */
.progress-container {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    margin: 24px 0;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color-light);
}

.progress-bar {
    background: var(--bg-tertiary);
    border-radius: 50px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    background: var(--hero-gradient);
    height: 100%;
    border-radius: 50px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
} 