/* 导入统一配色系统 */
@import url('./unified-colors.css');

/* 首页特定样式 */
:root {
    --primary-color-rgb: 99, 102, 241;  /* 更新的主色调RGB值 */
    /* 阴影别名 */
    --shadow-sm: var(--shadow-sm);
    --hero-gradient: var(--gradient-hero);
    --card-hover-shadow: var(--shadow-card-hover);
    --pulse-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --float-animation: float 6s ease-in-out infinite;
    --glow-animation: glow 3s ease-in-out infinite alternate;
}

/* 背景装饰 */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    font-size: 24px;
    opacity: 0.6;
    animation: var(--float-animation);
    filter: drop-shadow(0 0 10px var(--shadow-primary));
}

.floating-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.floating-2 {
    top: 20%;
    right: 15%;
    animation-delay: -2s;
    animation-duration: 10s;
}

.floating-3 {
    top: 60%;
    left: 5%;
    animation-delay: -4s;
    animation-duration: 12s;
}

.floating-4 {
    top: 70%;
    right: 10%;
    animation-delay: -1s;
    animation-duration: 9s;
}

.floating-5 {
    top: 40%;
    left: 85%;
    animation-delay: -3s;
    animation-duration: 11s;
}

.floating-6 {
    top: 85%;
    left: 50%;
    animation-delay: -5s;
    animation-duration: 7s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) rotate(5deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-40px) rotate(-5deg);
        opacity: 1;
    }
    75% {
        transform: translateY(-20px) rotate(3deg);
        opacity: 0.8;
    }
}

/* 八卦符号专用样式 - 解决下划线问题 */
.feature-icon,
.header-icon,
.highlight-icon,
.floating-element,
.hero-image,
.about-title,
.about-decoration,
.subtitle-decoration,
.hero-sub-text {
    font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Segoe UI Symbol', sans-serif !important;
    text-decoration: none !important;
    font-style: normal !important;
}

/* 强制移除 feature-card 链接的下划线 */
a.feature-card,
a.feature-card:link,
a.feature-card:visited,
a.feature-card:hover,
a.feature-card:active {
    text-decoration: none !important;
    border-bottom: none !important;
}

/* 确保 feature-icon 内的符号不显示下划线 */
a.feature-card .feature-icon,
a.feature-card .feature-icon:before,
a.feature-card .feature-icon:after {
    text-decoration: none !important;
    border-bottom: none !important;
    text-decoration-line: none !important;
}

/* 头部美化 */
.header-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 15px;
}

.header-icon {
    font-size: 2.5rem;
    animation: var(--glow-animation);
    filter: drop-shadow(0 0 15px var(--shadow-primary));
}

.header-text {
    text-align: center;
}

.header-text h1 {
    margin: 0;
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
}

.header-underline {
    height: 4px;
    background: var(--hero-gradient);
    border-radius: 2px;
    margin-top: 8px;
    animation: expandWidth 1.5s ease-out;
}

.subtitle-decoration {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
    opacity: 0.8;
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes expandWidth {
    0% { width: 0; }
    100% { width: 100%; }
}

@keyframes glow {
    0% {
        filter: drop-shadow(0 0 15px var(--shadow-primary));
        transform: scale(1);
    }
    100% {
        filter: drop-shadow(0 0 25px var(--shadow-primary));
        transform: scale(1.05);
    }
}

.hero-section {
    text-align: center;
    margin: 30px 0 50px;
    animation: fadeInDown 0.8s ease-out;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: var(--pulse-animation);
}

.hero-container {
    position: relative;
}

.hero-image {
    width: 200px;
    height: 200px;
    margin: 0 auto 25px;
    background: transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 85px;
    color: var(--text-primary);
    /* box-shadow: var(--shadow-lg); */
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.hero-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: none;
    border-radius: 50%;
    animation: rotate 20s linear infinite;
    z-index: 1;
}

.hero-ring-2 {
    position: absolute;
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    border: none;
    border-radius: 50%;
    animation: rotate 30s linear infinite reverse;
    z-index: 1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hero-text {
    margin-top: 20px;
}

.hero-main-text {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
}

.hero-sub-text {
    font-size: 1rem;
    color: var(--text-secondary);
    font-style: italic;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, var(--bg-glass) 50%, transparent 70%);
    transform: rotate(-45deg);
    animation: shimmer 3s ease-in-out infinite;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.hero-image:hover {
    transform: scale(1.05) rotate(5deg);
    /* box-shadow: var(--card-hover-shadow), 0 0 0 6px rgba(255, 255, 255, 0.15); */
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(-45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.features-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 50px;
    animation: fadeInUp 0.8s ease-out 0.2s both;
    position: relative;
}

.features-container::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-50), var(--secondary-50), var(--accent-50));
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.features-container:hover::before {
    opacity: 1;
}

.feature-card {
    background: var(--bg-card);
    border-radius: 16px;
    padding: 30px 24px;
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--primary-100), transparent);
    transition: left 0.6s ease;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--hero-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    border-radius: 16px 16px 0 0;
}

.feature-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--card-hover-shadow), 0 0 30px rgba(102, 126, 234, 0.1);
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--bg-card) 0%, rgba(102, 126, 234, 0.02) 100%);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover::after {
    transform: scaleX(1);
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }

.feature-icon {
    font-size: 48px;
    margin-bottom: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    position: relative;
    z-index: 2;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.4s ease;
    z-index: -1;
}

.feature-card:hover .feature-icon {
    transform: scale(1.15) rotate(8deg);
    filter: drop-shadow(0 6px 12px rgba(102, 126, 234, 0.4));
    animation: iconBounce 0.6s ease;
}

.feature-card:hover .feature-icon::before {
    transform: translate(-50%, -50%) scale(1);
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1.15) rotate(8deg); }
    50% { transform: scale(1.25) rotate(12deg); }
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.feature-card:hover .feature-title {
    color: var(--primary-color);
}

.feature-description {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.6;
    transition: color 0.3s ease;
}

.feature-card:hover .feature-description {
    color: var(--text-primary);
}

.about-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 35px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    animation: fadeInUp 0.8s ease-out 0.4s both;
    position: relative;
    overflow: hidden;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--hero-gradient);
    z-index: 1;
}

.about-header {
    text-align: center;
    margin-bottom: 30px;
}

.about-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.about-decoration {
    font-size: 1.2rem;
    color: var(--primary-color);
    animation: var(--glow-animation);
}

.about-content {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.about-highlight {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(102, 126, 234, 0.03);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.about-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    transition: left 0.6s ease;
}

.about-highlight:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.about-highlight:hover::before {
    left: 100%;
}

.highlight-icon {
    font-size: 2rem;
    flex-shrink: 0;
    margin-top: 2px;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.about-highlight p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem;
}

.about-highlight strong {
    color: var(--primary-color);
    font-weight: 600;
}

.cta-section {
    text-align: center;
    margin: 50px 0;
    animation: fadeInUp 0.8s ease-out 0.6s both;
    position: relative;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 50%, rgba(240, 147, 251, 0.03) 100%);
    border-radius: 20px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    border-radius: 20px;
    z-index: -1;
}

.cta-button {
    display: inline-block;
    padding: 20px 45px;
    background: var(--hero-gradient);
    color: white;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.2rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md), 0 0 0 0 rgba(102, 126, 234, 0.4);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    animation: var(--pulse-animation);
    border: 2px solid transparent;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.cta-button:hover {
    transform: translateY(-6px) scale(1.08);
    box-shadow: var(--card-hover-shadow), 0 0 0 12px rgba(102, 126, 234, 0.15);
    animation: none;
    border-color: rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #667eea 100%);
    background-size: 200% 200%;
    animation: gradientShift 2s ease infinite;
}

.cta-button:hover::before {
    width: 350px;
    height: 350px;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.cta-button:active {
    transform: translateY(-2px) scale(1.02);
}

.cta-subtitle {
    margin-top: 20px;
    color: var(--text-secondary);
    font-size: 0.95rem;
    font-weight: 400;
}

.card-purchase-button {
    display: inline-block;
    padding: 16px 32px;
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 30px;
    font-weight: 600;
    font-size: 1.05rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    margin-top: 20px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card-purchase-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.15), transparent);
    transition: left 0.5s ease;
}

.card-purchase-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.card-purchase-button:hover {
    background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.25);
    border-color: #764ba2;
    color: #764ba2;
}

.card-purchase-button:hover::before {
    left: 100%;
}

.card-purchase-button:hover::after {
    width: 200px;
    height: 200px;
}

.card-purchase-button span {
    margin-right: 10px;
    transition: all 0.3s ease;
    display: inline-block;
}

.card-purchase-button:hover span {
    transform: scale(1.3) rotate(15deg);
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .floating-element {
        font-size: 18px;
        opacity: 0.4;
    }
    
    .header-decoration {
        flex-direction: column;
        gap: 10px;
    }
    
    .header-icon {
        font-size: 2rem;
    }
    
    .header-text h1 {
        font-size: 2rem;
    }
    
    .subtitle-decoration {
        font-size: 0.8rem;
        padding: 0 10px;
    }
    
    .features-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .feature-card {
        padding: 25px 20px;
    }
    
    .feature-icon {
        font-size: 40px;
    }
    
    .hero-image {
        width: 150px;
        height: 150px;
        font-size: 60px;
    }
    
    .hero-ring {
        top: -12px;
        left: -12px;
        right: -12px;
        bottom: -12px;
    }
    
    .hero-ring-2 {
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
    }
    
    .hero-main-text {
        font-size: 1.2rem;
    }
    
    .hero-sub-text {
        font-size: 0.9rem;
        padding: 0 10px;
    }
    
    .about-section {
        padding: 25px 20px;
    }
    
    .about-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 8px;
    }
    
    .about-highlight {
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 15px;
    }
    
    .highlight-icon {
        font-size: 1.5rem;
        margin-top: 0;
    }
    
    .cta-section {
        padding: 30px 15px;
        margin: 30px 0;
    }
    
    .cta-button {
        padding: 16px 35px;
        font-size: 1.1rem;
    }
    
    .card-purchase-button {
        padding: 14px 28px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 12px;
        padding-top: 20px;
    }
    
    .header-text h1 {
        font-size: 1.6rem;
        line-height: 1.3;
    }
    
    .subtitle {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 8px;
    }
    
    .subtitle-decoration {
        font-size: 0.75rem;
        padding: 8px 10px;
        line-height: 1.3;
    }
    
    .hero-section {
        margin: 20px 0;
        padding: 20px 10px;
    }
    
    .hero-image {
        width: 120px;
        height: 120px;
        font-size: 50px;
        margin-bottom: 16px;
    }
    
    .hero-main-text {
        font-size: 1.1rem;
        line-height: 1.4;
        margin-bottom: 8px;
    }
    
    .hero-sub-text {
        font-size: 0.85rem;
        line-height: 1.4;
        padding: 0 5px;
    }
    
    .features-container {
        gap: 16px;
        margin: 20px 0;
    }
    
    .feature-card {
        padding: 20px 16px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .feature-icon {
        font-size: 36px;
        margin-bottom: 12px;
    }
    
    .feature-title {
        font-size: 1.1rem;
        margin-bottom: 8px;
        line-height: 1.3;
    }
    
    .feature-description {
        font-size: 0.85rem;
        line-height: 1.4;
    }
    
    .about-section {
        padding: 20px 12px;
        margin: 20px 0;
    }
    
    .about-title {
        font-size: 1.4rem;
        margin-bottom: 16px;
        line-height: 1.3;
    }
    
    .about-highlight {
        padding: 16px 12px;
        margin-bottom: 12px;
        border-radius: 12px;
    }
    
    .highlight-text {
        font-size: 0.85rem;
        line-height: 1.4;
    }
    
    .highlight-icon {
        font-size: 1.3rem;
    }
    
    .cta-section {
        padding: 25px 12px;
        margin: 25px 0;
        border-radius: 16px;
    }
    
    .cta-title {
        font-size: 1.4rem;
        margin-bottom: 12px;
        line-height: 1.3;
    }
    
    .cta-description {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 20px;
    }
    
    .cta-button {
        padding: 16px 32px;
        font-size: 1.05rem;
        min-height: 48px;
        border-radius: 12px;
    }
    
    .card-purchase-button {
        padding: 14px 28px;
        font-size: 0.95rem;
        min-height: 44px;
        border-radius: 10px;
    }
    
    .footer {
        padding: 24px 12px;
        margin-top: 30px;
    }
    
    .footer p {
        font-size: 0.8rem;
        line-height: 1.4;
    }
    
    .floating-element {
        font-size: 16px;
        opacity: 0.3;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .cta-button,
    .card-purchase-button,
    .feature-card {
        min-height: 44px;
        touch-action: manipulation;
    }
    
    .feature-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    .cta-button:active,
    .card-purchase-button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        padding: 16px 10px;
    }
    
    .hero-container {
        flex-direction: row;
        align-items: center;
        gap: 20px;
    }
    
    .hero-image {
        width: 100px;
        height: 100px;
        font-size: 40px;
        margin-bottom: 0;
    }
    
    .hero-text {
        text-align: left;
        flex: 1;
    }
    
    .features-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .about-section {
        padding: 16px 12px;
    }
    
    .cta-section {
        padding: 20px 12px;
    }
}

/* 额外动画效果 */
@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动动画 */
@media (prefers-reduced-motion: no-preference) {
    .feature-card {
        animation: fadeInUp 0.6s ease-out both;
    }
    
    .about-highlight {
        animation: fadeInUp 0.6s ease-out both;
    }
    
    .about-highlight:nth-child(1) { animation-delay: 0.1s; }
    .about-highlight:nth-child(2) { animation-delay: 0.2s; }
    .about-highlight:nth-child(3) { animation-delay: 0.3s; }
}

/* 性能优化 */
.feature-card,
.hero-image,
.floating-element {
    will-change: transform;
}

.feature-card:not(:hover),
.hero-image:not(:hover) {
    will-change: auto;
}

.container {
    max-width: 800px;
}

.card-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: underline;
    transition: var(--transition);
}

.card-link:hover {
    opacity: 0.8;
}