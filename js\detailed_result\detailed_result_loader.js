/**
 * @file 八字分析结果数据加载模块
 * @description 处理从不同来源（API、文件、本地存储）加载分析结果的功能
 */

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
function getApiBaseUrl() {
    console.log('获取API基础URL，当前主机名:', window.location.hostname);
    
    // 根据主机名确定API基础URL
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        return 'http://127.0.0.1:5000';
    } else if (window.location.hostname === '***********') {
        return 'http://***********:5000';
    } else {
        // 尝试从window对象中获取全局配置的API URL
        if (window.apiBaseUrl) {
            return window.apiBaseUrl;
        }
        
        // 默认返回空字符串，表示使用相对路径
        return '';
    }
}

/**
 * 加载分析结果的主函数
 * @returns {Promise<void>}
 */
async function loadAnalysisResult() {
    try {
        console.log('开始加载分析结果...');
        
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const cardKey = urlParams.get('cardKey');
        const reportId = urlParams.get('id');
        const filePath = urlParams.get('file');
        const progressive = urlParams.get('progressive');
        const adminToken = urlParams.get('adminToken');
        const isAdmin = urlParams.get('admin') === 'true';

        console.log('URL参数:', { cardKey, reportId, filePath, progressive, adminToken, isAdmin });
        console.log('当前主机名:', window.location.hostname);

        // 如果指定了渐进式加载，使用渐进式加载但采用正常样式
        if (progressive === 'true' && window.progressiveLoader) {
            console.log('使用渐进式加载模式（正常样式）');
            await window.progressiveLoader.startProgressiveLoad(cardKey, reportId);
            return;
        }

        // 如果有文件路径参数，直接加载本地JSON文件
        if (filePath) {
            console.log('直接加载本地JSON文件:', filePath);
            await this.loadResultFromFile(filePath);
            return;
        }

        // 如果是管理员访问，使用管理员API
        if (isAdmin && adminToken && reportId) {
            console.log('管理员访问，使用管理员API获取报告:', reportId);
            await this.loadAdminReport(reportId, adminToken);
            return;
        }

        // 如果有卡密，通过API获取
        if (cardKey) {
            console.log('使用卡密获取分析结果:', cardKey, '报告ID:', reportId);
            await this.loadResultFromAPI(cardKey, reportId);
            return;
        }
        
        // 如果没有卡密但有报告ID，尝试直接通过API获取
        if (reportId) {
            console.log('尝试通过API获取报告:', reportId);
            // 从reportId中提取卡密（格式：cardKey_timestamp_uuid）
            const cardKeyFromId = reportId.split('_')[0];
            if (cardKeyFromId) {
                console.log('从报告ID提取卡密:', cardKeyFromId);
                await this.loadResultFromAPI(cardKeyFromId, reportId);
            } else {
                // 如果无法从ID提取卡密，尝试从本地存储获取
                console.log('无法从ID提取卡密，尝试从本地存储获取报告:', reportId);
                await this.loadResultFromLocal(reportId);
            }
            return;
        }
        
        // 尝试从localStorage加载
        const savedResult = localStorage.getItem('baziAnalysisResult');
        if (savedResult) {
            try {
                const result = JSON.parse(savedResult);
                this.displayResult(result);
                return;
            } catch (error) {
                console.error('解析保存的结果失败:', error);
                this.showError('解析数据失败', '保存的分析结果格式错误，请重新进行分析');
                return;
            }
        }
        
        // 如果没有任何数据，显示空状态
        this.showEmptyState();
        
    } catch (error) {
        console.error('加载分析结果失败:', error);
        this.showError('加载分析结果失败', error.message || '请检查网络连接或重试');
    }
}

/**
 * 管理员加载报告
 * @param {string} reportId - 报告ID
 * @param {string} adminToken - 管理员token
 * @returns {Promise<void>}
 */
async function loadAdminReport(reportId, adminToken) {
    try {
        console.log('管理员加载报告:', reportId);

        const apiBaseUrl = getApiBaseUrl();
        const url = `${apiBaseUrl}/api/admin/reports/${reportId}?token=${adminToken}`;

        console.log('请求管理员API:', url);

        const response = await fetch(url);

        if (!response.ok) {
            if (response.status === 401) {
                this.showError('权限验证失败', '管理员token无效');
                return;
            } else if (response.status === 404) {
                this.showError('报告不存在', `找不到ID为 ${reportId} 的报告`);
                return;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }

        const result = await response.json();
        console.log('管理员API返回结果:', result);

        if (result) {
            this.displayResult(result);
        } else {
            this.showError('数据为空', '管理员API返回的数据为空');
        }

    } catch (error) {
        console.error('管理员加载报告失败:', error);
        this.showError('加载失败', `管理员加载报告时出错: ${error.message}`);
    }
}

/**
 * 通过API加载分析结果
 * @param {string} cardKey - 卡密
 * @param {string} reportId - 报告ID（可选）
 * @param {number} retryCount - 重试次数
 * @returns {Promise<void>}
 */
async function loadResultFromAPI(cardKey, reportId, retryCount = 0) {
    const maxRetries = 20; // 最大重试次数，约100秒
    
    try {
        // 使用通用函数获取API基础URL
        const apiBaseUrl = getApiBaseUrl();
        console.log('API基础URL:', apiBaseUrl);
        
        // 如果有具体的reportId，优先尝试直接获取历史报告
        if (reportId && reportId !== cardKey) {
            console.log('尝试直接获取历史报告:', reportId);
            const historyResult = await this.loadHistoryReport(cardKey, reportId);
            if (historyResult) {
                console.log('成功获取历史报告');
                return;
            }
            console.log('历史报告获取失败，回退到状态检查');
        }
        
        // 始终使用cardKey进行查询，reportId作为附加参数
        let queryUrl = `${apiBaseUrl}/webhook/check-status?cardKey=${encodeURIComponent(cardKey)}`;
        if (reportId) {
            queryUrl += `&reportId=${encodeURIComponent(reportId)}`;
        }
        
        console.log(`正在请求API (第${retryCount + 1}次):`, queryUrl);
        const response = await fetch(queryUrl);
        
        console.log('API响应状态:', response.status, response.statusText);
        
        if (response.ok) {
            const result = await response.json();
            console.log('API返回结果:', result);
            
            if (result.completed && result.result) {
                // 验证返回的结果是否属于请求的卡密
                const resultCardKey = result.result.cardKey || 
                                     result.result.card_key ||
                                     (result.result.data && result.result.data.cardKey) ||
                                     (result.result.data && result.result.data.card_key) ||
                                     (result.result.analysisData && result.result.analysisData.cardKey) ||
                                     (result.result.analysisData && result.result.analysisData.card_key);
                
                console.log('提取的卡密:', resultCardKey, '请求的卡密:', cardKey);
                
                if (resultCardKey && resultCardKey === cardKey) {
                    this.displayResult(result.result);
                    this.updateGenerateTime();
                    return;
                } else {
                    console.warn('卡密验证失败 - 提取的卡密:', resultCardKey, '请求的卡密:', cardKey);
                    throw new Error('卡密验证失败，无法访问该报告');
                }
            } else if (!result.completed) {
                // 检查是否超过最大重试次数
                if (retryCount >= maxRetries) {
                    console.error('轮询超时，分析可能遇到问题');
                    this.showError('分析超时，请刷新页面重试或联系管理员');
                    return;
                }
                
                // 分析尚未完成，显示进度信息并设置轮询
                console.log('分析进行中，进度:', result.progress || 0, '%');
                this.showAnalysisProgress(result);
                
                // 设置轮询，每5秒检查一次
                setTimeout(() => {
                    console.log('轮询检查分析状态...');
                    this.loadResultFromAPI(cardKey, reportId, retryCount + 1);
                }, 5000);
                return;
            } else if (result.completed && !result.result) {
                // 分析已完成但没有结果数据，可能是历史报告，尝试重新获取
                if (retryCount < 3) { // 限制重新获取次数
                    console.log('分析已完成但无结果数据，尝试重新获取...');
                    setTimeout(() => {
                        this.loadResultFromAPI(cardKey, reportId, retryCount + 1);
                    }, 1000);
                    return;
                } else {
                    console.error('多次尝试后仍无法获取结果数据');
                    this.showError('无法获取分析结果，请刷新页面重试');
                    return;
                }
            } else {
                console.warn('API返回数据不完整:', { completed: result.completed, hasResult: !!result.result });
                throw new Error('分析结果尚未完成或数据不完整');
            }
        } else {
            console.error('API请求失败:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('错误详情:', errorText);
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        throw new Error('无法从API获取分析结果');
    } catch (error) {
        console.error('从API加载结果失败:', error);

        // 增强的重试逻辑：检查更多类型的可重试错误
        const isRetryableError = error.name === 'TypeError' ||
                                error.message.includes('fetch') ||
                                error.message.includes('network') ||
                                error.message.includes('timeout') ||
                                error.message.includes('500') ||
                                error.message.includes('502') ||
                                error.message.includes('503') ||
                                error.message.includes('504') ||
                                error.message.includes('Internal Server Error') ||
                                error.message.includes('服务器内部错误');

        if (retryCount < 3 && isRetryableError) {
            const delay = Math.min(2 ** (retryCount + 1), 8); // 指数退避：2, 4, 8秒
            console.log(`🔄 遇到可重试错误，${delay}秒后重试 (${retryCount + 1}/3):`, error.message);
            setTimeout(() => {
                this.loadResultFromAPI(cardKey, reportId, retryCount + 1);
            }, delay * 1000);
            return;
        }

        // 如果常规加载失败，尝试渐进式加载
        console.log('常规加载失败，尝试渐进式加载...');
        if (window.progressiveLoader && retryCount === 0) {
            try {
                await window.progressiveLoader.startProgressiveLoad(cardKey, reportId);
                return;
            } catch (progressiveError) {
                console.error('渐进式加载也失败:', progressiveError);
            }
        }

        this.showError('无法获取分析结果，请检查卡密是否正确或返回重新进行分析');
    }
}

/**
 * 独立加载历史报告（绕过状态检查）
 * @param {string} cardKey - 卡密
 * @param {string} reportId - 报告ID
 * @returns {Promise<boolean>} - 是否成功加载
 */
async function loadHistoryReport(cardKey, reportId) {
    try {
        // 使用通用函数获取API基础URL
        const apiBaseUrl = getApiBaseUrl();
        console.log('历史报告API基础URL:', apiBaseUrl);
        
        // 尝试通过历史报告API直接获取
        const historyUrl = `${apiBaseUrl}/api/reports/card/${encodeURIComponent(cardKey)}`;
        console.log('请求历史报告API:', historyUrl);
        
        const response = await fetch(historyUrl);
        
        if (response.ok) {
            const historyData = await response.json();
            console.log('历史报告API返回:', historyData);
            
            // 检查API返回格式，后端返回 {success: true, data: reports}
            if (historyData.success && historyData.data && Array.isArray(historyData.data)) {
                const reports = historyData.data;
                
                // 查找匹配的报告
                const targetReport = reports.find(report => 
                    report.id === reportId || 
                    report.reportId === reportId ||
                    report.request_id === reportId
                );
                
                if (targetReport && targetReport.data) {
                    console.log('找到目标历史报告:', targetReport);
                    this.displayResult(targetReport.data);
                    this.updateGenerateTime();
                    return true;
                }
                
                // 如果没有找到特定报告，显示最新的报告
                if (reports.length > 0 && reports[0].data) {
                    console.log('显示最新历史报告:', reports[0]);
                    this.displayResult(reports[0].data);
                    this.updateGenerateTime();
                    return true;
                }
            }
            
            // 兼容旧格式：直接返回reports数组
            if (historyData.reports && Array.isArray(historyData.reports)) {
                const targetReport = historyData.reports.find(report => 
                    report.id === reportId || 
                    report.reportId === reportId ||
                    report.request_id === reportId
                );
                
                if (targetReport && targetReport.result) {
                    console.log('找到目标历史报告(旧格式):', targetReport);
                    this.displayResult(targetReport.result);
                    this.updateGenerateTime();
                    return true;
                }
            }
            
            // 兼容旧格式：直接返回result
            if (historyData.result) {
                console.log('显示历史报告(旧格式)');
                this.displayResult(historyData.result);
                this.updateGenerateTime();
                return true;
            }
        }
        
        return false;
    } catch (error) {
        console.error('加载历史报告失败:', error);
        return false;
    }
}

/**
 * 从文件加载分析结果
 * @param {string} filePath - 文件路径
 * @returns {Promise<void>}
 */
async function loadResultFromFile(filePath) {
    try {
        // 直接加载本地JSON文件
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`无法加载文件: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('成功加载本地JSON文件:', data);
        
        this.displayResult(data);
        this.updateGenerateTime();
        
    } catch (error) {
        console.error('从文件加载结果失败:', error);
        this.showError('无法加载指定的分析结果文件');
    }
}

/**
 * 从本地存储加载分析结果
 * @param {string} reportId - 报告ID
 * @returns {Promise<void>}
 */
async function loadResultFromLocal(reportId) {
    try {
        // 从localStorage获取报告
        const reportsJson = localStorage.getItem('baziReports');
        if (!reportsJson) {
            throw new Error('本地没有找到报告数据');
        }
        
        const reports = JSON.parse(reportsJson);
        const report = reports.find(r => r.id === reportId);
        
        if (!report) {
            throw new Error('未找到指定的报告');
        }
        
        if (!report.cardKey) {
            throw new Error('报告缺少卡密信息，无法验证访问权限');
        }
        
        // 使用报告中的卡密信息通过API获取完整数据
        console.log('从本地报告获取卡密:', report.cardKey, '重新通过API获取数据');
        await this.loadResultFromAPI(report.cardKey, reportId);
        
    } catch (error) {
        console.error('从本地加载结果失败:', error);
        this.showError('无法从本地获取报告数据，请返回重新进行分析');
    }
}