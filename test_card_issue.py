#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定卡密的问题
"""

import requests
import time

def test_card_issue():
    """测试卡密问题"""
    print("🔍 测试卡密问题")
    print("=" * 60)
    
    # 测试不同的卡密
    test_cases = [
        {
            "name": "测试wdd卡密 - 1995年6月20日午时",
            "cardKey": "wdd",
            "data": {
                "year": "1995",
                "month": "6",
                "day": "20",
                "hour": "午时",
                "gender": "1"
            }
        },
        {
            "name": "测试Xs1MR9iVx9RNZOk6卡密 - 1995年6月20日午时",
            "cardKey": "Xs1MR9iVx9RNZOk6",
            "data": {
                "year": "1995",
                "month": "6",
                "day": "20",
                "hour": "午时",
                "gender": "1"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送分析请求
            payload = {
                "cardKey": test_case["cardKey"],
                **test_case["data"]
            }
            
            print(f"📤 发送请求: {payload}")
            
            response = requests.post(
                'http://localhost:5000/webhook/bazi-analysis',
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                request_id = result.get('requestId')
                print(f"✅ 请求成功，ID: {request_id}")
                
                # 等待处理
                print("⏳ 等待5秒...")
                time.sleep(5)
                
                # 查询结果 - 使用卡密查询
                print(f"🔍 查询卡密 {test_case['cardKey']} 的结果...")
                result_response = requests.get(f'http://localhost:5000/api/get_result/{test_case["cardKey"]}')
                
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    
                    result_obj = result_data.get('result', {})
                    bz = result_obj.get('data', {}).get('bz', {})
                    
                    if bz:
                        birth_info = bz.get('8', '未知')
                        print(f"📅 返回的生辰: {birth_info}")
                        
                        # 检查是否是我们刚才提交的时间
                        if '1995' in birth_info and '五月' in birth_info:
                            print("✅ 返回了正确的1995年数据")
                        else:
                            print(f"❌ 返回了错误的数据: {birth_info}")
                            print("   应该是1995年五月的数据")
                    else:
                        print("❌ 没有八字数据")
                else:
                    print(f"❌ 查询失败: {result_response.status_code}")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"💥 测试失败: {str(e)}")

def check_existing_data():
    """检查现有数据"""
    print(f"\n📊 检查现有数据")
    print("=" * 60)
    
    cards_to_check = ["wdd", "Xs1MR9iVx9RNZOk6"]
    
    for card in cards_to_check:
        print(f"\n🔍 检查卡密: {card}")
        print("-" * 30)
        
        try:
            response = requests.get(f'http://localhost:5000/api/get_result/{card}')
            
            if response.status_code == 200:
                data = response.json()
                result_obj = data.get('result', {})
                bz = result_obj.get('data', {}).get('bz', {})
                
                if bz:
                    birth_info = bz.get('8', '未知')
                    bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                    
                    print(f"  📅 生辰: {birth_info}")
                    print(f"  🔮 八字: {bazi_str}")
                    print(f"  ✅ 完成状态: {data.get('completed', 'N/A')}")
                    print(f"  🤖 LLM分析: {data.get('has_llm_analysis', 'N/A')}")
                else:
                    print(f"  ❌ 没有八字数据")
            else:
                print(f"  ❌ 查询失败: {response.status_code}")
                
        except Exception as e:
            print(f"  💥 查询失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 卡密问题诊断")
    print("=" * 60)
    
    # 检查现有数据
    check_existing_data()
    
    # 测试新请求
    test_card_issue()
    
    print(f"\n📋 问题分析")
    print("=" * 60)
    print("如果 Xs1MR9iVx9RNZOk6 总是返回 '2010年冬月廿十 酉时'，")
    print("说明系统中有这个卡密的旧数据，API优先返回了旧数据。")
    print("")
    print("解决方案：")
    print("1. 清理特定卡密的旧数据")
    print("2. 修改API逻辑，让新请求覆盖旧数据")
    print("3. 使用不同的卡密进行测试")

if __name__ == "__main__":
    main()
