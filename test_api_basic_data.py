#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API基础数据返回
"""

import requests
import json

def test_api_basic_data():
    """测试API是否返回基础数据"""
    print("🎯 测试API基础数据返回")
    print("=" * 60)
    
    # 测试API调用
    url = 'http://localhost:5000/api/get_result/Xs1MR9iVx9RNZOk6'
    
    print(f"📡 请求URL: {url}")
    
    response = requests.get(url)
    
    if response.status_code == 200:
        result = response.json()
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查是否有基础数据
        if result.get('processing') == True:
            print("\n🔍 检测到处理中状态")
            
            if result.get('result') and result.get('result', {}).get('data'):
                print("✅ 找到基础八字数据！")
                bz_data = result['result']['data'].get('bz', {})
                if bz_data:
                    birth_info = bz_data.get('8', '未知')
                    print(f"📅 生辰信息: {birth_info}")
                else:
                    print("❌ 八字数据为空")
            else:
                print("❌ 没有找到基础八字数据")
        elif result.get('completed') == True:
            print("\n✅ 处理已完成")
            if result.get('result') and result.get('result', {}).get('data'):
                print("✅ 找到完整结果数据")
            else:
                print("❌ 没有找到结果数据")
        else:
            print(f"\n⚠️ 未知状态: {result}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        print(f"响应内容: {response.text}")

def main():
    """主函数"""
    test_api_basic_data()

if __name__ == "__main__":
    main()
