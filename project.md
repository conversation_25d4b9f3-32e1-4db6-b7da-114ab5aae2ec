# 八字分析系统项目结构

## 项目概述

本项目是一个基于网页的八字分析系统，提供命理分析服务。系统采用前后端分离架构，前端使用纯HTML/CSS/JavaScript实现，后端使用Python提供API服务。

## 目录结构

```
项目根目录
├── index.html                 # 系统首页
├── up.html                    # 用户信息输入页面
├── result.html                # 分析结果列表页面
├── detailed_result.html       # 详细分析结果页面
├── config.html                # 系统配置页面
├── card_generator.html        # 卡密生成器页面
├── blog.html                  # 博客页面
├── article.html               # 文章详情页面
├── styles.css                 # 全局样式表
├── favicon.ico                # 网站图标
├── final_review_gate.py       # 审查脚本
├── bazi_results.json          # 八字分析结果示例数据
├── cards.json                 # 卡密数据
├── CONFIG_README.md           # 配置说明文档
├── design_prompts.md          # 设计提示文档
├── script.js.backup           # 脚本备份
│
├── css/                       # CSS样式目录
│   ├── article.css            # 文章页面样式
│   ├── blog.css               # 博客页面样式
│   ├── config.css             # 配置页面样式
│   ├── debug.css              # 调试样式
│   ├── detailed_result.css    # 详细结果页面样式
│   ├── index.css              # 首页样式
│   ├── result.css             # 结果列表页面样式
│   ├── up.css                 # 用户信息输入页面样式
│   └── card_generator.css     # 卡密生成器页面样式
│
├── js/                        # JavaScript脚本目录
│   ├── analyzer.js            # 分析处理脚本
│   ├── api.js                 # API接口封装
│   ├── config.js              # 配置处理脚本
│   ├── detailed_result.js     # 详细结果页面脚本
│   ├── main.js                # 主要脚本
│   ├── README.md              # JavaScript说明文档
│   ├── ui.js                  # UI交互脚本
│   └── utils.js               # 工具函数
│
├── backend/                   # 后端代码目录
│   ├── app.py                 # 主应用入口
│   ├── bazi_analyzer.py       # 八字分析核心逻辑
│   ├── bazi_service.py        # 八字服务
│   ├── bazi_summary.py        # 八字总结生成
│   ├── card_manager.py        # 卡密管理
│   ├── config_api.py          # 配置API
│   ├── config_manager.py      # 配置管理
│   ├── LLMapi.py              # 大语言模型API接口
│   ├── request_manager.py     # 请求管理
│   ├── start_server.py        # 服务器启动脚本
│   ├── url_generator.py       # URL生成工具
│   ├── config.json            # 系统配置文件
│   ├── requirements.txt       # Python依赖列表
│   ├── cards.json             # 卡密数据
│   ├── bazi_results.json      # 八字结果数据
│   │
│   ├── prompts/               # 提示词目录
│   ├── analysis_results/      # 分析结果存储目录
│   ├── llm_logs/              # 大语言模型日志目录
│   └── summaries/             # 摘要存储目录
│
├── analysis_results/          # 前端分析结果存储
├── articles/                  # 文章内容存储
├── llm_logs/                  # 前端LLM日志存储
├── pic/                       # 图片资源目录
└── summaries/                 # 前端摘要存储
```

## 核心功能模块

### 前端模块

1. **用户界面**
   - `index.html`: 系统首页，提供系统导航和功能入口
   - `up.html`: 用户输入页面，收集用户的八字信息
   - `result.html`: 显示历史分析报告列表
   - `detailed_result.html`: 显示单个报告的详细分析结果
   - `config.html`: 系统配置界面
   - `card_generator.html`: 卡密生成和管理界面
   - `blog.html`: 博客文章列表
   - `article.html`: 单篇文章详情页

2. **核心脚本**
   - `js/api.js`: 封装与后端API的交互
   - `js/analyzer.js`: 前端分析处理逻辑
   - `js/detailed_result.js`: 详细结果页面的交互和数据处理
   - `js/ui.js`: 用户界面交互逻辑

3. **样式表**
   - `styles.css`: 全局样式定义
   - 各页面专用样式表位于`css/`目录

### 后端模块

1. **核心服务**
   - `backend/app.py`: Flask应用主入口，提供HTTP API
   - `backend/start_server.py`: 服务器启动脚本

2. **八字分析引擎**
   - `backend/bazi_analyzer.py`: 八字分析核心算法
   - `backend/bazi_service.py`: 八字服务层，处理分析请求
   - `backend/bazi_summary.py`: 生成八字分析摘要

3. **系统管理**
   - `backend/card_manager.py`: 卡密管理系统
   - `backend/config_manager.py`: 配置管理
   - `backend/config_api.py`: 配置API路由
   - `backend/request_manager.py`: 请求处理和管理

4. **AI集成**
   - `backend/LLMapi.py`: 大语言模型API集成

## 数据流向

1. 用户在`up.html`页面输入个人信息
2. 数据通过API发送到后端`app.py`
3. 后端使用`bazi_analyzer.py`进行分析
4. 分析结果存储在`analysis_results/`目录
5. 结果返回前端，显示在`result.html`和`detailed_result.html`页面

## API路径

### 卡密管理API

1. **查询卡密**
   - `GET /api/cards`: 获取所有卡密信息
   - `GET /api/cards/json`: 获取原始卡密JSON数据
   - `GET /api/config/cards`: 获取有效卡密列表

2. **卡密操作**
   - `POST /api/config/cards/add`: 添加新卡密
   - `POST /api/config/cards/remove`: 从配置中移除卡密
   - `POST /api/cards/remove`: 彻底删除单个卡密
   - `POST /api/cards/batch-remove`: 批量删除卡密
   - `POST /api/validate-card`: 验证卡密有效性

### 八字分析API

1. **分析请求**
   - `GET /webhook/a7712e3b-7f96-4b2e-9573-a6f87d9fd848`: 处理八字请求
   - `POST /webhook/bazi-analysis`: 处理八字分析请求

2. **状态查询**
   - `GET /webhook/check-status`: 检查处理状态
   - `GET /api/analysis/status/<analysis_id>`: 获取分析状态
   - `GET /api/analysis/llm-progress/<analysis_id>`: 获取LLM分析进度

3. **报告管理**
   - `GET /api/reports/list`: 获取历史报告列表
   - `GET /api/reports/card/<card_key>`: 获取特定卡密的历史报告
   - `DELETE /api/reports/delete/<report_id>`: 删除特定的分析报告

## 依赖关系

### 前端依赖
- HTML5
- CSS3
- JavaScript (ES6+)
- 无需额外的JavaScript库，使用原生JavaScript实现

### 后端依赖
- Python 3.8+
- Flask 2.3.3 - Web框架
- Flask-CORS 4.0.0 - 跨域资源共享支持
- Requests 2.31.0 - HTTP请求库
- 其他可能的依赖在代码中动态引入

## 部署说明

1. 安装后端依赖: `pip install -r backend/requirements.txt`
2. 启动后端服务: `python backend/start_server.py`
3. 使用Web服务器(如Nginx)提供前端静态文件
4. 配置`backend/config.json`以适应部署环境

## 开发指南

- 前端开发主要修改HTML、CSS和JavaScript文件
- 后端开发主要修改Python文件
- 测试文件位于`backend/`目录下，以`test_`开头
- 使用`final_review_gate.py`进行代码审查

## 系统功能

### 核心功能
1. **八字分析** - 根据用户输入的出生日期时间生成八字分析
2. **历史报告管理** - 存储和查看历史分析报告
3. **详细分析结果展示** - 多维度展示分析结果
4. **卡密系统** - 通过卡密控制用户访问权限

### 辅助功能
1. **系统配置** - 通过`config.html`页面管理系统参数
2. **卡密管理** - 通过`card_generator.html`页面生成和管理卡密
3. **博客系统** - 提供相关文章和知识
4. **数据导出** - 支持下载分析报告 