#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理API接口
提供配置的查看、修改和保存功能
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
from config_manager import get_config
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
config_bp = Blueprint('config', __name__, url_prefix='/api/config')
CORS(config_bp)  # 允许跨域请求

@config_bp.route('/get', methods=['GET'])
def get_config_api():
    """获取当前配置"""
    try:
        config = get_config()
        return jsonify({
            'success': True,
            'config': config.config
        })
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/set', methods=['POST'])
def set_config_api():
    """设置配置项"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        key = data.get('key')
        value = data.get('value')
        
        if not key:
            return jsonify({
                'success': False,
                'error': '配置键不能为空'
            }), 400
        
        config = get_config()
        config.set(key, value)
        
        return jsonify({
            'success': True,
            'message': f'配置项 {key} 已更新'
        })
    except Exception as e:
        logger.error(f"设置配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/save', methods=['POST'])
def save_config_api():
    """保存配置到文件"""
    try:
        config = get_config()
        success = config.save()
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置已保存到文件'
            })
        else:
            return jsonify({
                'success': False,
                'error': '保存配置文件失败'
            }), 500
    except Exception as e:
        logger.error(f"保存配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/reload', methods=['POST'])
def reload_config_api():
    """重新加载配置文件"""
    try:
        config = get_config()
        config.reload()
        
        # 同步卡密数据 - 确保config.valid_cards与card_manager.cards_data保持一致
        try:
            from card_manager_singleton import get_card_manager
            card_manager = get_card_manager()
            
            # 获取卡密文件中的所有卡密
            cards_in_file = list(card_manager.cards_data.keys())
            
            # 获取配置中的卡密列表
            cards_in_config = config.get_valid_cards()
            
            # 如果不一致，以cards.json为准，更新配置
            if set(cards_in_file) != set(cards_in_config):
                logger.info(f"同步卡密数据: 卡密文件中有 {len(cards_in_file)} 个卡密，配置中有 {len(cards_in_config)} 个卡密")
                
                # 更新配置中的卡密列表
                config.set('card_validation.valid_cards', cards_in_file)
                
                # 保存配置
                config.save()
                
                logger.info(f"卡密数据已同步: {cards_in_file}")
        except Exception as sync_error:
            logger.error(f"同步卡密数据失败: {str(sync_error)}")
        
        return jsonify({
            'success': True,
            'message': '配置已重新加载'
        })
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/toggle-personality-only', methods=['POST'])
def toggle_personality_only_api():
    """切换是否只分析性格维度"""
    try:
        config = get_config()
        old_value = config.is_personality_only()
        config.toggle_personality_only()
        new_value = config.is_personality_only()
        
        return jsonify({
            'success': True,
            'message': f'性格分析模式已切换: {old_value} -> {new_value}',
            'old_value': old_value,
            'new_value': new_value,
            'enabled_dimensions': config.get_enabled_dimensions()
        })
    except Exception as e:
        logger.error(f"切换性格分析模式失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/dimensions', methods=['GET'])
def get_dimensions_api():
    """获取维度配置"""
    try:
        config = get_config()
        return jsonify({
            'success': True,
            'available_dimensions': config.get_available_dimensions(),
            'enabled_dimensions': config.get_enabled_dimensions(),
            'personality_only': config.is_personality_only()
        })
    except Exception as e:
        logger.error(f"获取维度配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/dimensions', methods=['POST'])
def set_dimensions_api():
    """设置启用的维度"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        dimensions = data.get('dimensions', [])
        if not isinstance(dimensions, list):
            return jsonify({
                'success': False,
                'error': '维度必须是数组格式'
            }), 400
        
        config = get_config()
        available = config.get_available_dimensions()
        
        # 验证维度是否有效
        invalid_dimensions = [d for d in dimensions if d not in available]
        if invalid_dimensions:
            return jsonify({
                'success': False,
                'error': f'无效的维度: {invalid_dimensions}'
            }), 400
        
        config.set_enabled_dimensions(dimensions)
        
        # 如果只启用了性格维度，自动设置personality_only为True
        if dimensions == ['性格特征']:
            config.set('analysis.personality_only', True)
        elif len(dimensions) > 1:
            config.set('analysis.personality_only', False)
        
        return jsonify({
            'success': True,
            'message': f'已设置启用维度: {dimensions}',
            'enabled_dimensions': config.get_enabled_dimensions(),
            'personality_only': config.is_personality_only()
        })
    except Exception as e:
        logger.error(f"设置维度失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/cards', methods=['GET'])
def get_cards_api():
    """获取卡密配置"""
    try:
        config = get_config()
        return jsonify({
            'success': True,
            'enabled': config.is_card_validation_enabled(),
            'valid_cards': config.get_valid_cards()
        })
    except Exception as e:
        logger.error(f"获取卡密配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/cards/add', methods=['POST'])
def add_card_api():
    """添加卡密"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        card = data.get('card', '').strip()
        if not card:
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        # 获取有效期和使用次数参数
        expire_days = data.get('expire_days', 30)
        max_usage = data.get('max_usage', 100)
        
        # 确保参数类型正确
        try:
            expire_days = int(expire_days)
            max_usage = int(max_usage)
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': '有效期和使用次数必须是整数'
            }), 400
        
        # 验证参数范围
        if expire_days < 1:
            expire_days = 30  # 默认30天
        if max_usage < 1:
            max_usage = 100  # 默认100次
        
        # 获取卡密管理器
        from card_manager_singleton import get_card_manager
        card_manager = get_card_manager()
        
        # 添加卡密
        card_manager.add_card(card, expire_days, max_usage)
        
        # 同时更新配置
        config = get_config()
        config.add_valid_card(card)
        
        # 保存配置到文件，确保持久化
        config.save()
        
        # 确保card_manager中的数据被保存
        card_manager._save_cards()
        
        # 获取cards.json中的所有卡密作为实际可用卡密
        cards_from_file = list(card_manager.cards_data.keys())
        
        # 重新读取配置，确保内存中数据为最新
        config.reload()
        
        # 确保配置中的卡密列表与文件一致
        if set(cards_from_file) != set(config.get_valid_cards()):
            logger.info(f"更新配置中的卡密列表，使其与文件一致")
            config.set('card_validation.valid_cards', cards_from_file)
            config.save()
        
        logger.info(f"添加卡密成功: {card}, 有效期: {expire_days}天, 最大使用次数: {max_usage}次")
        logger.info(f"当前有效卡密数量: {len(cards_from_file)}")
        
        return jsonify({
            'success': True,
            'message': f'卡密 {card} 已添加',
            'valid_cards': cards_from_file
        })
    except Exception as e:
        logger.error(f"添加卡密失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@config_bp.route('/cards/remove', methods=['POST'])
def remove_card_api():
    """移除卡密"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        card = data.get('card', '').strip()
        if not card:
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        # 从配置中移除卡密
        config = get_config()
        config.remove_valid_card(card)
        
        # 从卡密管理器中彻底删除卡密
        try:
            from card_manager_singleton import get_card_manager
            card_manager = get_card_manager()
            removed = card_manager.remove_card(card)
            if removed:
                logger.info(f"卡密已彻底删除: {card}")
            else:
                logger.warning(f"卡密不存在，无法删除: {card}")
        except Exception as card_error:
            logger.error(f"删除卡密失败: {card_error}")
        
        return jsonify({
            'success': True,
            'message': f'卡密 {card} 已移除',
            'valid_cards': config.get_valid_cards()
        })
    except Exception as e:
        logger.error(f"移除卡密失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500