@echo off
echo ========================================
echo     八字分析系统 - 服务器启动脚本
echo ========================================
echo.
echo 正在启动后端服务器...
echo 启动时间: %DATE% %TIME%
echo.

cd /d "%~dp0backend"
echo 当前目录: %CD%
echo.

echo 检查Python环境...
python --version
echo.

echo 启动Flask服务器...
echo 服务器地址: http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo.
echo ========================================
python app.py
echo.
echo 服务器已停止
pause
