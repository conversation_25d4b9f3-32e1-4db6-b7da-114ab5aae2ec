# 测试八字分析API
$uri = "http://localhost:5000/webhook/bazi-analysis"
$headers = @{
    'Content-Type' = 'application/json'
}
$body = @{
    cardKey = "4QokDJieJvLUZORR"
    name = "测试用户"
    gender = "0"
    year = "1990"
    month = "5"
    day = "15"
    hour = "10"
    minute = "30"
    isLunar = $false
} | ConvertTo-Json

Write-Host "正在测试API: $uri"
Write-Host "请求数据: $body"
Write-Host ("=" * 50)

try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers $headers -Body $body
    Write-Host "状态码: $($response.StatusCode)"
    Write-Host "响应内容: $($response.Content)"
    Write-Host "测试成功！卡密验证通过。"
} catch {
    Write-Host "错误: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode.value__)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "错误响应: $responseBody"
    }
}