/**
 * 报告管理器核心
 * 提供历史报告的基础管理功能
 */

class HistoryReportsManager {
    constructor() {
        this.reportsList = document.getElementById('reportsList');
        this.init();
    }
    
    init() {
        console.log('初始化报告管理器');
        this.loadHistoryReports();
        this.initEventListeners();
    }
    
    initEventListeners() {
        // 初始化事件监听
        console.log('初始化报告管理器事件监听');
    }
    
    // 提取原始出生日期
    extractOriginalBirthDate(report) {
        console.log('尝试提取原始出生日期:', report.id);
        
        // 多个可能的路径获取原始请求数据
        let requestData = null;
        
        // 路径1: report.analysisData.requestData
        if (report.analysisData && report.analysisData.requestData) {
            requestData = report.analysisData.requestData;
        }
        // 路径2: report.data.requestData
        else if (report.data && report.data.requestData) {
            requestData = report.data.requestData;
        }
        // 路径3: report.data.resultData.requestData
        else if (report.data && report.data.resultData && report.data.resultData.requestData) {
            requestData = report.data.resultData.requestData;
        }
        // 路径4: report.resultData.requestData
        else if (report.resultData && report.resultData.requestData) {
            requestData = report.resultData.requestData;
        }
        
        if (requestData) {
            const year = requestData.year;
            const month = requestData.month;
            const day = requestData.day;
            
            if (year && month && day) {
                const formattedDate = `${year}年${month}月${day}日`;
                console.log('成功提取到原始出生日期:', formattedDate);
                return formattedDate;
            }
        }
        
        console.log('未找到原始出生日期');
        return null;
    }
    
    async loadHistoryReports() {
        try {
            console.log('调用loadHistoryReports方法，但主要逻辑已移至initializeReportPage');
            // 大部分逻辑已移至initializeReportPage函数
            // 这个方法现在主要是为了兼容性，实际上initializeReportPage会直接调用fetchHistoryReports
            const reports = await this.fetchHistoryReports();
            return reports;
        } catch (error) {
            console.error('加载历史报告失败:', error);
            this.showError('加载历史报告失败，请稍后重试');
            return [];
        }
    }
    
    checkRunningAnalysis() {
        // 检查URL参数中是否有requestId或cardKey
        const urlParams = new URLSearchParams(window.location.search);
        const requestId = urlParams.get('requestId');
        const cardKey = urlParams.get('cardKey');
        
        console.log('URL参数检查:', {requestId, cardKey});
        
        // 检查localStorage中是否有已完成的分析记录
        const completedAnalysis = localStorage.getItem('completedAnalysis');
        
        // 如果有URL参数，且与已完成的分析匹配，则不启动新分析
        if ((requestId || cardKey) && completedAnalysis) {
            try {
                const completed = JSON.parse(completedAnalysis);
                // 检查是否是同一次分析
                if ((requestId && completed.requestId === requestId) || 
                    (cardKey && completed.cardKey === cardKey)) {
                    
                    console.log('检测到已完成的分析，不再启动新分析');
                    this.clearAnalysisParams();
                    return null;
                }
            } catch (error) {
                console.error('解析已完成分析记录失败:', error);
                localStorage.removeItem('completedAnalysis');
            }
        }
        
        // 1. 首先检查URL参数 - 只有同时包含requestId和cardKey才认为是有效的分析请求
        if (requestId && cardKey) {
            console.log('从URL参数检测到完整的分析信息:', {requestId, cardKey});
            return {
                id: requestId,
                type: 'request',
                startTime: Date.now(),
                cardKey: cardKey,
                requestId: requestId
            };
        } else if (cardKey && !requestId) {
            console.log('⚠️ URL中只有cardKey没有requestId，这不是一个有效的分析请求，忽略');
            // 清除URL参数，避免误导
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('cardKey');
            window.history.replaceState({}, '', newUrl.toString());
            return null;
        }
        
        // 2. 然后检查localStorage中是否有正在进行的分析
        const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
        if (ongoingAnalysis) {
            try {
                const analysis = JSON.parse(ongoingAnalysis);
                console.log('从localStorage检测到进行中的分析:', analysis);
                
                // 检查是否超过30分钟，如果是则认为已失效
                if (Date.now() - analysis.startTime < 30 * 60 * 1000) {
                    return analysis;
                } else {
                    console.log('检测到的分析已超过30分钟，视为已失效');
                    localStorage.removeItem('ongoingAnalysis');
                }
            } catch (error) {
                console.error('解析进行中分析记录失败:', error);
                localStorage.removeItem('ongoingAnalysis');
            }
        }
        
        // 3. 没有检测到任何分析信息
        console.log('未检测到任何进行中的分析');
        return null;
    }
    
    displayContent(runningAnalysis, reports) {
        console.log('调用displayContent，使用新的显示函数');
        if (runningAnalysis) {
            // 使用新的显示函数
            displayAnalysisAndReports(runningAnalysis, reports);
        } else {
            // 使用新的显示函数
            displayReportsOnly(reports);
        }
    }
    
    // 注意：generateRunningAnalysisHtml 函数已移至 ui_reports_components.js
    // 避免重复定义，这里不再重复声明这个函数
    
    generateReportsHtml(reports) {
        console.log('开始生成报告HTML，报告数量:', reports ? reports.length : 0);
        
        // 检查报告数据是否有效
        if (!reports || !Array.isArray(reports) || reports.length === 0) {
            console.log('没有有效的报告数据，返回空状态');
            return this.generateEmptyStateHtml();
        }
        
        let reportsHtml = '<div class="reports-section"><h3>📋 历史报告</h3><div class="reports-grid">';
        
        reports.forEach(report => {
            try {
                // 记录日志以便调试
                console.log('处理报告:', report.id);
                
                // 安全地获取时间戳
                let timestamp = report.timestamp;
                if (typeof timestamp === 'string') {
                    timestamp = new Date(timestamp);
                } else if (typeof timestamp === 'number') {
                    timestamp = new Date(timestamp);
                } else {
                    timestamp = new Date(); // 默认使用当前时间
                }
                
                const formattedDate = timestamp.toLocaleString('zh-CN');
                
                // 安全地生成摘要和标题
                let summary = '';
                let reportTitle = '';
                
                try {
                    summary = this.generateReportSummary(report.data);
                    reportTitle = this.generateReportTitle(report);
                } catch (innerError) {
                    console.error('生成报告摘要或标题时出错:', innerError);
                    summary = '无法生成摘要';
                    reportTitle = report.id || '未命名报告';
                }
                
                reportsHtml += `
                    <div class="report-card" onclick="viewReport('${report.id}')">
                        <div class="report-header">
                            <h3 class="report-title">${reportTitle}</h3>
                            <span class="report-source ${report.source}">${report.source === 'local' ? '本地' : '云端'}</span>
                        </div>
                        <div class="report-meta">
                            <span class="report-date">📅 ${formattedDate}</span>
                        </div>
                        <div class="report-summary">
                            ${summary}
                        </div>
                        <div class="report-actions">
                            <button class="view-btn" onclick="event.stopPropagation(); viewReport('${report.id}')">📖 查看详情</button>
                            <button class="delete-btn" onclick="event.stopPropagation(); deleteReport('${report.id}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('处理报告卡片时出错:', error, report);
            }
        });
        
        reportsHtml += '</div></div>';
        return reportsHtml;
    }
    
    generateEmptyStateHtml() {
        return `
            <div class="no-reports-section">
                <div class="empty-state" style="padding: 20px; margin-top: 20px;">
                    <div class="empty-icon">📋</div>
                    <h3>暂无历史报告</h3>
                    <p>分析完成后，您的报告将显示在这里</p>
                    <a href="up.html" class="action-btn primary" style="display: inline-block; margin-top: 15px;">☯ 新建分析</a>
                </div>
            </div>
        `;
    }
} 