/**
 * 报告管理器 - 数据提取工具(3)
 * 处理报告数据中各种信息的提取
 */

// 添加一个辅助方法，用于递归搜索嵌套对象中的特定属性
HistoryReportsManager.prototype.searchNestedObject = function(obj, ...keys) {
    if (!obj || typeof obj !== 'object') return null;
    
    // 创建结果对象
    const result = {};
    let foundAny = false;
    
    // 检查当前对象是否有我们要找的键
    for (const key of keys) {
        if (obj[key] !== undefined) {
            console.log(`在嵌套对象中找到 ${key}:`, obj[key]);
            result[key] = obj[key];
            foundAny = true;
        }
    }
    
    // 如果在当前层级找到了任何信息，返回结果
    if (foundAny) {
        return result;
    }
    
    // 递归检查所有嵌套对象
    for (const prop in obj) {
        if (obj[prop] && typeof obj[prop] === 'object') {
            const nestedResult = this.searchNestedObject(obj[prop], ...keys);
            if (nestedResult) {
                return nestedResult;
            }
        }
    }
    
    return null;
};

// 生成报告摘要
HistoryReportsManager.prototype.generateReportSummary = function(data) {
    if (!data) return '<p>无法获取报告摘要</p>';
    
    try {
        console.log('生成摘要的原始数据:', data);
        // 尝试提取摘要信息
        let summary = '';
        
        // 处理可能的嵌套结构
        let actualData = data;
        if (data.data && typeof data.data === 'object') {
            actualData = data.data;
        }
        
        // 提取基本信息
        let basicInfo = actualData.basicInfo || actualData.data || {};
        if (typeof basicInfo !== 'object') basicInfo = {};
        
        // 显示八字信息
        if (basicInfo.bazi) {
            summary += `<p><strong>八字:</strong> ${basicInfo.bazi || '未知'}</p>`;
        }
        
        // 显示性别和出生日期
        let genderInfo = '';
        let dateInfo = '';
        
        if (basicInfo.gender) {
            const genderText = basicInfo.gender === 'male' || basicInfo.gender === '1' ? '男' : '女';
            genderInfo = genderText;
        }
        
        if (basicInfo.birth_date) {
            dateInfo = basicInfo.birth_date;
        }
        
        if (genderInfo || dateInfo) {
            summary += `<p><strong>基本信息:</strong> ${genderInfo} ${dateInfo}</p>`;
        }
        
        // 尝试获取摘要文本
        let summaryText = '';
        
        // 处理各种可能的摘要位置
        if (actualData.analysis && actualData.analysis.summary) {
            summaryText = actualData.analysis.summary;
        } else if (actualData.summary) {
            if (typeof actualData.summary === 'string') {
                summaryText = actualData.summary;
            } else if (typeof actualData.summary === 'object' && actualData.summary !== null) {
                summaryText = JSON.stringify(actualData.summary);
            }
        } else if (actualData.llm_analysis && actualData.llm_analysis.results) {
            // 尝试从LLM结果中提取第一个维度作为摘要
            const results = actualData.llm_analysis.results;
            if (typeof results === 'object' && results !== null) {
                const firstKey = Object.keys(results)[0];
                if (firstKey && results[firstKey]) {
                    if (typeof results[firstKey] === 'string') {
                        summaryText = results[firstKey].substring(0, 100);
                    } else if (typeof results[firstKey] === 'object') {
                        summaryText = JSON.stringify(results[firstKey]).substring(0, 100);
                    }
                }
            }
        }
        
        if (summaryText) {
            const displayText = summaryText.length > 80 ? 
                summaryText.substring(0, 80) + '...' : 
                summaryText;
            summary += `<p>${displayText}</p>`;
        }
        
        return summary || '<p>点击查看完整分析报告</p>';
    } catch (error) {
        console.error('生成报告摘要失败:', error, '数据:', data);
        return '<p>点击查看完整分析报告</p>';
    }
}; 