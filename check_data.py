#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统中的八字数据
"""

import sys
sys.path.append('backend')

from bazi_service import BaziService

def check_bazi_data():
    """检查八字数据"""
    bs = BaziService()
    
    print(f"总结果数: {len(bs.results)}")
    print("\n前10个请求的详细信息:")
    print("=" * 80)
    
    for i, (req_id, result) in enumerate(list(bs.results.items())[:10]):
        print(f"\n{i+1}. 请求ID: {req_id}")
        
        # 基本信息
        card_key = result.get('card_key', '未知')
        print(f"   卡密: {card_key}")
        
        # 八字数据
        data = result.get('data', {})
        bz = data.get('bz', {})
        
        if bz:
            birth_info = bz.get('8', '未知生辰')
            print(f"   生辰: {birth_info}")
            
            # 显示八字四柱
            if len(bz) >= 8:
                bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                print(f"   八字: {bazi_str}")
        else:
            print("   八字: 无数据")
        
        # 处理时间
        processed_time = result.get('processed_time', '未知')
        print(f"   处理时间: {processed_time}")
        
        print("-" * 60)

def check_specific_card(card_key):
    """检查特定卡密的所有请求"""
    bs = BaziService()
    
    print(f"\n检查卡密 {card_key} 的所有请求:")
    print("=" * 60)
    
    matching_requests = []
    for req_id, result in bs.results.items():
        if result.get('card_key') == card_key or req_id.startswith(card_key + '_'):
            matching_requests.append((req_id, result))
    
    if matching_requests:
        print(f"找到 {len(matching_requests)} 个相关请求:")
        
        for i, (req_id, result) in enumerate(matching_requests):
            print(f"\n{i+1}. {req_id}")
            
            data = result.get('data', {})
            bz = data.get('bz', {})
            
            if bz:
                birth_info = bz.get('8', '未知生辰')
                print(f"   生辰: {birth_info}")
                
                if len(bz) >= 8:
                    bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                    print(f"   八字: {bazi_str}")
            
            processed_time = result.get('processed_time', '未知')
            print(f"   处理时间: {processed_time}")
    else:
        print("没有找到相关请求")

def search_requests_by_keyword(keyword):
    """搜索包含关键词的请求"""
    bs = BaziService()

    print(f"\n搜索包含 '{keyword}' 的请求:")
    print("=" * 60)

    found = []
    for req_id, result in bs.results.items():
        if keyword in req_id:
            found.append((req_id, result))

    print(f"找到 {len(found)} 个匹配请求")

    for req_id, result in found:
        print(f"\n  请求ID: {req_id}")
        data = result.get('data', {})
        bz = data.get('bz', {})
        if bz:
            birth_info = bz.get('8', '未知生辰')
            print(f"    生辰: {birth_info}")
            if len(bz) >= 8:
                bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                print(f"    八字: {bazi_str}")
        processed_time = result.get('processed_time', '未知')
        print(f"    处理时间: {processed_time}")

if __name__ == "__main__":
    check_bazi_data()

    # 检查特定卡密
    check_specific_card("Xs1MR9iVx9RNZOk6")
    check_specific_card("wdd")

    # 搜索关键词
    search_requests_by_keyword("Xs1MR9iVx9RNZOk6")
