#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字摘要生成器
解析八字API返回的数据，生成易读的八字分析摘要
"""

import json
import os
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class BaziSummaryGenerator:
    """八字摘要生成器"""
    
    def __init__(self, output_dir="summaries"):
        """初始化摘要生成器
        
        Args:
            output_dir: 摘要文件保存目录
        """
        self.output_dir = output_dir
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"📁 创建摘要目录: {self.output_dir}", flush=True)
    
    def generate_summary(self, bazi_data, card_key, save_to_file=True):
        """生成八字摘要
        
        Args:
            bazi_data: 八字API返回的原始数据
            card_key: 卡密（用于文件命名）
            save_to_file: 是否保存到文件
            
        Returns:
            dict: 包含摘要内容和文件路径的字典
        """
        try:
            print(f"📝 开始生成八字摘要...", flush=True)
            
            # 解析基本信息
            basic_info = self._parse_basic_info(bazi_data)
            
            # 解析八字信息
            bazi_info = self._parse_bazi_info(bazi_data)
            
            # 解析运势信息
            fortune_info = self._parse_fortune_info(bazi_data)
            
            # 解析神煞信息
            shensha_info = self._parse_shensha_info(bazi_data)
            
            # 生成完整摘要
            summary = self._build_summary(
                basic_info, bazi_info, fortune_info, shensha_info
            )
            
            result = {
                'summary': summary,
                'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'card_key': card_key
            }
            
            # 保存到文件
            if save_to_file:
                file_path = self._save_summary_to_file(summary, card_key)
                result['file_path'] = file_path
            
            print(f"✅ 八字摘要生成完成", flush=True)
            return result
            
        except Exception as e:
            error_msg = f"生成八字摘要时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            raise
    
    def _parse_basic_info(self, data):
        """解析基本信息"""
        # 处理不同格式的性别数据
        sex_value = data.get('sex', '未知')
        if sex_value == 1 or sex_value == '男命':
            sex_display = '男'
        elif sex_value == 0 or sex_value == '女命':
            sex_display = '女'
        else:
            sex_display = '未知'
            
        return {
            'sex': sex_display,
            'runtime': data.get('runtime', '未知')
        }
    
    def _parse_bazi_info(self, data):
        """解析八字信息"""
        bz = data.get('bz', {})
        bazi_formatted = []
        
        # 处理不同格式的八字数据
        if isinstance(bz, dict) and all(str(i) in bz for i in range(8)):
            # API返回的字典格式：0246是天干，1357是地支
            bazi_formatted.append(f"{bz['0']}{bz['1']}")
            bazi_formatted.append(f"{bz['2']}{bz['3']}")
            bazi_formatted.append(f"{bz['4']}{bz['5']}")
            bazi_formatted.append(f"{bz['6']}{bz['7']}")
        elif isinstance(bz, list) and len(bz) >= 4:
            # 测试数据的列表格式：直接使用四柱
            bazi_formatted = bz[:4]
        
        bazi_info = {
            'bazi': bazi_formatted,  # 格式化的八字四柱
            'shishen': data.get('ss', []),  # 十神
            'canggan': data.get('cg', []),  # 藏干
            'canggan_shishen': data.get('cgss', [])  # 藏干十神
        }
        return bazi_info
    
    def _parse_fortune_info(self, data):
        """解析运势信息"""
        fortune_info = {
            'dayun': data.get('dayun', []),  # 大运
            'qiyunsui': data.get('qiyunsui', '')  # 起运岁数（虚岁）
        }
        return fortune_info
    
    def _parse_shensha_info(self, data):
        """解析神煞信息（暂时保留为空，根据用户要求只保留核心字段）"""
        return {}
    
    def _safe_join(self, data, separator=" "):
        """安全地拼接数据，处理嵌套列表和非字符串类型"""
        if not data:
            return ""
        
        try:
            # 如果是嵌套列表，先展平
            if isinstance(data, list) and data and isinstance(data[0], list):
                flattened = []
                for item in data:
                    if isinstance(item, list):
                        flattened.extend([str(x) for x in item])
                    else:
                        flattened.append(str(item))
                return separator.join(flattened)
            
            # 如果是普通列表，转换为字符串后拼接
            elif isinstance(data, list):
                return separator.join([str(item) for item in data])
            
            # 如果是单个值，直接返回字符串
            else:
                return str(data)
                
        except Exception as e:
            print(f"⚠️ 数据拼接警告: {str(e)}, 数据类型: {type(data)}", flush=True)
            return str(data)
    
    def _build_summary(self, basic_info, bazi_info, fortune_info, shensha_info):
        """构建完整摘要"""
        summary_lines = []
        
        # 标题
        summary_lines.append("="*50)
        summary_lines.append("八字命理分析摘要")
        summary_lines.append("="*50)
        summary_lines.append("")
        
        # 基本信息
        summary_lines.append("【基本信息】")
        summary_lines.append(f"性别: {basic_info['sex']}")
        summary_lines.append(f"分析时间: {basic_info['runtime']}")
        summary_lines.append("")
        
        # 八字信息
        summary_lines.append("【八字四柱】")
        if bazi_info['bazi']:
            bazi_str = " ".join(bazi_info['bazi'])
            summary_lines.append(f"八字: {bazi_str}")
        summary_lines.append("")
        
        # 四柱详解（天干地支及对应十神）
        summary_lines.append("【四柱详解】")
        self._add_structured_sizhu_info(summary_lines, bazi_info)
        summary_lines.append("")
        
        # 运势信息
        summary_lines.append("【运势分析】")
        if fortune_info['qiyunsui']:
            summary_lines.append(f"起运岁数（虚岁）: {fortune_info['qiyunsui']}")
        
        if fortune_info['dayun']:
            dayun_data = fortune_info['dayun'][:10] if len(fortune_info['dayun']) > 10 else fortune_info['dayun']
            dayun_str = self._safe_join(dayun_data)
            summary_lines.append(f"大运: {dayun_str}{'...' if len(fortune_info['dayun']) > 10 else ''}")
        summary_lines.append("")
        
        # 结尾
        summary_lines.append("="*50)
        summary_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary_lines.append("="*50)
        
        return "\n".join(summary_lines)
    
    def _add_structured_sizhu_info(self, summary_lines, bazi_info):
        """添加结构化的四柱信息（天干地支及对应十神）"""
        shishen = bazi_info.get('shishen', [])
        canggan = bazi_info.get('canggan', [])
        canggan_shishen = bazi_info.get('canggan_shishen', [])
        
        # 提取天干和地支
        tiangan_names = []
        dizhi_names = []
        if bazi_info['bazi']:
            for bazi_zhu in bazi_info['bazi']:
                if len(bazi_zhu) >= 2:
                    tiangan_names.append(bazi_zhu[0])  # 天干
                    dizhi_names.append(bazi_zhu[1])   # 地支
        
        zhu_names = ["年柱", "月柱", "日柱", "时柱"]
        
        # 确保数据长度一致
        max_len = min(len(zhu_names), len(tiangan_names), len(dizhi_names))
        
        for i in range(max_len):
            summary_lines.append(f"{zhu_names[i]}({tiangan_names[i]}{dizhi_names[i]}):")
            
            # 天干十神
            if i < len(shishen) and shishen[i]:
                summary_lines.append(f"  天干({tiangan_names[i]}): {shishen[i]}")
            
            # 地支藏干及十神
            if i < len(canggan) and canggan[i]:
                canggan_str = self._safe_join(canggan[i]) if isinstance(canggan[i], list) else str(canggan[i])
                summary_lines.append(f"  地支({dizhi_names[i]})藏干: {canggan_str}")
                
                # 藏干十神
                if i < len(canggan_shishen) and canggan_shishen[i]:
                    cgss_str = self._safe_join(canggan_shishen[i]) if isinstance(canggan_shishen[i], list) else str(canggan_shishen[i])
                    summary_lines.append(f"  藏干十神: {cgss_str}")
            
            summary_lines.append("")  # 空行分隔
    
    def _save_summary_to_file(self, summary, card_key):
        """保存摘要到文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"bazi_summary_{card_key}_{timestamp}.txt"
        file_path = os.path.join(self.output_dir, filename)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            print(f"💾 摘要已保存到: {file_path}", flush=True)
            logger.info(f"八字摘要已保存: {file_path}")
            return file_path
            
        except Exception as e:
            error_msg = f"保存摘要文件时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            raise
    
    def load_summary_from_file(self, file_path):
        """从文件加载摘要"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            error_msg = f"加载摘要文件时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            raise


# 全局变量存储当前八字数据
_current_bazi_data = None

def set_bazi_data(bazi_data):
    """设置当前八字数据，供bazi_analyzer使用"""
    global _current_bazi_data
    _current_bazi_data = bazi_data

def get_bazi_info():
    """获取八字信息，供bazi_analyzer使用
    
    生成结构化、易读的命盘信息文本，便于AI理解和分析
    """
    global _current_bazi_data
    
    if _current_bazi_data is None:
        return """【命盘信息】

基本信息：
- 性别：[请填入性别]
- 出生时间：[请填入出生年月日时]

八字四柱：[请填入具体的八字信息]

请基于以上信息进行详细分析。"""
    
    try:
        # 创建临时生成器实例来解析数据
        temp_generator = BaziSummaryGenerator()
        basic_info = temp_generator._parse_basic_info(_current_bazi_data)
        bazi_info = temp_generator._parse_bazi_info(_current_bazi_data)
        fortune_info = temp_generator._parse_fortune_info(_current_bazi_data)
        
        # 获取基本数据
        bazi_pillars = bazi_info.get('bazi', [])
        shishen = bazi_info.get('shishen', [])
        canggan = bazi_info.get('canggan', [])
        canggan_shishen = bazi_info.get('canggan_shishen', [])
        dayun = fortune_info.get('dayun', [])
        qiyunsui = fortune_info.get('qiyunsui', '未知')
        
        # 构建结构化的命盘信息
        info_lines = []
        info_lines.append("【命盘信息】")
        info_lines.append("")
        
        # 基本信息
        info_lines.append("一、基本信息")
        info_lines.append(f"性别：{basic_info.get('sex', '未知')}")
        info_lines.append(f"分析时间：{basic_info.get('runtime', '未知')}")
        info_lines.append("")
        
        # 八字四柱概览
        info_lines.append("二、八字四柱")
        if bazi_pillars:
            bazi_str = "  ".join(bazi_pillars)
            info_lines.append(f"四柱：{bazi_str}")
        else:
            info_lines.append("四柱：数据缺失")
        info_lines.append("")
        
        # 十神配置
        info_lines.append("三、十神配置")
        if shishen:
            shishen_str = "  ".join([str(s) for s in shishen])
            info_lines.append(f"天干十神：{shishen_str}")
        else:
            info_lines.append("天干十神：数据缺失")
        info_lines.append("")
        
        # 四柱详细分析
        info_lines.append("四、四柱详细分析")
        zhu_names = ["年柱", "月柱", "日柱", "时柱"]
        
        for i, pillar in enumerate(bazi_pillars[:4]):
            if i < len(zhu_names) and len(pillar) >= 2:
                tiangan = pillar[0]
                dizhi = pillar[1]
                
                info_lines.append(f"{i+1}. {zhu_names[i]}（{pillar}）")
                
                # 天干及其十神
                if i < len(shishen) and shishen[i]:
                    info_lines.append(f"   天干：{tiangan} → {shishen[i]}")
                else:
                    info_lines.append(f"   天干：{tiangan} → 十神未知")
                
                # 地支藏干及其十神
                if i < len(canggan) and canggan[i]:
                    canggan_list = canggan[i] if isinstance(canggan[i], list) else [canggan[i]]
                    canggan_str = ", ".join([str(c) for c in canggan_list])
                    info_lines.append(f"   地支：{dizhi} 藏干 [{canggan_str}]")
                    
                    # 藏干对应的十神
                    if i < len(canggan_shishen) and canggan_shishen[i]:
                        cgss_list = canggan_shishen[i] if isinstance(canggan_shishen[i], list) else [canggan_shishen[i]]
                        cgss_str = ", ".join([str(c) for c in cgss_list])
                        info_lines.append(f"   藏干十神：[{cgss_str}]")
                    else:
                        info_lines.append(f"   藏干十神：未知")
                else:
                    info_lines.append(f"   地支：{dizhi} 藏干信息缺失")
                
                info_lines.append("")  # 空行分隔
        
        # 大运信息
        info_lines.append("五、大运信息")
        info_lines.append(f"起运岁数：{qiyunsui}岁")
        if dayun:
            info_lines.append("大运序列（前8步）：")
            for i, yun in enumerate(dayun[:8]):
                start_age = int(qiyunsui) + i * 10 if str(qiyunsui).isdigit() else "未知"
                end_age = int(qiyunsui) + (i + 1) * 10 - 1 if str(qiyunsui).isdigit() else "未知"
                info_lines.append(f"  第{i+1}步：{yun} ({start_age}-{end_age}岁)")
        else:
            info_lines.append("大运信息：数据缺失")
        
        # 不再添加"分析指导"部分，避免与格式模板和维度提示词重复
        
        return "\n".join(info_lines)
        
    except Exception as e:
        logger.error(f"解析八字数据时出错: {e}")
        return """【命盘信息】

基本信息：
- 性别：数据解析失败
- 出生时间：数据解析失败

八字四柱：数据解析失败，请检查数据格式

【错误提示】
数据解析过程中发生错误，请检查输入的八字数据格式是否正确。"""