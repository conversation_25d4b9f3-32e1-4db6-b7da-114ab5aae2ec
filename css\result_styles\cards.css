/* 报告列表样式 */
.report-list {
    display: grid;
    gap: 20px;
    margin-top: 32px;
}

.report-item {
    background: var(--card-gradient, linear-gradient(145deg, #ffffff 0%, #f8fafc 100%));
    border-radius: var(--radius-large);
    padding: 28px;
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.report-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0 3px 3px 0;
}

.report-item:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--card-hover-shadow);
    border-color: rgba(100, 116, 139, 0.3);
}

.report-item:hover::before {
    transform: scaleY(1);
}

/* 卡片背景装饰 */
.report-item::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(100, 116, 139, 0.08) 0%, transparent 70%);
    opacity: 0;
    z-index: -1;
    transform: scale(0.8);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.report-item:hover::after {
    opacity: 1;
    transform: scale(1);
}

/* 闪光效果 */
.report-item .shimmer {
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: rotate(-45deg);
    animation: shimmer 3s ease-in-out infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.report-item:hover .shimmer {
    opacity: 1;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(-45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
}

.report-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color-light);
}

.report-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.report-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: var(--success-gradient);
    color: white;
}

.status-processing {
    background: var(--gradient-info);
    color: white;
    animation: var(--pulse-animation);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .report-item {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .report-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .report-item {
        padding: 14px;
        margin-bottom: 12px;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .report-item {
        background: var(--bg-card);
        border-color: var(--border-color);
        color: var(--text-primary);
        box-shadow: var(--shadow-md);
    }
    
    .report-item:hover {
        background: var(--bg-tertiary);
        border-color: var(--border-color-dark);
        box-shadow: var(--card-hover-shadow);
    }
    
    .report-date {
        color: var(--text-light);
    }
    
    .report-item::after {
        background: radial-gradient(circle, rgba(100, 116, 139, 0.15) 0%, transparent 70%);
    }
}

/* 卡片深色模式 */
@media (prefers-color-scheme: dark) {
    .card {
        background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
        border-color: #334155;
        color: #f8fafc;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 20px rgba(100, 116, 139, 0.2);
        border-color: rgba(100, 116, 139, 0.5);
    }
    
    .card-header {
        background: linear-gradient(to right, rgba(100, 116, 139, 0.15), rgba(71, 85, 105, 0.15));
        border-bottom-color: #334155;
    }
    
    .card-body {
        color: #f1f5f9;
    }
    
    .card-title {
        color: #f8fafc;
    }
    
    .card-footer {
        border-top-color: #334155;
        background: rgba(15, 23, 42, 0.5);
    }
    
    /* 确保卡片内所有元素在暗色模式下可见 */
    .card * {
        color: #f1f5f9;
    }
    
    .card h1, .card h2, .card h3, .card h4, .card h5, .card h6 {
        color: #f8fafc !important;
    }
    
    /* 卡片内特殊元素的样式修正 */
    .card [style*="color: white"],
    .card [style*="color: #fff"],
    .card [style*="color: #ffffff"],
    .card [style*="color:#fff"],
    .card [style*="color:#ffffff"] {
        color: #0f172a !important;
        background-color: #f1f5f9 !important;
        padding: 0 4px !important;
        border-radius: 4px !important;
    }
    
    /* 卡片内白色背景元素的修正 */
    .card [style*="background: white"],
    .card [style*="background: #fff"],
    .card [style*="background: #ffffff"],
    .card [style*="background-color: white"],
    .card [style*="background-color: #fff"],
    .card [style*="background-color: #ffffff"],
    .card [style*="background:#fff"],
    .card [style*="background:#ffffff"],
    .card [style*="background-color:#fff"],
    .card [style*="background-color:#ffffff"] {
        background: rgba(15, 23, 42, 0.8) !important;
        background-color: rgba(15, 23, 42, 0.8) !important;
        color: #f1f5f9 !important;
    }
}

.card-filter-input:focus {
    outline: none;
    border-color: rgba(100, 116, 139, 0.3);
    box-shadow: 0 0 0 3px rgba(100, 116, 139, 0.1);
}