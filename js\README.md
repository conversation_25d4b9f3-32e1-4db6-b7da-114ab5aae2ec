# JavaScript 模块化文件说明

原来的 `script.js` 文件已经被拆分成多个模块化的文件，以提高代码的可维护性和可读性。

## 文件结构

```
js/
├── config.js      # 配置相关函数
├── utils.js       # 工具函数
├── ui.js          # UI相关函数
├── api.js         # API请求相关函数
├── analyzer.js    # 主要的分析器类
├── main.js        # 主入口文件
└── README.md      # 本说明文件
```

## 各文件功能说明

### config.js
- `getApiBaseUrl()` - 获取API基础URL，根据环境自动切换

### utils.js
- `getFormValue(elementId)` - 获取表单元素的值
- `validateCardKey(cardKey)` - 验证卡密格式
- `clearCardKeyStatus()` - 清除卡密状态显示
- `showCardKeyStatus(message, type)` - 显示卡密状态
- `getFormData()` - 获取表单数据
- `saveAnalysisResult(cardKey, resultData, analysisData)` - 保存分析结果到本地存储

### ui.js
- `setSubmitting(isSubmitting)` - 设置提交状态
- `showProgress(message, progress, elapsedTime)` - 显示进度
- `createProgressSection(container)` - 创建进度显示区域
- `showError(message)` - 显示错误信息
- `showSuccess(cardKey)` - 显示成功信息并跳转
- `addPulseAnimation(element)` - 添加脉冲动画
- `showStatusChange(oldStatus, newStatus)` - 显示状态变化

### api.js
- `sendBaziRequest(formData, onSuccess, onError)` - 发送八字分析请求
- `checkCompletionStatus(identifier, callback, isRequestId)` - 检查完成状态

### analyzer.js
- `BaziAnalyzer` 类 - 主要的分析器类，包含：
  - `init()` - 初始化
  - `handleSubmit(event)` - 处理表单提交
  - `onRequestSuccess(response, cardKey)` - 请求成功处理
  - `onRequestError(error)` - 请求错误处理
  - `startPollingForCompletion(identifier, isRequestId)` - 开始轮询完成状态
  - `calculateProgressByStatus(status, pollCount)` - 根据状态计算进度
  - `displayLLMProgress(llmProgress, elapsedTime)` - 显示LLM进度
  - `getStatusMessage(status)` - 获取状态消息

### main.js
- 主入口文件，负责：
  - 页面加载完成后的初始化
  - 创建 `BaziAnalyzer` 实例
  - 页面卸载时的清理工作

## 引用顺序

在HTML文件中，必须按照以下顺序引用这些文件：

```html
<!-- 模块化JavaScript文件 -->
<script src="js/config.js"></script>
<script src="js/utils.js"></script>
<script src="js/ui.js"></script>
<script src="js/api.js"></script>
<script src="js/analyzer.js"></script>
<script src="js/main.js"></script>
```

## 依赖关系

- `config.js` - 无依赖，提供基础配置
- `utils.js` - 无依赖，提供工具函数
- `ui.js` - 依赖 `utils.js` 中的部分函数
- `api.js` - 依赖 `config.js` 和 `ui.js`
- `analyzer.js` - 依赖所有前面的文件
- `main.js` - 依赖 `analyzer.js`

## 优势

1. **模块化** - 每个文件职责单一，便于维护
2. **可读性** - 代码结构清晰，易于理解
3. **可复用性** - 工具函数可以在其他项目中复用
4. **调试友好** - 问题定位更加精确
5. **团队协作** - 不同开发者可以专注于不同模块

## 注意事项

1. 文件引用顺序很重要，不能随意调整
2. 如果需要添加新功能，请根据功能类型放入对应的文件中
3. 全局变量和函数尽量减少，优先使用模块内部的作用域
4. 保持向后兼容性，确保现有功能正常工作