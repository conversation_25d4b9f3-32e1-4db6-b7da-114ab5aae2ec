/* 定义所有动画的关键帧 */

/* 脉冲动画 - 用于状态指示 */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 淡入动画 - 用于组件初始显示 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 向上滑入 - 用于内容加载 */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 向下滑入 - 用于下拉菜单 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 旋转 - 用于加载图标 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 闪烁 - 用于提示注意 */
@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 波纹效果 - 用于按钮点击 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* 呼吸效果 - 用于强调重要元素 */
@keyframes breathe {
    0%, 100% {
        box-shadow: 0 0 0 rgba(59, 130, 246, 0);
    }
    50% {
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
    }
}

/* 定义CSS变量供其他文件使用 */
:root {
    --fade-in-animation: fadeIn 0.3s ease forwards;
    --slide-up-animation: slideUp 0.4s ease forwards;
    --slide-down-animation: slideDown 0.3s ease forwards;
    --pulse-animation: pulse 2s infinite ease-in-out;
    --rotate-animation: rotate 1s linear infinite;
    --blink-animation: blink 1.5s infinite;
    --ripple-animation: ripple 0.8s ease-out;
    --breathe-animation: breathe 3s infinite ease-in-out;
} 