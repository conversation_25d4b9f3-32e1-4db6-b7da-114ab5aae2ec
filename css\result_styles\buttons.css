/* 操作按钮区域 */
.action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 32px 0;
    padding: 24px;
    background: var(--card-gradient);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

/* 渐变背景效果 */
.action-buttons::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, rgba(100, 116, 139, 0.05), rgba(71, 85, 105, 0.05), rgba(148, 163, 184, 0.05));
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-buttons:hover::before {
    opacity: 1;
}

.action-buttons .btn {
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

/* 通用按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    min-width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn.primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn.primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(100, 116, 139, 0.4);
}

.btn.primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color-light);
}

.btn.secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn.danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn.danger:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(239, 68, 68, 0.4);
}

/* 操作按钮样式 */
.actions-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin: 24px 0;
}

.action-btn {
    padding: 12px 24px;
    border-radius: var(--radius-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn.primary {
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

.action-btn.primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--card-hover-shadow);
}

.action-btn.secondary {
    background: var(--bg-secondary) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color);
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: var(--text-secondary) !important;
    text-fill-color: var(--text-secondary) !important;
}

.action-btn.secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn.outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.action-btn.outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    background: rgba(100, 116, 139, 0.05);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 16px;
        margin: 20px 0;
        padding: 16px;
    }
    
    .btn {
        width: 100%;
        padding: 14px 20px;
        font-size: 0.95rem;
        min-width: auto;
        justify-content: center;
    }
    
    .actions-section {
        flex-direction: column;
        width: 100%;
    }
    
    .action-btn {
        width: 100%;
        padding: 14px 20px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
    
    .action-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn, .action-btn {
        min-height: 44px;
        touch-action: manipulation;
    }
}