/**
 * 报告骨架屏和懒加载功能
 * 优化报告加载体验
 */

// 生成骨架屏HTML
function generateSkeletonScreen(count = 6) {
    let skeletonHtml = '<div class="reports-section"><div class="skeleton-title"></div><div class="reports-grid">';
    
    for (let i = 0; i < count; i++) {
        skeletonHtml += `
            <div class="report-card skeleton">
                <div class="report-header">
                    <div class="skeleton-line title"></div>
                    <div class="skeleton-badge"></div>
                </div>
                <div class="report-meta">
                    <div class="skeleton-line date"></div>
                </div>
                <div class="report-summary">
                    <div class="skeleton-line"></div>
                    <div class="skeleton-line"></div>
                </div>
                <div class="report-actions">
                    <div class="skeleton-button"></div>
                    <div class="skeleton-button"></div>
                </div>
            </div>
        `;
    }
    
    skeletonHtml += '</div></div>';
    return skeletonHtml;
}

// 显示骨架屏
function showSkeletonScreen(container, count = 6) {
    if (typeof container === 'string') {
        container = document.getElementById(container);
    }
    
    if (container) {
        container.innerHTML = generateSkeletonScreen(count);
    }
}

// 显示加载更多指示器
function showLoadMoreIndicator(container) {
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'loading-more';
    loadingIndicator.innerHTML = '<div class="loading-icon">⚊</div><span>加载更多...</span>';
    loadingIndicator.id = 'loadMoreIndicator';
    
    if (typeof container === 'string') {
        container = document.getElementById(container);
    }
    
    if (container) {
        container.appendChild(loadingIndicator);
    }
    
    return loadingIndicator;
}

// 移除加载更多指示器
function removeLoadMoreIndicator() {
    const indicator = document.getElementById('loadMoreIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// 初始化无限滚动功能
function initInfiniteScroll() {
    let page = 1;
    let loading = false;
    let hasMore = true;
    
    // 定义加载更多函数
    async function loadMoreReports() {
        if (loading || !hasMore) return;
        
        loading = true;
        
        // 显示加载指示器
        showLoadMoreIndicator('reportsList');
        
        // 加载下一页
        page++;
        try {
            const result = await window.reportsManager.fetchHistoryReports(page);
            
            // 添加新报告到列表
            if (result.reports && result.reports.length > 0) {
                const reportsContainer = document.querySelector('.reports-grid');
                if (reportsContainer) {
                    const reportsHtml = window.reportsManager.generateReportsHtml(result.reports);
                    // 使用DOM解析器解析HTML
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(reportsHtml, 'text/html');
                    // 提取报告卡片元素
                    const cards = doc.querySelectorAll('.report-card');
                    // 添加到现有容器
                    cards.forEach(card => {
                        reportsContainer.appendChild(card);
                    });
                }
                
                hasMore = result.hasMore;
            } else {
                hasMore = false;
            }
        } catch (error) {
            console.error('加载更多报告失败:', error);
            hasMore = false;
        } finally {
            // 移除加载指示器
            removeLoadMoreIndicator();
            loading = false;
        }
    }
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
        if (document.querySelector('.reports-grid')) {
            // 检查是否滚动到底部附近
            const scrollPosition = window.innerHeight + window.scrollY;
            const documentHeight = document.body.offsetHeight;
            
            if (scrollPosition >= documentHeight - 500) {
                loadMoreReports();
            }
        }
    });
    
    // 返回控制对象，方便外部调用
    return {
        reset: function() {
            page = 1;
            hasMore = true;
        },
        loadMore: loadMoreReports
    };
}

// 延迟加载报告内容，减少界面卡顿
function delayedDisplayReports(reports, container) {
    return new Promise((resolve) => {
        setTimeout(() => {
            if (typeof container === 'string') {
                container = document.getElementById(container);
            }
            
            if (!container) return;
            
            let contentHtml = '';
            
            if (reports && reports.length > 0) {
                contentHtml = window.reportsManager.generateReportsHtml(reports);
            } else {
                contentHtml = window.reportsManager.generateEmptyStateHtml();
            }
            
            container.innerHTML = contentHtml;
            resolve();
        }, 100); // 短暂延迟，让骨架屏有时间显示
    });
}

// 保存为全局变量，方便其他文件调用
window.reportUIHelper = {
    showSkeletonScreen,
    delayedDisplayReports,
    initInfiniteScroll
}; 