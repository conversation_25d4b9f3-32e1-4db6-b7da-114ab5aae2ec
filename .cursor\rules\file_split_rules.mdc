---
description: 
globs: 
alwaysApply: true
---
# 八字分析系统文件拆分规则

## 文件大小控制规则

1. **单文件大小限制**
   - JavaScript文件不超过5KB（约200行）
   - CSS文件不超过3KB（约150行）
   - HTML文件不超过4KB（约100行）
   - Python文件不超过8KB（约300行）

2. **模块化命名约定**
   - 使用前缀标识功能模块：`ui_`, `api_`, `bazi_`, `config_`
   - 使用后缀标识文件类型：`_component.js`, `_service.py`, `_view.html`

## 代码拆分策略

### 前端拆分规则

1. **JavaScript拆分**
   - 按功能拆分：`ui_card.js`, `ui_form.js`, `api_bazi.js`
   - 公共工具函数放入`utils/`目录：`utils/date.js`, `utils/validation.js`
   - 大型类拆分为多个文件，使用模式如：
     ```javascript
     // ui_report.js - 主文件
     import { renderCharts } from './ui_report_charts.js';
     import { formatData } from './ui_report_formatter.js';
     ```

2. **CSS拆分**
   - 按组件拆分：`card.css`, `form.css`, `button.css`
   - 使用变量文件：`variables.css`（存储颜色、字体等）
   - 媒体查询单独放置：`responsive.css`

3. **HTML拆分**
   - 使用模板系统，将页面拆分为头部、主体、底部
   - 使用`<template>`标签或动态加载HTML片段
   - 复杂表单拆分为多个步骤页面

### 后端拆分规则

1. **Python拆分**
   - 按领域拆分：`bazi_calculator.py`, `bazi_interpreter.py`
   - 大型类拆分为基类和子类：`analyzer_base.py`, `analyzer_advanced.py`
   - 工具函数放入`utils`包：`utils/date_utils.py`, `utils/logging.py`

2. **API拆分**
   - 按资源类型拆分：`api_user.py`, `api_report.py`, `api_card.py`
   - 使用蓝图（Blueprint）组织API：`blueprints/user_bp.py`

## 数据存储拆分

1. **JSON数据拆分**
   - 大型JSON文件按日期或ID范围拆分：`reports_2023_01.json`
   - 使用分页机制，每页数据不超过100条记录

2. **配置拆分**
   - 拆分为基础配置和环境配置：`config_base.json`, `config_dev.json`
   - 敏感配置单独存放：`secrets.json`（加入.gitignore）

## 动态加载策略

1. **前端动态加载**
   - 使用动态import()加载JS模块
   - 实现代码分割：核心功能立即加载，次要功能懒加载
   - 示例：
     ```javascript
     // 核心功能立即加载
     import { core } from './core.js';
     
     // 次要功能懒加载
     button.addEventListener('click', async () => {
       const { feature } = await import('./feature.js');
       feature.init();
     });
     ```

2. **后端动态加载**
   - 使用工厂模式动态加载分析器
   - 按需导入大型模块：`importlib.import_module()`

## 实现指南

1. **重构现有大文件**
   - `detailed_result.js`（35KB）拆分为：
     - `detailed_result_core.js`（核心逻辑）
     - `detailed_result_display.js`（显示逻辑）
     - `detailed_result_data.js`（数据处理）
     - `detailed_result_events.js`（事件处理）

2. **使用构建工具**
   - 开发时使用拆分的小文件
   - 部署时可选择使用构建工具合并（保留源映射）

3. **文档规范**
   - 每个拆分文件头部必须包含：
     - 文件用途说明
     - 依赖关系列表
     - 导出接口说明

## 监控与维护

1. **文件大小监控**
   - 建立CI检查，超过大小限制时发出警告
   - 定期审查大型文件并进行拆分

2. **依赖关系图**
   - 维护模块依赖关系图，避免循环依赖
   - 定期检查未使用的导入

## 示例实现

### 拆分前（大文件）：
```javascript
// detailed_result.js - 35KB 大文件
class DetailedResultViewer {
  constructor() { /* 大量初始化代码 */ }
  loadData() { /* 大量数据加载代码 */ }
  renderCharts() { /* 大量图表渲染代码 */ }
  setupEvents() { /* 大量事件处理代码 */ }
}
```

### 拆分后：
```javascript
// detailed_result_core.js - 核心逻辑（4KB）
import { loadData } from './detailed_result_data.js';
import { renderUI } from './detailed_result_display.js';
import { setupEvents } from './detailed_result_events.js';

class DetailedResultViewer {
  constructor() {
    this.dataLoader = loadData;
    this.renderer = renderUI;
    this.eventHandler = setupEvents;
    this.init();
  }
  
  init() {
    this.dataLoader.init(this);
    this.renderer.init(this);
    this.eventHandler.init(this);
  }
}

export { DetailedResultViewer };
```

## AI工具适配规则

1. **文件注释标记**
   - 在大文件开头添加引导注释，指示AI工具如何处理：
   ```javascript
   /**
    * @file-group detailed_result
    * @related-files 
    * - detailed_result_data.js
    * - detailed_result_display.js
    * - detailed_result_events.js
    */
   ```

2. **AI友好的代码结构**
   - 使用明确的函数和变量命名，避免简写
   - 添加类型注释，帮助AI理解代码结构
   - 关键逻辑添加注释说明

3. **文件索引**
   - 维护`file_index.md`文件，列出所有拆分文件及其关系
   - 在每个目录添加`README.md`，说明该目录下文件的用途和关系 