import requests
import json
import threading
import time
import traceback
import argparse
import sys
import re
from timeout_config import get_timeout

# Constants (Replace with your actual API details)
API_URL = "https://openrouter.ai/api/v1/chat/completions"  # 例如: "https://api.openai.com/v1/chat/completions"
API_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer 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"
}
MODEL_NAME = "deepseek/deepseek-r1:free" # 或者你希望使用的模型

# API调用函数
def call_api(url = API_URL, headers = API_HEADERS, data = None, result_list=None, index=None):
    """执行API调用并返回结果"""
    try:
        # 使用统一的超时配置
        timeout = get_timeout('llm_api')
        response = requests.post(
            url=url,
            headers=headers,
            data=data,
            timeout=timeout
        )
        response.raise_for_status()
        result = response.json()
        
        if result_list is not None and index is not None:
            result_list[index] = result
        
        return result
    except requests.exceptions.Timeout:
        timeout = get_timeout('llm_api')
        error_msg = f"API请求超时（{timeout}秒）"
        print(f"❌ {error_msg}")
        if result_list is not None and index is not None:
            result_list[index] = {"error": error_msg}
        return {"error": error_msg}
    except Exception as e:
        error_msg = str(e)
        print(f"❌ API调用错误: {error_msg}")
        if result_list is not None and index is not None:
            result_list[index] = {"error": error_msg}
        return {"error": error_msg}

# --- 假设这是你调用LLM或执行分析的函数 ---
def perform_analysis(topic, requirement, bazi_info_text):
    """
    模拟调用LLM进行分析
    Args:
        topic (str): 分析主题, e.g., "感情"
        requirement (str): 具体要求
        bazi_info_text (str): 八字基本信息文本
    Returns:
        str: 分析结果
    """
    print(f"线程 [{threading.current_thread().name}]：开始分析 [{topic}]...")
    # 模拟网络延迟或计算时间
    time.sleep(3)
    # 在实际应用中，这里会调用 LLM API
    # result = call_your_llm_api(prompt=f"基于以下八字信息：\n{bazi_info_text}\n\n请分析【{topic}】：{requirement}")
    result = f"这是关于【{topic}】的分析结果，基于要求：'{requirement[:20]}...'"
    print(f"线程 [{threading.current_thread().name}]：完成分析 [{topic}]")
    return result
# --- ---

# --- 线程工作函数 ---
def analysis_worker(topic, requirement, bazi_info_text, results_dict, lock):
    """
    线程执行的函数，调用分析逻辑并将结果存入共享字典
    """
    try:
        # 1. 构建 Prompt
        prompt = f"请根据以下八字信息：\n\n{bazi_info_text}\n\n严格按照以下要求进行分析：\n分析主题：{topic}\n具体要求：{requirement}"

        # 2. 构建 API 请求数据
        api_data = {
            "model": MODEL_NAME,
            "messages": [
                {"role": "user", "content": prompt}
            ]
            # 可以添加其他API参数，如 "temperature": 0.7
        }

        # 3. 调用 API
        # 注意: call_api 需要接收字符串形式的data，所以用json.dumps()
        api_result = call_api(API_URL, API_HEADERS, json.dumps(api_data))

        # 4. 处理结果
        analysis_content = f"分析【{topic}】时出错或未返回有效内容。"
        if isinstance(api_result, dict):
            if "error" in api_result:
                analysis_content = f"API调用错误【{topic}】: {api_result['error']}"
            elif "choices" in api_result and len(api_result["choices"]) > 0:
                try:
                    analysis_content = api_result["choices"][0]["message"]["content"]
                except (KeyError, IndexError, TypeError) as e:
                    analysis_content = f"解析API响应【{topic}】时出错: {str(e)}\n响应内容: {str(api_result)[:200]}..."
            else:
                 analysis_content = f"API响应【{topic}】格式无效或choices为空。响应: {str(api_result)[:200]}..."
        else:
            analysis_content = f"API调用【{topic}】返回了非字典类型: {type(api_result)}"

        # 使用锁来安全地更新共享字典
        with lock:
            results_dict[topic] = analysis_content

    except Exception as e:
        error_message = f"执行分析【{topic}】的线程时发生意外错误: {str(e)}"
        with lock:
            results_dict[topic] = error_message

def main(input_string):
    # 1. & 2. 解析输入，分离数据与任务
    json_match = re.search(r'(\{.*\})', input_string, re.DOTALL)
    if not json_match:
        return

    json_string = json_match.group(1)
    bazi_info_text = input_string[:json_match.start()].strip()

    try:
        analysis_tasks = json.loads(json_string)
    except json.JSONDecodeError as e:
        return

    # 3. 创建并执行线程
    threads = []
    results = {}  # 共享字典，用于存储结果
    lock = threading.Lock() # 创建锁

    for topic, requirement in analysis_tasks.items():
        thread = threading.Thread(
            target=analysis_worker,
            args=(topic, requirement, bazi_info_text, results, lock),
            name=f"Thread-{topic}" # 给线程命名方便调试
        )
        threads.append(thread)
        thread.start()

    # 4. 等待所有线程完成
    for thread in threads:
        thread.join()

    # 5. 整合输出为 JSON
    final_output = {
        "bazi_info": bazi_info_text,
        "analysis_results": results  # results 字典已经包含了 topic -> analysis_content 的映射
    }

    # 只输出JSON，不带任何格式化
    print(json.dumps(final_output, ensure_ascii=False))

if __name__ == "__main__":
    # 从命令行参数获取输入
    if len(sys.argv) > 1:
        input_data = sys.argv[1]
        main(input_data)
    else:
        print("{\"error\":\"需要提供输入数据\"}")

