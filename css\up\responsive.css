/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    /* 容器优化 */
    .container {
        padding: 16px;
        padding-top: 20px;
    }
    
    /* 导航链接优化 */
    .nav-link {
        padding: 10px 16px;
        margin-bottom: 16px;
        font-size: 0.9rem;
        border-radius: 8px;
    }
    
    /* 页面头部优化 */
    .page-header {
        margin-bottom: 24px;
        padding: 20px 16px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
        margin: 12px 0 8px 0;
    }
    
    .header-icon {
        font-size: 2.5rem;
        margin-bottom: 8px;
    }
    
    .header-subtitle {
        font-size: 0.9rem;
        margin-top: 8px;
    }
    
    /* 表单容器优化 */
    .form-container {
        padding: 20px 16px;
        margin: 0;
        border-radius: 12px;
    }
    
    /* 表单区块优化 */
    .form-section {
        padding: 16px;
        margin-bottom: 20px;
        border-radius: 12px;
    }
    
    .section-title {
        font-size: 1.1rem;
        margin-bottom: 16px;
    }
    
    /* 表单行布局优化 */
    .form-row {
        flex-direction: column;
        gap: 16px;
        margin-bottom: 16px;
    }
    
    /* 输入组优化 */
    .input-group {
        margin-bottom: 16px;
    }
    
    .input-group label {
        font-size: 0.9rem;
        margin-bottom: 6px;
        font-weight: 500;
    }
    
    .input-group input,
    .input-group select {
        padding: 14px 16px;
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 10px;
        min-height: 48px; /* 触摸友好 */
    }
    
    .input-group select {
        padding-right: 40px;
    }
    
    .input-icon {
        right: 14px;
        font-size: 1rem;
    }
    
    /* 购买卡密区域优化 */
    .card-purchase-section {
        margin-top: 16px;
        padding-top: 16px;
    }
    
    .purchase-hint {
        font-size: 0.85rem;
        margin-bottom: 10px;
    }
    
    .card-purchase-button {
        padding: 12px 20px;
        font-size: 0.9rem;
        border-radius: 25px;
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    /* 提交按钮优化 */
    .form-actions {
        margin-top: 24px;
        padding: 0 16px;
    }
    
    .submit-btn {
        width: 100%;
        max-width: none;
        padding: 16px 24px;
        font-size: 1rem;
        min-height: 52px;
        border-radius: 26px;
    }
    
    /* 结果容器优化 */
    .result-container {
        margin-top: 20px;
        padding: 16px;
    }
    
    .progress-section {
        padding: 16px;
        border-radius: 12px;
    }
    
    .progress-header h4 {
        font-size: 1rem;
    }
    
    /* 页脚优化 */
    .footer {
        margin-top: 32px;
        padding: 16px;
        text-align: center;
    }
    
    .footer p {
        font-size: 0.8rem;
        line-height: 1.4;
    }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
    .container {
        padding: 12px;
        padding-top: 16px;
    }
    
    .nav-link {
        padding: 12px 16px;
        margin-bottom: 12px;
        font-size: 0.9rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .page-header {
        padding: 16px 12px;
        margin-bottom: 20px;
    }
    
    .page-header h1 {
        font-size: 1.6rem;
        line-height: 1.3;
        margin: 8px 0;
    }
    
    .header-icon {
        font-size: 2.2rem;
    }
    
    .header-subtitle {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-top: 8px;
    }
    
    .form-container {
        padding: 16px 12px;
        border-radius: 12px;
        margin: 0;
    }
    
    .form-section {
        padding: 16px 12px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .section-title {
        font-size: 1.05rem;
        margin-bottom: 16px;
        line-height: 1.3;
    }
    
    .form-row {
        gap: 12px;
        margin-bottom: 12px;
    }
    
    .input-group {
        margin-bottom: 16px;
    }
    
    .input-group label {
        font-size: 0.9rem;
        margin-bottom: 6px;
        font-weight: 500;
    }
    
    .input-group input,
    .input-group select {
        padding: 12px 16px;
        font-size: 1rem;
        min-height: 44px;
        border-radius: 8px;
        width: 100%;
    }
    
    .input-icon {
        font-size: 1.1rem;
        right: 12px;
    }
    
    .submit-btn {
        padding: 14px 20px;
        font-size: 0.95rem;
        min-height: 48px;
        width: 100%;
        border-radius: 24px;
    }
    
    .card-purchase-button {
        padding: 10px 16px;
        font-size: 0.85rem;
        min-height: 40px;
        width: 100%;
        border-radius: 20px;
    }
    
    .button-group {
        gap: 12px;
        margin-top: 24px;
        flex-direction: column;
    }
    
    .button-group .btn {
        padding: 14px 24px;
        font-size: 1rem;
        min-width: auto;
        width: 100%;
        min-height: 48px;
        border-radius: 10px;
        justify-content: center;
    }
    
    .progress-container {
        padding: 20px 16px;
        margin: 16px 0;
        border-radius: 12px;
    }
    
    .footer {
        padding: 20px 12px;
        margin-top: 30px;
    }
    
    .footer p {
        font-size: 0.8rem;
        line-height: 1.4;
    }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .page-header {
        margin-bottom: 16px;
        padding: 12px 16px;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
        margin: 8px 0 4px 0;
    }
    
    .header-icon {
        font-size: 2rem;
        margin-bottom: 4px;
    }
    
    .form-section {
        margin-bottom: 16px;
    }
    
    .form-actions {
        margin-top: 16px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    /* 增大可点击区域 */
    .nav-link {
        min-height: 44px;
        padding: 12px 20px;
    }
    
    .input-group input,
    .input-group select {
        min-height: 48px;
    }
    
    .submit-btn {
        min-height: 52px;
    }
    
    .card-purchase-button {
        min-height: 44px;
    }
    
    /* 移除悬停效果，优化点击反馈 */
    .nav-link:hover {
        transform: none;
    }
    
    .nav-link:active {
        transform: scale(0.98);
        background: rgba(102, 126, 234, 0.1);
    }
    
    .submit-btn:hover {
        transform: none;
    }
    
    .submit-btn:active {
        transform: scale(0.98);
    }
    
    .card-purchase-button:hover {
        transform: none;
    }
    
    .card-purchase-button:active {
        transform: scale(0.98);
        background: rgba(102, 126, 234, 0.1);
    }
} 