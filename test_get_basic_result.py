#!/usr/bin/env python3
"""
测试 get_basic_result 方法
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from bazi_service import BaziService
from request_manager import RequestMana<PERSON>

def test_get_basic_result():
    """测试获取基础结果"""
    print("🔍 测试 get_basic_result 方法")
    print("=" * 50)
    
    # 创建服务实例
    bazi_service = BaziService()
    
    # 测试请求ID
    request_id = "NI45OgqRJEf7KZBonjMXLX7W_1753875709_1637cbc6"
    
    print(f"🎯 测试请求ID: {request_id}")
    
    # 检查请求管理器中的数据
    print(f"\n📊 检查请求管理器状态:")
    request_info = bazi_service.request_manager.get_request_info(request_id)
    print(f"   请求信息存在: {request_info is not None}")
    
    if request_info:
        print(f"   请求信息键: {list(request_info.keys())}")
        print(f"   有basic_data: {'basic_data' in request_info}")
        if 'basic_data' in request_info:
            basic_data = request_info['basic_data']
            print(f"   basic_data类型: {type(basic_data)}")
            if isinstance(basic_data, dict):
                print(f"   basic_data键: {list(basic_data.keys())}")
                if 'bz' in basic_data:
                    print(f"   八字数据: {basic_data['bz']}")
    
    # 测试 get_basic_result 方法
    print(f"\n🧪 测试 get_basic_result 方法:")
    basic_result = bazi_service.get_basic_result(request_id)
    print(f"   返回结果: {basic_result is not None}")
    
    if basic_result:
        print(f"   结果类型: {type(basic_result)}")
        print(f"   结果键: {list(basic_result.keys()) if isinstance(basic_result, dict) else 'N/A'}")
    else:
        print(f"   ❌ get_basic_result 返回 None")
    
    # 检查完整结果
    print(f"\n📋 检查完整结果:")
    full_result = bazi_service.results.get(request_id)
    print(f"   完整结果存在: {full_result is not None}")
    
    if full_result:
        print(f"   完整结果类型: {type(full_result)}")
        print(f"   完整结果键: {list(full_result.keys()) if isinstance(full_result, dict) else 'N/A'}")
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")

if __name__ == "__main__":
    test_get_basic_result()
