/**
 * @file 八字分析结果事件处理模块
 * @description 处理页面交互事件
 */

// 注意：这些函数在 detailed_result_utils.js 中定义，通过全局作用域访问
// saveTitle, toggleSection, editTitle, downloadReport 在其他文件中定义

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 设置标题编辑功能
    const titleElement = document.getElementById('resultTitle');
    if (titleElement) {
        titleElement.addEventListener('blur', () => {
            this.saveTitle(titleElement.textContent);
        });
        
        titleElement.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                titleElement.blur();
            }
        });
    }
    
    // 设置下载按钮事件监听（如果有）
    const downloadButton = document.getElementById('downloadReport');
    if (downloadButton) {
        downloadButton.addEventListener('click', downloadReport);
    }
    
    // 设置编辑标题按钮事件监听（如果有）
    const editTitleButton = document.getElementById('editTitle');
    if (editTitleButton) {
        editTitleButton.addEventListener('click', editTitle);
    }
    
    // 注册全局函数，使HTML中的onclick事件可以调用
    window.toggleSection = toggleSection;
    window.editTitle = editTitle;
    window.downloadReport = downloadReport;
} 