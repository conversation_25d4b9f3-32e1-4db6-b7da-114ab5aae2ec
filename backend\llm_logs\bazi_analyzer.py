#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字分析器模块
处理八字数据并调用LLM进行分析
"""

import json
import os
import logging
import threading
import time
from datetime import datetime
from bazi_summary import get_bazi_info, set_bazi_data

logger = logging.getLogger(__name__)

class BaziAnalyzer:
    """八字分析器"""
    
    def __init__(self, personality_only=False, bazi_data=None):
        """初始化八字分析器
        
        Args:
            personality_only: 是否只分析性格维度
            bazi_data: 八字数据
        """
        self.personality_only = personality_only
        self.bazi_data = bazi_data
        self.output_path = None
        self.analysis_results = {}
        self.lock = threading.Lock()
    
    def is_valid_data(self):
        """验证八字数据是否有效"""
        return self.bazi_data is not None
    
    def get_available_dimensions(self):
        """获取可用的分析维度"""
        if self.personality_only:
            return ["性格"]
        else:
            # 返回所有可用维度
            return ["日主强弱", "健康", "2025运势", "性格", "格局", "学业", "职业", "感情"]
    
    def _get_dimension_filename(self, dimension):
        """获取维度对应的提示词文件名
        
        Args:
            dimension: 维度名称
            
        Returns:
            str: 对应的文件名
        """
        try:
            # 从index.json加载维度到文件的映射
            index_file = os.path.join(os.path.dirname(__file__), "prompts", "index.json")
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    if "prompts_index" in index_data and dimension in index_data["prompts_index"]:
                        return index_data["prompts_index"][dimension]
            
            # 如果无法从索引文件中找到，使用默认映射
            dimension_filename_map = {
                "日主强弱": "strength.json",
                "健康": "health.json",
                "2025运势": "fortune_2025.json",
                "性格": "personality.json",
                "格局": "common.json",
                "学业": "education.json",
                "职业": "career.json",
                "感情": "relationship.json"
            }
            return dimension_filename_map.get(dimension, "common.json")
        except Exception as e:
            logger.error(f"获取维度 {dimension} 的提示词文件名时出错: {str(e)}")
            return "common.json"  # 出错时使用通用模板
    
    def analyze_dimension(self, dimension):
        """分析特定维度
        
        Args:
            dimension: 分析维度
            
        Returns:
            str: 分析结果
        """
        try:
            print(f"🧠 开始分析维度: {dimension}", flush=True)
            
            # 确保先设置八字数据，再获取结构化的八字信息
            if self.bazi_data:
                set_bazi_data(self.bazi_data)
            
            # 获取结构化的八字信息
            bazi_info_text = get_bazi_info()
            
            # 获取维度对应的提示词文件名
            dimension_filename = self._get_dimension_filename(dimension)
            
            # 从prompts目录加载分析模板
            prompt_file = os.path.join(os.path.dirname(__file__), "prompts", dimension_filename)
            if not os.path.exists(prompt_file):
                # 如果没有特定维度的模板，使用通用模板
                prompt_file = os.path.join(os.path.dirname(__file__), "prompts", "common.json")
                print(f"⚠️ 未找到维度 {dimension} 的提示词模板，使用通用模板", flush=True)
            else:
                print(f"📝 维度 {dimension} 使用提示词模板: {dimension_filename}", flush=True)
            
            # 加载维度约束和格式模板
            format_template_file = os.path.join(os.path.dirname(__file__), "prompts", "format_template.json")
            dimension_constraints_file = os.path.join(os.path.dirname(__file__), "prompts", "dimension_constraints.json")
            
            # 从文件加载提示词模板
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt_template = json.load(f)
                
            format_template = {}
            dimension_constraints = {}
            
            # 尝试加载格式模板
            if os.path.exists(format_template_file):
                try:
                    with open(format_template_file, 'r', encoding='utf-8') as f:
                        format_template = json.load(f)
                    print(f"📋 加载格式模板: format_template.json", flush=True)
                except Exception as e:
                    print(f"⚠️ 加载格式模板时出错: {str(e)}", flush=True)
            
            # 尝试加载维度约束
            if os.path.exists(dimension_constraints_file):
                try:
                    with open(dimension_constraints_file, 'r', encoding='utf-8') as f:
                        dimension_constraints = json.load(f)
                    print(f"🔒 加载维度约束: dimension_constraints.json", flush=True)
                except Exception as e:
                    print(f"⚠️ 加载维度约束时出错: {str(e)}", flush=True)
            
            # 构建完整提示词
            prompt = f"请根据以下八字信息：\n\n{bazi_info_text}\n\n"
            prompt += f"严格按照以下要求进行【{dimension}】维度的分析：\n"
            
            # 添加格式要求
            if format_template:
                prompt += "【输出格式要求】：\n"
                prompt += json.dumps(format_template, ensure_ascii=False, indent=2)
                prompt += "\n\n"
            
            # 添加维度约束
            if dimension_constraints and dimension in dimension_constraints:
                prompt += "【维度约束】：\n"
                prompt += json.dumps(dimension_constraints[dimension], ensure_ascii=False, indent=2)
                prompt += "\n\n"
            
            # 添加主要分析要求
            prompt += "【分析要求】：\n"
            prompt += json.dumps(prompt_template, ensure_ascii=False, indent=2)
            
            # 调用LLM API进行分析
            from LLMapi import call_api
            
            # 构建API请求数据
            api_data = {
                "model": "deepseek/deepseek-r1-0528:free",
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            # 记录提示词到日志
            self._log_prompt(dimension, prompt)
            
            # 调用API
            api_result = call_api(data=json.dumps(api_data))
            
            # 处理结果
            if "error" in api_result:
                return f"分析失败: {api_result['error']}"
            
            if "choices" in api_result and len(api_result["choices"]) > 0:
                analysis_content = api_result["choices"][0]["message"]["content"]
                
                # 清理内容中的代码块标记
                analysis_content = self._clean_result_format(analysis_content)
                
                # 保存结果
                with self.lock:
                    self.analysis_results[dimension] = analysis_content
                
                print(f"✅ 维度 {dimension} 分析完成", flush=True)
                return analysis_content
            else:
                return f"API返回格式无效或为空"
            
        except Exception as e:
            error_msg = f"分析维度 {dimension} 时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            return f"分析出错: {str(e)}"
    
    def batch_analyze_with_queue(self, dimensions, concurrency=10, progress_callback=None):
        """批量分析多个维度
        
        Args:
            dimensions: 维度列表
            concurrency: 并发数
            progress_callback: 进度回调函数
            
        Returns:
            dict: 分析结果
        """
        results = {}
        total = len(dimensions)
        completed = 0
        
        # 创建线程池
        threads = []
        
        # 定义线程函数
        def worker(dim):
            nonlocal completed
            try:
                result = self.analyze_dimension(dim)
                with self.lock:
                    results[dim] = result
                    completed += 1
                    if progress_callback:
                        progress_callback(completed, total, dim)
            except Exception as e:
                with self.lock:
                    results[dim] = f"分析出错: {str(e)}"
                    completed += 1
                    if progress_callback:
                        progress_callback(completed, total, dim)
        
        # 创建并启动所有线程
        for dim in dimensions:
            thread = threading.Thread(target=worker, args=(dim,))
            threads.append(thread)
            thread.start()
            
            # 控制并发数
            while sum(1 for t in threads if t.is_alive()) >= concurrency:
                time.sleep(0.1)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 保存结果到文件
        if self.output_path:
            try:
                with open(self.output_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"✅ 分析结果已保存到: {self.output_path}", flush=True)
            except Exception as e:
                print(f"❌ 保存结果到文件时出错: {str(e)}", flush=True)
                logger.error(f"保存结果到文件时出错: {str(e)}")
        else:
            print("⚠️ 未设置输出路径，分析结果未保存到文件", flush=True)
        
        return results
    
    def _log_prompt(self, dimension, prompt):
        """记录提示词到日志文件"""
        try:
            log_dir = os.path.join(os.path.dirname(__file__), "llm_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
            log_file = os.path.join(log_dir, f"llm_prompt_{timestamp}.json")
            
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "model": "deepseek/deepseek-r1-0528:free",
                "dimension": dimension,
                "prompt_length": len(prompt),
                "prompt_content": prompt
            }
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"记录提示词到日志时出错: {str(e)}")
    
    def _clean_result_format(self, content):
        """清理LLM返回结果中的格式问题
        
        Args:
            content: LLM返回的原始内容
            
        Returns:
            str: 清理后的内容
        """
        if content is None:
            return ""
            
        # 去除可能的代码块标记
        # 处理 ```json ... ``` 格式
        if content.startswith('```json') and '```' in content[6:]:
            start_idx = content.find('{', content.find('```json'))
            end_idx = content.rfind('}', 0, content.rfind('```'))
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                try:
                    # 提取JSON部分
                    json_content = content[start_idx:end_idx+1]
                    parsed_json = json.loads(json_content)
                    # 如果JSON中只有一个键且值是该维度的内容，直接返回值部分
                    if len(parsed_json) == 1:
                        key = list(parsed_json.keys())[0]
                        return json.dumps(parsed_json[key], ensure_ascii=False)
                    return json.dumps(parsed_json, ensure_ascii=False)
                except json.JSONDecodeError:
                    pass  # 解析失败，使用原内容继续处理
                    
        # 处理 ``` ... ``` 格式（无语言指定）
        elif content.startswith('```') and content.endswith('```'):
            content = content[3:-3].strip()
            
        # 去除多余的注释行
        lines = content.split('\n')
        cleaned_lines = [line for line in lines if not line.strip().startswith('//')]
        content = '\n'.join(cleaned_lines)
            
        return content 