#!/usr/bin/env python3
"""
测试配置API端点
"""

from app import app
import json

def test_config_endpoints():
    with app.test_client() as client:
        print("=== 测试配置API ===")
        
        # 测试获取配置
        print("\n1. 测试获取配置...")
        response = client.get('/api/config/get')
        print(f'GET /api/config/get - Status: {response.status_code}')
        if response.status_code == 200:
            config = response.get_json()
            print(f'Config loaded: {config.get("success", False)}')
            print(f'Personality only: {config.get("config", {}).get("personality_only", "N/A")}')
        
        # 测试获取维度配置
        print("\n2. 测试获取维度配置...")
        response = client.get('/api/config/dimensions')
        print(f'GET /api/config/dimensions - Status: {response.status_code}')
        if response.status_code == 200:
            dimensions = response.get_json()
            print(f'Dimensions count: {len(dimensions.get("dimensions", []))}')
        
        # 测试卡密列表
        print("\n3. 测试卡密列表...")
        response = client.get('/api/cards')
        print(f'GET /api/cards - Status: {response.status_code}')
        if response.status_code == 200:
            cards = response.get_json()
            print(f'Cards count: {len(cards.get("cards", []))}')
        
        return True

if __name__ == '__main__':
    try:
        test_config_endpoints()
        print("\n✅ 配置API测试完成")
    except Exception as e:
        print(f"\n❌ 配置API测试失败: {e}")
        import traceback
        traceback.print_exc()
