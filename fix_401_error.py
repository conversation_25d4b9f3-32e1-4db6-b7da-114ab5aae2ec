# 修复401错误的脚本
import json
import os

def fix_check_status_logic():
    """修复check-status接口的401错误问题"""
    
    # 1. 修复后端app.py中的check-status逻辑
    app_py_path = "backend/app.py"
    
    if os.path.exists(app_py_path):
        with open(app_py_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找需要修复的部分
        if "if original_card_key not in card_manager.cards_data:" in content:
            print("找到需要修复的check-status逻辑")
            
            # 添加更详细的调试信息和错误处理
            old_logic = '''if original_card_key not in card_manager.cards_data:
                print(f"❌ 卡密不存在: {original_card_key}", flush=True)
                # 返回401错误
                return jsonify({
                    'completed': False,
                    'success': False,
                    'error': '卡密不存在或无效'
                }), 401'''
            
            new_logic = '''# 重新加载卡密数据以防数据不同步
            card_manager.cards_data = card_manager._load_cards()
            
            if original_card_key not in card_manager.cards_data:
                print(f"❌ 卡密不存在: {original_card_key}", flush=True)
                print(f"当前可用卡密: {list(card_manager.cards_data.keys())}", flush=True)
                
                # 检查是否是请求已完成的情况
                bazi_status = bazi_service.get_processing_status(identifier)
                if bazi_status and bazi_status.get('completed'):
                    print(f"请求已完成，返回完成状态而非401错误", flush=True)
                    return jsonify({
                        'completed': True,
                        'success': True,
                        'status': 'completed',
                        'message': '分析已完成'
                    }), 200
                
                # 返回401错误
                return jsonify({
                    'completed': False,
                    'success': False,
                    'error': '卡密不存在或无效'
                }), 401'''
            
            content = content.replace(old_logic, new_logic)
            
            with open(app_py_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            print("✅ 已修复后端check-status逻辑")
        else:
            print("未找到需要修复的check-status逻辑")
    else:
        print(f"文件不存在: {app_py_path}")

def fix_frontend_polling():
    """修复前端轮询逻辑，避免在请求完成后继续轮询"""
    
    api_js_path = "js/api.js"
    
    if os.path.exists(api_js_path):
        with open(api_js_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找checkCompletionStatus函数
        if "checkCompletionStatus" in content:
            print("找到checkCompletionStatus函数")
            
            # 添加更好的错误处理
            if "response.status === 401" in content:
                print("找到401错误处理逻辑")
                
                # 改进401错误处理，避免无限轮询
                old_401_logic = '''} else if (response.status === 401) {
                    // 卡密无效处理
                    const errorData = await response.text();
                    console.error('卡密无效:', errorData);
                    if (callback) callback(false, '卡密无效');
                    return;'''
                
                new_401_logic = '''} else if (response.status === 401) {
                    // 卡密无效处理
                    const errorData = await response.text();
                    console.error('卡密验证失败:', errorData);
                    
                    // 检查是否是请求已完成但卡密验证失败的情况
                    // 这种情况下应该停止轮询而不是报错
                    console.log('停止轮询，可能请求已完成');
                    if (callback) callback(false, '请求状态检查失败，请刷新页面查看结果');
                    return;'''
                
                content = content.replace(old_401_logic, new_401_logic)
                
                with open(api_js_path, "w", encoding="utf-8") as f:
                    f.write(content)
                
                print("✅ 已修复前端401错误处理逻辑")
            else:
                print("未找到401错误处理逻辑")
        else:
            print("未找到checkCompletionStatus函数")
    else:
        print(f"文件不存在: {api_js_path}")

if __name__ == "__main__":
    print("开始修复401错误...")
    fix_check_status_logic()
    fix_frontend_polling()
    print("修复完成！")
    print("\n修复说明:")
    print("1. 后端: 改进了check-status接口的卡密验证逻辑，添加了数据重新加载和完成状态检查")
    print("2. 前端: 改进了401错误处理，避免在请求完成后继续报错")
    print("\n请重启后端服务以使修改生效。")