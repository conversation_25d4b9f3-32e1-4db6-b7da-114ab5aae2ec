/**
 * 详细分析结果页面状态管理模块
 * 包含页面导航、状态切换等功能
 */

// 页面辅助功能
function goHome() {
    window.location.href = 'index.html';
}

// 重试加载功能
function retryLoad() {
    if (window.detailedResultViewer && typeof window.detailedResultViewer.loadAnalysisResult === 'function') {
        showLoadingState();
        window.detailedResultViewer.loadAnalysisResult();
    } else {
        location.reload();
    }
}

// 尝试渐进式加载
function tryProgressiveLoad() {
    console.log('用户点击渐进式加载');

    // 隐藏错误状态
    const errorState = document.getElementById('errorState');
    if (errorState) {
        errorState.style.display = 'none';
    }

    // 显示加载状态
    const loadingState = document.getElementById('loadingState');
    if (loadingState) {
        loadingState.style.display = 'block';
        const loadingText = loadingState.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = '正在加载排盘结果...';
        }
    }

    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const cardKey = urlParams.get('cardKey');
    const reportId = urlParams.get('id');

    if (cardKey || reportId) {
        // 启动渐进式加载
        if (window.progressiveLoader) {
            window.progressiveLoader.startProgressiveLoad(cardKey, reportId);
        } else {
            console.error('渐进式加载器未找到');
            showErrorState('加载器错误', '渐进式加载器未正确初始化');
        }
    } else {
        console.error('缺少必要的URL参数');
        showErrorState('参数错误', '缺少卡密或报告ID参数');
    }
}

// 状态管理函数
function showLoadingState() {
    hideAllStates();
    document.getElementById('loadingState').style.display = 'flex';
    
    // 添加加载动画
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        let progress = 0;
        const interval = setInterval(() => {
            if (progress >= 90) {
                clearInterval(interval);
            } else {
                progress += Math.random() * 5;
                progressBar.style.width = `${Math.min(progress, 90)}%`;
                progressBar.setAttribute('aria-valuenow', Math.round(Math.min(progress, 90)));
            }
        }, 300);
        
        // 存储interval ID以便稍后清除
        window.loadingInterval = interval;
    }
}

function showErrorState(message, details) {
    hideAllStates();
    const errorState = document.getElementById('errorState');
    const errorMessage = document.getElementById('errorMessage');
    const errorDetails = document.getElementById('errorDetails');
    
    if (message) {
        errorMessage.textContent = message;
    }
    if (details) {
        errorDetails.textContent = details;
    }
    
    errorState.style.display = 'flex';
}

function showEmptyState() {
    hideAllStates();
    document.getElementById('emptyState').style.display = 'flex';
}

function showAnalysisContent() {
    hideAllStates();
    document.getElementById('analysisContent').style.display = 'block';
}

function hideAllStates() {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('errorState').style.display = 'none';
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('analysisContent').style.display = 'none';
    
    // 清除加载动画interval
    if (window.loadingInterval) {
        clearInterval(window.loadingInterval);
        window.loadingInterval = null;
    }
}

// 页面初始化
function initDetailedPage() {
    // 显示加载状态
    showLoadingState();
    
    // 将状态管理函数设置为全局函数
    window.showLoadingState = showLoadingState;
    window.showErrorState = showErrorState;
    window.showEmptyState = showEmptyState;
    window.showAnalysisContent = showAnalysisContent;
    window.goHome = goHome;
    window.retryLoad = retryLoad;
    
    // 添加键盘导航支持
    document.addEventListener('keydown', function(e) {
        // ESC键退出编辑模式
        if (e.key === 'Escape') {
            const titleElement = document.getElementById('resultTitle');
            if (titleElement.contentEditable === 'true') {
                titleElement.contentEditable = 'false';
                titleElement.style.border = 'none';
                titleElement.style.padding = '0';
                titleElement.blur();
                
                // 移除辅助提示
                const editHint = document.getElementById('editHint');
                if (editHint) editHint.remove();
            }
        }
    });
    
    console.log('页面加载完成，检查downloadReport函数:', typeof window.downloadReport);
    
    // 如果函数不可用，尝试添加一个备用函数
    if (typeof window.downloadReport !== 'function') {
        console.warn('downloadReport函数不可用，添加备用函数');
        window.downloadReport = function() {
            console.log('备用downloadReport函数被调用');
            
            // 获取报告内容
            const title = document.getElementById('resultTitle').textContent || '八字分析报告';
            const dateStr = new Date().toISOString().slice(0, 10);
            const analysisContent = document.getElementById('analysisContent');
            
            if (!analysisContent) {
                alert('无法找到报告内容，请确保页面已完全加载');
                return;
            }
            
            // 在导出前，确保所有流年部分都被展开
            const fortuneYearSections = document.querySelectorAll('.detailed-dimension-section');
            fortuneYearSections.forEach(section => {
                const title = section.querySelector('.detailed-dimension-title');
                // 检查是否为流年部分（通过标题文本判断）
                if (title && title.textContent && 
                    (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                    // 确保内容显示
                    const content = section.querySelector('.detailed-dimension-content');
                    if (content) content.style.display = 'block';
                }
            });
            
            // 获取八字信息部分
            const baziInfoElement = document.querySelector('.bazi-info-section');
            let baziInfoHtml = '';
            if (baziInfoElement) {
                baziInfoHtml = `
                    <div class="bazi-info">
                        ${baziInfoElement.innerHTML}
                    </div>
                `;
            }
            
            // 获取分析维度部分，确保格式正确
            let sectionsHtml = '';
            const sections = document.querySelectorAll('.detailed-dimension-section');
            sections.forEach(section => {
                const title = section.querySelector('.detailed-dimension-title');
                const content = section.querySelector('.detailed-dimension-text');
                
                if (title && content) {
                    sectionsHtml += `
                        <div class="section">
                            <div class="section-title">${title.innerHTML}</div>
                            <div class="section-content">${content.innerHTML}</div>
                        </div>
                    `;
                }
            });
            
            // 如果没有找到分析维度，直接使用分析内容
            if (!sectionsHtml) {
                sectionsHtml = analysisContent.innerHTML;
            }
            
            // 创建更完整的HTML文档，包含更丰富的样式
            const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>${title}</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 基础样式 */
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        /* 标题样式 */
        h1 {
            color: #4a00e0;
            text-align: center;
            margin-bottom: 20px;
            font-size: 28px;
            border-bottom: 2px solid #4a00e0;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #4a00e0;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 22px;
        }
        
        h3 {
            color: #4a00e0;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        /* 段落样式 */
        p {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        /* 区域样式 */
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .meta-info {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
        }
        
        /* 八字信息区 */
        .bazi-info {
            background-color: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #4a00e0;
            margin-bottom: 25px;
            border-radius: 5px;
        }
        
        /* 分析部分 */
        .section {
            margin-bottom: 25px;
            border: 1px solid #eaeaea;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .section-content {
            padding: 15px;
            background-color: white;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 10px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        /* 页脚 */
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${title}</h1>
        <div class="meta-info">
            <p>生成时间: ${new Date().toLocaleString()}</p>
        </div>
        
        ${baziInfoHtml}
        
        <div class="analysis-content">
            ${sectionsHtml}
        </div>
        
        <div class="footer">
            <p>八字分析系统 - 专业命理分析报告</p>
            <p>生成日期: ${new Date().toLocaleDateString()}</p>
        </div>
    </div>
</body>
</html>`;
            
            // 创建下载
            const blob = new Blob([html], {type: 'text/html'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `八字分析报告_${title.replace(/[^\w\s-]/g, '')}_${dateStr}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('HTML报告已导出，样式已优化');
        };
    }
    
    // 添加导出源文件功能
    window.exportSource = function() {
        if (window.detailedResultViewer && typeof window.detailedResultViewer.exportSeparateFiles === 'function') {
            window.detailedResultViewer.exportSeparateFiles();
        } else {
            alert('导出功能不可用，请刷新页面后重试');
        }
    };
}

// 在页面加载完成后执行初始化
document.addEventListener('DOMContentLoaded', initDetailedPage); 