from flask import Flask, request, jsonify, send_from_directory, send_file
from flask_cors import CORS
import logging
from datetime import datetime
import os
import sys
import json  # 确保导入json模块

# 导入其他模块
from bazi_service import BaziService
from card_manager import CardManager
from url_generator import URLGenerator
from config_manager import get_config

# 导入提示词管理API
try:
    from prompt_api import init_prompt_api
    PROMPT_API_AVAILABLE = True
    print("✅ 提示词管理API模块加载成功")
except ImportError as e:
    PROMPT_API_AVAILABLE = False
    print(f"⚠️ 提示词管理API不可用: {e}")
    print("提示词管理功能将不可用，但不影响其他功能")

# 获取配置
config = get_config()

# 配置静态文件路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(backend_dir)

# 创建Flask应用，配置静态文件夹
app = Flask(__name__,
           static_folder=project_dir,  # 设置静态文件夹为项目根目录
           static_url_path='/static')  # 设置静态文件URL路径

# 配置CORS，允许所有源访问所有API和webhook路由
CORS(app, resources={
    r"/api/*": {"origins": "*", "methods": ["GET", "POST", "DELETE", "OPTIONS"]},
    r"/webhook/*": {"origins": "*", "methods": ["GET", "POST", "DELETE", "OPTIONS"]}
})

# 配置日志
log_level = getattr(logging, config.get('logging.level', 'INFO').upper())
log_format = config.get('logging.format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')

handlers = []
if config.get('logging.console_enabled', True):
    handlers.append(logging.StreamHandler(sys.stdout))
if config.get('logging.file_enabled', False):
    log_file = os.path.join(config.get_logs_dir(), 'app.log')
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    handlers.append(logging.FileHandler(log_file, encoding='utf-8'))

logging.basicConfig(
    level=log_level,
    format=log_format,
    handlers=handlers
)
logger = logging.getLogger(__name__)

# 确保print输出立即显示
sys.stdout.flush()

# 初始化服务
bazi_service = BaziService()
from card_manager_singleton import get_card_manager
card_manager = get_card_manager()
url_generator = URLGenerator()

# 注册配置API蓝图
from config_api import config_bp
app.register_blueprint(config_bp)

# 初始化提示词管理API
if PROMPT_API_AVAILABLE:
    try:
        init_prompt_api(app)
        logger.info("提示词管理API初始化成功")
    except Exception as e:
        logger.error(f"提示词管理API初始化失败: {str(e)}")
        PROMPT_API_AVAILABLE = False

# 添加分析统计API
@app.route('/api/stats/analysis', methods=['GET'])
def get_analysis_stats():
    """获取分析统计数据"""
    try:
        # 获取分析次数统计
        # 使用全局bazi_service实例

        # 统计总分析次数
        total_analysis = 0
        if hasattr(bazi_service, 'analysis_count'):
            total_analysis = bazi_service.analysis_count
        else:
            # 如果没有计数器，尝试从其他地方获取
            # 这里可以根据实际情况调整
            total_analysis = len(getattr(bazi_service, 'results', []))

        # 统计今日分析次数
        today_analysis = 0  # 这里可以添加今日统计逻辑

        # 统计各维度分析次数
        dimension_stats = {
            "性格": 0,
            "感情": 0,
            "职业": 0,
            "健康": 0,
            "日主强弱": 0,
            "学业": 0,
            "2025运势": 0
        }

        return jsonify({
            "total_analysis": total_analysis,
            "today_analysis": today_analysis,
            "dimension_stats": dimension_stats,
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取分析统计失败: {str(e)}")
        return jsonify({
            "total_analysis": 0,
            "today_analysis": 0,
            "dimension_stats": {},
            "error": str(e)
        }), 200  # 返回200避免前端报错

# 添加报告管理API
@app.route('/api/reports', methods=['GET'])
def get_reports():
    """获取所有分析报告列表"""
    try:
        # 使用全局bazi_service实例
        reports = []

        # 从bazi_service获取报告数据
        if hasattr(bazi_service, 'results') and bazi_service.results:
            # 按时间倒序排列（最新的在前）
            sorted_results = sorted(bazi_service.results.items(),
                                  key=lambda x: x[1].get('processed_time', datetime.min),
                                  reverse=True)

            for i, (key, result) in enumerate(sorted_results):
                # 提取八字信息
                data = result.get('data', {})
                bz_info = data.get('bz', {})

                # 构建八字字符串
                if bz_info and isinstance(bz_info, dict):
                    # 从bz字典中提取八字信息
                    year_gan = bz_info.get('0', '')
                    year_zhi = bz_info.get('1', '')
                    month_gan = bz_info.get('2', '')
                    month_zhi = bz_info.get('3', '')
                    day_gan = bz_info.get('4', '')
                    day_zhi = bz_info.get('5', '')
                    hour_gan = bz_info.get('6', '')
                    hour_zhi = bz_info.get('7', '')
                    birth_time = bz_info.get('8', '')

                    # 检查是否有有效的八字数据
                    if year_gan and year_zhi and month_gan and month_zhi and day_gan and day_zhi and hour_gan and hour_zhi:
                        bazi_str = f"{year_gan}{year_zhi} {month_gan}{month_zhi} {day_gan}{day_zhi} {hour_gan}{hour_zhi}"
                        birth_info = birth_time if birth_time else "未知生辰"
                    else:
                        bazi_str = "未知八字"
                        birth_info = "未知生辰"
                else:
                    bazi_str = "未知八字"
                    birth_info = "未知生辰"

                # 获取性别信息
                sex = data.get('sex', 0)
                gender = "男" if sex == 1 else "女" if sex == 0 else "未知"

                # 获取处理时间
                processed_time = result.get('processed_time', datetime.now())
                if isinstance(processed_time, str):
                    created_at = processed_time
                else:
                    created_at = processed_time.strftime('%Y-%m-%d %H:%M:%S') if processed_time else datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 获取摘要信息
                summary_data = result.get('summary', '')
                summary = ''

                if summary_data:
                    if isinstance(summary_data, dict):
                        # 如果summary是字典，提取其中的summary字段
                        summary = summary_data.get('summary', '')
                    elif isinstance(summary_data, str):
                        summary = summary_data

                if not summary:
                    # 如果没有摘要，尝试从LLM分析中获取
                    llm_analysis = result.get('llm_analysis', {})
                    if llm_analysis and isinstance(llm_analysis, dict):
                        analysis_results = llm_analysis.get('results', {})
                        if analysis_results:
                            # 获取第一个分析维度的内容作为预览
                            first_dimension = next(iter(analysis_results.values()), '')
                            if isinstance(first_dimension, str):
                                summary = first_dimension[:200]

                # 提取报告信息
                report = {
                    "id": str(i + 1),
                    "title": f"{gender}性八字分析报告 #{i + 1}",
                    "created_at": created_at,
                    "birth_info": birth_info,
                    "bazi": bazi_str,
                    "preview": (summary[:100] + '...' if len(summary) > 100 else summary) if summary else '暂无预览内容...',
                    "gender": gender,
                    "card_key": result.get('card_key', ''),
                    "request_id": key
                }
                reports.append(report)

        # 按时间倒序排列
        reports.reverse()

        return jsonify(reports)

    except Exception as e:
        logger.error(f"获取报告列表失败: {str(e)}")
        # 返回模拟数据
        return jsonify([
            {
                "id": "1",
                "title": "八字分析报告示例",
                "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "birth_info": "1990年5月15日 14:30",
                "bazi": "庚午年 辛巳月 甲子日 辛未时",
                "preview": "此人性格严谨务实，具有强烈的责任感和原则性..."
            }
        ])

# 添加管理员专用的报告访问API
@app.route('/api/admin/reports/<report_id>', methods=['GET'])
def get_admin_report(report_id):
    """管理员获取报告详情 - 无需卡密验证"""
    try:
        # 验证管理员权限（简单的密码验证）
        admin_token = request.args.get('token')
        if admin_token != '88888888':
            return jsonify({"error": "管理员权限验证失败"}), 401

        # 使用全局bazi_service实例

        if hasattr(bazi_service, 'results') and bazi_service.results:
            try:
                index = int(report_id) - 1
                if 0 <= index < len(bazi_service.results):
                    result = bazi_service.results[index]

                    # 如果是字符串，尝试解析
                    if isinstance(result, str):
                        try:
                            result = json.loads(result)
                        except:
                            result = {
                                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                "birth_info": "未知生辰",
                                "bazi": "未知八字",
                                "content": result
                            }

                    return jsonify(result)
            except (ValueError, IndexError):
                pass

        # 如果找不到报告，返回404
        return jsonify({"error": "报告不存在"}), 404

    except Exception as e:
        logger.error(f"获取管理员报告详情失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/reports/<report_id>', methods=['GET'])
def get_report_detail(report_id):
    """获取特定报告的详细信息"""
    try:
        # 使用全局bazi_service实例

        if hasattr(bazi_service, 'results') and bazi_service.results:
            try:
                index = int(report_id) - 1
                if 0 <= index < len(bazi_service.results):
                    result = bazi_service.results[index]
                    return jsonify(result)
            except (ValueError, IndexError):
                pass

        # 如果找不到报告，返回404
        return jsonify({"error": "报告不存在"}), 404

    except Exception as e:
        logger.error(f"获取报告详情失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# 环境检测函数
def is_server_environment():
    """检测是否运行在服务器环境中"""
    server_paths = [
        '/www.wuladehuanxiang.com',
        '/wwwroot/www.wuladehuanxiang.com',
        'C:\\wwwroot\\www.wuladehuanxiang.com'
    ]
    
    for path in server_paths:
        if os.path.exists(path):
            return True
    
    # 检查环境变量
    return os.environ.get('SERVER_ENV') == 'production'

# 初始化静态文件
def ensure_static_files():
    """确保必要的静态文件存在"""
    # 无论是什么环境，都确保能找到静态文件
    logger.info("检查和准备静态文件...")
    
    # 无论环境如何，优先使用相对路径
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(backend_dir)
    logger.info(f"项目根目录: {project_dir}")
    
    # 检查相对路径上的文件是否存在
    static_files = ['up.html', 'config.html', 'card_generator.html', 'index.html', 'styles.css']
    for file_name in static_files:
        file_path = os.path.join(project_dir, file_name)
        if os.path.exists(file_path):
            logger.info(f"找到文件 {file_name}: {file_path}")
        else:
            logger.warning(f"在项目根目录中找不到文件: {file_name}")
    
    # 如果是服务器环境，可能还需要处理特殊路径
    if is_server_environment():
        logger.info("服务器环境: 检查其他可能的文件路径")
        
        # 服务器环境下的其他可能路径
        server_paths = [
            os.path.join(STATIC_FOLDER, file_name) for file_name in static_files
        ]
        
        for file_path in server_paths:
            if os.path.exists(file_path):
                logger.info(f"在服务器路径找到文件: {file_path}")

# 静态文件目录（前端文件所在目录）
# 使用Flask应用配置的静态文件夹
STATIC_FOLDER = app.static_folder  # 使用Flask配置的静态文件夹

# 检测环境并记录信息
if is_server_environment():
    # 服务器环境
    SERVER_ROOT = 'C:\\wwwroot\\www.wuladehuanxiang.com\\project'
    # 仅当服务器路径确实存在时才使用它
    if os.path.exists(SERVER_ROOT):
        STATIC_FOLDER = SERVER_ROOT
        logger.info(f"检测到服务器环境，使用静态文件路径: {STATIC_FOLDER}")
    else:
        logger.info(f"检测到服务器环境，但服务器路径不存在，使用相对路径: {STATIC_FOLDER}")
else:
    # 本地开发环境
    logger.info(f"检测到本地环境，使用相对路径: {STATIC_FOLDER}")

# 确保静态文件存在
ensure_static_files()

@app.route('/')
def index():
    """主页 - 返回前端HTML文件"""
    try:
        # 使用相对路径查找up.html
        up_file_path = os.path.join(project_dir, 'up.html')
        if os.path.exists(up_file_path):
            logger.info(f"使用相对路径找到up.html: {up_file_path}")
            return send_file(up_file_path)
            
        # 如果相对路径找不到，尝试STATIC_FOLDER
        up_file_path = os.path.join(STATIC_FOLDER, 'up.html')
        if os.path.exists(up_file_path):
            logger.info(f"使用STATIC_FOLDER找到up.html: {up_file_path}")
            return send_file(up_file_path)
            
        # 如果仍找不到，返回一个简单的默认页面
        logger.warning("找不到up.html文件，返回默认页面")
        default_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字分析系统</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        h1 {{
            color: #4CAF50;
            margin-bottom: 10px;
        }}
        .subtitle {{
            color: #666;
            font-size: 1.1em;
        }}
        .form-container {{
            margin-top: 30px;
        }}
        .form-section {{
            margin-bottom: 25px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 5px;
        }}
        .section-title {{
            margin-top: 0;
            color: #4CAF50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .form-row {{
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }}
        .form-col {{
            flex: 1;
            padding: 0 10px;
            min-width: 200px;
        }}
        .input-group {{
            margin-bottom: 15px;
        }}
        label {{
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }}
        input, select {{
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border 0.3s;
        }}
        input:focus, select:focus {{
            border-color: #4CAF50;
            outline: none;
        }}
        .input-icon {{
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }}
        .form-actions {{
            text-align: center;
            margin-top: 30px;
        }}
        .submit-btn {{
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 18px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }}
        .submit-btn:hover {{
            background: #45a049;
        }}
        .btn-icon {{
            margin-right: 10px;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>八字分析系统</h1>
            <p class="subtitle">专业的命理分析，洞察人生运势</p>
        </div>
        
        <div class="form-container">
            <div class="form-section">
                <h3 class="section-title">🚀 服务已启动</h3>
                <p>八字分析系统已成功启动，请使用完整的前端页面访问系统功能。</p>
                <p>当前环境: <strong>{is_server_environment() and '服务器环境' or '本地环境'}</strong></p>
                <p>静态文件路径: <strong>{STATIC_FOLDER}</strong></p>
                <p>项目根目录: <strong>{project_dir}</strong></p>
            </div>
            
            <div class="form-section">
                <h3 class="section-title">🔎 文件搜索结果</h3>
                <p>系统尝试查找的文件路径:</p>
                <ul>
                    <li>相对路径: {os.path.join(project_dir, 'up.html')} - <strong>{'存在' if os.path.exists(os.path.join(project_dir, 'up.html')) else '不存在'}</strong></li>
                    <li>静态路径: {os.path.join(STATIC_FOLDER, 'up.html')} - <strong>{'存在' if os.path.exists(os.path.join(STATIC_FOLDER, 'up.html')) else '不存在'}</strong></li>
                </ul>
            </div>
            
            <div class="form-section">
                <h3 class="section-title">🔗 快速链接</h3>
                <ul>
                    <li><a href="/config">配置管理</a></li>
                    <li><a href="/card-generator">卡密生成器</a></li>
                    <li><a href="/api/health">健康检查</a></li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 八字分析系统 | 专业命理分析服务</p>
        </div>
    </div>
</body>
</html>
        """
        return default_html
    except Exception as e:
        logger.error(f"访问主页时出错: {str(e)}")
        return f"<h1>欢迎使用八字服务</h1><p>错误: {str(e)}</p>", 500

@app.route('/config')
def config_page():
    """配置管理页面"""
    try:
        # 尝试相对路径
        config_file_path = os.path.join(project_dir, 'config.html')
        if os.path.exists(config_file_path):
            logger.info(f"使用相对路径找到config.html: {config_file_path}")
            return send_file(config_file_path)
            
        # 尝试静态目录
        config_file_path = os.path.join(STATIC_FOLDER, 'config.html')
        if os.path.exists(config_file_path):
            logger.info(f"使用STATIC_FOLDER找到config.html: {config_file_path}")
            return send_file(config_file_path)
            
        return f"<h1>配置管理</h1><p>错误: 找不到配置页面文件</p>", 500
    except Exception as e:
        logger.error(f"访问配置页面时出错: {str(e)}")
        return f"<h1>配置管理</h1><p>错误: {str(e)}</p>", 500

@app.route('/card-generator')
def card_generator_page():
    """卡密生成器页面"""
    try:
        # 尝试相对路径
        file_path = os.path.join(project_dir, 'card_generator.html')
        if os.path.exists(file_path):
            logger.info(f"使用相对路径找到card_generator.html: {file_path}")
            return send_file(file_path)
            
        # 尝试静态目录
        file_path = os.path.join(STATIC_FOLDER, 'card_generator.html')
        if os.path.exists(file_path):
            logger.info(f"使用STATIC_FOLDER找到card_generator.html: {file_path}")
            return send_file(file_path)
            
        return f"<h1>卡密生成器</h1><p>错误: 找不到卡密生成器页面文件</p>", 500
    except Exception as e:
        logger.error(f"访问卡密生成器页面时出错: {str(e)}")
        return f"<h1>卡密生成器</h1><p>错误: {str(e)}</p>", 500

@app.route('/bazi/index.html')
def bazi_result_page():
    """八字结果页面"""
    try:
        # 获取卡密参数
        card_key = request.args.get('cardKey')
        
        if not card_key:
            return "<h1>错误</h1><p>缺少卡密参数，无法显示结果</p>", 400
        
        # 检查结果是否存在
        result = bazi_service.get_result(card_key)
        if not result:
            return "<h1>错误</h1><p>未找到对应的分析结果</p>", 404
        
        # 获取API数据
        api_data = result.get('data', {})
        
        # 格式化JSON数据用于显示
        formatted_json = json.dumps(api_data, ensure_ascii=False, indent=2) if api_data else '无数据'
        
        # 生成结果页面HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字分析结果</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; margin-bottom: 20px; }}
        .section {{ margin-bottom: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px; }}
        .section h3 {{ color: #4CAF50; margin-top: 0; border-bottom: 1px solid #ddd; padding-bottom: 8px; }}
        .data-item {{ margin: 8px 0; padding: 8px; background: white; border-radius: 3px; border-left: 3px solid #4CAF50; }}
        .summary-section {{ background: #e8f5e8; border-left: 4px solid #4CAF50; }}
        .llm-analysis-section {{ background: #e3f2fd; border-left: 4px solid #2196F3; }}
        .json-container {{ background: #f8f8f8; border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0; max-height: 500px; overflow-y: auto; }}
        .json-data {{ font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap; word-wrap: break-word; }}
        .back-button {{ display: inline-block; margin-top: 20px; padding: 10px 20px; background: #4CAF50; color: white; text-decoration: none; border-radius: 5px; transition: background 0.3s; }}
        .back-button:hover {{ background: #45a049; }}
        .toggle-button {{ background: #2196F3; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer; margin: 5px 0; }}
        .toggle-button:hover {{ background: #1976D2; }}
        .hidden {{ display: none; }}
        .data-summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0; }}
        .summary-card {{ background: white; padding: 10px; border-radius: 5px; border: 1px solid #ddd; }}
        .summary-card h4 {{ margin: 0 0 8px 0; color: #333; font-size: 14px; }}
        .summary-card p {{ margin: 0; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>八字分析结果</h1>
            <p>卡密: <strong>{card_key}</strong></p>
        </div>
        
        <div class="section">
            <h3>📊 处理信息</h3>
            <div class="data-item">✅ 状态: {result.get('success', False) and '成功' or '失败'}</div>
            <div class="data-item">⏰ 处理时间: {result.get('processed_time', '未知')}</div>
            <div class="data-item">🔗 API链接: {result.get('bazi_url', '未知')}</div>
        </div>
        
        {_generate_summary_section(result)}
        
        {_generate_llm_analysis_section(result)}
        
        <div class="section">
            <h3>📋 数据概览</h3>
            <div class="data-summary">
                <div class="summary-card">
                    <h4>数据大小</h4>
                    <p>{len(str(api_data))} 字符</p>
                </div>
                <div class="summary-card">
                    <h4>主要字段</h4>
                    <p>{len(api_data.keys()) if isinstance(api_data, dict) else 0} 个</p>
                </div>
                <div class="summary-card">
                    <h4>数据类型</h4>
                    <p>{type(api_data).__name__}</p>
                </div>
            </div>
            
            {f'<div class="data-item">🔑 数据字段: {", ".join(list(api_data.keys())[:10])}{", ..." if len(api_data.keys()) > 10 else ""}</div>' if isinstance(api_data, dict) and api_data else ''}
        </div>
        
        <div class="section">
            <h3>🔍 完整API数据</h3>
            <button class="toggle-button" onclick="toggleJsonData()">显示/隐藏原始数据</button>
            <div id="jsonData" class="json-container hidden">
                <div class="json-data">{formatted_json}</div>
            </div>
        </div>
        
        <a href="/" class="back-button">🏠 返回首页</a>
    </div>
    
    <script>
        function toggleJsonData() {{
            const jsonData = document.getElementById('jsonData');
            jsonData.classList.toggle('hidden');
        }}
        
        function toggleLLMData() {{
            const llmData = document.getElementById('llmData');
            llmData.classList.toggle('hidden');
        }}
    </script>
</body>
</html>
        """
        
        return html_content
        
    except Exception as e:
        logger.error(f"显示八字结果页面时出错: {str(e)}")
        return f"<h1>错误</h1><p>显示结果时出错: {str(e)}</p>", 500

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    try:
        # 排除API路径 - 这些应该由API路由处理
        if filename.startswith('api/'):
            logger.warning(f"API路径 {filename} 不应该到达静态文件处理器")
            return "API路径错误", 404

        # 安全检查，防止目录遍历攻击
        if '..' in filename or filename.startswith('/'):
            return "访问被拒绝", 403

        # 允许的文件扩展名
        allowed_extensions = ['.html', '.css', '.js', '.json', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.ttf', '.woff', '.woff2', '.eot']
        file_ext = os.path.splitext(filename)[1].lower()

        if file_ext not in allowed_extensions:
            return "文件类型不被支持", 403
        
        # 首先尝试从项目根目录获取文件（相对路径）
        file_path = os.path.join(project_dir, filename)
        if os.path.isfile(file_path):
            logger.debug(f"从项目根目录提供文件: {filename}")
            return send_file(file_path)
            
        # 如果不存在，尝试从配置的静态目录获取文件
        file_path = os.path.join(STATIC_FOLDER, filename)
        if os.path.isfile(file_path):
            logger.debug(f"从STATIC_FOLDER提供文件: {filename}")
            return send_file(file_path)
        
        # 记录尝试的路径，以便调试
        logger.error(f"找不到文件: {filename}")
        logger.error(f"尝试的路径: {os.path.join(project_dir, filename)}, {os.path.join(STATIC_FOLDER, filename)}")
        
        return "文件未找到", 404
    except FileNotFoundError:
        logger.error(f"文件未找到: {filename}")
        return "文件未找到", 404
    except Exception as e:
        logger.error(f"访问静态文件时出错: {str(e)}")
        return "服务器错误", 500

@app.route('/webhook/a7712e3b-7f96-4b2e-9573-a6f87d9fd848', methods=['GET'])
def handle_bazi_request():
    """处理八字请求"""
    try:
        print(f"\n🔔 收到新的八字请求", flush=True)
        print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
        
        # 获取请求参数
        year = request.args.get('year')
        month = request.args.get('month')
        day = request.args.get('day')
        time = request.args.get('time')
        gender = request.args.get('gender')
        card_key = request.args.get('cardKey')
        
        print(f"请求参数:", flush=True)
        print(f"  年份: {year}", flush=True)
        print(f"  月份: {month}", flush=True)
        print(f"  日期: {day}", flush=True)
        print(f"  时辰: {time}", flush=True)
        print(f"  性别: {gender}", flush=True)
        print(f"  卡密: {card_key}", flush=True)
        
        # 验证必填参数
        if not all([year, month, day, time, gender, card_key]):
            print(f"❌ 参数验证失败: 缺少必填参数", flush=True)
            return jsonify({
                'success': False,
                'message': '缺少必填参数'
            }), 400
        
        print(f"✅ 参数验证通过", flush=True)
        
        # 验证卡密
        print(f"🔐 验证卡密...", flush=True)
        if not card_manager.validate_card(card_key):
            print(f"❌ 卡密验证失败: {card_key}", flush=True)
            return jsonify({
                'success': False,
                'message': '卡密无效或已过期'
            }), 401
        
        print(f"✅ 卡密验证通过: {card_key}", flush=True)
        
        # 标记卡密为处理中
        card_manager.mark_processing(card_key)
        print(f"📝 卡密已标记为处理中", flush=True)
        
        # 生成八字API链接
        print(f"🔗 生成八字API链接...", flush=True)
        bazi_url = url_generator.generate_bazi_url(year, month, day, time, gender)
        print(f"生成的链接: {bazi_url}", flush=True)
        
        # 异步处理八字请求
        print(f"🚀 启动异步处理...", flush=True)
        request_id = bazi_service.process_bazi_async(card_key, bazi_url)
        
        print(f"✅ 请求已提交到处理队列，请求ID: {request_id}", flush=True)
        logger.info(f"八字请求已提交，卡密: {card_key}, 请求ID: {request_id}")
        
        return jsonify({
            'success': True,
            'message': '请求已提交，正在处理中...',
            'cardKey': card_key,
            'requestId': request_id
        })
        
    except Exception as e:
        print(f"💥 处理请求时发生异常: {str(e)}", flush=True)
        logger.error(f"处理八字请求时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': '服务器内部错误'
        }), 500

@app.route('/webhook/check-status', methods=['GET'])
def check_status():
    """检查处理状态"""
    try:
        card_key = request.args.get('cardKey')
        request_id = request.args.get('requestId')
        is_admin = request.args.get('admin') == 'true'

        # 确定查询标识符（优先使用请求ID）
        identifier = request_id if request_id else card_key
        
        print(f"🔍 检查状态请求: 卡密={card_key}, 请求ID={request_id}, 管理员={is_admin}", flush=True)
        print(f"使用标识符: {identifier}", flush=True)
        print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", flush=True)
        
        # 调试信息：打印所有可用的卡密
        print(f"系统中所有的卡密: {list(card_manager.cards_data.keys())}", flush=True)
        
        if not identifier:
            print(f"❌ 缺少卡密或请求ID参数", flush=True)
            return jsonify({
                'success': False,
                'message': '缺少卡密或请求ID参数'
            }), 400
        
        # 管理员访问跳过卡密验证
        if is_admin:
            print("🔓 管理员访问，跳过卡密验证", flush=True)
        # 如果提供了卡密，检查卡密是否存在
        elif card_key:
            print(f"卡密检查: {card_key}", flush=True)
            
            # 重新加载卡密数据以防数据不同步
            card_manager.cards_data = card_manager._load_cards()
            
            # 首先检查是否是直接的卡密
            original_card_key = card_key
            if card_key in card_manager.cards_data:
                print(f"✅ 直接找到卡密: {card_key}", flush=True)
            else:
                # 如果直接查找失败，检查是否是requestId格式，尝试提取原始卡密
                if '_' in card_key and len(card_key.split('_')) >= 3:
                    # requestId格式: {card_key}_{timestamp}_{uuid}
                    parts = card_key.split('_')
                    if len(parts) >= 3:
                        # 重新组合原始卡密（可能包含连字符）
                        potential_card_key = '_'.join(parts[:-2])  # 去掉最后两部分（时间戳和UUID）
                        print(f"尝试从requestId提取原始卡密: {potential_card_key}", flush=True)
                        if potential_card_key in card_manager.cards_data:
                            original_card_key = potential_card_key
                            print(f"✅ 从requestId成功提取到卡密: {original_card_key}", flush=True)
            
            print(f"检查最终卡密是否在库中: {original_card_key in card_manager.cards_data}", flush=True)
            
            if original_card_key not in card_manager.cards_data:
                print(f"❌ 卡密不存在: {original_card_key}", flush=True)
                print(f"当前可用卡密: {list(card_manager.cards_data.keys())}", flush=True)
                
                # 检查是否是请求已完成的情况
                bazi_status = bazi_service.get_processing_status(identifier)
                if bazi_status and bazi_status.get('completed'):
                    print(f"请求已完成，返回完成状态而非401错误", flush=True)
                    result = bazi_service.get_result(identifier)
                    if result:
                        return jsonify({
                            'completed': True,
                            'success': True,
                            'status': 'completed',
                            'result': result
                        }), 200
                
                # 返回401错误
                return jsonify({
                    'completed': False,
                    'success': False,
                    'error': '卡密不存在或无效'
                }), 401
            else:
                # 卡密存在，打印详细信息
                card_info = card_manager.cards_data[original_card_key]
                print(f"卡密信息: {card_info}", flush=True)
                print(f"卡密有效性: {card_info.get('valid')}", flush=True)
                print(f"过期时间: {card_info.get('expire_time')}", flush=True)
                print(f"使用次数: {card_info.get('usage_count')}/{card_info.get('max_usage')}", flush=True)
                # 更新card_key为原始卡密，用于后续状态查询
                card_key = original_card_key
        
        # 检查处理状态
        print(f"📊 查询卡密管理器状态...", flush=True)
        card_status = card_manager.get_status(card_key) if card_key else None
        print(f"卡密状态: {card_status}", flush=True)
        
        print(f"📊 查询八字服务状态...", flush=True)
        bazi_status = bazi_service.get_processing_status(identifier)
        print(f"八字服务状态: {bazi_status}", flush=True)
        
        # 优先检查八字服务的状态和结果
        print(f"📋 检查八字服务完成状态: {bazi_status.get('completed', False)}", flush=True)
        if bazi_status.get('completed', False):
            # 直接使用bazi_status中的结果
            result = bazi_status.get('result')
            print(f"八字服务状态中的结果存在: {result is not None}", flush=True)
            
            if result:
                print(f"✅ 处理已完成，返回结果", flush=True)
                return jsonify({
                    'completed': True,
                    'status': 'completed',
                    'result': result
                })
            else:
                # 如果状态中没有结果，再尝试获取
                print(f"📋 状态中无结果，尝试重新获取...", flush=True)
                result = bazi_service.get_result(identifier)
                print(f"重新获取的结果存在: {result is not None}", flush=True)
                
                if result:
                    print(f"✅ 重新获取成功，返回结果", flush=True)
                    return jsonify({
                        'completed': True,
                        'status': 'completed',
                        'result': result
                    })
                else:
                    print(f"⚠️ 八字服务显示完成但无结果数据", flush=True)
        
        # 检查卡密管理器状态（仅当提供了卡密时）
        if card_status and card_status.get('completed', False):
            print(f"📋 卡密状态显示完成，再次尝试获取结果...", flush=True)
            result = bazi_service.get_result(identifier)
            if result:
                print(f"✅ 从卡密状态获取到结果", flush=True)
                return jsonify({
                    'completed': True,
                    'status': 'completed', 
                    'result': result
                })
        
        # 如果都没有完成，返回处理中状态
        print(f"⚠️ 处理尚未完成或无结果数据", flush=True)
        # 仍在处理中
        current_status = bazi_status.get('status', card_status.get('status', 'unknown') if card_status else 'unknown')
        print(f"⏳ 仍在处理中，状态: {current_status}", flush=True)
        
        # 构建详细的状态响应
        response_data = {
            'completed': False,
            'status': current_status,
            'progress': bazi_status.get('progress', 0)  # 添加进度百分比
        }
        
        # 如果有消息，添加到响应中
        if 'message' in bazi_status:
            response_data['message'] = bazi_status['message']
            print(f"📝 状态消息: {bazi_status['message']}", flush=True)
        
        # 如果有LLM进度信息，包含在响应中
        if 'llm_progress' in bazi_status:
            response_data['llm_progress'] = bazi_status['llm_progress']
            print(f"📊 LLM进度信息: {bazi_status['llm_progress']}", flush=True)
            
            # 从LLM进度计算总体进度
            if 'dimensions' in bazi_status['llm_progress'] and 'completed_dimensions' in bazi_status['llm_progress']:
                dimensions = bazi_status['llm_progress']['dimensions']
                completed = bazi_status['llm_progress']['completed_dimensions']
                
                if dimensions and len(dimensions) > 0:
                    progress = int((len(completed) / len(dimensions)) * 100)
                    response_data['progress'] = progress
                    print(f"📊 计算进度: {progress}%", flush=True)
        
        # 添加处理时间信息
        if 'start_time' in bazi_status:
            start_time = bazi_status['start_time']
            if isinstance(start_time, datetime):
                elapsed_seconds = (datetime.now() - start_time).total_seconds()
                response_data['elapsed_time'] = int(elapsed_seconds)
                response_data['elapsed_time_formatted'] = f"{int(elapsed_seconds // 60)}分{int(elapsed_seconds % 60)}秒"
                print(f"⏱️ 已用时间: {response_data['elapsed_time_formatted']}", flush=True)
        
        return jsonify(response_data)
            
    except Exception as e:
        error_msg = f"检查状态时出错: {str(e)}"
        print(f"💥 {error_msg}", flush=True)
        print(f"异常类型: {type(e).__name__}", flush=True)
        print(f"异常详情: {str(e)}", flush=True)
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}", flush=True)
        logger.error(error_msg)
        
        return jsonify({
            'completed': False,
            'error': '状态检查失败',
            'details': str(e)
        })

@app.route('/webhook/bazi-analysis', methods=['POST'])
def webhook_bazi_analysis():
    """处理八字分析请求"""
    try:
        print("\n=== 处理八字分析请求 ===", flush=True)
        
        data = request.get_json()
        print(f"接收到的原始数据: {data}", flush=True)
        
        if not data:
            print("❌ 请求数据为空", flush=True)
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        # 验证必要字段
        required_fields = ['cardKey', 'year', 'month', 'day', 'gender']
        for field in required_fields:
            if field not in data:
                print(f"❌ 缺少必要字段: {field}", flush=True)
                return jsonify({
                    'success': False,
                    'error': f'缺少必要字段: {field}'
                }), 400
        
        print(f"✅ 所有必要字段都存在", flush=True)
        
        card_key = data['cardKey'].strip()
        if not card_key:
            print("❌ 卡密不能为空", flush=True)
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        print(f"📋 卡密: {card_key}", flush=True)
        
        # 验证卡密
        print(f"🔍 验证卡密...", flush=True)
        is_valid = card_manager.validate_card(card_key)
        print(f"卡密验证结果: {is_valid}", flush=True)
        
        if not is_valid:
            print("❌ 卡密无效或已过期", flush=True)
            return jsonify({
                'success': False,
                'error': '卡密无效或已过期'
            }), 401
        
        # 生成请求ID
        import uuid
        request_id = str(uuid.uuid4())
        print(f"📋 生成请求ID: {request_id}", flush=True)
        
        # 启动八字分析处理
        try:
            logger.info(f"收到八字分析请求，卡密: {card_key}, 请求ID: {request_id}")
            logger.info(f"分析数据: {data}")
            
            # 生成八字API URL
            print(f"🔧 提取和处理分析参数...", flush=True)
            year = data['year']
            month = data['month']
            day = data['day']
            
            # 获取并处理时辰输入
            hour_input = data.get('hour', '12')  # 获取时辰输入（可能是时辰名称）
            print(f"原始时辰输入: {hour_input}, 类型: {type(hour_input)}", flush=True)
            
            # 处理性别参数
            gender_input = data.get('gender', 'male')
            gender = '男' if gender_input == '1' or gender_input.lower() == 'male' else '女'
            print(f"处理后的性别: {gender}", flush=True)
            
            # 导入URL生成器进行时辰转换
            from url_generator import URLGenerator
            url_generator = URLGenerator()
            
            # 将时辰名称转换为具体时间
            print(f"🕙 转换时辰: {hour_input}", flush=True)
            try:
                # 确保hour_input是字符串
                str_hour_input = str(hour_input)
                print(f"转换为字符串后的时辰输入: {str_hour_input}", flush=True)
                
                converted_time = url_generator.convert_time_to_hour(str_hour_input)
                print(f"转换后的时间: {converted_time}", flush=True)
                
                # 确保converted_time是字符串
                if not isinstance(converted_time, str):
                    print(f"转换时间不是字符串，强制转换为字符串: {converted_time}", flush=True)
                    converted_time = str(converted_time)
                
                if ':' not in converted_time:
                    print(f"转换后的时间格式错误，添加默认分钟: {converted_time}", flush=True)
                    converted_time = f"{converted_time}:00"
                
                hour, minute = converted_time.split(':')
                print(f"分割后的小时和分钟: hour={hour}, minute={minute}", flush=True)
            except Exception as time_error:
                print(f"❌ 时辰转换出错: {str(time_error)}", flush=True)
                logger.error(f"时辰转换失败: {str(time_error)}")
                return jsonify({
                    'success': False,
                    'error': f'时辰处理错误: {str(time_error)}'
                }), 500
            
            logger.info(f"时辰转换: {hour_input} -> {converted_time} (hour={hour}, minute={minute})")

            # 生成标准八字API链接
            print(f"🔗 生成标准八字API链接...", flush=True)
            try:
                bazi_url = bazi_service.generate_standard_bazi_url(
                    year, month, day, hour, minute, gender
                )
                print(f"生成的链接: {bazi_url}", flush=True)
            except Exception as url_error:
                print(f"❌ 生成API链接出错: {str(url_error)}", flush=True)
                logger.error(f"生成八字API链接失败: {str(url_error)}")
                return jsonify({
                    'success': False,
                    'error': f'生成链接错误: {str(url_error)}'
                }), 500
            
            # 启动异步处理
            print(f"🚀 启动异步处理...", flush=True)
            try:
                actual_request_id = bazi_service.process_bazi_async(card_key, bazi_url)
                print(f"✅ 异步处理已启动，实际请求ID: {actual_request_id}", flush=True)
            except Exception as async_error:
                print(f"❌ 启动异步处理出错: {str(async_error)}", flush=True)
                logger.error(f"启动八字异步处理失败: {str(async_error)}")
                return jsonify({
                    'success': False,
                    'error': f'启动处理错误: {str(async_error)}'
                }), 500
            
            logger.info(f"八字分析已启动，实际请求ID: {actual_request_id}")
            
            return jsonify({
                'success': True,
                'message': '八字分析请求已接收，正在处理中',
                'requestId': actual_request_id,
                'data': {
                    'cardKey': card_key,
                    'status': 'processing'
                }
            })
            
        except Exception as process_error:
            print(f"❌ 处理八字分析请求时出错: {str(process_error)}", flush=True)
            import traceback
            print(f"错误堆栈: {traceback.format_exc()}", flush=True)
            logger.error(f"处理八字分析请求时出错: {str(process_error)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            return jsonify({
                'success': False,
                'error': '处理请求时出错',
                'details': str(process_error)
            }), 500
            
    except Exception as e:
        print(f"❌ 八字分析请求处理失败: {str(e)}", flush=True)
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}", flush=True)
        logger.error(f"八字分析请求处理失败: {str(e)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误',
            'details': str(e)
        }), 500

@app.route('/api/validate-card', methods=['POST'])
def validate_card():
    """验证卡密接口"""
    global card_manager
    print("=== 开始处理卡密验证请求 ===")
    try:
        data = request.get_json()
        print(f"接收到的原始数据: {data}")
        
        if not data or 'cardKey' not in data:
            print("请求数据验证失败: 缺少卡密参数")
            return jsonify({
                'success': False,
                'error': '缺少卡密参数'
            }), 400
        
        card_key = data['cardKey'].strip()
        print(f"处理后的卡密: '{card_key}'")
        
        if not card_key:
            print("卡密验证失败: 卡密为空")
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        # 重新加载卡密数据以确保最新状态
        card_manager._load_cards()
        
        # 检查卡密是否存在
        exists = card_key in card_manager.cards_data
        print(f"卡密是否存在于数据中: {exists}")
        print(f"CardManager实例ID: {id(card_manager)}")
        print(f"CardManager中总卡密数: {len(card_manager.cards_data)}")
        
        if exists:
            card_info = card_manager.cards_data[card_key]
            print(f"卡密详细信息: {card_info}")
        
        # 验证卡密
        print("开始调用card_manager.validate_card()...")
        is_valid = card_manager.validate_card(card_key)
        print(f"card_manager.validate_card()返回结果: {is_valid}")
        
        if is_valid:
            # 获取卡密信息
            card_info = card_manager.get_card_info(card_key)
            print(f"获取到的卡密信息: {card_info}")
            
            result = {
                'success': True,
                'message': '卡密验证成功',
                'data': {
                    'valid': True,
                    'remaining_usage': card_info.get('max_usage', 0) - card_info.get('usage_count', 0),
                    'expire_time': card_info.get('expire_time')
                }
            }
            print(f"返回成功结果: {result}")
            return jsonify(result)
        else:
            print(f"卡密验证失败: {card_key}")
            result = {
                'success': False,
                'error': '卡密无效或已过期'
            }
            print(f"返回失败结果: {result}")
            return jsonify(result), 401
            
    except Exception as e:
        print(f"验证卡密时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

@app.route('/api/cards', methods=['GET'])
def get_cards():
    """获取所有卡密信息（管理接口）"""
    try:
        cards = card_manager.get_all_cards()
        return jsonify({
            'success': True,
            'data': cards
        })
    except Exception as e:
        logger.error(f"获取卡密信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/cards/json', methods=['GET'])
def get_cards_json():
    """获取原始卡密JSON数据"""
    try:
        # 记录访问信息
        logger.info(f"访问卡密JSON数据 API，远程地址: {request.remote_addr}")
        
        # 重新加载卡密数据，确保获取最新数据
        from card_manager_singleton import reload_card_manager
        card_manager = reload_card_manager()
        
        # 确保配置与卡密文件同步
        from config_manager import get_config
        config = get_config()
        
        # 从卡密文件中获取最新卡密列表
        cards_in_file = list(card_manager.cards_data.keys())
        
        # 获取配置中的卡密列表
        cards_in_config = config.get_valid_cards()
        
        # 如果两者不一致，同步配置
        if set(cards_in_file) != set(cards_in_config):
            logger.info(f"同步卡密数据: 卡密文件中有 {len(cards_in_file)} 个卡密，配置中有 {len(cards_in_config)} 个卡密")
            config.set('card_validation.valid_cards', cards_in_file)
            config.save()
            logger.info(f"卡密数据已同步: {cards_in_file}")
        
        # 直接返回卡密管理器中的原始数据
        cards_data = card_manager.cards_data
        logger.info(f"返回卡密数据，包含 {len(cards_data)} 个卡密: {list(cards_data.keys())}")
        
        return jsonify(cards_data)
    except Exception as e:
        logger.error(f"获取卡密JSON数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/cards/remove', methods=['POST'])
def remove_card():
    """删除卡密"""
    try:
        data = request.get_json()
        if not data or 'card' not in data:
            return jsonify({
                'success': False,
                'error': '缺少卡密参数'
            }), 400
        
        card_key = data['card'].strip()
        if not card_key:
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        # 删除卡密
        removed = card_manager.remove_card(card_key)
        
        # 同时从配置中移除
        from config_manager import get_config
        config = get_config()
        config.remove_valid_card(card_key)
        
        if removed:
            logger.info(f"卡密已成功删除: {card_key}")
            return jsonify({
                'success': True,
                'message': f'卡密 {card_key} 已删除'
            })
        else:
            logger.warning(f"卡密不存在，无法删除: {card_key}")
            return jsonify({
                'success': False,
                'error': '卡密不存在'
            }), 404
    except Exception as e:
        logger.error(f"删除卡密失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/cards/batch-remove', methods=['POST'])
def batch_remove_cards():
    """批量删除卡密"""
    try:
        data = request.get_json()
        if not data or 'cards' not in data or not isinstance(data['cards'], list):
            logger.error(f"批量删除卡密请求数据格式错误: {data}")
            return jsonify({
                'success': False,
                'error': '缺少卡密列表参数'
            }), 400
        
        card_keys = [card.strip() for card in data['cards'] if card and isinstance(card, str) and card.strip()]
        if not card_keys:
            logger.error("批量删除卡密列表为空")
            return jsonify({
                'success': False,
                'error': '卡密列表为空'
            }), 400
        
        logger.info(f"准备批量删除 {len(card_keys)} 个卡密: {card_keys}")
        
        # 获取配置
        from config_manager import get_config
        config = get_config()
        
        # 批量删除卡密
        success_count = 0
        failed_count = 0
        for card_key in card_keys:
            try:
                # 删除卡密
                removed = card_manager.remove_card(card_key)
                # 同时从配置中移除
                config.remove_valid_card(card_key)
                
                if removed:
                    success_count += 1
                    logger.info(f"卡密已成功删除: {card_key}")
                else:
                    failed_count += 1
                    logger.warning(f"卡密不存在，无法删除: {card_key}")
            except Exception as card_error:
                failed_count += 1
                logger.error(f"删除卡密 {card_key} 时出错: {str(card_error)}")
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {success_count} 个卡密，失败 {failed_count} 个',
            'success_count': success_count,
            'failed_count': failed_count
        })
    except Exception as e:
        logger.error(f"批量删除卡密失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/get_result/<identifier>', methods=['GET'])
def get_result_api(identifier):
    """获取分析结果API - 支持渐进式加载"""
    print(f"🔍 [DEBUG] API调用: get_result_api, identifier={identifier}")
    try:
        is_admin = request.args.get('admin') == 'true'
        logger.info(f"API请求获取结果，标识符: {identifier}, 管理员: {is_admin}")
        print(f"🔍 [DEBUG] 管理员模式: {is_admin}")

        # 首先检查是否有正在处理的最新请求（无论是否找到旧结果）
        logger.info(f"检查 {identifier} 的最新请求状态")
        latest_processing_request = bazi_service.request_manager.get_latest_request_by_card(identifier)

        if latest_processing_request:
            processing_request_id, processing_info = latest_processing_request
            logger.info(f"找到正在处理的请求: {processing_request_id}")

            # 检查这个请求是否已经完成
            if processing_request_id not in bazi_service.results:
                # 这是一个正在处理中的请求，返回处理中状态
                logger.info(f"请求 {processing_request_id} 正在处理中")
                return jsonify({
                    'completed': False,
                    'has_llm_analysis': False,
                    'processing': True,
                    'request_id': processing_request_id,
                    'status': 'processing'
                })

        # 获取分析结果
        result = bazi_service.get_result(identifier)
        logger.info(f"bazi_service.get_result({identifier}) 返回: {type(result)}")

        # 如果直接找不到结果，尝试将identifier作为卡密查找最新的请求
        if not result:
            logger.info(f"直接查找失败，尝试将 {identifier} 作为卡密查找最新请求")
            logger.info(f"当前 bazi_service.results 中有 {len(bazi_service.results)} 个结果")

            # 首先检查是否有正在处理的请求
            print(f"🔍 [DEBUG] 查找正在处理的请求，卡密: {identifier}")
            latest_processing_request = bazi_service.request_manager.get_latest_request_by_card(identifier)
            print(f"🔍 [DEBUG] get_latest_request_by_card 返回: {latest_processing_request is not None}")
            if latest_processing_request:
                processing_request_id, processing_info = latest_processing_request
                logger.info(f"找到正在处理的请求: {processing_request_id}")

                # 检查这个请求是否已经完成
                if processing_request_id not in bazi_service.results:
                    # 这是一个正在处理中的请求，检查是否有基础八字数据
                    logger.info(f"请求 {processing_request_id} 正在处理中，检查基础数据")

                    # 尝试获取基础八字数据（可能已经完成八字计算，但LLM分析还在进行）
                    print(f"🔍 [DEBUG] 尝试获取基础数据，请求ID: {processing_request_id}")
                    basic_result = bazi_service.get_basic_result(processing_request_id)
                    print(f"🔍 [DEBUG] get_basic_result 返回: {basic_result is not None}")
                    if basic_result:
                        logger.info(f"找到基础八字数据，返回带基础数据的处理中状态")
                        return jsonify({
                            'completed': False,
                            'has_llm_analysis': False,
                            'processing': True,
                            'request_id': processing_request_id,
                            'status': 'processing',
                            'result': basic_result  # 包含基础八字数据
                        })
                    else:
                        logger.info(f"未找到基础八字数据，返回纯处理中状态")
                        return jsonify({
                            'completed': False,
                            'has_llm_analysis': False,
                            'processing': True,
                            'request_id': processing_request_id,
                            'status': 'processing'
                        })

            # 查找所有以该卡密开头的已完成请求ID
            matching_requests = []
            for request_id in bazi_service.results.keys():
                if request_id.startswith(identifier + '_'):
                    request_result = bazi_service.results[request_id]
                    if request_result:
                        matching_requests.append((request_id, request_result))

            logger.info(f"找到 {len(matching_requests)} 个已完成的匹配请求")

            if matching_requests:
                # 定义时间戳提取函数
                def extract_timestamp(request_id):
                    """从请求ID中提取时间戳"""
                    try:
                        parts = request_id.split('_')
                        if len(parts) >= 2:
                            return int(parts[1])  # 时间戳部分
                        return 0
                    except:
                        return 0

                # 按时间戳排序，获取最新的
                matching_requests.sort(key=lambda x: extract_timestamp(x[0]), reverse=True)
                latest_request_id, result = matching_requests[0]
                logger.info(f"找到最新的已完成请求: {latest_request_id}")
            else:
                logger.info(f"未找到以 {identifier} 开头的请求")
        else:
            logger.info(f"直接查找成功，结果类型: {type(result)}")

            # 即使直接找到了结果，也要检查是否有更新的请求
            logger.info(f"检查是否有更新的 {identifier} 请求")
            matching_requests = []
            for request_id in bazi_service.results.keys():
                if request_id.startswith(identifier + '_'):
                    request_result = bazi_service.results[request_id]
                    if request_result:
                        matching_requests.append((request_id, request_result))

            if matching_requests:
                # 定义时间解析函数
                def parse_time(time_value):
                    """解析时间值，支持字符串和datetime对象"""
                    if isinstance(time_value, str):
                        try:
                            return datetime.fromisoformat(time_value)
                        except:
                            return datetime.min
                    elif isinstance(time_value, datetime):
                        return time_value
                    else:
                        return datetime.min

                # 按请求ID中的时间戳排序（更可靠），获取最新的
                def extract_timestamp(request_id):
                    """从请求ID中提取时间戳"""
                    try:
                        parts = request_id.split('_')
                        if len(parts) >= 2:
                            return int(parts[1])  # 时间戳部分
                        return 0
                    except:
                        return 0

                matching_requests.sort(key=lambda x: extract_timestamp(x[0]), reverse=True)
                latest_request_id, latest_result = matching_requests[0]

                # 比较请求ID时间戳
                current_request_id = result.get('request_id', '')
                current_timestamp = extract_timestamp(current_request_id)
                latest_timestamp = extract_timestamp(latest_request_id)

                logger.info(f"时间戳比较 - 当前请求ID: {current_request_id} (时间戳: {current_timestamp})")
                logger.info(f"最新请求ID: {latest_request_id} (时间戳: {latest_timestamp})")

                if latest_timestamp > current_timestamp:
                    logger.info(f"找到更新的请求: {latest_request_id}, 使用更新的结果")
                    result = latest_result
                else:
                    logger.info(f"当前结果已是最新的")

        if result:
            logger.info(f"找到结果，类型: {type(result)}")

            # 检查结果是否完整
            has_llm_analysis = False
            if isinstance(result, dict):
                llm_analysis = result.get('llm_analysis')
                if llm_analysis and isinstance(llm_analysis, dict):
                    # 检查两种可能的字段名：analysis_results 或 results
                    analysis_results = llm_analysis.get('analysis_results') or llm_analysis.get('results', {})
                    has_llm_analysis = bool(analysis_results and len(analysis_results) > 0)

            # 获取处理状态
            status = bazi_service.get_processing_status(identifier)
            completed = status.get('completed', False) if status else False

            response_data = {
                'success': True,
                'completed': completed,
                'status': 'completed' if completed else 'processing',
                'result': result,
                'has_llm_analysis': has_llm_analysis
            }

            logger.info(f"返回结果，completed: {completed}, has_llm_analysis: {has_llm_analysis}")
            return jsonify(response_data)
        else:
            logger.warning(f"未找到结果，标识符: {identifier}")
            return jsonify({
                'success': False,
                'error': '未找到分析结果'
            }), 404

    except Exception as e:
        logger.error(f"获取结果API失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reports/list', methods=['GET'])
def get_reports_list():
    """获取历史报告列表 - 需要卡密验证"""
    try:
        # 获取卡密参数
        card_key = request.args.get('cardKey')
        if not card_key:
            return jsonify({
                'success': False,
                'error': '需要提供卡密'
            }), 401

        # 验证卡密
        if not card_manager.validate_card(card_key):
            return jsonify({
                'success': False,
                'error': '卡密无效或已过期'
            }), 401

        # 只返回该卡密的报告
        reports = bazi_service.get_reports_by_card_key(card_key)
        return jsonify({
            'success': True,
            'data': reports
        })
    except Exception as e:
        logger.error(f"获取报告列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reports/card/<card_key>', methods=['GET'])
def get_reports_by_card(card_key):
    """获取特定卡密的历史报告列表"""
    try:
        reports = bazi_service.get_reports_by_card_key(card_key)
        return jsonify({
            'success': True,
            'data': reports
        })
    except Exception as e:
        logger.error(f"获取卡密报告列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reports/delete/<report_id>', methods=['DELETE'])
def delete_report(report_id):
    """删除特定的分析报告 - 需要卡密验证"""
    try:
        # 获取卡密参数
        card_key = request.args.get('cardKey') or (request.json.get('cardKey') if request.json else None)
        if not card_key:
            return jsonify({
                'success': False,
                'error': '需要提供卡密'
            }), 401
        
        # 验证卡密
        if not card_manager.validate_card(card_key):
            return jsonify({
                'success': False,
                'error': '卡密无效或已过期'
            }), 401
        
        # 验证报告是否属于该卡密
        if not report_id.startswith(card_key + '_'):
            return jsonify({
                'success': False,
                'error': '无权限删除该报告'
            }), 403
        
        # 调用删除报告的方法
        success = bazi_service.delete_report(report_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': '报告删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '报告不存在或删除失败'
            }), 404
    except Exception as e:
        logger.error(f"删除报告失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/analysis/status/<analysis_id>', methods=['GET'])
def get_analysis_status(analysis_id):
    """获取分析状态"""
    try:
        # 从analysis_id中提取卡密
        # analysis_id格式通常是: cardkey_timestamp_randomid
        parts = analysis_id.split('_')
        if len(parts) >= 1:
            card_key = parts[0]
        else:
            return jsonify({
                'success': False,
                'error': '无效的分析ID格式'
            }), 400
        
        # 检查分析结果是否存在
        result = bazi_service.get_result(card_key)
        logger.info(f"获取到的result类型: {type(result)}, 内容: {result}")
        
        if result:
            # 分析已完成
            try:
                summary_data = result.get('summary')
                logger.info(f"summary_data类型: {type(summary_data)}, 内容: {summary_data}")
                
                llm_analysis_data = result.get('llm_analysis', {})
                logger.info(f"llm_analysis_data类型: {type(llm_analysis_data)}, 内容: {llm_analysis_data}")
                
                # 安全地检查summary
                has_summary = False
                if summary_data is not None:
                    if isinstance(summary_data, str):
                        has_summary = summary_data.strip() != ''
                    elif isinstance(summary_data, dict):
                        has_summary = len(summary_data) > 0
                    else:
                        has_summary = bool(summary_data)
                
                # 安全地检查llm_analysis
                has_llm_analysis = False
                if isinstance(llm_analysis_data, dict):
                    has_llm_analysis = llm_analysis_data.get('success', False)
                
                response_data = {
                    'success': True,
                    'status': 'completed',
                    'message': '分析已完成',
                    'data': {
                        'card_key': card_key,
                        'completed_time': result.get('processed_time'),
                        'has_summary': has_summary,
                        'has_llm_analysis': has_llm_analysis
                    }
                }
                logger.info(f"准备返回的response_data: {response_data}")
                return jsonify(response_data)
                
            except Exception as inner_e:
                logger.error(f"处理分析结果时出错: {str(inner_e)}, 结果类型: {type(result)}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                raise inner_e
        else:
            # 分析还在进行中
            return jsonify({
                'success': True,
                'status': 'processing',
                'message': '分析正在进行中',
                'data': {
                    'card_key': card_key
                }
            })
            
    except Exception as e:
        logger.error(f"获取分析状态失败: {str(e)}")
        import traceback
        logger.error(f"完整错误堆栈: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/analysis/llm-progress/<analysis_id>', methods=['GET'])
def get_llm_progress(analysis_id):
    """获取LLM分析进度详情"""
    try:
        # 查询处理状态
        bazi_status = bazi_service.get_processing_status(analysis_id)
        
        # 构建详细的状态响应
        response_data = {
            'completed': bazi_status.get('completed', False),
            'status': bazi_status.get('status', 'unknown'),
            'progress': 0
        }
        
        # 添加时间信息
        if 'start_time' in bazi_status:
            start_time = bazi_status['start_time']
            if isinstance(start_time, datetime):
                elapsed_seconds = (datetime.now() - start_time).total_seconds()
                response_data['elapsed_time'] = int(elapsed_seconds)
                response_data['elapsed_time_formatted'] = f"{int(elapsed_seconds // 60)}分{int(elapsed_seconds % 60)}秒"
        
        # 如果有LLM进度信息，特别详细地包含
        if 'llm_progress' in bazi_status:
            llm_progress = bazi_status['llm_progress']
            response_data['llm_progress'] = llm_progress
            
            # 计算进度百分比
            if 'completed_dimensions' in llm_progress and 'dimensions' in llm_progress:
                completed = len(llm_progress['completed_dimensions'])
                total = len(llm_progress['dimensions'])
                if total > 0:
                    progress = int((completed / total) * 100)
                    response_data['progress'] = progress
            
            print(f"详细LLM进度信息: {llm_progress}")
        
        return jsonify(response_data)
            
    except Exception as e:
        logger.error(f"获取LLM进度时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """API健康检查"""
    try:
        # 获取系统环境信息
        env_info = {
            'is_server': is_server_environment(),
            'environment': is_server_environment() and '服务器环境' or '本地环境',
            'static_folder': STATIC_FOLDER,
            'project_dir': project_dir,
            'current_dir': os.path.abspath(os.curdir),
            'backend_dir': os.path.dirname(os.path.abspath(__file__)),
            'python_version': sys.version,
            'os_platform': sys.platform
        }
        
        # 检查关键文件是否存在
        file_checks = {}
        important_files = [
            ('up.html', os.path.join(project_dir, 'up.html')),
            ('config.html', os.path.join(project_dir, 'config.html')),
            ('card_generator.html', os.path.join(project_dir, 'card_generator.html')),
            ('index.html', os.path.join(project_dir, 'index.html')),
            ('styles.css', os.path.join(project_dir, 'styles.css')),
        ]
        
        for file_name, file_path in important_files:
            file_checks[file_name] = {
                'exists': os.path.exists(file_path),
                'path': file_path,
                'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                'relative_path': os.path.relpath(file_path, project_dir) if os.path.exists(file_path) else '未找到'
            }
        
        # 获取可用的静态HTML文件
        try:
            html_files = [f for f in os.listdir(project_dir) if f.endswith('.html')]
            logger.info(f"项目根目录中的HTML文件: {html_files}")
        except Exception as e:
            html_files = [f"错误: {str(e)}"]
        
        return jsonify({
            'success': True,
            'status': 'API服务正常运行',
            'time': datetime.now().isoformat(),
            'environment': env_info,
            'file_checks': file_checks,
            'available_html_files': html_files,
            'paths': {
                'project_dir': project_dir,
                'static_folder': STATIC_FOLDER,
                'current_dir': os.path.abspath(os.curdir),
                'backend_dir': os.path.dirname(os.path.abspath(__file__)),
            },
            'routes': {
                'cards': '/api/cards',
                'cards_json': '/api/cards/json',
                'remove_card': '/api/cards/remove',
                'batch_remove': '/api/cards/batch-remove',
                'reports': '/api/reports/list',
                'check_status': '/webhook/check-status'
            }
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        import traceback
        logger.error(f"健康检查错误详情: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

def _generate_summary_section(result):
    """生成摘要部分的HTML"""
    if not result.get('summary'):
        return ''
    
    summary_info = result.get('summary', {})
    summary_status = '已生成' if summary_info else '未生成'
    file_path = summary_info.get('file_path', '未知') if summary_info else ''
    
    html = f'<div class="section summary-section"><h3>📝 八字摘要</h3>'
    html += f'<div class="data-item">📄 摘要状态: {summary_status}</div>'
    
    # 读取并显示摘要内容
    if file_path and file_path != '未知' and os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                summary_content = f.read()
            
            html += f'<div class="data-item">📁 摘要文件: {os.path.basename(file_path)}</div>'
            html += f'<div class="data-item">'
            html += f'<h4>📋 摘要内容:</h4>'
            html += f'<div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; margin-top: 10px; white-space: pre-wrap; font-family: monospace; max-height: 400px; overflow-y: auto;">{summary_content}</div>'
            html += f'</div>'
        except Exception as e:
            html += f'<div class="data-item">❌ 读取摘要文件失败: {str(e)}</div>'
    elif file_path and file_path != '未知':
        html += f'<div class="data-item">❌ 摘要文件不存在: {file_path}</div>'
    
    html += '</div>'
    
    return html

def _generate_llm_analysis_section(result):
    """生成LLM分析结果部分的HTML"""
    if not result.get('llm_analysis'):
        return ''
    
    llm_info = result.get('llm_analysis', {})
    analysis_status = '已完成' if llm_info.get('success') else '失败'
    
    html = f'<div class="section llm-analysis-section"><h3>🤖 LLM深度分析</h3>'
    html += f'<div class="data-item">🔍 分析状态: {analysis_status}</div>'
    
    if llm_info.get('success'):
        # 显示分析维度
        dimensions = llm_info.get('dimensions', [])
        html += f'<div class="data-item">📊 分析维度: {", ".join(dimensions)}</div>'
        
        # 显示分析时间
        analysis_time = llm_info.get('analysis_time')
        if analysis_time:
            html += f'<div class="data-item">⏰ 分析时间: {analysis_time}</div>'
        
        # 显示分析结果文件
        file_path = llm_info.get('file_path')
        if file_path and os.path.exists(file_path):
            html += f'<div class="data-item">📁 结果文件: {os.path.basename(file_path)}</div>'
        
        # 显示分析结果内容
        analysis_results = llm_info.get('results', {})
        if analysis_results:
            html += f'<div class="data-item">'
            html += f'<h4>分析结果:</h4>'
            
            for dimension, content in analysis_results.items():
                html += f'<div style="margin: 15px 0; padding: 15px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #2196F3;">'
                html += f'<h5 style="color: #2196F3; margin: 0 0 10px 0;">📈 {dimension}维度分析</h5>'
                html += f'<div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; white-space: pre-wrap; font-family: \'Microsoft YaHei\', Arial, sans-serif; line-height: 1.6; max-height: 500px; overflow-y: auto;">{content}</div>'
                html += f'</div>'
            
            html += f'</div>'
        
        # 显示原始JSON数据（可折叠）
        if file_path and os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    raw_data = f.read()
                
                html += f'<div class="data-item">'
                html += f'<button class="toggle-button" onclick="toggleLLMData()">显示/隐藏原始分析数据</button>'
                html += f'<div id="llmData" class="json-container hidden">'
                html += f'<div class="json-data">{raw_data}</div>'
                html += f'</div>'
                html += f'</div>'
            except Exception as e:
                html += f'<div class="data-item">❌ 读取分析文件失败: {str(e)}</div>'
    else:
        # 显示错误信息
        error_msg = llm_info.get('error', '未知错误')
        html += f'<div class="data-item">❌ 分析失败: {error_msg}</div>'
    
    html += '</div>'
    
    return html

if __name__ == '__main__':
    # 使用配置文件中的服务器设置
    host = config.get_server_host()
    port = config.get_server_port()
    debug = config.is_debug_mode()
    
    logger.info(f"启动服务器: {host}:{port}, Debug模式: {debug}")
    app.run(host=host, port=port, debug=debug)