/* 响应式设计样�?*/

/* 超大屏幕 (1400px+) */
@media (min-width: 1400px) {
    .detailed-container {
        max-width: 1400px;
        padding: 32px;
    }
    
    .detailed-header h1 {
        font-size: 2.5rem;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 32px;
    }
}

/* 大屏�?(1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .detailed-container {
        padding: 28px;
    }
}

/* 平板横屏 (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .detailed-container {
        padding: 24px 20px;
    }
    
    .detailed-header {
        padding: 28px;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
    }
}

/* 平板竖屏 (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .detailed-container {
        padding: 20px;
    }
    
    .detailed-header {
        padding: 24px;
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .detailed-header h1 {
        font-size: 2rem;
    }
    
    .detailed-header-actions {
        justify-content: center;
        width: 100%;
        flex-wrap: wrap;
    }
    
    .detailed-action-btn {
        min-width: 140px;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }
}

/* 手机横屏 (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .detailed-container {
        padding: 16px;
        background: var(--bg-primary);
    }
    
    /* 确保所有容器背景色统一 */
    .detailed-result-container,
    .detailed-header,
    .detailed-dimension-section,
    .detailed-bazi-info-item {
        background: var(--bg-card) !important;
    }
    
    .detailed-header {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .detailed-header h1 {
        font-size: 1.8rem;
    }
    
    .detailed-header-actions {
        justify-content: center;
        width: 100%;
        gap: 12px;
    }
    
    .detailed-action-btn {
        flex: 1;
        min-width: 120px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    /* 确保在这个尺寸下按钮文本也能显示 */
    .detailed-action-btn .btn-text {
        display: inline-block;
    }
    
    .detailed-result-header {
        padding: 24px 16px;
    }
    
    .detailed-result-header h2 {
        font-size: 1.6rem;
    }
    
    .detailed-analysis-content {
        padding: 20px;
    }
    
    .detailed-dimension-header {
        padding: 16px 20px;
    }
    
    .detailed-dimension-title {
        font-size: 1.1rem;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 8px;
    }
    
    .detailed-bazi-info-item {
        padding: 18px;
    }
    
    .detailed-bazi-info-item strong {
        min-width: 100px;
        font-size: 0.95rem;
    }
}

/* 手机竖屏 (最大 575px) */
@media (max-width: 575px) {
    .detailed-container {
        padding: 12px;
        background: var(--bg-primary);
    }
    
    /* 确保所有容器背景色统一 */
    .detailed-result-container,
    .detailed-header,
    .detailed-dimension-section,
    .detailed-bazi-info-item {
        background: var(--bg-card) !important;
    }
    
    .detailed-header {
        padding: 16px;
        flex-direction: column;
        text-align: center;
        gap: 12px;
        margin-bottom: 20px;
    }
    
    .detailed-header h1 {
        font-size: 1.6rem;
        line-height: 1.3;
    }
    
    .detailed-header-actions {
        flex-direction: column;
        width: 100%;
        gap: 8px;
    }
    
    .detailed-action-btn {
        width: 100%;
        justify-content: center;
        padding: 12px;
        font-size: 0.9rem;
    }
    
    /* 确保在小屏幕下按钮文本可见 */
    .detailed-action-btn .btn-text {
        display: inline-block !important;
        margin-left: 6px;
        font-size: 14px;
    }
    
    .detailed-result-header {
        padding: 20px 12px;
    }
    
    .detailed-result-header h2 {
        font-size: 1.4rem;
        line-height: 1.3;
    }
    
    .detailed-analysis-content {
        padding: 16px;
    }
    
    .detailed-dimension-header {
        padding: 14px 16px;
    }
    
    .detailed-dimension-title {
        font-size: 1rem;
        gap: 12px;
    }
    
    .detailed-dimension-icon {
        font-size: 1.2rem;
        width: 28px;
        height: 28px;
    }
    
    .detailed-dimension-content.active {
        padding: 16px;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 4px;
    }
    
    .detailed-bazi-info-item {
        padding: 16px;
    }
    
    .detailed-bazi-info-item strong {
        display: block;
        margin-bottom: 8px;
        margin-right: 0;
        min-width: auto;
        font-size: 0.9rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .detailed-action-btn {
        min-height: 48px;
        padding: 14px 24px;
        touch-action: manipulation;
    }
    
    .detailed-action-btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    .detailed-dimension-header {
        min-height: 60px;
        touch-action: manipulation;
    }
    
    .detailed-dimension-header:active {
        background-color: var(--hover-bg);
        transition: background-color 0.1s ease;
        padding: 18px 24px;
    }
    
    .detailed-bazi-info-item {
        padding: 20px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .detailed-result-page {
        --border-color: #000;
        --text-primary: #000;
        --bg-card: #fff;
    }
    
    .detailed-action-btn {
        border: 2px solid currentColor;
    }
    
    .detailed-dimension-section {
        border: 2px solid currentColor;
    }
}

/* 八字表格移动端优�?*/
@media (max-width: 768px) {
    /* 确保八字表格正确显示 */
    .detailed-bazi-table {
        width: 100%;
        table-layout: fixed;
        font-size: 12px;
        margin: 0 auto;
        border-collapse: separate;
        border-spacing: 2px;
    }
    
    .detailed-pillar-cell {
        padding: 5px 2px;
        vertical-align: top;
        text-align: center;
    }
    
    .detailed-shishen {
        font-size: 10px;
        margin-bottom: 2px;
    }
    
    .detailed-tiangan, .detailed-dizhi {
        font-size: 14px;
        line-height: 1.2;
    }
    
    .detailed-canggan-table {
        width: 100%;
        margin-top: 3px;
    }
    
    .detailed-canggan-cell {
        padding: 2px;
        font-size: 10px;
        line-height: 1.2;
    }
    
    /* 大运表格优化 */
    .detailed-dayun-table {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        white-space: nowrap;
        border-collapse: collapse;
        font-size: 12px;
    }
    
    .detailed-dayun-table th, 
    .detailed-dayun-table td {
        padding: 4px 8px;
        font-size: 12px;
        white-space: nowrap;
        border: 1px solid var(--border-color, #e5e7eb);
    }
    
    /* 只隐藏空列，保留序号�?*/
    .detailed-dayun-table th:nth-child(1),
    .detailed-dayun-table td:nth-child(1) {
        min-width: 30px;
        text-align: center;
        width: auto;
    }
    
    .detailed-dayun-table th:nth-child(2),
    .detailed-dayun-table td:nth-child(2) {
        min-width: 40px;
        text-align: center;
    }
    
    .detailed-dayun-table th:nth-child(3),
    .detailed-dayun-table td:nth-child(3) {
        min-width: 50px;
        text-align: center;
    }
    
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 120px;
    }
    
    .detailed-dayun-table th:nth-child(5),
    .detailed-dayun-table td:nth-child(5) {
        min-width: 80px;
        text-align: center;
    }
    
    .detailed-dayun-table th:nth-child(6),
    .detailed-dayun-table td:nth-child(6) {
        min-width: 120px;
    }
    
    /* 神煞列样�?*/
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 100px !important;
        text-align: left !important;
    }
}

/* 进一步优化小屏幕 */
@media (max-width: 480px) {
    .detailed-bazi-table {
        font-size: 11px;
    }
    
    .detailed-tiangan, .detailed-dizhi {
        font-size: 13px;
    }
    
    .detailed-shishen {
        font-size: 9px;
    }
    
    .detailed-canggan-cell {
        font-size: 9px;
    }
    
    /* 大运表格小屏优化 */
    .detailed-dayun-table th, 
    .detailed-dayun-table td {
        padding: 3px 6px;
        font-size: 11px;
    }
    
    .detailed-dayun-table th:nth-child(3),
    .detailed-dayun-table td:nth-child(3) {
        min-width: 30px;
    }
    
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 40px;
    }
    
    .detailed-dayun-table th:nth-child(5),
    .detailed-dayun-table td:nth-child(5) {
        min-width: 60px;
    }
    
    .detailed-dayun-table th:nth-child(6),
    .detailed-dayun-table td:nth-child(6) {
        min-width: 100px;
    }
}

/* 大运表格移动端完全重�?*/
@media (max-width: 768px) {
    /* 大运表格容器增强样式 */
    .detailed-dayun-section {
        overflow-x: hidden !important; /* 防止水平溢出 */
        width: 100% !important;
        max-width: 100% !important;
        position: relative !important;
        display: block !important;
    }
    
    .detailed-dayun-container {
        overflow-x: auto !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        -webkit-overflow-scrolling: touch !important;
    }
    
    /* 确保表格整体居中且不溢出 */
    .detailed-dayun-table {
        margin: 0 auto !important;
        width: 100% !important;
        max-width: 100% !important;
        border-radius: 8px !important;
        border-collapse: collapse !important;
        border-spacing: 0 !important;
        table-layout: fixed !important; /* 改为fixed确保列宽一�?*/
    }
    
    /* 清除所有可能导致错位的样式 */
    .detailed-dayun-table tr,
    .detailed-dayun-table tr:hover {
        transform: none !important;
        transition: none !important;
        box-shadow: none !important;
        border: none !important;
        position: static !important;
        left: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 去除所有伪元素 */
    .detailed-dayun-table tr::before,
    .detailed-dayun-table tr::after,
    .detailed-dayun-table tr:hover::before,
    .detailed-dayun-table tr:hover::after {
        display: none !important;
    }
    
    /* 确保所有单元格样式一�?*/
    .detailed-dayun-table th,
    .detailed-dayun-table td {
        padding: 6px 8px !important;
        text-align: center !important;
        vertical-align: middle !important;
        font-size: 12px !important;
        border: 1px solid rgba(99, 102, 241, 0.2) !important;
        white-space: nowrap !important;
        position: static !important;
        transform: none !important;
        transition: none !important;
    }
    
    /* 序号列样�?*/
    .detailed-dayun-table th:nth-child(1),
    .detailed-dayun-table td:nth-child(1) {
        min-width: 40px !important;
        width: 40px !important;
        max-width: 40px !important;
        text-align: center !important;
        font-weight: 600 !important;
    }
    
    /* 天干地支列样�?*/
    .detailed-dayun-table th:nth-child(2),
    .detailed-dayun-table td:nth-child(2) {
        min-width: 60px !important;
        width: 60px !important;
        max-width: 60px !important;
        font-weight: 600 !important;
        text-align: center !important;
    }
    
    /* 年龄范围列样�?*/
    .detailed-dayun-table th:nth-child(3),
    .detailed-dayun-table td:nth-child(3) {
        min-width: 80px !important;
        width: 80px !important;
        max-width: 80px !important;
        text-align: center !important;
    }
    
    /* 神煞列样�?*/
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 120px !important;
        width: 120px !important;
        max-width: 120px !important;
        text-align: left !important;
    }
    
    /* 防止表头错位 */
    .detailed-dayun-table thead {
        display: table-header-group !important;
    }
    
    .detailed-dayun-table thead tr {
        display: table-row !important;
    }
    
    .detailed-dayun-table thead th {
        display: table-cell !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%) !important;
    }
    
    /* Explicitly remove border-left from tbody rows to prevent misalignment with header */
    .detailed-dayun-table tbody tr,
    .detailed-dayun-table tbody tr:nth-child(even),
    .detailed-dayun-table tbody tr:nth-child(odd) {
        border-left: none !important;
    }
    
    /* 移动端当前大运样�?- 确保高优先级 */
    body .detailed-dayun-table tr.current-dayun {
        background-color: rgba(99, 102, 241, 0.15) !important;
        transform: none !important;
        box-shadow: none !important;
        border: none !important;
        position: static !important;
        margin: 0 !important;
        padding: 0 !important;
        outline: 2px solid var(--primary-color, #6366f1) !important; /* 使用outline代替border */
        outline-offset: -2px !important;
        z-index: auto !important;
    }
    
    /* 强制禁用当前大运伪元�?- 移动�?*/
    body .detailed-dayun-table tr.current-dayun::before,
    body .detailed-dayun-table tr.current-dayun::after {
        display: none !important;
        content: none !important;
        width: 0 !important;
        height: 0 !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
    
    body .detailed-dayun-table tr.current-dayun td {
        color: var(--primary-color, #6366f1) !important;
        font-weight: 700 !important;
        position: static !important;
        transform: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
        border: 1px solid rgba(99, 102, 241, 0.3) !important;
        z-index: auto !important;
    }
    
    /* 防止悬停效果导致的位�?*/
    body .detailed-dayun-table tr:hover {
        background-color: rgba(99, 102, 241, 0.05) !important;
        transform: none !important;
        box-shadow: none !important;
    }
}

/* 八字表格移动端完全重�?*/
@media (max-width: 768px) {
    /* 八字表格容器 */
    .bazi-table-container {
        margin: 10px 0;
        padding: 0;
        overflow: visible;
        width: 100%;
    }
    
    /* 八字表格 - 完全重构布局 */
    .detailed-bazi-table {
        width: 100%;
        table-layout: fixed;
        border-spacing: 0;
        border-collapse: separate;
        margin: 0 auto;
    }
    
    /* 表头处理 */
    .detailed-bazi-table th {
        padding: 6px 2px;
        font-size: 12px;
        font-weight: 600;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        border-radius: 6px 6px 0 0;
    }
    
    /* 单元格处�?*/
    .detailed-pillar-cell {
        padding: 6px 0 !important;
        vertical-align: top;
        width: 25% !important;
        max-width: 25% !important;
        border: 1px solid rgba(99, 102, 241, 0.3);
        border-radius: 0 0 6px 6px;
    }
    
    /* 日柱高亮 */
    /* Remove day pillar special styles */
    
    /* 十神标签 */
    .detailed-shishen {
        font-size: 9px !important;
        padding: 1px 2px !important;
        margin: 0 auto 2px auto !important;
        display: inline-block !important;
        background: rgba(99, 102, 241, 0.8);
        color: white;
        border-radius: 3px;
        max-width: 90% !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
    }
    
    /* 天干处理 */
    .detailed-tiangan {
        font-size: 12px !important;
        line-height: 1.2 !important;
        font-weight: 700 !important;
        margin: 2px 0 !important;
        padding: 0 !important;
    }
    
    /* 地支处理 */
    .detailed-dizhi {
        font-size: 12px !important;
        line-height: 1.2 !important;
        font-weight: 700 !important;
        margin: 3px 0 2px 0 !important;
        padding: 2px 0 !important;
    }
    
    /* 藏干表格 - 完全重构 */
    .detailed-canggan-table {
        width: 94% !important;
        margin: 3px auto !important;
        border-spacing: 0 !important;
        border-collapse: collapse !important;
        background: rgba(246, 246, 255, 0.5) !important;
        border-radius: 4px !important;
        table-layout: fixed !important;
    }
    
    /* 藏干表格标题 */
    .detailed-canggan-table::before {
        content: "藏干";
        display: block;
        text-align: center;
        font-size: 8px !important;
        color: #6366f1;
        background: rgba(99, 102, 241, 0.1);
        border-radius: 3px;
        padding: 1px;
        margin-bottom: 2px;
        font-weight: 600;
    }
    
    /* 藏干单元�?*/
    .detailed-canggan-cell {
        padding: 0 !important;
        font-size: 9px !important;
        line-height: 1.1 !important;
        text-align: center !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
    
    /* 藏干字符和十神样�?*/
    .canggan-gan {
        font-weight: 700 !important;
        display: inline-block !important;
        margin-right: 1px !important;
    }
    
    .canggan-ss {
        font-size: 8px !important;
        opacity: 0.9 !important;
        display: inline-block !important;
    }
    
    /* 隐藏地支和藏干之间的分隔 */
    .detailed-bazi-cell {
        margin-bottom: 0 !important;
        padding: 2px 0 !important;
    }
}

/* 移动端适配 - 优化版本 */
@media (max-width: 768px) {
    .detailed-container {
        padding: 8px;
        max-width: 100%;
        background: transparent;
        box-shadow: none;
        border: none;
        overflow-x: hidden;
    }
    
    /* 防止内容超出边界的通用样式 */
    * {
        max-width: 100%;
        box-sizing: border-box;
    }
    
    /* 确保所有表格和容器都能响应式显示 */
    table, .detailed-bazi-table, .detailed-dayun-table, .detailed-shensha-table, .detailed-canggan-table {
        max-width: 100%;
        overflow-x: auto;
    }
    
    .detailed-header {
        padding: 12px 16px;
        margin-bottom: 8px;
        margin-top: 0;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .detailed-header h1 {
        font-size: 1.6rem;
    }
    
    .detailed-header-actions {
        gap: 8px;
        flex-wrap: wrap;
    }
    
    .detailed-action-btn {
        padding: 10px 12px;
        font-size: 14px;
        min-height: 44px;
        border-radius: 8px;
        flex: 1;
        min-width: 80px;
    }
    
    .btn-text {
        display: inline-block !important;
        margin-left: 6px;
        font-size: 14px;
    }
    
    .detailed-result-container {
        margin: 0;
        padding: 16px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        background: var(--bg-secondary);
    }
    
    .detailed-result-header {
        padding: 20px 0;
        text-align: center;
    }
    
    .detailed-result-header h2 {
        font-size: 22px;
        line-height: 1.3;
        margin-bottom: 12px;
    }
    
    .detailed-result-meta {
        font-size: 13px;
        flex-direction: column;
        gap: 6px;
        color: var(--text-secondary);
    }
    
    /* 基本信息移动端优化 */
    .basic-info {
        padding: 16px;
        margin-top: 16px;
        border-radius: 12px;
    }
    
    .basic-info-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 0;
        gap: 4px;
    }
    
    .info-label {
        min-width: auto;
        font-size: 13px;
        color: var(--text-muted);
    }
    
    .info-value {
        font-size: 15px;
        font-weight: 600;
    }
    
    /* 起运信息移动端优化 */
    .qiyun-info {
        margin: 16px 0;
        padding: 16px;
        border-radius: 12px;
        font-size: 14px;
        text-align: center;
    }
    
    .current-dayun-note {
        margin-top: 12px;
        padding: 8px;
        font-size: 12px;
        border-radius: 8px;
    }
    
    /* 维度内容移动端优化 */
    .detailed-dimension-section {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    
    .detailed-dimension-header {
        padding: 16px 18px;
        font-size: 15px;
        min-height: 52px;
        display: flex;
        align-items: center;
        font-weight: 600;
        background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .detailed-dimension-header:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6b4190 100%);
    }
    
    .detailed-dimension-content {
        padding: 18px;
        font-size: 15px;
        line-height: 1.7;
        background: var(--bg-card);
    }
    
    /* Markdown内容移动端优化 */
    .detailed-dimension-text h1,
    .detailed-dimension-text h2,
    .detailed-dimension-text h3 {
        font-size: 16px;
        margin: 12px 0 8px 0;
        color: var(--text-primary);
    }
    
    .detailed-dimension-text p {
        margin: 10px 0;
        font-size: 15px;
        line-height: 1.7;
    }
    
    .detailed-dimension-text ul,
    .detailed-dimension-text ol {
        padding-left: 20px;
        margin: 10px 0;
    }
    
    .detailed-dimension-text li {
        margin: 6px 0;
        line-height: 1.6;
        font-size: 15px;
    }
    
    .detailed-dimension-text blockquote {
        margin: 12px 0;
        padding: 12px 16px;
        font-size: 14px;
        border-left: 3px solid var(--primary-color);
        background: rgba(102, 126, 234, 0.05);
    }
    
    /* 状态页面移动端优化 */
    .loading-state, .error-state, .empty-state {
        padding: 30px 20px;
        margin: 16px;
        border-radius: 12px;
    }
    
    .loading-state h3, .error-state h3, .empty-state h3 {
        font-size: 20px;
        margin-bottom: 12px;
    }
    
    .error-actions, .empty-actions {
        flex-direction: column;
        gap: 12px;
        margin-top: 20px;
    }
    
    .error-actions button, .empty-actions button {
        width: 100%;
        min-height: 48px;
        font-size: 16px;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .loading-state,
    .error-state,
    .empty-state {
        min-height: 280px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    
    .error-icon,
    .empty-icon {
        font-size: 3.5rem;
        margin-bottom: 16px;
    }
    
    .error-title,
    .empty-title {
        font-size: 1.4rem;
        text-align: center;
        margin-bottom: 8px;
    }
    
    .error-message,
    .empty-message {
        font-size: 15px;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 20px;
        color: var(--text-secondary);
    }
    
    .retry-btn,
    .back-home-btn,
    .start-analysis-btn {
        width: 100%;
        max-width: 240px;
        padding: 12px 20px;
    }
    
    /* 加载进度条优化 */
    .loading-progress {
        width: 100%;
        max-width: 280px;
        margin-top: 20px;
    }
    
    .progress-bar {
        height: 4px;
        border-radius: 2px;
    }
    
    /* 错误详情优化 */
    .error-details {
        width: 100%;
        max-width: 320px;
        margin-top: 16px;
    }
    
    #errorDetails {
        font-size: 12px;
        max-height: 150px;
    }
}

/* 更小屏幕的移动设备优化 - 360px以下 */
@media (max-width: 360px) {
    /* 更窄屏幕上的大运表格优化 */
    .detailed-dayun-table {
        font-size: 10px !important;
    }
    
    .detailed-dayun-table th, 
    .detailed-dayun-table td {
        padding: 4px 5px !important;
        font-size: 10px !important;
    }
    
    .detailed-dayun-table td:nth-child(1) {
        min-width: 24px !important;
        width: 24px !important;
    }
    
    .detailed-dayun-table td:nth-child(2) {
        min-width: 40px !important;
    }
    
    .detailed-dayun-table td:nth-child(3) {
        min-width: 50px !important;
    }
    
    .detailed-dayun-table td:nth-child(4) {
        min-width: 80px !important;
    }
    
    /* 强制确保选中行正常显示 */
    body .detailed-dayun-table tr.current-dayun {
        transform: none !important;
        position: static !important;
        box-shadow: none !important;
        border: none !important;
        outline: 1px solid var(--primary-color, #6366f1) !important;
        background: rgba(99, 102, 241, 0.1) !important;
    }
    
    body .detailed-dayun-table tr.current-dayun td {
        padding: 3px 4px !important; /* 更小的内边距 */
        font-size: 10px !important; /* 更小的字体 */
    }
}
