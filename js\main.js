// 主入口文件

// 全局变量
let baziAnalyzer = null;
let pollingInterval = null;
let logger = null;

// 页面加载完成后的初始化
window.addEventListener('DOMContentLoaded', function() {
    // 确保日志工具已加载
    if (window.BaziLogger) {
        logger = window.BaziLogger.forModule('Main');
    } else {
        // 如果日志工具尚未加载，使用临时logger
        logger = {
            debug: console.log,
            info: console.log,
            warn: console.warn,
            error: console.error
        };
    }
    
    logger.info('页面加载完成');
    
    // 清空结果区域
    const resultDiv = document.getElementById('result');
    if (resultDiv) {
        resultDiv.innerHTML = '';
    }
    
    // 初始化分析器
    baziAnalyzer = new BaziAnalyzer();
    
    // 延迟显示出生信息部分，与BaziIntegration同步
    setTimeout(() => {
        const birthInfoSections = document.querySelectorAll('.birth-info-section');
        birthInfoSections.forEach(section => {
            section.classList.add('show');
        });
    }, 1100); // 比BaziIntegration稍晚100ms显示
    
    logger.info('八字分析器初始化完成');
});

// 页面卸载时清理轮询
window.addEventListener('beforeunload', function() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    
    if (baziAnalyzer && baziAnalyzer.pollingInterval) {
        clearInterval(baziAnalyzer.pollingInterval);
        baziAnalyzer.pollingInterval = null;
    }
});

// 导出全局变量（如果需要）
if (typeof window !== 'undefined') {
    window.baziAnalyzer = baziAnalyzer;
}