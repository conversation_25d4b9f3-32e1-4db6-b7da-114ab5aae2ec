// 八字分析器主类文件

class BaziAnalyzer {
    constructor() {
        this.pollingInterval = null;
        this.resultContent = null;
        this.progressSection = null;
        this.init();
    }

    init() {
        // 绑定表单提交事件
        const form = document.getElementById('textForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleSubmit(e));
        }

        // 绑定卡密输入事件
        const cardKeyInput = document.getElementById('cardKeyInput');
        if (cardKeyInput) {
            cardKeyInput.addEventListener('input', () => {
                const cardKey = cardKeyInput.value.trim();
                if (cardKey) {
                    if (validateCardKey(cardKey)) {
                        showCardKeyStatus('✅ 卡密格式正确', 'success');
                    } else {
                        showCardKeyStatus('⚠️ 卡密格式不正确', 'warning');
                    }
                } else {
                    clearCardKeyStatus();
                }
            });
        }

        this.resultContent = document.getElementById('result');
    }

    /**
     * 处理表单提交
     * @param {Event} event - 提交事件
     */
    handleSubmit(event) {
        event.preventDefault();

        // 清除旧的分析缓存，避免数据混乱
        console.log('🧹 清除旧的分析缓存...');
        localStorage.removeItem('ongoingAnalysis');
        localStorage.removeItem('latestBaziResult');

        const formData = getFormData();
        console.log('📋 当前表单数据:', formData);
        
        // 验证必填字段
        if (!formData.cardKey) {
            showError('请输入卡密');
            return;
        }
        
        if (formData.inputMode === 'bazi') {
            // 八字反推模式验证
            if (!formData.yearPillar || !formData.monthPillar || !formData.dayPillar || !formData.timePillar) {
                showError('请填写完整的四柱八字信息，或先进行八字反推');
                return;
            }
            
            // 检查是否已经反推出阳历时间
            if (!formData.year || !formData.month || !formData.day) {
                showError('请先点击"反推阳历时间"按钮获取对应的阳历日期');
                return;
            }
        } else {
            // 阳历输入模式验证
            if (!formData.year || !formData.month || !formData.day) {
                showError('请填写完整的出生日期信息');
                return;
            }
        }
        
        // 验证卡密
        if (!validateCardKey(formData.cardKey)) {
            showError('请输入有效的卡密');
            return;
        }
        
        // 设置提交状态
        setSubmitting(true);
        
        // 发送请求
        sendBaziRequest(formData, 
            (response) => this.onRequestSuccess(response, formData.cardKey),
            (error) => this.onRequestError(error)
        );
    }

    /**
     * 请求成功处理
     * @param {Object} response - 响应数据
     * @param {string} cardKey - 卡密
     */
    onRequestSuccess(response, cardKey) {
        console.log('请求成功，直接跳转到结果页面');

        // 保存当前卡密到全局变量，供其他功能使用
        window.currentCardKey = cardKey;

        // 重置提交状态
        setSubmitting(false);

        // 直接跳转到结果页面，传递原始卡密和请求ID
        const params = new URLSearchParams();
        params.set('cardKey', cardKey);
        if (response.requestId) {
            params.set('requestId', response.requestId);
        }
        window.location.href = `result.html?${params.toString()}`;
    }

    /**
     * 请求错误处理
     * @param {string} error - 错误信息
     */
    onRequestError(error) {
        setSubmitting(false);
        showError(error);
    }

    /**
     * 开始轮询完成状态
     * @param {string} identifier - 标识符
     * @param {boolean} isRequestId - 是否为请求ID
     */
    startPollingForCompletion(identifier, isRequestId = false) {
        let fallbackProgress = 10;
        let pollCount = 0;
        let lastStatus = null;
        
        showProgress('🚀 请求已提交，正在初始化处理...', fallbackProgress);
        
        // 开始轮询
        this.pollingInterval = setInterval(() => {
            pollCount++;
            checkCompletionStatus(identifier, (isCompleted, resultData, statusData) => {
                if (isCompleted) {
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                    setSubmitting(false);
                    showProgress('✅ 分析完成，正在保存结果...', 100);
                    
                    // 分析结果的保存由 api_analysis_service.js 中的 saveAnalysisResult 函数处理
                    
                    setTimeout(() => {
                        showSuccess(identifier);
                    }, 1000);
                    
                    if (resultData) {
                        console.log('处理结果:', resultData);
                    }
                } else {
                    // 优先使用后端返回的实际进度
                    if (statusData && statusData.llm_progress) {
                        this.displayLLMProgress(statusData.llm_progress, statusData.elapsed_time_formatted);
                    } else if (statusData && statusData.status) {
                        // 智能进度计算
                        const statusProgress = this.calculateProgressByStatus(statusData.status, pollCount);
                        fallbackProgress = Math.max(fallbackProgress, statusProgress);
                        
                        const statusMessage = this.getStatusMessage(statusData.status);
                        showProgress(statusMessage, fallbackProgress, statusData.elapsed_time_formatted);
                        
                        // 状态变化时给用户反馈
                        if (lastStatus !== statusData.status) {
                            showStatusChange(lastStatus, statusData.status);
                            lastStatus = statusData.status;
                        }
                    } else {
                        // 基于轮询次数的智能进度
                        fallbackProgress = Math.min(10 + (pollCount * 2), 85);
                        const messages = [
                            '🔄 正在处理中...',
                            '📊 正在分析八字信息...',
                            '🤖 正在调用AI分析引擎...',
                            '📝 正在生成详细报告...'
                        ];
                        const messageIndex = Math.floor(pollCount / 3) % messages.length;
                        showProgress(messages[messageIndex], fallbackProgress);
                    }
                }
            }, isRequestId);
        }, 2000); // 每2秒检查一次
        
        // 设置超时时间（10分钟，给LLM更多处理时间）
        setTimeout(() => {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
                setSubmitting(false);
                showError('⏰ 等待超时（10分钟），请稍后重试或联系客服。如果问题持续，可能是LLM服务繁忙。');
            }
        }, 600000); // 10分钟超时
    }

    /**
     * 根据状态计算进度
     * @param {string} status - 状态
     * @param {number} pollCount - 轮询次数
     * @returns {number} 进度百分比
     */
    calculateProgressByStatus(status, pollCount) {
        const statusProgressMap = {
            'pending': 15,
            'processing': 25,
            'generating_summary': 40,
            'llm_analyzing': 60,
            'completing': 90,
            'completed': 100,
            'failed': 0
        };
        
        const baseProgress = statusProgressMap[status] || 20;
        
        // 根据轮询次数微调进度
        const timeBonus = Math.min(pollCount * 1, 15);
        
        return Math.min(baseProgress + timeBonus, 95);
    }

    /**
     * 显示LLM进度
     * @param {Object} llmProgress - LLM进度数据
     * @param {string} elapsedTime - 已用时间
     */
    displayLLMProgress(llmProgress, elapsedTime) {
        const progress = llmProgress.progress || 60;
        
        if (!this.progressSection) {
            this.progressSection = document.querySelector('.progress-section');
        }
        
        if (!this.progressSection) return;
        
        const llmProgressElement = this.progressSection.querySelector('.llm-progress');
        const llmDimensionsElement = this.progressSection.querySelector('.llm-dimensions');
        
        llmProgressElement.style.display = 'block';
        
        // 清空现有维度标签
        llmDimensionsElement.innerHTML = '';
        
        // 使用后端返回的实际进度
        const actualProgress = llmProgress.progress || progress;
        
        // 添加维度标签，区分已完成和当前处理的维度
        if (llmProgress.dimensions && Array.isArray(llmProgress.dimensions)) {
            llmProgress.dimensions.forEach(dimension => {
                const tag = document.createElement('span');
                tag.className = 'dimension-tag';
                
                // 标记已完成的维度
                if (llmProgress.completed_dimensions && llmProgress.completed_dimensions.includes(dimension)) {
                    tag.classList.add('completed');
                    tag.innerHTML = `✅ ${dimension}`;
                } else if (llmProgress.current_dimension === dimension) {
                    tag.classList.add('current');
                    tag.innerHTML = `🔄 ${dimension}`;
                } else {
                    tag.classList.add('pending');
                    tag.innerHTML = `⏳ ${dimension}`;
                }
                
                tag.textContent = tag.innerHTML;
                llmDimensionsElement.appendChild(tag);
            });
        }
        
        // 构建详细的状态消息
        let statusMessage = '🤖 LLM深度分析中';
        if (llmProgress.current_dimension) {
            statusMessage += ` - 正在分析：${llmProgress.current_dimension}`;
        }
        if (llmProgress.completed_dimensions && llmProgress.dimensions) {
            const completed = llmProgress.completed_dimensions.length;
            const total = llmProgress.dimensions.length;
            statusMessage += ` (${completed}/${total})`;
        }
        
        showProgress(statusMessage, actualProgress, elapsedTime);
    }
    
    /**
     * 获取状态消息
     * @param {string} status - 状态
     * @returns {string} 状态消息
     */
    getStatusMessage(status) {
        const statusMessages = {
            'pending': '⏳ 等待处理',
            'processing': '🔄 正在处理',
            'generating_summary': '📝 生成摘要中',
            'llm_analyzing': '🤖 LLM深度分析中',
            'completing': '✅ 即将完成',
            'completed': '✅ 处理完成',
            'failed': '❌ 处理失败'
        };
        
        return statusMessages[status] || `🔄 ${status}`;
    }
}

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaziAnalyzer;
}