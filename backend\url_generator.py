from urllib.parse import quote
import logging

logger = logging.getLogger(__name__)

class URLGenerator:
    """八字API URL生成器"""
    
    def __init__(self):
        self.base_url = "https://bzapi2.iwzbz.com/getbasebz7.php"
        self.fixed_params = {
            'today': 'undefined-NaN-NaN%20NaN:NaN:00',
            'vip': '0',
            'userguid': '',
            'yzs': '0'
        }
        # 时辰转换映射表（使用30分作为代表时间）
        self.time_mapping = {
            '早子时': '00:30',
            '夜子时': '23:30',
            '子时': '23:30',  # 保持向后兼容
            '丑时': '01:30',
            '寅时': '03:30',
            '卯时': '05:30',
            '辰时': '07:30',
            '巳时': '09:30',
            '午时': '11:30',
            '未时': '13:30',
            '申时': '15:30',
            '酉时': '17:30',
            '戌时': '19:30',
            '亥时': '21:30'
        }
    
    def generate_bazi_url(self, year, month, day, time, gender):
        """生成八字API链接"""
        try:
            # 转换时辰为具体时间
            converted_time = self.convert_time_to_hour(time)
            logger.info(f"时辰转换: {time} -> {converted_time}")
            
            # 确保月份和日期为两位数格式
            month_str = str(month).zfill(2)
            day_str = str(day).zfill(2)
            
            # 处理时间格式：如果是HH:MM格式，直接使用；如果是HH:MM:SS格式，去掉秒数
            if ':' in converted_time:
                time_parts = converted_time.split(':')
                if len(time_parts) >= 2:
                    hour_str = time_parts[0].zfill(2)
                    minute_str = time_parts[1].zfill(2)
                    formatted_time = f"{hour_str}:{minute_str}"
                else:
                    formatted_time = converted_time
            else:
                formatted_time = converted_time
            
            # 格式化日期时间为标准格式：YYYY-MM-DD HH:MM
            datetime_str = f"{year}-{month_str}-{day_str} {formatted_time}"
            
            # URL编码日期时间（空格变为%20）
            encoded_datetime = quote(datetime_str, safe='-:')
            
            # 转换性别参数（男=1，女=0）
            if gender == '男' or gender == '1':
                gender_param = '1'
            else:
                gender_param = '0'
            
            # 构建完整URL
            url = f"{self.base_url}?d={encoded_datetime}&s={gender_param}"
            
            # 添加固定参数
            for key, value in self.fixed_params.items():
                url += f"&{key}={value}"
            
            logger.info(f"生成的八字API链接: {url}")
            return url
            
        except Exception as e:
            logger.error(f"生成URL时出错: {str(e)}")
            raise
    
    def convert_time_to_hour(self, time):
        """将时辰转换为具体时间"""
        # 确保输入参数是字符串
        time = str(time) if not isinstance(time, str) else time
        
        # 如果已经是HH:MM格式，直接返回
        if ':' in time and time not in self.time_mapping:
            return time
        
        # 如果是时辰名称，进行转换
        if time in self.time_mapping:
            return self.time_mapping[time]
        
        # 如果是纯数字，格式化为HH:00
        try:
            hour = int(time)
            if 0 <= hour <= 23:
                return f"{hour:02d}:00"
            else:
                logger.warning(f"小时数值超出范围 (0-23): {hour}，使用默认值12:00")
                return "12:00"
        except ValueError:
            # 如果无法转换为数字，则使用默认值
            pass
        
        # 如果都不匹配，默认返回12:00
        logger.warning(f"未识别的时间格式: {time}，使用默认值12:00")
        return "12:00"
    
    def validate_datetime_format(self, year, month, day, time):
        """验证日期时间格式"""
        try:
            # 验证年份
            year_int = int(year)
            if year_int < 1900 or year_int > 2100:
                return False, "年份必须在1900-2100之间"
            
            # 验证月份
            month_int = int(month)
            if month_int < 1 or month_int > 12:
                return False, "月份必须在1-12之间"
            
            # 验证日期
            day_int = int(day)
            if day_int < 1 or day_int > 31:
                return False, "日期必须在1-31之间"
            
            # 转换时辰为具体时间后验证
            converted_time = self.convert_time_to_hour(time)
            
            # 验证时间格式（HH:MM）
            if ':' not in converted_time:
                return False, "时间格式错误，应为HH:MM"
            
            hour, minute = converted_time.split(':')
            hour_int = int(hour)
            minute_int = int(minute)
            
            if hour_int < 0 or hour_int > 23:
                return False, "小时必须在0-23之间"
            
            if minute_int < 0 or minute_int > 59:
                return False, "分钟必须在0-59之间"
            
            return True, "格式正确"
            
        except ValueError:
            return False, "日期时间格式错误"