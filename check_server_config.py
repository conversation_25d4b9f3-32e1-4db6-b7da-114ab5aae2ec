#!/usr/bin/env python3
"""
检查服务器配置和网络问题
"""

import os
import sys
import socket
import subprocess
import requests
from urllib.parse import urlparse

def check_network_connectivity():
    """检查网络连接"""
    print("🌐 网络连接检查")
    print("=" * 50)
    
    # 检查本地回环
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        if result == 0:
            print("✅ 本地回环 127.0.0.1:5000 可连接")
        else:
            print("❌ 本地回环 127.0.0.1:5000 无法连接")
    except Exception as e:
        print(f"❌ 本地回环测试失败: {e}")
    
    # 检查外部IP
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('**************', 5000))
        sock.close()
        if result == 0:
            print("✅ 外部IP **************:5000 可连接")
        else:
            print("❌ 外部IP **************:5000 无法连接")
    except Exception as e:
        print(f"❌ 外部IP测试失败: {e}")
    
    # 检查防火墙
    print("\n🔥 防火墙检查")
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles'], 
                                  capture_output=True, text=True)
            if 'State' in result.stdout:
                print("ℹ️ Windows防火墙状态:")
                for line in result.stdout.split('\n'):
                    if 'State' in line:
                        print(f"  {line.strip()}")
        else:  # Linux
            result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
            print(f"ℹ️ UFW状态: {result.stdout.strip()}")
    except Exception as e:
        print(f"⚠️ 防火墙检查失败: {e}")

def check_web_server_conflict():
    """检查Web服务器冲突"""
    print("\n🌐 Web服务器冲突检查")
    print("=" * 50)
    
    # 检查常见Web服务器端口
    web_ports = [80, 443, 8080, 8000, 3000]
    for port in web_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            if result == 0:
                print(f"⚠️ 端口 {port} 被占用 (可能有其他Web服务器)")
            else:
                print(f"✅ 端口 {port} 空闲")
        except:
            print(f"❓ 端口 {port} 检查失败")
    
    # 检查IIS (Windows)
    if os.name == 'nt':
        try:
            result = subprocess.run(['sc', 'query', 'w3svc'], 
                                  capture_output=True, text=True)
            if 'RUNNING' in result.stdout:
                print("⚠️ IIS服务正在运行，可能占用80端口")
            else:
                print("✅ IIS服务未运行")
        except:
            print("❓ 无法检查IIS状态")

def test_api_endpoints():
    """测试API端点"""
    print("\n🔌 API端点测试")
    print("=" * 50)
    
    base_urls = [
        'http://127.0.0.1:5000',
        'http://**************:5000'
    ]
    
    endpoints = ['/', '/test', '/api/health']
    
    for base_url in base_urls:
        print(f"\n测试 {base_url}:")
        for endpoint in endpoints:
            url = f"{base_url}{endpoint}"
            try:
                response = requests.get(url, timeout=5)
                print(f"  ✅ {endpoint} -> {response.status_code}")
            except requests.exceptions.ConnectRefused:
                print(f"  ❌ {endpoint} -> 连接被拒绝")
            except requests.exceptions.Timeout:
                print(f"  ⏱️ {endpoint} -> 超时")
            except Exception as e:
                print(f"  ❌ {endpoint} -> {e}")

def check_cors_headers():
    """检查CORS头"""
    print("\n🔗 CORS头检查")
    print("=" * 50)
    
    try:
        # 测试OPTIONS请求
        response = requests.options('http://127.0.0.1:5000/api/health', timeout=5)
        headers = response.headers
        
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers'
        ]
        
        for header in cors_headers:
            if header in headers:
                print(f"✅ {header}: {headers[header]}")
            else:
                print(f"❌ {header}: 缺失")
                
    except Exception as e:
        print(f"❌ CORS检查失败: {e}")

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n💡 修复建议")
    print("=" * 50)
    
    suggestions = [
        "1. 确保Flask应用监听 0.0.0.0:5000 而不是 127.0.0.1:5000",
        "2. 检查防火墙是否允许5000端口的入站连接",
        "3. 如果有IIS或其他Web服务器，确保没有端口冲突",
        "4. 验证服务器的网络配置允许外部访问",
        "5. 检查云服务器的安全组设置（如果使用云服务）",
        "6. 确保Flask应用的CORS配置正确",
        "7. 检查是否有反向代理（如Nginx）需要配置"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def create_network_test_script():
    """创建网络测试脚本"""
    print("\n📝 创建网络测试脚本")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
"""
网络连接测试脚本
"""

import socket
import time

def test_port_binding():
    """测试端口绑定"""
    print("测试端口绑定...")
    
    # 测试绑定到所有接口
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.bind(('0.0.0.0', 5000))
        sock.listen(1)
        print("✅ 成功绑定到 0.0.0.0:5000")
        
        # 测试连接
        client_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_sock.settimeout(3)
        result = client_sock.connect_ex(('127.0.0.1', 5000))
        client_sock.close()
        
        if result == 0:
            print("✅ 本地连接测试成功")
        else:
            print("❌ 本地连接测试失败")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ 端口绑定失败: {e}")

if __name__ == "__main__":
    test_port_binding()
'''
    
    with open('network_test.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 已创建 network_test.py")

def main():
    """主函数"""
    print("🔍 服务器配置检查工具")
    print("=" * 60)
    
    check_network_connectivity()
    check_web_server_conflict()
    test_api_endpoints()
    check_cors_headers()
    generate_fix_suggestions()
    create_network_test_script()
    
    print("\n" + "=" * 60)
    print("🎯 检查完成！")
    print("\n📋 下一步:")
    print("1. 运行: python network_test.py")
    print("2. 运行: python server_deployment_fix.py")
    print("3. 启动: python server_app.py")

if __name__ == "__main__":
    main()
