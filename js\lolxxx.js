/**
 * 系统管理后台 JavaScript
 * 包含登录验证、报告管理等功能
 */

class AdminDashboard {
    constructor() {
        this.apiBase = '/api';
        this.promptApiBase = '/api/prompt';
        this.updateInterval = 30000; // 30秒更新一次
        this.correctPassword = '88888888';
        this.reports = [];
        
        this.init();
    }
    
    init() {
        // 检查登录状态
        if (this.isLoggedIn()) {
            this.showAdminPanel();
            this.loadDashboardData();
            this.loadPromptStatus();
            this.startAutoUpdate();
        } else {
            this.showLoginPanel();
        }
        
        // 绑定回车键登录
        document.getElementById('password-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.login();
            }
        });
    }
    
    // 登录相关方法
    isLoggedIn() {
        const loginTime = localStorage.getItem('adminLoginTime');
        if (!loginTime) return false;
        
        // 检查登录是否过期（24小时）
        const now = new Date().getTime();
        const loginTimestamp = parseInt(loginTime);
        const hoursPassed = (now - loginTimestamp) / (1000 * 60 * 60);
        
        return hoursPassed < 24;
    }
    
    login() {
        const password = document.getElementById('password-input').value;
        const errorDiv = document.getElementById('login-error');
        
        if (password === this.correctPassword) {
            // 登录成功
            localStorage.setItem('adminLoginTime', new Date().getTime().toString());
            this.showAdminPanel();
            this.loadDashboardData();
            this.loadPromptStatus();
            this.startAutoUpdate();
        } else {
            // 登录失败
            errorDiv.textContent = '密码错误，请重试';
            errorDiv.style.display = 'block';
            document.getElementById('password-input').value = '';
            document.getElementById('password-input').focus();
            
            // 3秒后隐藏错误信息
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }
    }
    
    logout() {
        localStorage.removeItem('adminLoginTime');
        this.showLoginPanel();
    }
    
    showLoginPanel() {
        document.getElementById('login-overlay').style.display = 'flex';
        document.getElementById('admin-container').style.display = 'none';
        document.getElementById('password-input').focus();
    }
    
    showAdminPanel() {
        document.getElementById('login-overlay').style.display = 'none';
        document.getElementById('admin-container').style.display = 'block';
    }
    
    // API调用封装
    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(endpoint, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || '请求失败');
            }
            
            return result;
        } catch (error) {
            console.error('API调用错误:', error);
            throw error;
        }
    }
    
    // 加载仪表盘数据
    async loadDashboardData() {
        try {
            const dashboardData = await this.apiCall(`${this.promptApiBase}/dashboard`);
            
            // 更新统计数据
            const stats = dashboardData.stats;
            document.getElementById('total-analysis').textContent = stats.total_analysis || 0;
            document.getElementById('active-cards').textContent = stats.active_cards || 0;
            document.getElementById('prompt-versions').textContent = stats.prompt_versions || 0;
            document.getElementById('system-uptime').textContent = stats.system_uptime || '0h';
            
            // 更新系统信息
            const systemInfo = dashboardData.system_info;
            document.getElementById('server-status').textContent = systemInfo.server_status || '运行中';
            document.getElementById('system-version').textContent = systemInfo.system_version || 'v2.0.0';
            document.getElementById('db-status').textContent = systemInfo.db_status || '正常';
            document.getElementById('last-update').textContent = systemInfo.last_update || new Date().toLocaleTimeString();
            
            // 更新API状态
            document.getElementById('api-status').textContent = '正常';
            document.getElementById('api-status').style.color = '#28a745';
            
            // 更新缓存命中率
            const promptInfo = dashboardData.prompt_info;
            let cacheHitRate = '不可用';
            if (promptInfo.cache_hit_rate && promptInfo.cache_hit_rate !== 'N/A') {
                try {
                    const cacheStr = promptInfo.cache_hit_rate;
                    const hitsMatch = cacheStr.match(/hits=(\d+)/);
                    const missesMatch = cacheStr.match(/misses=(\d+)/);
                    
                    if (hitsMatch && missesMatch) {
                        const hits = parseInt(hitsMatch[1]);
                        const misses = parseInt(missesMatch[1]);
                        const total = hits + misses;
                        if (total > 0) {
                            const hitRate = ((hits / total) * 100).toFixed(1);
                            cacheHitRate = `${hitRate}%`;
                        } else {
                            cacheHitRate = '0%';
                        }
                    }
                } catch (e) {
                    console.log('解析缓存命中率失败:', e);
                }
            }
            document.getElementById('cache-hit-rate').textContent = cacheHitRate;
            
        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
            // 显示错误状态
            document.getElementById('api-status').textContent = 'API错误';
            document.getElementById('api-status').style.color = '#dc3545';
            document.getElementById('cache-hit-rate').textContent = '不可用';
            this.showAlert('error', 'API连接失败，显示的可能是缓存数据');
        }
    }
    
    // 加载提示词系统状态
    async loadPromptStatus() {
        try {
            const dashboardData = await this.apiCall(`${this.promptApiBase}/dashboard`);
            const promptInfo = dashboardData.prompt_info;
            
            // 这里可以添加更多提示词状态的显示逻辑
            console.log('提示词状态:', promptInfo);
            
        } catch (error) {
            console.error('加载提示词状态失败:', error);
        }
    }
    
    // 加载报告列表
    async loadReports() {
        try {
            // 获取报告列表
            const response = await fetch('/api/reports');
            if (!response.ok) {
                throw new Error('获取报告列表失败');
            }
            
            this.reports = await response.json();
            this.renderReports();
            
        } catch (error) {
            console.error('加载报告失败:', error);
            // 如果API不可用，尝试从本地存储获取
            this.loadReportsFromStorage();
        }
    }
    
    // 从本地存储加载报告（备用方案）
    loadReportsFromStorage() {
        try {
            // 模拟报告数据
            this.reports = [
                {
                    id: '1',
                    title: '张三的八字分析报告',
                    created_at: '2025-01-30 10:30:00',
                    birth_info: '1990年5月15日 14:30',
                    bazi: '庚午年 辛巳月 甲子日 辛未时',
                    preview: '此人性格严谨务实，具有强烈的责任感和原则性...'
                },
                {
                    id: '2', 
                    title: '李四的八字分析报告',
                    created_at: '2025-01-30 09:15:00',
                    birth_info: '1985年3月20日 08:45',
                    bazi: '乙丑年 己卯月 丁亥日 甲辰时',
                    preview: '此人性格温和善良，具有很强的同理心和包容性...'
                },
                {
                    id: '3',
                    title: '王五的八字分析报告', 
                    created_at: '2025-01-30 08:20:00',
                    birth_info: '1992年8月10日 16:20',
                    bazi: '壬申年 戊申月 癸卯日 庚申时',
                    preview: '此人性格机敏灵活，思维敏捷，善于变通...'
                }
            ];
            
            this.renderReports();
            
        } catch (error) {
            console.error('加载本地报告失败:', error);
            this.renderEmptyReports();
        }
    }
    
    // 渲染报告列表
    renderReports() {
        const reportsList = document.getElementById('reports-list');
        const reportsCount = document.getElementById('reports-count');
        
        if (!this.reports || this.reports.length === 0) {
            this.renderEmptyReports();
            return;
        }
        
        reportsCount.textContent = this.reports.length;
        
        const reportsHtml = this.reports.map(report => `
            <div class="report-card" onclick="viewReport('${report.id}')">
                <div class="report-title">${report.title || '未命名报告'}</div>
                <div class="report-meta">
                    <div>📅 ${report.created_at || '未知时间'}</div>
                    <div>🎂 ${report.birth_info || '未知生辰'}</div>
                    <div>📊 ${report.bazi || '未知八字'}</div>
                </div>
                <div class="report-preview">
                    ${report.preview || '暂无预览内容...'}
                </div>
                <div class="report-actions">
                    <button class="btn btn-primary" onclick="event.stopPropagation(); viewDetailedReport('${report.id}')">
                        📄 查看详细
                    </button>
                    <button class="btn btn-secondary" onclick="event.stopPropagation(); downloadReport('${report.id}')">
                        📥 下载
                    </button>
                </div>
            </div>
        `).join('');
        
        reportsList.innerHTML = reportsHtml;
    }
    
    // 渲染空报告状态
    renderEmptyReports() {
        const reportsList = document.getElementById('reports-list');
        const reportsCount = document.getElementById('reports-count');
        
        reportsCount.textContent = '0';
        reportsList.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666; grid-column: 1 / -1;">
                <div style="font-size: 3em; margin-bottom: 20px;">📋</div>
                <div style="font-size: 1.2em; margin-bottom: 10px;">暂无分析报告</div>
                <div style="font-size: 0.9em;">系统中还没有生成任何八字分析报告</div>
            </div>
        `;
    }
    
    // 开始自动更新
    startAutoUpdate() {
        setInterval(() => {
            this.loadDashboardData();
            this.loadPromptStatus();
        }, this.updateInterval);
    }
    
    // 显示提示信息
    showAlert(type, message, containerId = null) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<strong>${type === 'error' ? '错误' : type === 'success' ? '成功' : '提示'}：</strong> ${message}`;
        
        const container = containerId ? document.getElementById(containerId) : document.querySelector('.admin-section.active');
        container.insertBefore(alertDiv, container.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 全局变量
let adminDashboard;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    adminDashboard = new AdminDashboard();
});

// 切换管理面板
function showSection(sectionName) {
    // 隐藏所有section
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 移除所有导航项的active状态
    document.querySelectorAll('.admin-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示选中的section
    document.getElementById(`${sectionName}-section`).classList.add('active');
    
    // 设置选中的导航项为active
    event.target.classList.add('active');
    
    // 根据section执行特定的加载逻辑
    switch(sectionName) {
        case 'dashboard':
            adminDashboard.loadDashboardData();
            break;
        case 'reports':
            adminDashboard.loadReports();
            break;
        case 'prompts':
            adminDashboard.loadPromptStatus();
            break;
    }
}

// 登录和登出
function login() {
    adminDashboard.login();
}

function logout() {
    if (confirm('确定要退出登录吗？')) {
        adminDashboard.logout();
    }
}

// 报告相关功能
function refreshReports() {
    adminDashboard.showAlert('info', '正在刷新报告列表...', 'reports-section');
    adminDashboard.loadReports();
}

function viewReport(reportId) {
    console.log('查看报告:', reportId);
    adminDashboard.showAlert('info', `正在打开报告 ${reportId}`, 'reports-section');
}

function viewDetailedReport(reportId) {
    // 从后台访问时使用有效的卡密
    // 使用一个永不过期的卡密来访问报告
    const validCardKey = 'wdd'; // 使用一个有效的卡密
    const url = `detailed_result.html?cardKey=${validCardKey}&reportId=${reportId}`;
    window.open(url, '_blank');
}

function downloadReport(reportId) {
    adminDashboard.showAlert('info', `正在下载报告 ${reportId}`, 'reports-section');
    // 这里可以添加下载逻辑
}

function exportReports() {
    adminDashboard.showAlert('info', '正在导出所有报告...', 'reports-section');
    // 这里可以添加批量导出逻辑
}

// 导出到全局作用域
window.showSection = showSection;
window.login = login;
window.logout = logout;
window.refreshReports = refreshReports;
window.viewReport = viewReport;
window.viewDetailedReport = viewDetailedReport;
window.downloadReport = downloadReport;
window.exportReports = exportReports;
