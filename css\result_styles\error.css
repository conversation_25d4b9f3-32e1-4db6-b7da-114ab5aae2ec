/* 错误状态 */
.error-state {
    color: var(--text-primary);
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 24px;
    opacity: 0.8;
}

.error-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #e53e3e;
    margin-bottom: 12px;
}

.error-message {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 32px;
    max-width: 400px;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
    justify-content: center;
}

.retry-btn,
.back-home-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    min-width: 100px;
}

.retry-btn {
    background: var(--primary-gradient);
    color: white;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.back-home-btn {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.back-home-btn:hover {
    background: var(--bg-card);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.error-details {
    width: 100%;
    max-width: 500px;
}

.error-details details {
    text-align: left;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color-light);
    border-radius: var(--radius-medium);
    padding: 16px;
}

.error-details summary {
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.error-details summary:hover {
    color: var(--primary-color);
}

#errorDetails {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 12px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color-light);
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .error-icon {
        font-size: 3.5rem;
        margin-bottom: 16px;
    }
    
    .error-title {
        font-size: 1.4rem;
        text-align: center;
    }
    
    .error-message {
        font-size: 15px;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 20px;
    }
    
    .error-actions {
        flex-direction: column;
        gap: 12px;
        margin-top: 20px;
    }
    
    .error-actions button {
        width: 100%;
        min-height: 48px;
        font-size: 16px;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .error-details {
        width: 100%;
        max-width: 320px;
        margin-top: 16px;
    }
    
    #errorDetails {
        font-size: 12px;
        max-height: 150px;
    }
}

@media (max-width: 480px) {
    .error-icon {
        font-size: 3rem;
    }
    
    .error-title {
        font-size: 1.2rem;
    }
    
    .error-message {
        font-size: 14px;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .error-details {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
    }
    
    .error-details summary {
        color: var(--text-light);
    }
    
    #errorDetails {
        background: var(--bg-secondary);
        color: var(--text-light);
        border-color: var(--border-color);
    }
} 