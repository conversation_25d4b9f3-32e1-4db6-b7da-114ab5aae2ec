import uuid
import time
import threading
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class RequestManager:
    """请求管理器 - 生成和管理唯一请求ID"""
    
    def __init__(self):
        self.active_requests = {}  # 活跃请求记录
        self.request_lock = threading.Lock()  # 线程锁
        self.cleanup_interval = 3600  # 清理间隔（秒）
        self.max_request_age = 86400  # 最大请求保留时间（24小时）
        # 导入卡密管理器，避免循环导入
        from card_manager_singleton import get_card_manager
        self.card_manager = get_card_manager()
    
    def generate_request_id(self, card_key):
        """生成唯一请求ID
        
        格式: {card_key}_{timestamp}_{uuid}
        例如: test123_1640995200_a1b2c3d4
        """
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]  # 取UUID前8位
        request_id = f"{card_key}_{timestamp}_{unique_id}"
        
        with self.request_lock:
            # 记录请求信息
            self.active_requests[request_id] = {
                'card_key': card_key,
                'created_time': datetime.now(),
                'status': 'created',
                'last_update': datetime.now()
            }
        
        logger.info(f"生成请求ID: {request_id}")
        return request_id
    
    def start_request(self, request_id):
        """标记请求开始处理"""
        with self.request_lock:
            if request_id in self.active_requests:
                self.active_requests[request_id].update({
                    'status': 'processing',
                    'start_time': datetime.now(),
                    'last_update': datetime.now()
                })
                logger.info(f"请求开始处理: {request_id}")
                return True
            else:
                logger.warning(f"请求ID不存在: {request_id}")
                return False
    
    def update_request_status(self, request_id, status, progress=None, message=None):
        """更新请求状态"""
        with self.request_lock:
            if request_id in self.active_requests:
                update_data = {
                    'status': status,
                    'last_update': datetime.now()
                }
                
                if progress is not None:
                    update_data['progress'] = progress
                
                if message is not None:
                    update_data['message'] = message
                
                self.active_requests[request_id].update(update_data)
                logger.info(f"更新请求状态: {request_id} -> {status}")
                return True
            else:
                logger.warning(f"请求ID不存在: {request_id}")
                return False
    
    def complete_request(self, request_id, success=True, result=None, error=None):
        """标记请求完成"""
        with self.request_lock:
            if request_id in self.active_requests:
                completion_data = {
                    'status': 'completed' if success else 'failed',
                    'completed': True,
                    'end_time': datetime.now(),
                    'last_update': datetime.now(),
                    'success': success
                }
                
                if result is not None:
                    completion_data['result'] = result
                
                if error is not None:
                    completion_data['error'] = error
                
                self.active_requests[request_id].update(completion_data)
                
                # 更新卡密使用次数
                card_key = self.active_requests[request_id].get('card_key')
                if card_key:
                    self.card_manager.mark_completed(card_key, success=success)
                    logger.info(f"已更新卡密 {card_key} 的使用次数")
                
                logger.info(f"请求完成: {request_id} (成功: {success})")
                return True
            else:
                logger.warning(f"请求ID不存在: {request_id}")
                return False
    
    def fail_request(self, request_id, error=None):
        """标记请求失败"""
        return self.complete_request(request_id, success=False, error=error)
    
    def get_request_info(self, request_id):
        """获取请求信息"""
        with self.request_lock:
            return self.active_requests.get(request_id, None)

    def save_basic_data(self, request_id, basic_data):
        """保存基础八字数据到请求信息中

        Args:
            request_id: 请求ID
            basic_data: 基础八字数据
        """
        with self.request_lock:
            if request_id in self.active_requests:
                self.active_requests[request_id]['basic_data'] = basic_data
                logger.info(f"已保存基础八字数据，请求ID: {request_id}")
            else:
                logger.warning(f"请求ID不存在，无法保存基础数据: {request_id}")

    def get_requests_by_card(self, card_key):
        """根据卡密获取所有相关请求"""
        with self.request_lock:
            card_requests = {}
            for req_id, req_info in self.active_requests.items():
                if req_info.get('card_key') == card_key:
                    card_requests[req_id] = req_info
            return card_requests
    
    def is_card_processing(self, card_key):
        """检查卡密是否有正在处理的请求"""
        card_requests = self.get_requests_by_card(card_key)
        for req_info in card_requests.values():
            if req_info.get('status') in ['created', 'processing']:
                return True
        return False
    
    def get_latest_request_by_card(self, card_key):
        """获取卡密的最新请求"""
        card_requests = self.get_requests_by_card(card_key)
        if not card_requests:
            return None
        
        # 按创建时间排序，返回最新的
        latest_req = max(card_requests.items(), 
                        key=lambda x: x[1].get('created_time', datetime.min))
        return latest_req[0], latest_req[1]
    
    def cleanup_old_requests(self):
        """清理过期的请求记录"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(seconds=self.max_request_age)
        
        with self.request_lock:
            expired_requests = []
            for req_id, req_info in self.active_requests.items():
                created_time = req_info.get('created_time', datetime.min)
                if created_time < cutoff_time:
                    expired_requests.append(req_id)
            
            for req_id in expired_requests:
                del self.active_requests[req_id]
            
            if expired_requests:
                logger.info(f"清理了 {len(expired_requests)} 个过期请求")
    
    def get_active_request_count(self):
        """获取活跃请求数量"""
        with self.request_lock:
            return len(self.active_requests)
    
    def get_processing_request_count(self):
        """获取正在处理的请求数量"""
        with self.request_lock:
            processing_count = 0
            for req_info in self.active_requests.values():
                if req_info.get('status') in ['created', 'processing']:
                    processing_count += 1
            return processing_count