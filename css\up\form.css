/* 兼容性说明：此文件为旧版引用兼容，实际内容已迁移到 css/up/ 目录下的模块化文件中 */
.form-container {
    background: var(--card-gradient);
    border: 1px solid var(--border-color-light);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    position: relative;
}

.form-container:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

/* 表单阴影动画 */
.form-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(240, 147, 251, 0.02) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.form-container:hover::after {
    opacity: 1;
}

/* 表单分组 */
.form-section {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* 出生信息部分初始隐藏状态 */
.form-section.birth-info-section {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* 出生信息部分显示动画 */
.form-section.birth-info-section.show {
    opacity: 1;
    transform: translateY(0);
}

.form-section:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
}

/* 八字反推界面不要悬停变色效果 */
.bazi-input-section:hover {
    background: transparent;
    transform: none;
    box-shadow: none;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: var(--hero-gradient);
    transition: left 0.5s ease;
}

.form-section:hover::before {
    left: 0;
}

.bazi-input-section:hover::before {
    left: -100%;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(to right, var(--primary-color), transparent);
    margin-left: 16px;
}

/* 表单行和列 */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    position: relative;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-col {
    flex: 1;
}

/* 输入组 */
.input-group {
    margin-bottom: 24px;
}

.input-group label {
    position: relative;
    display: inline-block;
    margin-bottom: 8px;
}

.input-group label::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.input-group:hover label::before {
    opacity: 1;
}

.bazi-input-section .input-group:hover label::before {
    opacity: 0;
}

.input-group input,
.input-group select {
    transition: var(--transition);
}

.input-group input:focus,
.input-group select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 输入图标 */
.input-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    opacity: 0.6;
    transition: all 0.3s ease;
    pointer-events: none;
}

.input-group:focus-within .input-icon {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

/* 时辰选择样式 */
#hourInput option {
    padding: 8px;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}