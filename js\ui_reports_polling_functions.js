/**
 * 报告管理器 - 轮询函数
 * 提供分析状态轮询的辅助功能
 */

// 分析步骤定义 - 简化版，只保留深度分析相关步骤
const ANALYSIS_STEPS = [
    { key: 'personality_analysis', name: '性格深度分析', progress: 20 },
    { key: 'career_analysis', name: '事业运势分析', progress: 35 },
    { key: 'relationship_analysis', name: '感情婚姻分析', progress: 50 },
    { key: 'health_analysis', name: '健康状况分析', progress: 65 },
    { key: 'fortune_analysis', name: '运势趋势分析', progress: 80 },
    { key: 'llm_processing', name: 'AI深度整合', progress: 95 },
    { key: 'finalizing', name: '生成报告', progress: 100 }
];

// 确保HistoryReportsManager类已定义后再扩展原型
if (typeof HistoryReportsManager !== 'undefined') {
    // 初始化进度显示状态
    HistoryReportsManager.prototype.initializeProgressDisplay = function() {
    const runningStatus = document.getElementById('runningStatus');
    const progressPercent = document.getElementById('progressPercent');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    // 默认隐藏进度信息
    if (runningStatus) runningStatus.style.display = 'none';
    if (progressPercent) progressPercent.style.display = 'none';
    if (progressFill) progressFill.style.width = '0%';
    if (progressText) progressText.textContent = '等待开始分析...';
};

// 更新分析轮询进度 - 增强版本
HistoryReportsManager.prototype.updateAnalysisProgress = function(status) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const runningStatus = document.getElementById('runningStatus');
    // progressPercent 已在上面声明
    const progressSteps = document.getElementById('progressSteps');

    if (!progressFill || !progressText || !runningStatus) return;

    // 检查是否真的在进行分析 - 更宽松的检查
    const isReallyAnalyzing = status && (
        status.status === 'processing' ||
        status.status === 'llm_analyzing' ||
        status.status === 'queued' ||
        status.llm_progress || // 如果有LLM进度信息，说明在分析
        (status.progress && status.progress > 0) // 如果有进度且大于0，说明在分析
    );

    // 如果没有真正的分析状态，隐藏进度信息
    if (!isReallyAnalyzing) {
        runningStatus.style.display = 'none';
        if (progressPercent) progressPercent.style.display = 'none';
        progressFill.style.width = '0%';
        progressText.textContent = '等待开始分析...';
        return;
    }

    // 显示进度信息
    runningStatus.style.display = 'block';
    if (progressPercent) progressPercent.style.display = 'block';

    let progress = 0; // 默认从0开始
    let currentStep = '';
    let statusText = '';

    // 检查是否有LLM进度信息
    if (status.llm_progress) {
        const llmProgress = status.llm_progress;
        const completed = llmProgress.completed_dimensions ? llmProgress.completed_dimensions.length : 0;
        const total = llmProgress.dimensions ? llmProgress.dimensions.length : 8; // 默认8个维度

        // 修正进度计算：只有真正开始分析时才显示进度
        if (completed === 0 && !llmProgress.current_dimension && llmProgress.progress === 0) {
            // 完全没有开始分析，显示0%
            progress = 0;
        } else if (completed === 0) {
            // 刚开始分析但还没完成任何维度
            progress = Math.max(5, Math.min(llmProgress.progress || 0, 20)); // 5-20%
        } else {
            // 有完成的维度，按比例计算
            const llmProgressPercent = (completed / total) * 70 + 20; // 20-90%
            progress = Math.min(llmProgressPercent, 90);
        }

        // 根据实际进度设置状态文本
        if (completed === 0 && !llmProgress.current_dimension && llmProgress.progress === 0) {
            statusText = '等待开始分析...';
            currentStep = '准备进行AI深度分析';
        } else if (completed === 0) {
            statusText = '初始化分析...';
            currentStep = llmProgress.current_dimension || '正在启动AI分析引擎';
        } else {
            statusText = `AI深度分析中 (${completed}/${total})`;
            currentStep = llmProgress.current_dimension || '正在进行AI深度分析';
        }

        // 更新步骤显示
        this.updateProgressSteps('llm_processing', llmProgress.completed_dimensions || [], llmProgress);
    } else {
        // 根据状态确定当前步骤
        switch (status.status) {
            case 'queued':
                progress = 0;
                statusText = '排队中...';
                currentStep = '您的请求已加入队列，请耐心等待';
                break;
            case 'processing':
                // 根据消息内容推断当前步骤
                const message = status.message || '';
                const step = this.inferCurrentStep(message, status);
                progress = step.progress;
                statusText = '分析中...';
                currentStep = `正在${step.name}`;
                this.updateProgressSteps(step.key);
                break;
            case 'completed':
                progress = 100;
                statusText = '已完成';
                currentStep = '分析已完成，正在加载结果...';
                this.updateProgressSteps('completed');
                break;
            case 'failed':
                statusText = '分析失败';
                currentStep = status.error || '分析失败，请稍后重试';
                progressFill.style.backgroundColor = 'var(--error-color)';
                break;
        }
    }

    // 更新UI元素
    progressFill.style.width = `${progress}%`;
    runningStatus.textContent = statusText;
    progressText.textContent = currentStep;

    // 添加进度百分比显示
    if (progressPercent) {
        progressPercent.textContent = `${Math.round(progress)}%`;
    }
};

// 更新已用时间
HistoryReportsManager.prototype.updateElapsedTime = function(startTime) {
    const elapsedTimeElement = document.getElementById('elapsedTime');
    if (!elapsedTimeElement) return;
    
    const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
    let timeText = '';
    
    if (elapsedSeconds < 60) {
        timeText = `${elapsedSeconds}秒`;
    } else if (elapsedSeconds < 3600) {
        const minutes = Math.floor(elapsedSeconds / 60);
        const seconds = elapsedSeconds % 60;
        timeText = `${minutes}分${seconds}秒`;
    } else {
        const hours = Math.floor(elapsedSeconds / 3600);
        const minutes = Math.floor((elapsedSeconds % 3600) / 60);
        timeText = `${hours}小时${minutes}分`;
    }
    
    elapsedTimeElement.textContent = `已用时：${timeText}`;
};

// 推断当前分析步骤
HistoryReportsManager.prototype.inferCurrentStep = function(message, status) {
    const msg = message.toLowerCase();

    // 根据消息内容推断步骤
    // 处理后端返回的维度名称映射
    if (msg.includes('性格')) {
        return ANALYSIS_STEPS.find(s => s.key === 'personality_analysis') || ANALYSIS_STEPS[0];
    } else if (msg.includes('事业') || msg.includes('工作') || msg.includes('职业')) {
        return ANALYSIS_STEPS.find(s => s.key === 'career_analysis') || ANALYSIS_STEPS[1];
    } else if (msg.includes('感情') || msg.includes('婚姻')) {
        return ANALYSIS_STEPS.find(s => s.key === 'relationship_analysis') || ANALYSIS_STEPS[2];
    } else if (msg.includes('健康')) {
        return ANALYSIS_STEPS.find(s => s.key === 'health_analysis') || ANALYSIS_STEPS[3];
    } else if (msg.includes('运势') || msg.includes('大运') || msg.includes('流年') || msg.includes('2025')) {
        return ANALYSIS_STEPS.find(s => s.key === 'fortune_analysis') || ANALYSIS_STEPS[4];
    } else if (msg.includes('格局') || msg.includes('日主强弱')) {
        // 将"格局"和"日主强弱"映射到运势趋势分析
        return ANALYSIS_STEPS.find(s => s.key === 'fortune_analysis') || ANALYSIS_STEPS[4];
    } else if (msg.includes('学业') || msg.includes('文昌')) {
        // 将"学业"映射到性格深度分析
        return ANALYSIS_STEPS.find(s => s.key === 'personality_analysis') || ANALYSIS_STEPS[0];
    } else if (msg.includes('llm') || msg.includes('ai') || msg.includes('深度') || msg.includes('整合')) {
        return ANALYSIS_STEPS.find(s => s.key === 'llm_processing') || ANALYSIS_STEPS[5];
    } else if (msg.includes('生成') || msg.includes('完成')) {
        return ANALYSIS_STEPS.find(s => s.key === 'finalizing') || ANALYSIS_STEPS[6];
    }

    // 默认返回第一个步骤
    return ANALYSIS_STEPS[0];
};

// 后端维度到前端步骤的映射
const DIMENSION_TO_STEP_MAP = {
    '性格': 'personality_analysis',
    '事业': 'career_analysis',
    '职业': 'career_analysis',
    '感情': 'relationship_analysis',
    '健康': 'health_analysis',
    '运势': 'fortune_analysis',
    '2025运势': 'fortune_analysis',
    '格局': 'fortune_analysis',
    '日主强弱': 'fortune_analysis',
    '学业': 'personality_analysis'
};

// 根据后端维度计算前端步骤完成情况
HistoryReportsManager.prototype.calculateStepCompletion = function(completedDimensions = []) {
    const stepCompletion = {};

    // 初始化所有步骤为未完成
    ANALYSIS_STEPS.forEach(step => {
        stepCompletion[step.key] = { completed: false, count: 0 };
    });

    // 根据已完成的维度更新步骤状态
    completedDimensions.forEach(dimension => {
        const stepKey = DIMENSION_TO_STEP_MAP[dimension];
        if (stepKey && stepCompletion[stepKey]) {
            stepCompletion[stepKey].count++;
            stepCompletion[stepKey].completed = true;
        }
    });

    return stepCompletion;
};

// 更新进度步骤显示
HistoryReportsManager.prototype.updateProgressSteps = function(currentStepKey, completedItems = [], llmProgress = null) {
    const progressSteps = document.getElementById('progressSteps');
    if (!progressSteps) return;

    console.log('🔄 更新进度步骤:', { currentStepKey, completedItems, llmProgress });

    let stepsHtml = '<div class="progress-steps">';

    // 如果是LLM处理阶段，显示详细的维度进度
    if (currentStepKey === 'llm_processing' && llmProgress) {
        const completedDimensions = llmProgress.completed_dimensions || [];
        const currentDimension = llmProgress.current_dimension;
        const allDimensions = llmProgress.dimensions || [];

        console.log('📊 LLM进度详情:', { completedDimensions, currentDimension, allDimensions });

        // 显示每个分析维度的状态
        ANALYSIS_STEPS.slice(0, 5).forEach((step, index) => { // 前5个是分析步骤
            // 检查这个步骤对应的维度是否已完成
            const stepDimensions = this.getStepDimensions(step.key);
            const isCompleted = stepDimensions.some(dim => completedDimensions.includes(dim));
            const isCurrent = stepDimensions.includes(currentDimension);

            let stepClass = 'progress-step';
            if (isCompleted) stepClass += ' completed';
            else if (isCurrent) stepClass += ' current';

            let stepContent = step.name;
            const completedCount = stepDimensions.filter(dim => completedDimensions.includes(dim)).length;
            if (completedCount > 0) {
                stepContent += ` (${completedCount}/${stepDimensions.length})`;
            }

            stepsHtml += `
                <div class="${stepClass}">
                    <div class="step-indicator">${isCompleted ? '✅' : (isCurrent ? '⚡' : '○')}</div>
                    <div class="step-name">${stepContent}</div>
                </div>
            `;
        });

        // AI深度整合步骤
        const allAnalysisCompleted = completedDimensions.length >= allDimensions.length * 0.8; // 80%完成就开始整合
        stepsHtml += `
            <div class="progress-step ${allAnalysisCompleted ? 'current' : ''}">
                <div class="step-indicator">${allAnalysisCompleted ? '⚡' : '○'}</div>
                <div class="step-name">AI深度整合</div>
            </div>
        `;

        // 生成报告步骤
        stepsHtml += `
            <div class="progress-step">
                <div class="step-indicator">○</div>
                <div class="step-name">生成报告</div>
            </div>
        `;

    } else {
        // 常规步骤显示逻辑
        ANALYSIS_STEPS.forEach((step, index) => {
            const isCompleted = ANALYSIS_STEPS.findIndex(s => s.key === currentStepKey) > index;
            const isCurrent = step.key === currentStepKey;

            let stepClass = 'progress-step';
            if (isCompleted) stepClass += ' completed';
            if (isCurrent) stepClass += ' current';

            let stepContent = step.name;
            if (isCurrent && completedItems.length > 0) {
                stepContent += ` (${completedItems.length}项已完成)`;
            }

            stepsHtml += `
                <div class="${stepClass}">
                    <div class="step-indicator">${isCompleted ? '✅' : (isCurrent ? '⚡' : '○')}</div>
                    <div class="step-name">${stepContent}</div>
                </div>
            `;
        });
    }

    stepsHtml += '</div>';
    progressSteps.innerHTML = stepsHtml;

    console.log('✅ 进度步骤已更新');
};

// 获取步骤对应的维度列表
HistoryReportsManager.prototype.getStepDimensions = function(stepKey) {
    const stepToDimensions = {
        'personality_analysis': ['性格', '学业'],
        'career_analysis': ['事业', '职业'],
        'relationship_analysis': ['感情'],
        'health_analysis': ['健康'],
        'fortune_analysis': ['运势', '2025运势', '格局', '日主强弱']
    };

    return stepToDimensions[stepKey] || [];
};

// 更新调试信息
HistoryReportsManager.prototype.updateDebugInfo = function(info) {
    const debugContent = document.getElementById('debugContent');
    if (debugContent) {
        debugContent.textContent = JSON.stringify(info, null, 2);
    }

    // 保存最后一次状态到本地存储
    localStorage.setItem('lastAnalysisStatus', JSON.stringify(info));
};

// 查看当前结果（排盘信息）
function viewCurrentResults() {
    // 防止重复执行
    if (window.viewCurrentResultsExecuting) {
        console.log('⚠️ viewCurrentResults 正在执行中，忽略重复调用');
        return;
    }

    window.viewCurrentResultsExecuting = true;
    console.log('🔍 用户点击查看排盘结果按钮');

    let cardKey = null;

    // 尝试从URL参数获取卡密（适用于reports.html等页面）
    const urlParams = new URLSearchParams(window.location.search);
    cardKey = urlParams.get('cardKey');
    console.log('📋 从URL参数获取卡密:', cardKey);

    // 如果URL中没有卡密，尝试从输入框获取（适用于up.html页面）
    if (!cardKey) {
        const cardKeyInput = document.getElementById('cardKeyInput');
        if (cardKeyInput && cardKeyInput.value.trim()) {
            cardKey = cardKeyInput.value.trim();
            console.log('📝 从输入框获取卡密:', cardKey);
        }
    }

    // 如果还是没有卡密，尝试从全局变量获取
    if (!cardKey && window.currentCardKey) {
        cardKey = window.currentCardKey;
        console.log('🌐 从全局变量获取卡密:', cardKey);
    }

    if (!cardKey) {
        console.error('❌ 无法获取卡密信息');
        alert('无法获取卡密信息，请确保已输入有效的卡密');
        window.viewCurrentResultsExecuting = false;
        return;
    }

    console.log('✅ 最终使用的卡密:', cardKey);

    // 在新窗口中打开详细结果页面，使用渐进式加载
    const detailedUrl = `detailed_result.html?cardKey=${encodeURIComponent(cardKey)}&progressive=true`;
    console.log('🚀 打开详细结果页面:', detailedUrl);

    // 显示加载提示（在尝试打开窗口后）
    const viewBtn = document.querySelector('.view-results-btn');
    let originalText = '';
    if (viewBtn) {
        originalText = viewBtn.innerHTML;
        viewBtn.innerHTML = '🔄 加载中...';
        viewBtn.disabled = true; // 禁用按钮防止重复点击
        console.log('🔄 按钮状态已更新为加载中');
    }

    try {
        const newWindow = window.open(detailedUrl, '_blank');
        if (newWindow) {
            console.log('✅ 新窗口打开成功');

            // 恢复按钮状态
            if (viewBtn) {
                setTimeout(() => {
                    viewBtn.innerHTML = originalText;
                    viewBtn.disabled = false; // 重新启用按钮
                    window.viewCurrentResultsExecuting = false; // 重置执行状态
                    console.log('✅ 按钮状态已恢复');
                }, 1000);
            } else {
                // 如果没有按钮，直接重置执行状态
                setTimeout(() => {
                    window.viewCurrentResultsExecuting = false;
                }, 1000);
            }
        } else {
            console.error('❌ 新窗口打开失败，可能被浏览器阻止');
            alert('无法打开新窗口，请检查浏览器弹窗设置或直接访问：\n' + detailedUrl);

            // 立即恢复按钮状态
            if (viewBtn) {
                viewBtn.innerHTML = originalText;
                viewBtn.disabled = false; // 重新启用按钮
            }
            window.viewCurrentResultsExecuting = false; // 重置执行状态
        }
    } catch (error) {
        console.error('❌ 打开窗口时出错:', error);
        alert('打开窗口时出错：' + error.message);

        // 立即恢复按钮状态
        if (viewBtn) {
            viewBtn.innerHTML = originalText;
            viewBtn.disabled = false; // 重新启用按钮
        }
        window.viewCurrentResultsExecuting = false; // 重置执行状态
    }
}

// 确保函数在全局作用域中可用
window.viewCurrentResults = viewCurrentResults;

// 强制性的全局事件委托（确保按钮一定能点击）
document.addEventListener('click', function(e) {
    // 检查是否点击了查看排盘结果按钮
    const targetBtn = e.target.closest('.view-results-btn');
    if (targetBtn) {
        // 检查按钮是否被禁用
        if (targetBtn.disabled) {
            console.log('⚠️ 按钮已被禁用，忽略点击');
            return;
        }

        // 检查是否已经有onclick属性，如果有则不处理（避免重复执行）
        if (targetBtn.hasAttribute('onclick')) {
            console.log('🎯 按钮有onclick属性，由onclick处理');
            return;
        }

        e.preventDefault();
        e.stopPropagation();
        console.log('🎯 通过事件委托捕获到按钮点击');

        // 确保函数存在并执行
        if (typeof viewCurrentResults === 'function') {
            viewCurrentResults();
        } else {
            console.error('❌ viewCurrentResults 函数不存在');
            alert('功能暂时不可用，请刷新页面重试');
        }
    }
});

// 页面加载完成后绑定事件监听器（备用方案）
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 页面加载完成，检查查看排盘结果按钮');

    // 查找按钮并绑定事件
    const viewBtn = document.querySelector('.view-results-btn');
    if (viewBtn) {
        console.log('✅ 找到查看排盘结果按钮');

        // 检查按钮是否已经有onclick属性
        if (viewBtn.hasAttribute('onclick')) {
            console.log('🎯 按钮已有onclick属性，不重复绑定事件监听器');
            return;
        }

        // 检查是否已经绑定过事件
        if (viewBtn.hasAttribute('data-event-bound')) {
            console.log('🎯 按钮已绑定过事件监听器，跳过');
            return;
        }

        console.log('🔧 为按钮绑定事件监听器');

        // 添加新的事件监听器
        viewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🖱️ 按钮点击事件被触发');
            viewCurrentResults();
        });

        // 标记已绑定事件
        viewBtn.setAttribute('data-event-bound', 'true');

        // 添加鼠标悬停效果来确认按钮是可交互的
        viewBtn.addEventListener('mouseenter', function() {
            console.log('🖱️ 鼠标悬停在查看排盘结果按钮上');
        });

    } else {
        console.log('❌ 未找到查看排盘结果按钮');
    }
});

// 也可以通过MutationObserver监听动态添加的按钮
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const addedNodes = Array.from(mutation.addedNodes);
                addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const viewBtn = node.querySelector ? node.querySelector('.view-results-btn') : null;
                        if (viewBtn || (node.classList && node.classList.contains('view-results-btn'))) {
                            console.log('🔄 检测到动态添加的查看排盘结果按钮');
                            setTimeout(() => {
                                const btn = document.querySelector('.view-results-btn');
                                if (btn && !btn.hasAttribute('data-event-bound')) {
                                    btn.setAttribute('data-event-bound', 'true');
                                    btn.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        console.log('🖱️ 动态按钮点击事件被触发');
                                        viewCurrentResults();
                                    });
                                }
                            }, 100);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

} else {
    console.error('HistoryReportsManager 类未定义，无法扩展原型方法');
}