/**
 * @file 八字信息显示模块
 * @description 负责生成和显示八字信息部分的HTML
 */

/**
 * 生成八字信息部分的HTML
 * @returns {string} 八字信息HTML
 */
function generateBaziInfoSection() {
    console.log('🎨 [DEBUG] === generateBaziInfoSection 开始 ===');
    console.log('🎨 [DEBUG] this:', this);
    console.log('🎨 [DEBUG] this.currentData:', this ? this.currentData : 'this不存在');

    // 从当前数据中获取八字基本信息
    let baziInfo = null;
    try {
        console.log('🔍 [DEBUG] 开始获取八字信息...');
        // 从当前实例的数据中获取八字信息
        if (this && this.currentData) {
            console.log('✅ [DEBUG] 从 this.currentData 获取数据');
            console.log('📊 [DEBUG] this.currentData:', JSON.stringify(this.currentData, null, 2));
            const data = this.currentData;
            // 尝试从不同的数据结构中提取八字信息
            if (data.data && data.data.bz) {
                baziInfo = data.data;
            } else if (data.result && data.result.data && data.result.data.bz) {
                baziInfo = data.result.data;
            } else if (data.basicInfo) {
                baziInfo = data.basicInfo;
            } else if (data.bz) {
                // 直接从根级别获取八字信息
                baziInfo = data;
            }
        } else if (window.progressiveLoader && window.progressiveLoader.currentData) {
            console.log('✅ [DEBUG] 从 window.progressiveLoader.currentData 获取数据');
            console.log('📊 [DEBUG] progressiveLoader.currentData:', JSON.stringify(window.progressiveLoader.currentData, null, 2));
            const data = window.progressiveLoader.currentData;
            // 尝试从不同的数据结构中提取八字信息
            if (data.data && data.data.bz) {
                baziInfo = data.data;
            } else if (data.result && data.result.data && data.result.data.bz) {
                baziInfo = data.result.data;
            } else if (data.basicInfo) {
                baziInfo = data.basicInfo;
            } else if (data.bz) {
                // 直接从根级别获取八字信息
                baziInfo = data;
            }
        } else {
            console.log('⚠️ [DEBUG] 无法获取八字数据 - this和progressiveLoader都不可用');
        }
    } catch (e) {
        console.error('解析八字信息失败:', e);
    }
    
    if (!baziInfo || !baziInfo.bz) {
        return ''; // 如果没有八字信息，返回空字符串
    }
    
    // 提取八字四柱信息
    const yearGan = baziInfo.bz['0'] || '';
    const yearZhi = baziInfo.bz['1'] || '';
    const monthGan = baziInfo.bz['2'] || '';
    const monthZhi = baziInfo.bz['3'] || '';
    const dayGan = baziInfo.bz['4'] || '';
    const dayZhi = baziInfo.bz['5'] || '';
    const hourGan = baziInfo.bz['6'] || '';
    const hourZhi = baziInfo.bz['7'] || '';
    
    // 提取十神信息
    const shishenArray = baziInfo.ss || [];
    const yearShishen = shishenArray[0] || '';
    const monthShishen = shishenArray[1] || '';
    const dayShishen = '日主';
    const hourShishen = shishenArray[3] || '';
    
    // 提取性别信息
    let genderText = '';
    if (baziInfo.sex !== undefined) {
        genderText = baziInfo.sex === 1 ? '男' : '女';
    } else if (baziInfo.gender) {
        genderText = baziInfo.gender === '1' || baziInfo.gender === 'male' ? '男' : '女';
    }
    
    // 提取出生时间信息
    let birthTimeText = '';
    if (baziInfo.bz && baziInfo.bz['8']) {
        birthTimeText = baziInfo.bz['8'];
    }
    
    // 生成地支藏干表格
    const generateCangganTable = (pillarIndex) => {
        if (!baziInfo.cg || !baziInfo.cg[pillarIndex] || !baziInfo.cgss || !baziInfo.cgss[pillarIndex]) {
            return '';
        }
        
        let cangganHtml = '';
        const pillarCanggan = baziInfo.cg[pillarIndex];
        const pillarCangganShishen = baziInfo.cgss[pillarIndex];
        
        for (let i = 0; i < pillarCanggan.length; i++) {
            const gan = pillarCanggan[i];
            const ss = pillarCangganShishen[i] || '';
            cangganHtml += `<tr><td class="detailed-canggan-cell">
                <span class="canggan-gan" style="color: ${getTianganColor(gan)}">${gan}</span>
                <span class="canggan-ss">${ss}</span>
            </td></tr>`;
        }
        
        return `
            <div class="detailed-canggan-wrapper">
                <div class="detailed-canggan-label">藏干</div>
                <table class="detailed-canggan-table">
                    <tbody>${cangganHtml}</tbody>
                </table>
            </div>
        `;
    };
    
    // 生成八字表格HTML
    const baziTableHtml = `
        <div class="bazi-table-container">
            <table class="detailed-bazi-table">
                <thead>
                    <tr>
                        <th>年柱</th>
                        <th>月柱</th>
                        <th>日柱</th>
                        <th>时柱</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="detailed-pillar-cell">
                            <div class="detailed-bazi-cell">
                                <span class="detailed-shishen">${yearShishen}</span>
                                <div class="detailed-tiangan" style="color: ${getTianganColor(yearGan)}">${yearGan}</div>
                            </div>
                            <div class="detailed-dizhi" style="color: ${getDizhiColor(yearZhi)}">${yearZhi}</div>
                            ${generateCangganTable(0)}
                        </td>
                        <td class="detailed-pillar-cell">
                            <div class="detailed-bazi-cell">
                                <span class="detailed-shishen">${monthShishen}</span>
                                <div class="detailed-tiangan" style="color: ${getTianganColor(monthGan)}">${monthGan}</div>
                            </div>
                            <div class="detailed-dizhi" style="color: ${getDizhiColor(monthZhi)}">${monthZhi}</div>
                            ${generateCangganTable(1)}
                        </td>
                        <td class="detailed-pillar-cell">
                            <div class="detailed-bazi-cell">
                                <span class="detailed-shishen">${dayShishen}</span>
                                <div class="detailed-tiangan" style="color: ${getTianganColor(dayGan)}">${dayGan}</div>
                            </div>
                            <div class="detailed-dizhi" style="color: ${getDizhiColor(dayZhi)}">${dayZhi}</div>
                            ${generateCangganTable(2)}
                        </td>
                        <td class="detailed-pillar-cell">
                            <div class="detailed-bazi-cell">
                                <span class="detailed-shishen">${hourShishen}</span>
                                <div class="detailed-tiangan" style="color: ${getTianganColor(hourGan)}">${hourGan}</div>
                            </div>
                            <div class="detailed-dizhi" style="color: ${getDizhiColor(hourZhi)}">${hourZhi}</div>
                            ${generateCangganTable(3)}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
    
    // 构建神煞表格
    let shenshaTableHtml = '';
    if (baziInfo.shensha && baziInfo.shensha.length > 0) {
        const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
        let shenshaRows = '';
        
        for (let i = 0; i < Math.min(baziInfo.shensha.length, 4); i++) {
            const pillarShensha = baziInfo.shensha[i];
            if (pillarShensha && pillarShensha.length > 0) {
                shenshaRows += `
                    <tr>
                        <td><strong>${pillarNames[i]}</strong></td>
                        <td>${pillarShensha.join('、')}</td>
                    </tr>
                `;
            }
        }
        
        if (shenshaRows) {
            shenshaTableHtml = `
                <div class="detailed-shensha-section">
                    <h4>神煞</h4>
                    <div class="detailed-shensha-container">
                        <table class="detailed-shensha-table">
                            <thead>
                                <tr>
                                    <th>柱位</th>
                                    <th>神煞</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${shenshaRows}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    }
    
    // 构建大运表格
    let dayunTableHtml = '';
    if (baziInfo.dayun && baziInfo.dayun.length > 0) {
        let dayunRows = '';
        
        // 从数据中获取起运岁数，如果没有则默认为5岁
        let startAge = 5;
        if (baziInfo.qiyunsui) {
            // qiyunsui 就是起运岁数（虚岁）
            startAge = parseInt(baziInfo.qiyunsui) || 5;
        }
        
        // 计算当前年龄（基于当前时间）
        const currentYear = new Date().getFullYear();
        let birthYear = currentYear - 30; // 默认值
        
        // 尝试从八字信息中提取出生年份
        if (baziInfo.bz && baziInfo.bz[8]) {
            const birthTimeStr = baziInfo.bz[8];
            const yearMatch = birthTimeStr.match(/(\d{4})年/);
            if (yearMatch) {
                birthYear = parseInt(yearMatch[1]);
            }
        }
        
        const currentAge = currentYear - birthYear;
        
        for (let i = 0; i < Math.min(baziInfo.dayun.length, 10); i++) {
            const dayunPillar = baziInfo.dayun[i];
            const dayunStartAge = startAge + i * 10;
            const dayunEndAge = startAge + i * 10 + 9;
            const ageRange = `${dayunStartAge}-${dayunEndAge}岁`;
            
            // 判断是否为当前大运
            const isCurrentDayun = currentAge >= dayunStartAge && currentAge <= dayunEndAge;
            const currentClass = isCurrentDayun ? ' class="current-dayun"' : '';
            
            // 查找对应的大运神煞
            let dayunShenshaText = '';
            if (baziInfo.dyshensha && baziInfo.dyshensha.length > i) {
                const dayunShenshaItem = baziInfo.dyshensha[i];
                if (dayunShenshaItem && dayunShenshaItem[1] && dayunShenshaItem[1].length > 0) {
                    dayunShenshaText = dayunShenshaItem[1].join('、');
                }
            }
            
            dayunRows += `
                <tr${currentClass}>
                    <td>${i + 1}</td>
                    <td><strong>${dayunPillar}</strong></td>
                    <td>${ageRange}</td>
                    <td>${dayunShenshaText}</td>
                </tr>
            `;
        }
        
        if (dayunRows) {
            // 添加起运信息
            const qiyunInfo = baziInfo.qiyunsui ? `<p class="qiyun-info" style="padding: 10px 15px !important; border-radius: 8px !important; margin: 10px 0 !important; text-shadow: none !important; font-weight: 500 !important; border: 1px solid rgba(96, 165, 250, 0.4) !important; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;">起运岁数（虚岁）: <span style="color: #000000 !important; font-weight: 500 !important; text-shadow: none !important;">${baziInfo.qiyunsui}</span>，约<span style="color: #000000 !important; font-weight: 500 !important; text-shadow: none !important;">${startAge}</span>岁开始行大运</p>` : '';
            
            dayunTableHtml = `
                <div class="detailed-dayun-section">
                    <h4>大运</h4>
                    ${qiyunInfo}
                    <div class="detailed-dayun-container">
                        <table class="detailed-dayun-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>大运</th>
                                    <th>年龄范围</th>
                                    <th>神煞</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${dayunRows}
                            </tbody>
                        </table>
                    </div>
                    <p class="current-dayun-note">高亮行为当前所在大运</p>
                </div>
            `;
        }
    }
    
    // 生成基本信息HTML
    const qiyunText = baziInfo.qiyunsui ? `起运岁数: ${baziInfo.qiyunsui}（虚岁）` : '';
    const basicInfoHtml = `
        <div class="detailed-bazi-basic-info">
            ${genderText ? `<div class="detailed-bazi-info-item"><strong>性别：</strong><span class="info-value">${genderText}</span></div>` : ''}
            ${birthTimeText ? `<div class="detailed-bazi-info-item"><strong>出生时间：</strong><span class="info-value">${birthTimeText}</span></div>` : ''}
        </div>
    `;
    
    // 生成完整的八字信息HTML
    return `
        <div class="detailed-dimension-section detailed-bazi-info-section">
            <div class="detailed-dimension-header active">
                <div class="detailed-dimension-title">
                    <span class="detailed-dimension-icon">☯</span>
                    命主八字信息
                </div>
                <span class="detailed-toggle-icon">▼</span>
            </div>
            <div class="detailed-dimension-content active">
                ${basicInfoHtml}
                ${baziTableHtml}
                ${shenshaTableHtml}
                ${dayunTableHtml}
            </div>
        </div>
    `;
}

/**
 * 从wuxing_colors.js导入
 * 获取天干的显示颜色
 * @param {string} gan - 天干
 * @returns {string} 颜色代码
 */
function getTianganColor(gan) {
    if (!gan) return '#333333';
    const colorMap = {
        '甲': '#00a65a', // 绿色 (木)
        '乙': '#00a65a', // 绿色 (木)
        '丙': '#dd4b39', // 红色 (火)
        '丁': '#dd4b39', // 红色 (火)
        '戊': '#ffad4a', // 黄色 (土)
        '己': '#ffad4a', // 黄色 (土)
        '庚': '#bfbfbf', // 银灰色 (金)
        '辛': '#bfbfbf', // 银灰色 (金)
        '壬': '#3c8dbc', // 蓝色 (水)
        '癸': '#3c8dbc'  // 蓝色 (水)
    };
    return colorMap[gan] || '#333333';
}

/**
 * 从wuxing_colors.js导入
 * 获取地支的显示颜色
 * @param {string} zhi - 地支
 * @returns {string} 颜色代码
 */
function getDizhiColor(zhi) {
    if (!zhi) return '#333333';
    const colorMap = {
        '寅': '#00a65a', // 绿色 (木)
        '卯': '#00a65a', // 绿色 (木)
        '巳': '#dd4b39', // 红色 (火)
        '午': '#dd4b39', // 红色 (火)
        '辰': '#ffad4a', // 黄色 (土)
        '丑': '#ffad4a', // 黄色 (土)
        '未': '#ffad4a', // 黄色 (土)
        '戌': '#ffad4a', // 黄色 (土)
        '申': '#bfbfbf', // 银灰色 (金)
        '酉': '#bfbfbf', // 银灰色 (金)
        '亥': '#3c8dbc', // 蓝色 (水)
        '子': '#3c8dbc'  // 蓝色 (水)
    };
    return colorMap[zhi] || '#333333';
}

// 导出到全局作用域，供渐进式加载器使用
if (typeof window !== 'undefined') {
    window.generateBaziInfoSection = generateBaziInfoSection;
    window.getTianganColor = getTianganColor;
    window.getDizhiColor = getDizhiColor;
}