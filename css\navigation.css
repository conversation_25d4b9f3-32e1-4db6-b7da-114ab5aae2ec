/* 导航链接 */
.nav-link {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(99, 102, 241, 0.1);
    border-radius: var(--radius-medium, 8px);
    color: #6366f1;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(99, 102, 241, 0.2);
    margin-bottom: 16px;
}

.nav-link:hover {
    background: rgba(99, 102, 241, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.nav-link span {
    margin-left: 4px;
}

/* 次级导航 */
.sub-nav {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin: 24px 0;
}

.sub-nav-link {
    padding: 6px 14px;
    background: transparent;
    border-radius: var(--radius-medium, 8px);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.sub-nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.sub-nav-link.active {
    background: rgba(99, 102, 241, 0.2);
    color: #6366f1;
    border-color: rgba(99, 102, 241, 0.3);
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .nav-link {
        background: rgba(99, 102, 241, 0.15);
        color: #a5b4fc;
        border-color: rgba(99, 102, 241, 0.3);
    }
    
    .nav-link:hover {
        background: rgba(99, 102, 241, 0.25);
        color: #c7d2fe;
    }
    
    .sub-nav-link {
        color: rgba(229, 231, 235, 0.8);
    }
    
    .sub-nav-link:hover {
        color: #f9fafb;
    }
    
    .sub-nav-link.active {
        background: rgba(99, 102, 241, 0.25);
        color: #a5b4fc;
    }
} 