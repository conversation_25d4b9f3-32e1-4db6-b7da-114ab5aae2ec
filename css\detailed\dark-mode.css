/* 暗黑模式样式 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: #f8fafc;
    }
    
    .detailed-result-page {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: #f8fafc;
    }
    
    .detailed-container {
        background: rgba(15, 23, 42, 0.85);
    }
    
    /* 重新定义暗色模式下的CSS变量 */
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #f1f5f9;
        --text-light: #e2e8f0;
        --text-muted: #94a3b8;
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --bg-card: #1e293b;
        --bg-overlay: rgba(15, 23, 42, 0.85);
        --bg-glass: rgba(15, 23, 42, 0.95);
        --border-color: #334155;
        --border-color-light: #1e293b;
        --border-color-dark: #475569;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 8px -1px rgba(0, 0, 0, 0.5), 0 2px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 12px 20px -3px rgba(0, 0, 0, 0.5), 0 6px 10px -2px rgba(0, 0, 0, 0.4);
        --shadow-xl: 0 25px 30px -5px rgba(0, 0, 0, 0.6), 0 12px 15px -5px rgba(0, 0, 0, 0.5);
        /* 添加背景相关变量覆盖 */
        --background-light: #0f172a;
        /* 为了更好的视觉体验，更新渐变�?*/
        --hero-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
        --card-gradient: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
    }
    
    /* 文本内容暗色模式调整 */
    .detailed-dimension-text h1,
    .detailed-dimension-text h2,
    .detailed-dimension-text h3,
    .detailed-dimension-text h4,
    .detailed-dimension-text h5,
    .detailed-dimension-text h6 {
        color: #f8fafc;
        font-weight: 700;
    }
    
    .detailed-dimension-text strong {
        color: #f8fafc;
        font-weight: 700;
    }
    
    .detailed-dimension-text em {
        color: #cbd5e1;
    }
    
    .detailed-dimension-text a {
        color: #60a5fa;
    }
    
    .detailed-dimension-text a:hover {
        border-bottom-color: #60a5fa;
    }
    
    .detailed-dimension-text hr {
        border-top-color: #334155;
    }
    
    .detailed-dimension-text th,
    .detailed-dimension-text td {
        border-bottom-color: #334155;
    }
    
    .detailed-dimension-text th {
        background-color: #1e293b;
        color: #f8fafc;
        font-weight: 700;
    }
    
    .detailed-dimension-text blockquote {
        border-left-color: #60a5fa;
        background-color: #1e293b;
        color: #f1f5f9;
    }
    
    .detailed-dimension-text code {
        background-color: #334155;
        color: #fcd34d;
        font-weight: 600;
    }
    
    .detailed-dimension-text pre {
         background-color: #0f172a;
         color: #ffffff;
     }
     
     /* 按钮和卡片暗色模式调�?*/
     .detailed-edit-btn {
         background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
         color: #ffffff;
         box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
         border: none;
     }
     
     .detailed-edit-btn:hover {
         background: linear-gradient(135deg, #475569 0%, #334155 100%);
         box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
         transform: translateY(-2px);
     }
     
     .detailed-download-btn {
         color: #ffffff;
     }
     
     .detailed-back-btn {
         color: #ffffff;
     }
     
     .detailed-dimension-section {
         border-color: #334155;
         background: #1e293b;
     }
     
     .detailed-dimension-section:hover {
         border-color: #60a5fa;
         box-shadow: 0 6px 12px -1px rgba(0, 0, 0, 0.5);
     }
     
     .detailed-dimension-header {
         background: #1e293b;
         border-bottom-color: #334155;
         color: #f1f5f9;
     }
     
     .detailed-dimension-header:hover {
         background: #334155;
     }
     
     .detailed-dimension-header.active {
         color: #ffffff;
         background: #334155;
     }
     
     .detailed-result-container {
         background: #0f172a;
     }
     
     .detailed-analysis-content {
         background: #0f172a;
         color: #f1f5f9;
     }
     
     /* 修复标题样式在深色模式下的问�?*/
     h1, h2, h3, h4, h5, h6 {
         color: #f8fafc !important;
     }
     
     /* 专门处理带有背景渐变和文字裁剪的标题 */
     [class*="section"] h4,
     [class*="section"] h3,
     .detailed-dayun-section h4,
     .qiyun-info h4,
     h4[style*="background"],
     h3[style*="background"] {
         background: linear-gradient(135deg, #60a5fa, #818cf8, #a5b4fc) !important;
         -webkit-background-clip: text !important;
         -webkit-text-fill-color: transparent !important;
         background-clip: text !important;
         color: #60a5fa !important; /* 备用颜色，防止背景裁剪不生效 */
     }
     
     /* 全局强制覆盖白色背景 */
     [style*="background: white"],
     [style*="background: #fff"],
     [style*="background: #ffffff"],
     [style*="background-color: white"],
     [style*="background-color: #fff"],
     [style*="background-color: #ffffff"],
     [style*="background:#fff"],
     [style*="background:#ffffff"],
     [style*="background-color:#fff"],
     [style*="background-color:#ffffff"],
     div[style*="background"],
     span[style*="background"],
     p[style*="background"] {
         background: rgba(15, 23, 42, 0.8) !important;
         background-color: rgba(15, 23, 42, 0.8) !important;
         color: #f1f5f9 !important;
     }
     
     /* 全局强制覆盖白色文本颜色 - 排除特定元素 */
     [style*="color: white"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.action-btn),
     [style*="color: #fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.action-btn),
     [style*="color: #ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.action-btn),
     [style*="color:#fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.action-btn),
     [style*="color:#ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.action-btn) {
         color: #0f172a !important;
         background-color: #f1f5f9 !important;
         padding: 0 4px !important;
         border-radius: 4px !important;
     }

     /* 确保特定元素的白色文字不被覆盖 */
     .force-complete-btn,
     .item-title:hover .title-text,
     .item-title:hover .title-icon,
     .item-title:hover .title-level,
     .item-title:hover * {
         color: white !important;
         background-color: transparent !important;
         padding: initial !important;
         -webkit-text-fill-color: white !important;
         text-fill-color: white !important;
     }

     /* 确保 action-btn.secondary 按钮样式不被覆盖 */
     .action-btn.secondary {
         background: white !important;
         color: var(--primary-color) !important;
         -webkit-text-fill-color: var(--primary-color) !important;
         text-fill-color: var(--primary-color) !important;
     }

     .action-btn.secondary:hover {
         background: rgba(102, 126, 234, 0.05) !important;
         color: var(--primary-color) !important;
         -webkit-text-fill-color: var(--primary-color) !important;
         text-fill-color: var(--primary-color) !important;
     }
     
     /* 命主八字信息暗色模式样式 */
     .detailed-bazi-basic-info {
         background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95));
         border-color: rgba(96, 165, 250, 0.4);
         box-shadow: 0 12px 45px rgba(0, 0, 0, 0.3), 0 6px 16px rgba(96, 165, 250, 0.2);
         backdrop-filter: blur(10px);
     }
     
     .detailed-bazi-basic-info::after {
         opacity: 0.2;
     }
     
     .detailed-bazi-info-item {
         background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95));
         color: #000000;
         font-weight: 500;
         text-shadow: none;
     }
     
     .detailed-bazi-info-item strong {
         color: #000000;
         font-weight: 700;
     }
     
     .detailed-bazi-info-item .info-value {
         color: #000000;
         font-weight: 500;
     }
     
     .basic-info-item {
         background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95));
         border-color: rgba(51, 65, 85, 0.7);
         box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
     }
     
     .basic-info-item:hover {
         background: linear-gradient(135deg, rgba(245, 245, 245, 0.98), rgba(255, 255, 255, 0.98));
         border-color: rgba(96, 165, 250, 0.5);
         box-shadow: 0 10px 30px rgba(96, 165, 250, 0.25);
     }
     
     .info-label {
         color: #000000;
         font-weight: 600;
     }
     
     .info-value {
         color: #000000;
         font-weight: 500;
     }
     
     /* 性别、出生时间等基本信息文本颜色修复 */
     .basic-info-item *,
     .basic-info-item span,
     .basic-info-item div,
     .basic-info-item p,
     .info-value *,
     .info-value span,
     .info-value div,
     .info-value p,
     .detailed-bazi-info-item *,
     .detailed-bazi-info-item span,
     .detailed-bazi-info-item div,
     .detailed-bazi-info-item p {
         color: #000000 !important;
         background-color: transparent !important;
         -webkit-text-fill-color: #000000 !important;
         text-shadow: none !important;
         -webkit-background-clip: initial !important;
         background-clip: initial !important;
     }
     
     /* 八字表格暗色模式 */
     .detailed-pillar-cell {
         background-color: #1e293b;
         border-color: rgba(96, 165, 250, 0.4);
         box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
     }
     
     .detailed-pillar-cell:hover {
         border-color: var(--primary-color);
         box-shadow: 0 10px 25px rgba(0, 0, 0, 0.35);
     }
     
    /* Remove day pillar special styles */
     
     .detailed-bazi-cell {
         background-color: rgba(96, 165, 250, 0.25);
         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
     }
     
     /* 藏干表格暗色模式 */
     .detailed-canggan-table {
         background-color: rgba(15, 23, 42, 0.75);
         border-color: rgba(51, 65, 85, 0.6);
     }
     
     .detailed-canggan-cell {
         background: linear-gradient(135deg, #0f172a, #1e293b);
         border-color: rgba(96, 165, 250, 0.4);
         color: #f1f5f9;
     }
     
     .detailed-canggan-cell:hover {
         background: linear-gradient(135deg, #1e293b, #334155);
         border-color: rgba(96, 165, 250, 0.6);
     }
     
     .canggan-gan, 
     .canggan-ss {
         color: #f8fafc;
     }
     
     /* 起运信息和大运说明暗色模�?*/
     .qiyun-info {
         background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
         border-color: rgba(96, 165, 250, 0.4) !important;
         color: #000000 !important;
         font-weight: 500 !important;
         box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
         padding: 10px 15px !important;
         border-radius: 8px !important;
         margin: 10px 0 !important;
     }
     
     .qiyun-info::before {
         background-color: rgba(96, 165, 250, 0.1) !important;
         opacity: 0.2 !important;
     }
     
     .qiyun-info strong {
         color: #000000 !important;
         font-weight: 700 !important;
         text-shadow: none !important;
     }
     
     /* 特别处理起运信息文字元素，确保没有白色背�?*/
     .qiyun-info *:not(h4):not(h3),
     .qiyun-info p,
     .qiyun-info span,
     .qiyun-info div {
         background: transparent !important;
         color: #000000 !important;
         text-shadow: none !important;
         -webkit-background-clip: initial !important;
         -webkit-text-fill-color: #000000 !important;
         background-clip: initial !important;
         font-weight: 500 !important;
     }
     
     /* 专门处理起运岁数文本，无论出现在何处 */
     .qiyun-info,
     [class*="qiyun"],
     [class*="dayun"] .qiyun-info,
     .detailed-dayun-section .qiyun-info,
     .detailed-bazi-info-item:last-child .info-value,
     .detailed-bazi-info-item:nth-last-child(1) .info-value {
         color: #000000 !important;
         background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
         -webkit-text-fill-color: #000000 !important;
         text-shadow: none !important;
         font-weight: 500 !important;
     }
     
     /* 确保深色模式下的年龄范围和起运岁数信息显示正�?*/
     .detailed-dayun-table tr td:nth-child(3) {
         color: #f1f5f9 !important;
         font-weight: 500 !important;
     }
     
     /* 修复起运信息标题的渐变文�?*/
     .qiyun-info h4[style*="background"],
     .qiyun-info h3[style*="background"] {
         background: linear-gradient(135deg, #1e40af, #3b82f6, #60a5fa) !important;
         -webkit-background-clip: text !important;
         background-clip: text !important;
         -webkit-text-fill-color: transparent !important;
         text-fill-color: transparent !important;
     }
     
     /* 兜底确保文字可见 */
     .qiyun-info h4:not([style*="background"]),
     .qiyun-info h3:not([style*="background"]) {
         color: #1e40af !important;
         -webkit-text-fill-color: #1e40af !important;
         text-fill-color: #1e40af !important;
         font-weight: 700;
     }
     
     .current-dayun-note {
         background: rgba(96, 165, 250, 0.15);
         border-color: rgba(96, 165, 250, 0.25);
         color: #cbd5e1;
     }
     
     /* 通用背景覆盖 - 防止白底白字 */
     .detailed-result-page *:not(input):not(select):not(button):not(.btn):not([class*="bg-white"]) {
         background-color: transparent;
     }
     
     /* 强制所有容器背景为透明或深�?*/
     .container,
     .card,
     .section,
     .detailed-section,
     .detailed-card,
     .detailed-bazi-section,
     [class*="detailed-"] {
         background-color: transparent !important;
     }
     
     /* 强制所有文本容器内元素的背景透明 */
     [class*="info"] *,
     [class*="text"] *,
     [class*="note"] *,
     [class*="description"] * {
         background-color: transparent !important;
     }
     
     /* 全局文本颜色修复 - 确保所有文本在暗色背景下可�?*/
     [class*="info"] *:not(.wuxing-jin):not(.wuxing-mu):not(.wuxing-shui):not(.wuxing-huo):not(.wuxing-tu):not(.wuxing-kong):not(.detailed-tiangan):not(.detailed-dizhi):not(.canggan-gan),
     [class*="text"] *:not(.wuxing-jin):not(.wuxing-mu):not(.wuxing-shui):not(.wuxing-huo):not(.wuxing-tu):not(.wuxing-kong):not(.detailed-tiangan):not(.detailed-dizhi):not(.canggan-gan),
     [class*="note"] *:not(.wuxing-jin):not(.wuxing-mu):not(.wuxing-shui):not(.wuxing-huo):not(.wuxing-tu):not(.wuxing-kong):not(.detailed-tiangan):not(.detailed-dizhi):not(.canggan-gan),
     [class*="description"] *:not(.wuxing-jin):not(.wuxing-mu):not(.wuxing-shui):not(.wuxing-huo):not(.wuxing-tu):not(.wuxing-kong):not(.detailed-tiangan):not(.detailed-dizhi):not(.canggan-gan) {
         color: #f1f5f9 !important;
         -webkit-text-fill-color: initial !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
     
     /* 特殊元素颜色覆盖 */
     h1, h2, h3, h4, h5, h6, 
     strong, b {
         color: #f8fafc !important;
         -webkit-text-fill-color: initial !important;
     }
     
     /* 对于使用了背景渐变的特殊文本，保留其渐变效果 */
     [style*="background-clip: text"],
     [style*="-webkit-background-clip: text"] {
         background: linear-gradient(135deg, #60a5fa, #818cf8, #a5b4fc) !important;
         -webkit-background-clip: text !important;
         background-clip: text !important;
         -webkit-text-fill-color: transparent !important;
     }
     
     /* 修复大运表格的深色模式问�?*/
     .detailed-dayun-table {
         background-color: transparent !important;
         box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25) !important;
     }
     
     .detailed-dayun-table th {
         background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
         color: #ffffff !important;
         border: 1px solid rgba(59, 130, 246, 0.3) !important;
     }
     
     .detailed-dayun-table td {
         background-color: rgba(15, 23, 42, 0.75) !important;
         color: #f1f5f9 !important;
         border: 1px solid rgba(51, 65, 85, 0.4) !important;
     }
     
     .detailed-dayun-table tbody tr:nth-child(even) td {
         background-color: rgba(30, 41, 59, 0.75) !important;
     }
     
     .detailed-dayun-table tbody tr.current-dayun td {
         background-color: rgba(59, 130, 246, 0.2) !important;
         color: #f8fafc !important;
         font-weight: 600 !important;
     }
     
     /* 五行颜色在暗黑模式下的样�?- 确保与移动端保持一�?*/
     /* 这些样式直接应用于带有五行类的元�?*/
     html body .wuxing-jin,
     html body .detailed-result-page .wuxing-jin {
         color: #ffffff !important;
         background-color: rgb(231, 144, 15) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     html body .wuxing-mu,
     html body .detailed-result-page .wuxing-mu {
         color: #ffffff !important;
         background-color: rgb(13, 230, 51) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     html body .wuxing-shui,
     html body .detailed-result-page .wuxing-shui {
         color: #ffffff !important;
         background-color: rgb(49, 131, 239) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     html body .wuxing-huo,
     html body .detailed-result-page .wuxing-huo {
         color: #ffffff !important;
         background-color: rgb(211, 6, 5) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     html body .wuxing-tu,
     html body .detailed-result-page .wuxing-tu {
         color: #ffffff !important;
         background-color: rgb(135, 109, 2) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     html body .wuxing-kong,
     html body .detailed-result-page .wuxing-kong {
         color: #0f172a !important;
         background-color: rgb(255, 255, 255) !important;
         text-shadow: none !important;
     }
     
     .nayin-text {
         color: #f1f5f9 !important;
         background-color: transparent !important;
     }
     
     /* 确保所有藏干和地支的五行标记显示正�?*/
     .tiangan-wuxing,
     .dizhi-wuxing,
     .canggan-wuxing {
         color: #ffffff !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
     }
     
     /* 八字表格天干地支颜色覆盖 - 按照五行颜色规范 - 提高优先�?*/
     /* �?*/
     html body .detailed-bazi-table .detailed-tiangan[style*="color: #dd4b39"],
     html body .detailed-bazi-table .detailed-dizhi[style*="color: #dd4b39"],
     html body .detailed-bazi-table .canggan-gan[style*="color: #dd4b39"],
     html body .detailed-tiangan[style*="color: #dd4b39"],
     html body .detailed-dizhi[style*="color: #dd4b39"],
     html body .canggan-gan[style*="color: #dd4b39"] {
         color: rgb(211, 6, 5) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
         -webkit-text-fill-color: rgb(211, 6, 5) !important;
         background-image: none !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
     
     /* �?*/
     html body .detailed-bazi-table .detailed-tiangan[style*="color: #ffad4a"],
     html body .detailed-bazi-table .detailed-dizhi[style*="color: #ffad4a"],
     html body .detailed-bazi-table .canggan-gan[style*="color: #ffad4a"],
     html body .detailed-tiangan[style*="color: #ffad4a"],
     html body .detailed-dizhi[style*="color: #ffad4a"],
     html body .canggan-gan[style*="color: #ffad4a"] {
         color: rgb(135, 109, 2) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
         -webkit-text-fill-color: rgb(135, 109, 2) !important;
         background-image: none !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
     
     /* �?*/
     html body .detailed-bazi-table .detailed-tiangan[style*="color: #bfbfbf"],
     html body .detailed-bazi-table .detailed-dizhi[style*="color: #bfbfbf"],
     html body .detailed-bazi-table .canggan-gan[style*="color: #bfbfbf"],
     html body .detailed-tiangan[style*="color: #bfbfbf"],
     html body .detailed-dizhi[style*="color: #bfbfbf"],
     html body .canggan-gan[style*="color: #bfbfbf"] {
         color: rgb(231, 144, 15) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
         -webkit-text-fill-color: rgb(231, 144, 15) !important;
         background-image: none !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
     
     /* �?*/
     html body .detailed-bazi-table .detailed-tiangan[style*="color: #3c8dbc"],
     html body .detailed-bazi-table .detailed-dizhi[style*="color: #3c8dbc"],
     html body .detailed-bazi-table .canggan-gan[style*="color: #3c8dbc"],
     html body .detailed-tiangan[style*="color: #3c8dbc"],
     html body .detailed-dizhi[style*="color: #3c8dbc"],
     html body .canggan-gan[style*="color: #3c8dbc"] {
         color: rgb(49, 131, 239) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
         -webkit-text-fill-color: rgb(49, 131, 239) !important;
         background-image: none !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
     
     /* �?*/
     html body .detailed-bazi-table .detailed-tiangan[style*="color: #00a65a"],
     html body .detailed-bazi-table .detailed-dizhi[style*="color: #00a65a"],
     html body .detailed-bazi-table .canggan-gan[style*="color: #00a65a"],
     html body .detailed-tiangan[style*="color: #00a65a"],
     html body .detailed-dizhi[style*="color: #00a65a"],
     html body .canggan-gan[style*="color: #00a65a"] {
         color: rgb(13, 230, 51) !important;
         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
         -webkit-text-fill-color: rgb(13, 230, 51) !important;
         background-image: none !important;
         background-clip: initial !important;
         -webkit-background-clip: initial !important;
     }
}

/* 移动端暗色模式特殊处�?*/
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    .detailed-dimension-content {
        background: #0f172a !important;
        color: #f1f5f9 !important;
    }
    
    .detailed-result-container {
        background: #0f172a !important;
    }
    
    .detailed-dimension-text {
        color: #f1f5f9 !important;
    }
    
    .detailed-dimension-text h1,
    .detailed-dimension-text h2,
    .detailed-dimension-text h3,
    .detailed-dimension-text h4,
    .detailed-dimension-text h5,
    .detailed-dimension-text h6 {
        color: #f8fafc !important;
    }
    
    .detailed-dimension-text p {
        color: #cbd5e1 !important;
    }
    
    .detailed-dimension-text strong {
        color: #f8fafc !important;
    }
    
    .detailed-dimension-text em {
        color: #cbd5e1 !important;
    }
    
    /* 确保所有文本元素都有正确的颜色 */
    .detailed-dimension-text * {
        color: inherit !important;
    }
    
    /* 特殊元素的颜色覆�?*/
    .detailed-dimension-text a {
        color: #60a5fa !important;
    }
    
    .detailed-dimension-text code {
        background-color: #334155 !important;
        color: #fcd34d !important;
    }
    
    /* 移动端命主八字信息暗色模式加�?*/
    .detailed-bazi-basic-info {
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        border-color: rgba(96, 165, 250, 0.4) !important;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
    }
    
    .detailed-bazi-info-item {
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        color: #000000 !important;
        font-weight: 500 !important;
        text-shadow: none !important;
    }
    
    .detailed-bazi-info-item strong {
        color: #000000 !important;
        font-weight: 700 !important;
    }
    
    .detailed-bazi-info-item .info-value {
        color: #000000 !important;
        font-weight: 500 !important;
        background-color: transparent !important;
        -webkit-text-fill-color: #000000 !important;
        -webkit-background-clip: initial !important;
        background-clip: initial !important;
    }
    
    /* 强制修复所有性别和出生时间等基本信息文本颜色 */
    .detailed-bazi-info-item *,
    .detailed-bazi-info-item span,
    .detailed-bazi-info-item div,
    .detailed-bazi-info-item p,
    .detailed-bazi-basic-info *:not(h4):not(h3):not(.detailed-tiangan):not(.detailed-dizhi):not(.canggan-gan):not(.wuxing-jin):not(.wuxing-mu):not(.wuxing-shui):not(.wuxing-huo):not(.wuxing-tu):not(.wuxing-kong) {
        color: #000000 !important;
        background-color: transparent !important;
        -webkit-text-fill-color: #000000 !important;
        text-shadow: none !important;
        -webkit-background-clip: initial !important;
        background-clip: initial !important;
    }
    
    .basic-info-item {
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        border-color: rgba(51, 65, 85, 0.7) !important;
    }
    
    .info-label {
        color: #000000 !important;
        font-weight: 600 !important;
    }
    
    .info-value {
        color: #000000 !important;
        background-color: transparent !important;
        -webkit-text-fill-color: #000000 !important;
        -webkit-background-clip: initial !important;
        background-clip: initial !important;
    }
    
    /* 强制修复所有性别和出生时间等基本信息文本颜色 */
    .basic-info-item *,
    .basic-info-item span,
    .basic-info-item div,
    .basic-info-item p,
    .info-value *,
    .info-value span,
    .info-value div,
    .info-value p {
        color: #000000 !important;
        background-color: transparent !important;
        -webkit-text-fill-color: #000000 !important;
        text-shadow: none !important;
        -webkit-background-clip: initial !important;
        background-clip: initial !important;
    }
    
    /* 移动端八字表格加�?*/
    .detailed-pillar-cell {
        background-color: #0f172a !important;
        border-color: rgba(96, 165, 250, 0.4) !important;
    }
    
    /* Remove day pillar special styles */
    .detailed-bazi-cell {
        background-color: rgba(96, 165, 250, 0.25) !important;
    }
    
    /* 移动端起运信息和大运说明强化 */
    .qiyun-info,
    [class*="qiyun"],
    [class*="dayun"] .qiyun-info,
    .detailed-dayun-section .qiyun-info,
    .qiyun-info .info-value,
    [class*="qiyun"] .info-value,
    [class*="dayun"] .qiyun-info .info-value,
    .detailed-dayun-section .qiyun-info .info-value {
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        border-color: rgba(96, 165, 250, 0.4) !important;
        color: #000000 !important;
        font-weight: 500 !important;
        text-shadow: none !important;
        -webkit-text-fill-color: #000000 !important;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        margin: 10px 0 !important;
    }
    
    .qiyun-info strong,
    [class*="qiyun"] strong,
    .detailed-dayun-section .qiyun-info strong {
        color: #000000 !important;
        font-weight: 700 !important;
    }
    
    /* 特别处理起运信息文字元素，确保没有白色背�?*/
    .qiyun-info *:not(h4):not(h3),
    .qiyun-info p,
    .qiyun-info span,
    .qiyun-info div,
    .qiyun-info .info-value *,
    [class*="qiyun"] .info-value *,
    [class*="dayun"] .qiyun-info .info-value *,
    .detailed-dayun-section .qiyun-info .info-value * {
        background: transparent !important;
        color: #000000 !important;
        text-shadow: none !important;
        -webkit-background-clip: initial !important;
        -webkit-text-fill-color: #000000 !important;
        background-clip: initial !important;
        font-weight: 500 !important;
    }
    
    /* 修复移动端渐变文�?*/
    .detailed-dayun-section h4,
    .qiyun-info h4[style*="background"],
    .qiyun-info h3[style*="background"],
    h4[style*="background"],
    h3[style*="background"] {
        background: linear-gradient(135deg, #1e40af, #3b82f6, #60a5fa) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        color: #1e40af !important; /* 备用颜色 */
    }
    
    /* 兜底强制修复 */
    .qiyun-info h4:not([style*="background"]),
    .qiyun-info h3:not([style*="background"]) {
        color: #1e40af !important;
        -webkit-text-fill-color: #1e40af !important;
        text-fill-color: #1e40af !important;
        font-weight: 700 !important;
    }
    
    .current-dayun-note {
        background: rgba(96, 165, 250, 0.2) !important;
        border-color: rgba(96, 165, 250, 0.3) !important;
        color: #cbd5e1 !important;
    }
    
    /* 移动端大运表格深色模式强�?*/
    .detailed-dayun-table {
        background-color: transparent !important;
    }
    
    .detailed-dayun-table tbody tr td {
        background-color: rgba(15, 23, 42, 0.75) !important;
        color: #f1f5f9 !important;
    }
    
    .detailed-dayun-table tbody tr:nth-child(even) td {
        background-color: rgba(30, 41, 59, 0.75) !important;
    }
    
    /* 确保移动端的八字颜色与PC端保持一�?*/
    .wuxing-jin {
        color: #ffffff !important;
        background-color: rgb(231, 144, 15) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .wuxing-mu {
        color: #ffffff !important;
        background-color: rgb(13, 230, 51) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .wuxing-shui {
        color: #ffffff !important;
        background-color: rgb(49, 131, 239) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .wuxing-huo {
        color: #ffffff !important;
        background-color: rgb(211, 6, 5) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .wuxing-tu {
        color: #ffffff !important;
        background-color: rgb(135, 109, 2) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    .wuxing-kong {
        color: #0f172a !important;
        background-color: rgb(255, 255, 255) !important;
        text-shadow: none !important;
    }
    
    .nayin-text {
        color: #f1f5f9 !important;
        background-color: transparent !important;
    }
    
    /* 移动端确保所有藏干和地支的五行标记显示正�?*/
    .tiangan-wuxing,
    .dizhi-wuxing,
    .canggan-wuxing {
        color: #ffffff !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    }
    
    /* 八字表格天干地支颜色覆盖 - 移动�?- 增强优先�?*/
    /* �?*/
    .detailed-bazi-table .detailed-tiangan[style*="color: #dd4b39"],
    .detailed-bazi-table .detailed-dizhi[style*="color: #dd4b39"],
    .detailed-bazi-table .canggan-gan[style*="color: #dd4b39"],
    .detailed-tiangan[style*="color: #dd4b39"],
    .detailed-dizhi[style*="color: #dd4b39"],
    .canggan-gan[style*="color: #dd4b39"] {
        color: rgb(211, 6, 5) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        -webkit-text-fill-color: rgb(211, 6, 5) !important;
        background-image: none !important;
        background-clip: initial !important;
        -webkit-background-clip: initial !important;
    }
    
    /* �?*/
    .detailed-bazi-table .detailed-tiangan[style*="color: #ffad4a"],
    .detailed-bazi-table .detailed-dizhi[style*="color: #ffad4a"],
    .detailed-bazi-table .canggan-gan[style*="color: #ffad4a"],
    .detailed-tiangan[style*="color: #ffad4a"],
    .detailed-dizhi[style*="color: #ffad4a"],
    .canggan-gan[style*="color: #ffad4a"] {
        color: rgb(135, 109, 2) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        -webkit-text-fill-color: rgb(135, 109, 2) !important;
        background-image: none !important;
        background-clip: initial !important;
        -webkit-background-clip: initial !important;
    }
    
    /* �?*/
    .detailed-bazi-table .detailed-tiangan[style*="color: #bfbfbf"],
    .detailed-bazi-table .detailed-dizhi[style*="color: #bfbfbf"],
    .detailed-bazi-table .canggan-gan[style*="color: #bfbfbf"],
    .detailed-tiangan[style*="color: #bfbfbf"],
    .detailed-dizhi[style*="color: #bfbfbf"],
    .canggan-gan[style*="color: #bfbfbf"] {
        color: rgb(231, 144, 15) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        -webkit-text-fill-color: rgb(231, 144, 15) !important;
        background-image: none !important;
        background-clip: initial !important;
        -webkit-background-clip: initial !important;
    }
    
    /* �?*/
    .detailed-bazi-table .detailed-tiangan[style*="color: #3c8dbc"],
    .detailed-bazi-table .detailed-dizhi[style*="color: #3c8dbc"],
    .detailed-bazi-table .canggan-gan[style*="color: #3c8dbc"],
    .detailed-tiangan[style*="color: #3c8dbc"],
    .detailed-dizhi[style*="color: #3c8dbc"],
    .canggan-gan[style*="color: #3c8dbc"] {
        color: rgb(49, 131, 239) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        -webkit-text-fill-color: rgb(49, 131, 239) !important;
        background-image: none !important;
        background-clip: initial !important;
        -webkit-background-clip: initial !important;
    }
    
    /* �?*/
    .detailed-bazi-table .detailed-tiangan[style*="color: #00a65a"],
    .detailed-bazi-table .detailed-dizhi[style*="color: #00a65a"],
    .detailed-bazi-table .canggan-gan[style*="color: #00a65a"],
    .detailed-tiangan[style*="color: #00a65a"],
    .detailed-dizhi[style*="color: #00a65a"],
    .canggan-gan[style*="color: #00a65a"] {
        color: rgb(13, 230, 51) !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
        -webkit-text-fill-color: rgb(13, 230, 51) !important;
        background-image: none !important;
        background-clip: initial !important;
        -webkit-background-clip: initial !important;
    }
} 

/* 使用非媒体查询规则确保无论是否启用暗黑模式，天干地支颜色都正�?*/
/* �?*/
html body .detailed-tiangan[style*="color: #dd4b39"],
html body .detailed-dizhi[style*="color: #dd4b39"],
html body .canggan-gan[style*="color: #dd4b39"] {
    color: rgb(211, 6, 5) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    -webkit-text-fill-color: rgb(211, 6, 5) !important;
}

/* �?*/
html body .detailed-tiangan[style*="color: #ffad4a"],
html body .detailed-dizhi[style*="color: #ffad4a"],
html body .canggan-gan[style*="color: #ffad4a"] {
    color: rgb(135, 109, 2) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    -webkit-text-fill-color: rgb(135, 109, 2) !important;
}

/* �?*/
html body .detailed-tiangan[style*="color: #bfbfbf"],
html body .detailed-dizhi[style*="color: #bfbfbf"],
html body .canggan-gan[style*="color: #bfbfbf"] {
    color: rgb(231, 144, 15) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    -webkit-text-fill-color: rgb(231, 144, 15) !important;
}

/* �?*/
html body .detailed-tiangan[style*="color: #3c8dbc"],
html body .detailed-dizhi[style*="color: #3c8dbc"],
html body .canggan-gan[style*="color: #3c8dbc"] {
    color: rgb(49, 131, 239) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    -webkit-text-fill-color: rgb(49, 131, 239) !important;
}

/* �?*/
html body .detailed-tiangan[style*="color: #00a65a"],
html body .detailed-dizhi[style*="color: #00a65a"],
html body .canggan-gan[style*="color: #00a65a"] {
    color: rgb(13, 230, 51) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    -webkit-text-fill-color: rgb(13, 230, 51) !important;
} 

/* 手机横屏 (576px - 767px) 深色模式背景统一 */
@media (min-width: 576px) and (max-width: 767px) {
    .detailed-container {
        background: var(--bg-primary) !important;
    }
    
    .detailed-result-container,
    .detailed-header,
    .detailed-dimension-section,
    .detailed-bazi-info-item {
        background: var(--bg-card) !important;
    }
    
    /* 手机端八字基本信息紧凑样式 */
    .detailed-bazi-basic-info {
        padding: 16px !important;
        margin-top: 16px !important;
        gap: 12px !important;
        grid-template-columns: 1fr !important;
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }
    
    .detailed-bazi-info-item {
        padding: 12px 16px !important;
        margin-bottom: 8px !important;
        border-radius: 8px !important;
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
    }
    
    .detailed-bazi-info-item strong {
        font-size: 14px !important;
        margin-bottom: 4px !important;
        display: block !important;
    }
    
    .detailed-bazi-info-item .info-value {
        font-size: 15px !important;
        font-weight: 600 !important;
    }
}

/* 手机竖屏 (最大575px) 深色模式背景统一 */
@media (max-width: 575px) {
    .detailed-container {
        background: var(--bg-primary) !important;
    }
    
    .detailed-result-container,
    .detailed-header,
    .detailed-dimension-section,
    .detailed-bazi-info-item {
        background: var(--bg-card) !important;
    }
    
    /* 手机端八字基本信息紧凑样式 */
    .detailed-bazi-basic-info {
        padding: 12px !important;
        margin-top: 12px !important;
        gap: 8px !important;
        grid-template-columns: 1fr !important;
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
        border-radius: 10px !important;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
    }
    
    .detailed-bazi-info-item {
        padding: 10px 14px !important;
        margin-bottom: 6px !important;
        border-radius: 6px !important;
        background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
    }
    
    .detailed-bazi-info-item strong {
        font-size: 13px !important;
        margin-bottom: 3px !important;
        display: block !important;
    }
    
    .detailed-bazi-info-item .info-value {
        font-size: 14px !important;
        font-weight: 600 !important;
    }
}

/* 更高优先级的起运信息样式覆盖 */
html body .qiyun-info,
body .detailed-dayun-section .qiyun-info,
.detailed-result-page .qiyun-info,
#analysisContent .qiyun-info,
.detailed-result-container .qiyun-info,
.detailed-dimension-content .qiyun-info {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    text-fill-color: #000000 !important;
    background: linear-gradient(135deg, rgba(240, 240, 240, 0.95), rgba(250, 250, 250, 0.95)) !important;
    border: 1px solid rgba(96, 165, 250, 0.4) !important;
    text-shadow: none !important;
    font-weight: 500 !important;
    display: block !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important;
}

html body .qiyun-info *,
body .detailed-dayun-section .qiyun-info *,
.detailed-result-page .qiyun-info *,
#analysisContent .qiyun-info *,
.detailed-result-container .qiyun-info *,
.detailed-dimension-content .qiyun-info * {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    text-fill-color: #000000 !important;
    background: transparent !important;
    text-shadow: none !important;
    font-weight: inherit !important;
}
