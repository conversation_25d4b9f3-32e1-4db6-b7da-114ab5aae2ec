/**
 * 控制台输出清理工具
 * 在生产环境中禁用所有控制台输出，提高性能和安全性
 */

(function() {
    // 检测环境 - 开发环境不关闭控制台
    const isProduction = window.location.hostname !== 'localhost' && 
                        window.location.hostname !== '127.0.0.1' && 
                        !window.location.search.includes('debug=true');
    
    // 在生产环境中禁用所有控制台输出
    if (isProduction) {
        // 保存原始控制台方法的引用（用于调试目的）
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };
        
        // 存储到window对象上，以便在需要时能够恢复
        window._originalConsole = originalConsole;
        
        // 替换控制台方法为空函数
        console.log = function() {};
        console.info = function() {};
        console.warn = function() {};
        console.error = function() {};
        console.debug = function() {};
        
        // 恢复控制台的功能（仅供紧急调试使用）
        window.restoreConsole = function() {
            console.log = originalConsole.log;
            console.info = originalConsole.info;
            console.warn = originalConsole.warn;
            console.error = originalConsole.error;
            console.debug = originalConsole.debug;
            console.log('控制台功能已恢复');
        };
        
        // 设置BaziLogger为NONE级别
        if (window.BaziLogger && window.BaziLogLevel) {
            window.setLogLevel(window.BaziLogLevel.NONE);
        }
    }
})(); 