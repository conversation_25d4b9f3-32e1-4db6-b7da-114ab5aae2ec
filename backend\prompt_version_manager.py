#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词版本管理器
支持提示词的版本控制、回滚和A/B测试
"""

import json
import os
import shutil
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib
import random

logger = logging.getLogger(__name__)

class PromptVersionManager:
    """提示词版本管理器"""
    
    def __init__(self, prompts_dir: str = None):
        """初始化版本管理器
        
        Args:
            prompts_dir: 提示词根目录
        """
        self.prompts_dir = prompts_dir or os.path.join(os.path.dirname(__file__), "prompts")
        self.versions_dir = os.path.join(self.prompts_dir, "versions")
        self.current_link = os.path.join(self.versions_dir, "current")
        
        # 确保目录存在
        os.makedirs(self.versions_dir, exist_ok=True)
        
        # 加载版本历史
        self.version_history = self._load_version_history()
    
    def _load_version_history(self) -> Dict[str, Any]:
        """加载版本历史记录"""
        history_file = os.path.join(self.versions_dir, "history.json")
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载版本历史失败: {str(e)}")
        
        return {
            "versions": {},
            "current_version": None,
            "experiments": {}
        }
    
    def _save_version_history(self):
        """保存版本历史记录"""
        history_file = os.path.join(self.versions_dir, "history.json")
        try:
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.version_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存版本历史失败: {str(e)}")
    
    def create_version(self, 
                      version_name: str, 
                      description: str = "",
                      base_version: str = None) -> bool:
        """创建新版本
        
        Args:
            version_name: 版本名称
            description: 版本描述
            base_version: 基础版本，如果为None则基于当前版本
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 检查版本是否已存在
            if version_name in self.version_history["versions"]:
                logger.error(f"版本 {version_name} 已存在")
                return False
            
            # 创建版本目录
            version_dir = os.path.join(self.versions_dir, version_name)
            os.makedirs(version_dir, exist_ok=True)
            
            # 确定源目录
            if base_version:
                if base_version not in self.version_history["versions"]:
                    logger.error(f"基础版本 {base_version} 不存在")
                    return False
                source_dir = os.path.join(self.versions_dir, base_version)
            else:
                # 使用当前版本作为基础
                if os.path.exists(self.current_link):
                    source_dir = os.path.realpath(self.current_link)
                else:
                    # 如果没有当前版本，使用主目录
                    source_dir = self.prompts_dir
            
            # 复制文件
            if os.path.exists(source_dir):
                for item in os.listdir(source_dir):
                    if item == "versions":  # 跳过versions目录
                        continue
                    
                    source_path = os.path.join(source_dir, item)
                    dest_path = os.path.join(version_dir, item)
                    
                    if os.path.isfile(source_path):
                        shutil.copy2(source_path, dest_path)
                    elif os.path.isdir(source_path):
                        shutil.copytree(source_path, dest_path)
            
            # 记录版本信息
            version_info = {
                "name": version_name,
                "description": description,
                "created_at": datetime.now().isoformat(),
                "base_version": base_version,
                "status": "active",
                "checksum": self._calculate_version_checksum(version_dir)
            }
            
            self.version_history["versions"][version_name] = version_info
            self._save_version_history()
            
            logger.info(f"成功创建版本: {version_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建版本失败: {str(e)}")
            return False
    
    def switch_version(self, version_name: str) -> bool:
        """切换到指定版本
        
        Args:
            version_name: 目标版本名称
            
        Returns:
            bool: 是否切换成功
        """
        try:
            if version_name not in self.version_history["versions"]:
                logger.error(f"版本 {version_name} 不存在")
                return False
            
            version_dir = os.path.join(self.versions_dir, version_name)
            if not os.path.exists(version_dir):
                logger.error(f"版本目录不存在: {version_dir}")
                return False
            
            # 更新current链接
            if os.path.exists(self.current_link):
                os.remove(self.current_link)
            
            # 创建符号链接或复制文件
            try:
                os.symlink(version_dir, self.current_link)
            except OSError:
                # 如果不支持符号链接，则复制文件
                shutil.copytree(version_dir, self.current_link)
            
            # 更新历史记录
            self.version_history["current_version"] = version_name
            self._save_version_history()
            
            logger.info(f"成功切换到版本: {version_name}")
            return True
            
        except Exception as e:
            logger.error(f"切换版本失败: {str(e)}")
            return False
    
    def rollback_version(self, version_name: str) -> bool:
        """回滚到指定版本
        
        Args:
            version_name: 回滚目标版本
            
        Returns:
            bool: 是否回滚成功
        """
        return self.switch_version(version_name)
    
    def list_versions(self) -> List[Dict[str, Any]]:
        """列出所有版本"""
        versions = []
        for name, info in self.version_history["versions"].items():
            version_info = info.copy()
            version_info["is_current"] = (name == self.version_history.get("current_version"))
            versions.append(version_info)
        
        # 按创建时间排序
        versions.sort(key=lambda x: x.get("created_at", ""), reverse=True)
        return versions
    
    def delete_version(self, version_name: str) -> bool:
        """删除指定版本
        
        Args:
            version_name: 要删除的版本名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if version_name not in self.version_history["versions"]:
                logger.error(f"版本 {version_name} 不存在")
                return False
            
            # 不能删除当前版本
            if version_name == self.version_history.get("current_version"):
                logger.error(f"不能删除当前版本: {version_name}")
                return False
            
            # 删除版本目录
            version_dir = os.path.join(self.versions_dir, version_name)
            if os.path.exists(version_dir):
                shutil.rmtree(version_dir)
            
            # 从历史记录中删除
            del self.version_history["versions"][version_name]
            self._save_version_history()
            
            logger.info(f"成功删除版本: {version_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除版本失败: {str(e)}")
            return False
    
    def compare_versions(self, version1: str, version2: str) -> Dict[str, Any]:
        """比较两个版本的差异
        
        Args:
            version1: 版本1
            version2: 版本2
            
        Returns:
            Dict: 比较结果
        """
        comparison = {
            "version1": version1,
            "version2": version2,
            "differences": [],
            "summary": {}
        }
        
        try:
            if version1 not in self.version_history["versions"]:
                comparison["error"] = f"版本 {version1} 不存在"
                return comparison
            
            if version2 not in self.version_history["versions"]:
                comparison["error"] = f"版本 {version2} 不存在"
                return comparison
            
            dir1 = os.path.join(self.versions_dir, version1)
            dir2 = os.path.join(self.versions_dir, version2)
            
            # 比较文件列表
            files1 = set(self._get_all_files(dir1))
            files2 = set(self._get_all_files(dir2))
            
            # 新增文件
            added_files = files2 - files1
            # 删除文件
            removed_files = files1 - files2
            # 共同文件
            common_files = files1 & files2
            
            comparison["summary"] = {
                "added_files": len(added_files),
                "removed_files": len(removed_files),
                "modified_files": 0
            }
            
            # 检查修改的文件
            for file_path in common_files:
                file1 = os.path.join(dir1, file_path)
                file2 = os.path.join(dir2, file_path)
                
                if self._file_checksum(file1) != self._file_checksum(file2):
                    comparison["differences"].append({
                        "type": "modified",
                        "file": file_path
                    })
                    comparison["summary"]["modified_files"] += 1
            
            # 添加新增和删除的文件到差异列表
            for file_path in added_files:
                comparison["differences"].append({
                    "type": "added",
                    "file": file_path
                })
            
            for file_path in removed_files:
                comparison["differences"].append({
                    "type": "removed", 
                    "file": file_path
                })
            
        except Exception as e:
            logger.error(f"比较版本失败: {str(e)}")
            comparison["error"] = str(e)
        
        return comparison
    
    def _calculate_version_checksum(self, version_dir: str) -> str:
        """计算版本的校验和"""
        checksums = []
        for root, dirs, files in os.walk(version_dir):
            for file in sorted(files):
                file_path = os.path.join(root, file)
                checksums.append(self._file_checksum(file_path))
        
        combined = "".join(checksums)
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _file_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _get_all_files(self, directory: str) -> List[str]:
        """获取目录下所有文件的相对路径"""
        files = []
        for root, dirs, filenames in os.walk(directory):
            for filename in filenames:
                rel_path = os.path.relpath(os.path.join(root, filename), directory)
                files.append(rel_path)
        return files
    
    def get_current_version(self) -> Optional[str]:
        """获取当前版本"""
        return self.version_history.get("current_version")
    
    def get_version_info(self, version_name: str) -> Optional[Dict[str, Any]]:
        """获取版本信息"""
        return self.version_history["versions"].get(version_name)


class PromptABTester:
    """提示词A/B测试管理器"""
    
    def __init__(self, version_manager: PromptVersionManager):
        """初始化A/B测试管理器"""
        self.version_manager = version_manager
        self.experiments = {}
    
    def create_experiment(self, 
                         name: str,
                         variants: List[str],
                         traffic_split: List[float] = None,
                         description: str = "") -> bool:
        """创建A/B测试实验
        
        Args:
            name: 实验名称
            variants: 变体版本列表
            traffic_split: 流量分配比例
            description: 实验描述
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 验证变体版本是否存在
            for variant in variants:
                if variant not in self.version_manager.version_history["versions"]:
                    logger.error(f"变体版本 {variant} 不存在")
                    return False
            
            # 默认均匀分配流量
            if traffic_split is None:
                traffic_split = [1.0 / len(variants)] * len(variants)
            
            if len(traffic_split) != len(variants):
                logger.error("流量分配比例与变体数量不匹配")
                return False
            
            if abs(sum(traffic_split) - 1.0) > 0.001:
                logger.error("流量分配比例总和必须为1.0")
                return False
            
            experiment = {
                "name": name,
                "description": description,
                "variants": variants,
                "traffic_split": traffic_split,
                "created_at": datetime.now().isoformat(),
                "status": "active",
                "results": {}
            }
            
            self.experiments[name] = experiment
            
            # 保存到版本历史
            self.version_manager.version_history["experiments"][name] = experiment
            self.version_manager._save_version_history()
            
            logger.info(f"成功创建A/B测试实验: {name}")
            return True
            
        except Exception as e:
            logger.error(f"创建A/B测试实验失败: {str(e)}")
            return False
    
    def get_variant(self, user_id: str, experiment_name: str) -> Optional[str]:
        """为用户分配实验变体
        
        Args:
            user_id: 用户ID
            experiment_name: 实验名称
            
        Returns:
            Optional[str]: 分配的变体版本，如果实验不存在则返回None
        """
        if experiment_name not in self.experiments:
            return None
        
        experiment = self.experiments[experiment_name]
        if experiment["status"] != "active":
            return None
        
        # 使用用户ID和实验名称生成一致的随机数
        seed = hashlib.md5(f"{user_id}_{experiment_name}".encode()).hexdigest()
        random.seed(int(seed[:8], 16))
        
        # 根据流量分配选择变体
        rand_val = random.random()
        cumulative = 0.0
        
        for i, (variant, split) in enumerate(zip(experiment["variants"], experiment["traffic_split"])):
            cumulative += split
            if rand_val <= cumulative:
                return variant
        
        # 默认返回第一个变体
        return experiment["variants"][0]
    
    def record_result(self, 
                     user_id: str, 
                     experiment_name: str, 
                     metric: str,
                     value: float) -> bool:
        """记录实验结果
        
        Args:
            user_id: 用户ID
            experiment_name: 实验名称
            metric: 指标名称
            value: 指标值
            
        Returns:
            bool: 是否记录成功
        """
        try:
            if experiment_name not in self.experiments:
                return False
            
            variant = self.get_variant(user_id, experiment_name)
            if not variant:
                return False
            
            experiment = self.experiments[experiment_name]
            
            # 初始化结果结构
            if variant not in experiment["results"]:
                experiment["results"][variant] = {}
            
            if metric not in experiment["results"][variant]:
                experiment["results"][variant][metric] = []
            
            # 记录结果
            experiment["results"][variant][metric].append({
                "user_id": user_id,
                "value": value,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保存结果
            self.version_manager.version_history["experiments"][experiment_name] = experiment
            self.version_manager._save_version_history()
            
            return True
            
        except Exception as e:
            logger.error(f"记录实验结果失败: {str(e)}")
            return False
