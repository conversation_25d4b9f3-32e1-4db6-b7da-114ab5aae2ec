/* CSS变量定义 */
:root {
    /* 主色调 - 优化配色方案 */
    --primary-color: #6366f1;
    --primary-color-rgb: 99, 102, 241;
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --secondary-color: #ec4899;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 新增渐变色 - 更和谐的配色 */
    --hero-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --purple-gradient: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    
    /* 文字颜色 - 优化对比度 */
    --text-primary: #111827;
    --text-secondary: #374151;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --text-accent: #6366f1;
    --text-success: #059669;
    --text-warning: #d97706;
    --text-error: #dc2626;
    
    /* 背景色 - 更现代的配色 */
    --bg-primary: #f9fafb;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f3f4f6;
    --bg-card: #ffffff;
    --bg-overlay: rgba(17, 24, 39, 0.7);
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-gradient-light: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
    --bg-gradient-card: linear-gradient(145deg, #ffffff 0%, #f9fafb 100%);
    
    /* 边框和阴影 - 更精致的效果 */
    --border-color: #e5e7eb;
    --border-color-light: #f3f4f6;
    --border-color-dark: #d1d5db;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-colored: 0 8px 25px rgba(99, 102, 241, 0.15);
    --shadow-primary: 0 4px 14px 0 rgba(99, 102, 241, 0.2);
    --shadow-success: 0 4px 14px 0 rgba(16, 185, 129, 0.2);
    --shadow-warning: 0 4px 14px 0 rgba(245, 158, 11, 0.2);
    --shadow-error: 0 4px 14px 0 rgba(239, 68, 68, 0.2);
    
    /* 过渡动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* 动画关键帧 */
    --pulse-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --float-animation: float 3s ease-in-out infinite;
    --card-hover-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    
    /* 字体 */
    --font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 容器布局 */
.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 100vh;
    padding-top: 40px;
}

/* 移动端容器优化 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
        padding-top: 20px;
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 12px;
        padding-top: 16px;
    }
}

/* 页面头部 */
.header {
    text-align: center;
    margin-bottom: 30px;
    animation: fadeInDown 0.8s ease-out;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 10px;
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin: 0;
}

/* 表单容器 */
.form-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    margin-bottom: 30px;
    animation: fadeInUp 0.8s ease-out 0.2s both;
    border: 1px solid var(--border-color);
}

/* 表单分组 */
.form-section {
    margin-bottom: 32px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 表单行和列 */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

/* 输入组样式 */
.input-group {
    position: relative;
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.95rem;
    letter-spacing: 0.01em;
}

/* 表单控件样式 */
.input-group input,
.input-group select,
.input-group textarea {
    width: 100%;
    padding: 14px 16px;
    padding-right: 45px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: var(--transition);
    outline: none;
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.input-group input:hover,
.input-group select:hover {
    border-color: var(--primary-color);
}

.input-group input::placeholder {
    color: var(--text-light);
    font-size: 0.95rem;
}

/* 输入图标 */
.input-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    color: var(--text-secondary);
    pointer-events: none;
    margin-top: 14px;
}

/* 表单操作区域 */
.form-actions {
    margin-top: 32px;
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    padding: 16px 24px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: var(--shadow-md);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 1.3rem;
}

.btn-text {
    font-weight: 600;
}

/* 加载动画 */
.btn-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 结果容器 */
.result-container {
    margin-top: 30px;
}

/* 进度部分 */
.progress-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.progress-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.elapsed-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.progress-status {
    font-size: 0.95rem;
    color: var(--text-secondary);
    text-align: center;
}

/* LLM进度 */
.llm-progress {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.llm-dimensions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.dimension-tag {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition);
}

.dimension-tag.completed {
    background: var(--success-color);
    color: white;
}

.dimension-tag.current {
    background: var(--warning-color);
    color: white;
    animation: currentDimensionPulse 1.5s ease-in-out infinite;
}

/* 进度信息 */
.progress-info {
    margin: 8px 0;
    padding: 6px 10px;
    background-color: #f5f5f5;
    border-left: 3px solid #2196F3;
    border-radius: 3px;
    font-size: 0.9rem;
    color: #333;
    font-weight: 500;
}

.dimension-tag.pending {
    background: var(--text-light);
    color: var(--text-secondary);
}

@keyframes currentDimensionPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 结果内容 */
.result-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    text-align: center;
}

.result-content.success {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.05) 0%, rgba(0, 212, 170, 0.1) 100%);
}

.result-content.error {
    border-color: var(--error-color);
    background: linear-gradient(135deg, rgba(255, 82, 82, 0.05) 0%, rgba(255, 82, 82, 0.1) 100%);
}

/* 链接按钮 */
.link-button {
    display: inline-block;
    background: var(--success-color);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    margin-top: 16px;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.link-button:hover {
    background: #00c4a0;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 正在运行分析样式 */
.running-analysis-section {
    margin-bottom: 35px;
}

.running-analysis-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 25px;
    color: white;
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.running-analysis-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(255,255,255,0.15) 0%, transparent 70%);
    z-index: 1;
}

.running-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 22px;
    position: relative;
    z-index: 2;
}

.running-header h3 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.running-icon {
    font-size: 26px;
    margin-right: 12px;
    animation: pulse 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.running-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(0,0,0,0.2);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-container {
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
}

.progress-fill {
    background: linear-gradient(90deg, #00d4aa, #00b894);
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    width: 0%;
    box-shadow: 0 0 8px rgba(0, 212, 170, 0.5);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        rgba(255,255,255,0) 0%, 
        rgba(255,255,255,0.3) 50%, 
        rgba(255,255,255,0) 100%);
    animation: shimmer 1.5s infinite;
    transform: translateX(-100%);
}

@keyframes shimmer {
    to {
        transform: translateX(100%);
    }
}

.progress-text {
    font-size: 15px;
    opacity: 0.9;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(0,0,0,0.2);
}

.analysis-time {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    opacity: 0.8;
    position: relative;
    z-index: 2;
}

.time-icon {
    margin-right: 5px;
    font-style: normal;
}

.section-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, #e9ecef, transparent);
    margin: 30px 0;
}

.reports-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 页面底部 */
.footer {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 1020px) {
    .container {
        max-width: 95%;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
        max-width: 100%;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 24px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .submit-btn {
        padding: 14px 20px;
        font-size: 1rem;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .report-card {
        max-width: 100%;
    }
    
    .card-filter {
        flex-direction: column;
        width: 100%;
        margin-top: 10px;
        margin-left: 0;
    }
    
    .card-filter-input {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .actions-section {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .header {
        margin-bottom: 30px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .form-container {
        padding: 20px;
    }
    
    .section-title {
        font-size: 1.1rem;
    }
}

/* 动画效果 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #f1f5f9;
        --text-light: #e2e8f0;
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --bg-card: #2d3748;
        --border-color: #4a5568;
    }
    
    /* 深色模式下输入框样式修正 */
    .input-group input,
    .input-group select,
    .input-group textarea {
        background-color: #3a4556 !important; /* 更浅的背景色，增加对比度 */
        color: #f8fafc !important; /* 确保文本为浅色 */
        border-color: #4a5568 !important;
    }
    
    .input-group input:focus,
    .input-group select:focus,
    .input-group textarea:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
    }
    
    .input-group input::placeholder {
        color: #a0aec0 !important; /* 更浅的占位符颜色 */
    }
    
    .input-icon {
        color: #cbd5e0 !important; /* 更浅的图标颜色 */
    }
}


/* 结果页面专用样式 */
.result-container {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
    overflow: hidden;
}

.waiting-message, .error-message {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin: 20px 0;
}

.waiting-icon, .error-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.waiting-message h3, .error-message h3 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.3rem;
}

.waiting-message p, .error-message p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

.back-link, .retry-btn {
    display: inline-block;
    background: var(--primary-gradient);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.back-link:hover, .retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.analysis-result {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 0;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: 20px;
}

.analysis-result h3 {
    color: white;
    margin: 0;
    padding: 20px 24px;
    font-size: 1.3rem;
    background: var(--primary-gradient);
    font-weight: 600;
}

.result-text {
    color: var(--text-primary);
    line-height: 1.8;
    background: #ffffff;
    padding: 24px;
    font-size: 1rem;
}

/* 维度卡片样式 */
.dimension-card {
    background: #ffffff;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.dimension-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.dimension-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dimension-content {
    padding: 20px;
    color: var(--text-primary);
    line-height: 1.7;
    background: #ffffff;
    font-size: 0.95rem;
}

.dimension-content strong {
    color: var(--primary-color);
    font-weight: 600;
}

.dimension-content hr {
    margin: 16px 0;
    border: none;
    border-top: 2px solid #e9ecef;
    opacity: 0.6;
}

/* 结果操作按钮 */
.result-actions {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 改善文本对比度 */
.result-text, .dimension-content {
    color: #2c3e50 !important;
    background: #ffffff !important;
}

.result-text pre {
    background: #f8f9fa !important;
    color: #2c3e50 !important;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
}

/* 确保所有文本都有足够的对比度 */
.analysis-result .result-text,
.dimension-card .dimension-content,
.result-container .result-text {
    color: #2c3e50 !important;
    background-color: #ffffff !important;
    margin: 0;
    padding: 24px;
    white-space: pre-wrap;
    background: #ffffff;
    border-bottom: 1px solid var(--border-color);
    font-size: 1rem;
}

.result-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid var(--border-color);
}

.action-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-block;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.dimension-tag.current {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.actions-section {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.action-btn {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    box-shadow: var(--shadow-sm);
}

.action-btn.primary {
    background: var(--primary-gradient);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn.secondary {
    background: white !important;
    color: var(--primary-color) !important;
    border: 1px solid rgba(102, 126, 234, 0.3);
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: var(--primary-color) !important;
    text-fill-color: var(--primary-color) !important;
}

.action-btn.secondary:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    color: var(--primary-color) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    /* 防止深色模式CSS覆盖悬停状态 */
    -webkit-text-fill-color: var(--primary-color) !important;
    text-fill-color: var(--primary-color) !important;
}

.action-btn.outline {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.action-btn.outline:hover {
    background: rgba(0,0,0,0.03);
    color: var(--text-primary);
}

.card-filter {
    display: flex;
    gap: 10px;
    margin-left: auto;
    flex-wrap: wrap;
    align-items: center;
}

.card-filter-input {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    width: 250px;
    transition: var(--transition);
    font-family: var(--font-family);
    background-color: white;
}

.card-filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 报告列表相关样式 */
.reports-container {
    margin-bottom: 30px;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.report-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.report-header {
    padding: 16px;
    background: linear-gradient(to right, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-title {
    margin: 0;
    color: var(--text-primary) !important;
    font-size: 1rem;
    font-weight: 600;
}

.report-source {
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.report-source.local {
    background-color: rgba(0, 212, 170, 0.15);
    color: #00a884;
}

.report-source.server {
    background-color: rgba(79, 172, 254, 0.15);
    color: #4285f4;
}

.report-meta {
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid var(--border-color);
}

.report-date {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.report-summary {
    padding: 16px;
    color: var(--text-primary);
    flex-grow: 1;
    font-size: 0.9rem;
    line-height: 1.6;
}

.report-summary p {
    margin: 8px 0;
}

.report-summary p:first-child {
    margin-top: 0;
}

.report-summary p:last-child {
    margin-bottom: 0;
}

.report-summary strong {
    color: var(--primary-color);
    font-weight: 600;
}

.report-actions {
    display: flex;
    border-top: 1px solid var(--border-color);
    background: rgba(0, 0, 0, 0.02);
}

.view-btn,
.delete-btn {
    flex: 1;
    padding: 12px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.85rem;
    font-family: var(--font-family);
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.view-btn {
    color: var(--primary-color);
}

.view-btn:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.delete-btn {
    color: var(--error-color);
    border-left: 1px solid var(--border-color);
}

.delete-btn:hover {
    background-color: rgba(255, 82, 82, 0.1);
}

/* 空状态和加载状态 */
.empty-state,
.loading-message {
    text-align: center;
    padding: 40px 20px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    margin: 20px 0;
}

.empty-icon,
.loading-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.loading-icon {
    animation: pulse 1.5s ease-in-out infinite;
}

.empty-state h3,
.loading-message h3 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.3rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* 筛选相关 */
.filtered-reports {
    margin-bottom: 30px;
}

.filter-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(102, 126, 234, 0.1);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.filter-info strong {
    color: var(--primary-color);
    font-weight: 600;
}

.clear-filter-btn {
    background: rgba(255, 255, 255, 0.7);
    color: var(--primary-color);
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition);
    font-weight: 500;
}

.clear-filter-btn:hover {
    background: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.force-complete-btn {
    flex: 2;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;
    padding: 8px 12px;
}

.debug-btn {
    flex: 1;
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.force-complete-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(0);
}

.debug-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    color: white;
    transform: translateY(0);
}

.debug-info {
    margin-top: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 12px;
    position: relative;
}

.debug-title {
    font-size: 14px;
    font-weight: 500;
    color: white;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
}

.debug-toggle {
    cursor: pointer;
    text-decoration: underline;
    opacity: 0.8;
}

.debug-toggle:hover {
    opacity: 1;
}

#debugContent {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-family: monospace;
    font-size: 12px;
    overflow: auto;
    max-height: 200px;
    white-space: pre-wrap;
    word-break: break-word;
}

.analysis-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.success-message {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.2) 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--success-color);
    margin: 20px 0;
    animation: fadeIn 0.8s ease-out;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: var(--success-color);
}

.success-message h3 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.3rem;
}

.success-message p {
    color: var(--text-secondary);
    max-width: 500px;
    margin: 0 auto 10px auto;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 八字反推输入框专用样式 */
#yearPillar, #monthPillar, #dayPillar, #timePillar, 
#baziString, #lunarYear, #lunarMonth, #lunarDay, #lunarHour {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 2px solid var(--border-color) !important;
}

/* 隐藏流派和基准年输入框及其相关元素 */
#sect,
#baseYear,
label[for="sect"],
label[for="baseYear"],
#sect + .input-icon,
#baseYear + .input-icon,
.input-group:has(#sect),
.input-group:has(#baseYear) {
    display: none !important;
}

/* 确保在深色模式下这些输入框也有正确的样式 */
@media (prefers-color-scheme: dark) {
    #yearPillar, #monthPillar, #dayPillar, #timePillar, 
    #baziString, #lunarYear, #lunarMonth, #lunarDay, #lunarHour {
        background-color: #3a4556 !important;
        color: #f8fafc !important;
        border-color: #4a5568 !important;
    }
    
    #yearPillar:focus, #monthPillar:focus, #dayPillar:focus, #timePillar:focus,
    #baziString:focus, #lunarYear:focus, #lunarMonth:focus, #lunarDay:focus, #lunarHour:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
    }
}

/* 八字天干地支选择器样式 */
.pillar-select-group {
    display: flex;
    gap: 8px;
    width: 100%;
}

.tiangan-select, .dizhi-select {
    flex: 1;
    padding: 12px 10px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    cursor: pointer;
    transition: var(--transition);
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

.tiangan-select:focus, .dizhi-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.tiangan-select:hover, .dizhi-select:hover {
    border-color: var(--primary-color);
}

/* 深色模式下的选择器样式 */
@media (prefers-color-scheme: dark) {
    .tiangan-select, .dizhi-select {
        background-color: #3a4556;
        color: #f8fafc;
        border-color: #4a5568;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f8fafc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    }
    
    .tiangan-select:focus, .dizhi-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }
}

/* 八字选择器新样式 */
.bazi-selector-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.bazi-pillar-section {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 16px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.bazi-pillar-section:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.pillar-header {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed var(--border-color-light);
}

.selector-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.selector-options {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.tiangan-option, .dizhi-option {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 10px 0;
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    user-select: none;
}

.tiangan-option:hover, .dizhi-option:hover {
    background: var(--bg-glass);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.tiangan-option.selected, .dizhi-option.selected {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.pillar-preview {
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    background: var(--bg-primary);
    padding: 12px;
    border-radius: var(--border-radius);
    margin-top: 12px;
    min-height: 24px;
    border: 1px dashed var(--border-color);
}

.pillar-preview.completed {
    background: var(--success-gradient);
    color: white;
    border: none;
}

/* 深色模式下的样式调整 */
@media (prefers-color-scheme: dark) {
    .bazi-pillar-section {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .tiangan-option, .dizhi-option {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }
    
    .tiangan-option:hover, .dizhi-option:hover {
        background: rgba(255, 255, 255, 0.1);
    }
    
    .pillar-preview {
        background: rgba(0, 0, 0, 0.2);
        border-color: var(--border-color);
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .bazi-selector-container {
        grid-template-columns: 1fr;
    }
    
    .selector-options {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (max-width: 480px) {
    .selector-options {
        grid-template-columns: repeat(5, 1fr);
        gap: 4px;
    }
    
    .tiangan-option, .dizhi-option {
        padding: 8px 0;
        font-size: 0.9rem;
    }
}