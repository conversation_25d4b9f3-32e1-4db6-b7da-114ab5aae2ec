/**
 * 手机端暗色模式八字反推样式
 * 专门针对八字反推功能的暗色模式适配
 */

/* 导入统一配色系统 */
@import url('./unified-colors.css');

/* 暗色模式八字反推样式 */
@media (prefers-color-scheme: dark) {
    /* 八字反推按钮暗色模式 - 现代美观设计 */
    .reverse-btn {
        background: var(--gradient-bazi-button-dark) !important;
        color: #ffffff !important;
        border: none !important;
        border-radius: 50px;
        padding: 16px 32px;
        font-size: 1.1em;
        font-weight: 700;
        letter-spacing: 1px;
        text-transform: uppercase;
        box-shadow: var(--shadow-primary);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
    }
    
    .reverse-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.8s ease;
    }
    
    .reverse-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
        border-radius: 50%;
    }
    
    .reverse-btn:hover {
        background: var(--gradient-bazi-button-dark-hover) !important;
        transform: translateY(-4px) scale(1.05);
        box-shadow: var(--shadow-primary-hover);
    }
    
    .reverse-btn:hover::before {
        left: 100%;
    }
    
    .reverse-btn:hover::after {
        width: 300px;
        height: 300px;
    }
    
    .reverse-btn:active {
        transform: translateY(-2px) scale(1.02);
        box-shadow: 
            0 8px 25px rgba(102, 126, 234, 0.5),
            0 4px 15px rgba(118, 75, 162, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    }
    
    .reverse-btn span {
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    }
    
    .reverse-btn.loading {
        opacity: 0.7;
        cursor: not-allowed;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.7), rgba(118, 75, 162, 0.7)) !important;
    }
    
    .reverse-btn.loading::after {
        border-top-color: white;
        filter: brightness(1.2);
    }
    
    /* 八字反推结果区域暗色模式 */
    .reverse-results {
        background: var(--gradient-card-dark) !important;
        border: 2px solid var(--border-primary-dark) !important;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        margin-top: 20px;
        box-shadow: var(--shadow-lg);
        color: var(--text-primary-dark) !important;
    }
    
    .reverse-results h3 {
        color: var(--color-primary-dark) !important;
        margin-bottom: 15px;
        font-size: 1.2em;
        font-weight: 600;
        text-align: center;
        border-bottom: 2px solid var(--border-primary-dark);
        padding-bottom: 10px;
    }
    
    .results-list {
        max-height: 300px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 10px 0;
    }
    
    .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: rgba(102, 126, 234, 0.15) !important;
        border: 1px solid rgba(102, 126, 234, 0.3) !important;
        border-radius: var(--border-radius);
        transition: var(--transition);
        cursor: pointer;
        color: var(--text-primary) !important;
    }
    
    .result-item:hover {
        background: rgba(102, 126, 234, 0.25) !important;
        border-color: var(--primary-color) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    .result-item.selected {
        background: rgba(76, 175, 80, 0.25) !important;
        border-color: #4caf50 !important;
    }
    
    .result-date {
        font-weight: 600;
        color: var(--text-primary) !important;
        font-size: 1.1em;
    }
    
    .result-time {
        color: #ffa726 !important;
        font-weight: 500;
    }
    
    .result-weekday {
        color: var(--text-primary) !important;
        background: rgba(106, 27, 154, 0.3) !important;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9em;
    }
    
    .no-results {
        text-align: center;
        padding: 30px;
        background: rgba(211, 47, 47, 0.2) !important;
        border: 1px solid rgba(211, 47, 47, 0.4) !important;
        color: #ff6b6b !important;
    }
    
    /* 八字反推结果样式暗色模式 */
    .reverse-result {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-card)) !important;
        border: 2px solid rgba(212, 175, 55, 0.6) !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        color: var(--text-primary) !important;
    }
    
    .reverse-result h4 {
        color: var(--text-primary) !important;
    }
    
    .reverse-result p {
        color: var(--text-secondary) !important;
    }
    
    /* 确保所有文本在深色模式下可见 */
    .reverse-results * {
        color: inherit !important;
    }
    
    .reverse-results .result-item * {
        color: var(--text-primary) !important;
    }
    
    .reverse-results .result-time {
        color: #ffa726 !important;
    }
    
    /* 手机端特殊优化 */
    @media (max-width: 768px) {
        .reverse-btn {
            width: 100% !important;
            justify-content: center;
            padding: 15px 24px !important;
            font-size: 1.1em !important;
            min-height: 48px;
        }
    }
    
    .result-item {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 20px 15px;
    }
    
    .results-list {
        max-height: 250px;
        gap: 15px;
    }
    
    .result-date,
    .result-time,
    .result-weekday {
        padding: 12px;
        border-radius: 6px;
        width: 100%;
        text-align: center;
    }
    
    /* 触摸优化 */
    .result-item:active {
        transform: scale(0.98);
        background: rgba(102, 126, 234, 0.3);
    }
    
    .reverse-btn:active {
        transform: scale(0.98);
    }
    
    /* 滚动条优化 */
    .results-list::-webkit-scrollbar {
        width: 8px;
    }
    
    .results-list::-webkit-scrollbar-track {
        background: rgba(45, 55, 72, 0.5);
        border-radius: 4px;
    }
    
    .results-list::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.6);
        border-radius: 4px;
    }
    
    .results-list::-webkit-scrollbar-thumb:hover {
        background: rgba(102, 126, 234, 0.8);
    }
}


    
    /* 八字反推结果区域暗色模式 */
    .reverse-results {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
        border: 2px solid var(--border-color);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .reverse-results h4 {
        color: var(--text-primary);
    }
    
    /* 结果列表暗色模式 */
    .result-item {
        background: rgba(74, 85, 104, 0.6);
        border: 2px solid var(--border-color);
        color: var(--text-primary);
    }
    
    .result-item:hover {
        background: rgba(102, 126, 234, 0.2);
        border-color: var(--primary-color);
    }
    
    .result-item.selected {
        background: rgba(76, 175, 80, 0.2);
        border-color: var(--success-color);
    }
    
    .result-date {
        color: var(--primary-color);
    }
    
    .result-time {
        color: #ff9800;
    }
    
    .result-weekday {
        color: var(--text-primary);
        background: rgba(106, 27, 154, 0.2);
    }
    
    .no-results {
        background: rgba(211, 47, 47, 0.1);
        border: 1px solid rgba(211, 47, 47, 0.3);
        color: #ff6b6b;
    }
    
    .reverse-result {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-card));
        border: 2px solid rgba(212, 175, 55, 0.5);
    }
    
    .reverse-result h4 {
        color: var(--text-primary);
    }
    
    .reverse-result p {
        color: var(--text-secondary);
    }
    
    /* 八字输入框placeholder在深色模式下的样式 */
    #baziString::placeholder {
        color: var(--text-secondary) !important;
        opacity: 0.8;
    }
    
    /* 四柱输入框placeholder在深色模式下的样式 */
    #yearPillar::placeholder,
    #monthPillar::placeholder,
    #dayPillar::placeholder,
    #timePillar::placeholder {
        color: #6b7280 !important;
        opacity: 0.8;
    }
    
    /* baziString输入框深色模式样式 */
    #baziString {
        background: #ffffff !important;
        color: #1a202c !important;
        border-color: #4a5568 !important;
    }
    
    #baziString:focus {
        background: #ffffff !important;
        color: #1a202c !important;
        border-color: #818cf8 !important;
    }
    
    #baziString::placeholder {
        color: #6b7280 !important;
        opacity: 0.8;
    }
    
    /* 流派和基准年输入框深色模式样式 */
    #sect,
    #baseYear {
        background: var(--bg-input-dark) !important;
        color: var(--text-primary-dark) !important;
        border: 2px solid var(--border-primary-dark) !important;
    }
    
    #sect:focus,
    #baseYear:focus {
        border-color: var(--color-primary-dark) !important;
        box-shadow: 0 0 0 3px var(--shadow-focus-dark) !important;
    }
    
    /* 所有八字输入区域的输入框和选择框 */
    .bazi-input-section .input-group input,
    .bazi-input-section .input-group select {
        background: var(--bg-input-dark) !important;
        color: var(--text-primary-dark) !important;
        border: 2px solid var(--border-primary-dark) !important;
    }
    
    .bazi-input-section .input-group input:focus,
    .bazi-input-section .input-group select:focus {
        border-color: var(--color-primary-dark) !important;
        box-shadow: 0 0 0 3px var(--shadow-focus-dark) !important;
    }