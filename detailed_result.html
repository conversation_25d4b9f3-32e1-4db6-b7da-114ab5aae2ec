<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="八字详细分析报告 - 查看您的命理分析结果">
    <meta name="theme-color" content="#4a00e0">
    <title>八字详细分析报告</title>
    <link rel="stylesheet" href="css/fonts.css">
    <!-- Google Fonts 已替换为本地字体文件 -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/detailed/variables.css">
    <link rel="stylesheet" href="css/detailed/layout.css">
    <link rel="stylesheet" href="css/detailed/titles.css">
    <link rel="stylesheet" href="css/detailed/progressive.css">
    <link rel="stylesheet" href="css/detailed/bazi-table.css">
    <link rel="stylesheet" href="css/detailed/dayun-table.css">
    <link rel="stylesheet" href="css/detailed/responsive.css">
    <link rel="stylesheet" href="css/detailed/dark-mode.css">
    <link rel="stylesheet" href="css/detailed/supplementary.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
</head>
<body class="detailed-result-page">
    <div class="detailed-container">
        <div class="detailed-header">
            <div class="detailed-header-actions">
                <button onclick="window.editTitle()" class="detailed-action-btn detailed-edit-btn" title="编辑标题" aria-label="编辑标题">
                    <span class="edit-icon">✏️</span>
                    <span class="btn-text">编辑标题</span>
                </button>
                <button onclick="backupDownloadReport()" class="detailed-action-btn detailed-download-btn" title="下载PDF报告" aria-label="下载PDF报告">
                    <span class="download-icon">📥</span>
                    <span class="btn-text">下载PDF</span>
                </button>
                <button onclick="exportSource()" class="detailed-action-btn detailed-export-btn" title="导出源文件" aria-label="导出源文件">
                    <span class="export-icon">📤</span>
                    <span class="btn-text">导出源文件</span>
                </button>
                <button onclick="exportAsImage()" oncontextmenu="event.preventDefault(); exportAsImageAdvanced(); return false;" class="detailed-action-btn detailed-image-btn" title="导出长图 (右键高级选项)" aria-label="导出长图">
                    <span class="image-icon">🖼️</span>
                    <span class="btn-text">导出长图</span>
                </button>
                <button onclick="goHome()" class="detailed-action-btn detailed-back-btn" title="返回主页" aria-label="返回主页">
                    <span class="back-icon">🏠</span>
                    <span class="btn-text">返回主页</span>
                </button>
            </div>
        </div>
        <div class="detailed-result-container">
            <div class="detailed-result-header">
                <h2 id="resultTitle" contenteditable="false" aria-label="报告标题">八字分析报告</h2>
                <div class="detailed-result-meta">
                    <span id="generateTime" aria-label="生成时间">生成时间: 加载中...</span>
                </div>
            </div>
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state" style="display: none;" aria-live="polite">
                <div class="loading-spinner" aria-hidden="true"></div>
                <div class="loading-text">正在加载分析结果...</div>
                <div class="loading-progress">
                    <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- 错误状态 -->
            <div id="errorState" class="error-state" style="display: none;" aria-live="assertive">
                <div class="error-icon" aria-hidden="true">⚠️</div>
                <div class="error-title">无法获取分析结果</div>
                <div class="error-message" id="errorMessage">请检查卡密是否正确或网络连接是否正常</div>
                <div class="error-actions">
                    <button onclick="retryLoad()" class="retry-btn" aria-label="重试加载">重试</button>
                    <button onclick="tryProgressiveLoad()" class="progressive-btn" aria-label="渐进式加载">先看排盘</button>
                    <button onclick="goHome()" class="back-home-btn" aria-label="返回主页">返回主页</button>
                </div>
                <div class="error-details">
                    <details>
                        <summary>查看详细信息</summary>
                        <div id="errorDetails" class="error-details-content">暂无详细错误信息</div>
                    </details>
                </div>
            </div>
            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;" aria-live="polite">
                <div class="empty-icon" aria-hidden="true">📄</div>
                <div class="empty-title">暂无分析结果</div>
                <div class="empty-message">请先进行八字分析，或检查URL参数是否正确</div>
                <div class="empty-actions">
                    <button onclick="goHome()" class="start-analysis-btn" aria-label="开始分析">开始分析</button>
                </div>
            </div>
            <!-- 分析内容 -->
            <div id="analysisContent" class="detailed-analysis-content" style="display: none;">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
        <!-- 底部导航 -->
        <div class="detailed-footer">
            <button onclick="goHome()" class="detailed-footer-btn" aria-label="返回首页">
                <span class="footer-icon">🏠</span>
                <span class="footer-text">首页</span>
            </button>
            <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="detailed-footer-btn" aria-label="返回顶部">
                <span class="footer-icon">⬆️</span>
                <span class="footer-text">顶部</span>
            </button>
        </div>
    </div>

    <!-- 添加Marked.js库用于Markdown解析 -->
    <script src="js/marked.min.js"></script>
    <script src="js/wuxing_colors.js"></script>
    <script src="js/html2pdf.bundle.min.js"></script>
    <!-- 添加html2canvas库用于导出图片 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <!-- 添加JSZip库用于导出文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <!-- 拆分后的功能模块 -->
    <script src="js/ui_detailed_helpers.js"></script>
    <script src="js/ui_detailed_states.js"></script>
    <script src="js/detailed_result/detailed_result_bazi_info.js"></script>
    <script src="js/detailed_result/progressive_loader.js"></script>
    <script src="js/detailed_result/title_formatter.js"></script>
    <!-- 图片导出功能必须在主模块之前加载 -->
    <script src="js/detailed_result/image_exporter.js"></script>

    <!-- 确保导出函数可用的备用方案 -->
    <script>
        // 备用函数定义，确保按钮点击不会出错
        window.exportAsImage = window.exportAsImage || function() {
            console.log('尝试调用导出长图功能...');

            // 检查html2canvas是否可用
            if (typeof html2canvas === 'undefined') {
                alert('html2canvas库未加载，请刷新页面重试');
                return;
            }

            // 检查imageExporter是否可用
            if (window.imageExporter && typeof window.imageExporter.exportAsImage === 'function') {
                window.imageExporter.exportAsImage();
                return;
            }

            // 如果imageExporter不可用，使用简单的导出方法
            console.log('使用备用导出方法...');
            exportWithSimpleMethod();
        };

        window.exportAsImageAdvanced = window.exportAsImageAdvanced || function() {
            console.log('尝试调用高级导出功能...');

            if (window.imageExporter && typeof window.imageExporter.showExportOptions === 'function') {
                window.imageExporter.showExportOptions();
            } else {
                // 如果高级功能不可用，使用基础导出
                window.exportAsImage();
            }
        };

        // 简单的导出方法作为备用
        function exportWithSimpleMethod() {
            const contentElement = document.getElementById('analysisContent') ||
                                 document.querySelector('.detailed-result-container') ||
                                 document.querySelector('.detailed-container');

            if (!contentElement) {
                alert('未找到可导出的内容');
                return;
            }

            // 显示进度提示
            const progressDiv = document.createElement('div');
            progressDiv.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8); color: white; padding: 20px;
                border-radius: 10px; z-index: 10000; text-align: center;
            `;
            progressDiv.innerHTML = '正在生成图片，请稍候...';
            document.body.appendChild(progressDiv);

            // 使用html2canvas导出
            html2canvas(contentElement, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            }).then(canvas => {
                // 生成下载链接
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const filename = `八字分析报告_${timestamp}.png`;

                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    // 移除进度提示
                    document.body.removeChild(progressDiv);

                    // 显示成功提示
                    const successDiv = document.createElement('div');
                    successDiv.style.cssText = progressDiv.style.cssText.replace('rgba(0,0,0,0.8)', 'rgba(76,175,80,0.9)');
                    successDiv.innerHTML = '✅ 导出完成！';
                    document.body.appendChild(successDiv);
                    setTimeout(() => document.body.removeChild(successDiv), 2000);
                }, 'image/png', 0.95);
            }).catch(error => {
                console.error('导出失败:', error);
                document.body.removeChild(progressDiv);
                alert('导出失败: ' + error.message);
            });
        }

        console.log('备用导出函数已定义');
    </script>

    <!-- 加载所有详细结果相关的JavaScript文件 -->
    <script src="js/detailed_result/detailed_result_utils.js"></script>
    <script src="js/detailed_result/detailed_result_loader.js"></script>
    <script src="js/detailed_result/detailed_result_display.js"></script>
    <script src="js/detailed_result/detailed_result_events.js"></script>
    <script src="js/detailed_result/detailed_result_core.js"></script>

    <!-- 初始化脚本 -->
    <script>
        // 确保所有函数都正确绑定到全局作用域
        console.log('🔧 [DEBUG] 检查全局函数绑定...');
        console.log('🔧 [DEBUG] generateBaziInfoSection:', typeof window.generateBaziInfoSection);
        console.log('🔧 [DEBUG] toggleSection:', typeof window.toggleSection);
        console.log('🔧 [DEBUG] editTitle:', typeof window.editTitle);
        console.log('🔧 [DEBUG] downloadReport:', typeof window.downloadReport);
        console.log('🔧 [DEBUG] ProgressiveLoader:', typeof window.ProgressiveLoader);
        console.log('🔧 [DEBUG] progressiveLoader实例:', typeof window.progressiveLoader);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 [DEBUG] DOM加载完成，开始初始化...');

            // 确保事件监听器设置
            if (typeof setupEventListeners === 'function') {
                setupEventListeners();
                console.log('✅ [DEBUG] 事件监听器已设置');
            } else {
                console.log('⚠️ [DEBUG] setupEventListeners 函数不存在');
            }

            // 开始渐进式加载
            const urlParams = new URLSearchParams(window.location.search);
            const cardKey = urlParams.get('cardKey');
            const reportId = urlParams.get('id');

            if (cardKey && window.progressiveLoader) {
                console.log('🎯 [DEBUG] 开始渐进式加载，cardKey:', cardKey, 'reportId:', reportId);
                window.progressiveLoader.startProgressiveLoad(cardKey, reportId);
            } else {
                console.log('❌ [DEBUG] 缺少必要参数或progressiveLoader不存在');
                console.log('❌ [DEBUG] cardKey:', cardKey);
                console.log('❌ [DEBUG] progressiveLoader:', window.progressiveLoader);
            }
        });
    </script>
</body>
</html>