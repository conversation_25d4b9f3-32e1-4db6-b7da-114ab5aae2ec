/**
 * 手机端暗色模式农历输入样式
 * 专门针对农历输入部分的暗色模式适配
 */

/* 手机端暗色模式农历输入样式 */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    /* 农历输入区域暗色模式 */
    .lunar-input-section {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
        border: 2px solid var(--border-color);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }
    
    .lunar-input-section:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        transform: translateY(-2px);
    }
    
    .lunar-input-section .section-title {
        color: var(--text-primary);
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    /* 农历输入框暗色模式 */
    .lunar-input-section .input-group label {
        color: var(--text-primary);
    }
    
    .lunar-input-section .input-group input,
    .lunar-input-section .input-group select {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .lunar-input-section .input-group input::placeholder {
        color: var(--text-secondary);
    }
    
    .lunar-input-section .input-group input:focus,
    .lunar-input-section .input-group select:focus {
        border-color: var(--primary-color);
        background: var(--bg-secondary);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    /* 农历转换按钮暗色模式 - 现代美观设计 */
    .convert-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        color: #ffffff;
        border: none;
        border-radius: 50px;
        padding: 16px 32px;
        font-size: 1.1em;
        font-weight: 700;
        letter-spacing: 1px;
        text-transform: uppercase;
        box-shadow: 
            0 10px 30px rgba(102, 126, 234, 0.4),
            0 6px 20px rgba(118, 75, 162, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
    }
    
    .convert-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.8s ease;
    }
    
    .convert-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;
        border-radius: 50%;
    }
    
    .convert-btn:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 50%, #f093fb 100%);
        transform: translateY(-4px) scale(1.05);
        box-shadow: 
            0 15px 40px rgba(102, 126, 234, 0.6),
            0 10px 25px rgba(118, 75, 162, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.4);
    }
    
    .convert-btn:hover::before {
        left: 100%;
    }
    
    .convert-btn:hover::after {
        width: 300px;
        height: 300px;
    }
    
    .convert-btn:active {
        transform: translateY(-2px) scale(1.02);
        box-shadow: 
            0 8px 25px rgba(102, 126, 234, 0.5),
            0 4px 15px rgba(118, 75, 162, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    
    .convert-btn span {
        position: relative;
        z-index: 2;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        font-size: 1.2em;
    }
    
    /* 农历转换结果暗色模式 */
    .convert-result {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-card));
        border: 2px solid var(--border-color);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .convert-result h4 {
        color: var(--text-primary);
    }
    
    .convert-result-item {
        background: rgba(45, 55, 72, 0.8);
        border: 1px solid var(--border-color);
    }
    
    .result-date, 
    .result-time, 
    .result-weekday {
        background: rgba(102, 126, 234, 0.2);
        color: var(--text-primary);
        border: 1px solid rgba(102, 126, 234, 0.3);
    }
    
    .apply-convert-btn {
        background: linear-gradient(135deg, var(--success-color), #38a169);
        color: white;
    }
    
    .apply-convert-btn:hover {
        background: linear-gradient(135deg, #38a169, #48bb78);
    }
    
    /* 闰月复选框暗色模式 */
    .lunar-input-section input[type="checkbox"] {
        accent-color: var(--primary-color);
        filter: brightness(1.2);
    }
    
    .lunar-input-section label {
        color: var(--text-primary);
    }
    
    /* 输入图标暗色模式 */
    .lunar-input-section .input-icon {
        color: var(--text-secondary);
    }
    
    /* 手机端特殊优化 */
    .lunar-input-section .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .lunar-input-section .form-col {
        width: 100%;
    }
    
    .lunar-input-section .input-group {
        margin-bottom: 15px;
    }
    
    .convert-btn {
        width: 100%;
        justify-content: center;
        padding: 15px 24px;
        font-size: 1.1em;
    }
    
    /* 触摸优化 */
    .lunar-input-section .input-group input,
    .lunar-input-section .input-group select {
        min-height: 48px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .convert-btn:active {
        background: rgba(102, 126, 234, 0.8);
        transform: scale(0.98);
    }
    
    .apply-convert-btn:active {
        background: rgba(56, 161, 105, 0.8);
        transform: scale(0.98);
    }
    
    /* 转换结果在手机端的优化 */
    .convert-result-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .result-date, 
    .result-time, 
    .result-weekday {
        text-align: center;
        padding: 12px;
    }
    
    .apply-convert-btn {
        margin-top: 10px;
        width: 100%;
    }
}

/* 仅暗色模式（不限制屏幕尺寸）的农历输入样式 */
@media (prefers-color-scheme: dark) {
    .lunar-input-section {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
        border: 2px solid var(--border-color);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    
    .lunar-input-section .section-title {
        color: var(--text-primary);
    }
    
    .lunar-input-section .input-group label {
        color: var(--text-primary);
    }
    
    .lunar-input-section .input-group input,
    .lunar-input-section .input-group select {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .lunar-input-section .input-group input::placeholder {
        color: var(--text-secondary);
    }
    
    .convert-btn {
        background: linear-gradient(135deg, var(--primary-color), #5a67d8);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
    
    .convert-result {
        background: linear-gradient(135deg, var(--bg-secondary), var(--bg-card));
        border: 2px solid var(--border-color);
    }
    
    .convert-result h4 {
        color: var(--text-primary);
    }
    
    .convert-result-item {
        background: rgba(45, 55, 72, 0.6);
    }
    
    .result-date, 
    .result-time, 
    .result-weekday {
        background: rgba(102, 126, 234, 0.2);
        color: var(--text-primary);
    }
}