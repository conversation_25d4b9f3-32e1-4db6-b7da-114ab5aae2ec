#!/usr/bin/env python3
"""
测试错误处理机制
"""

from app import app
import json

def test_error_handling():
    with app.test_client() as client:
        print("=== 测试错误处理机制 ===")
        
        # 错误测试用例
        error_test_cases = [
            {
                'name': '缺少必要字段',
                'method': 'POST',
                'url': '/webhook/bazi-analysis',
                'data': {
                    "name": "测试用户",
                    "gender": "男"
                    # 缺少 cardKey, year, month, day
                },
                'expected_status': 400,
                'expected_error_contains': '缺少必要字段'
            },
            {
                'name': '无效的卡密',
                'method': 'POST',
                'url': '/webhook/bazi-analysis',
                'data': {
                    "cardKey": "invalid_card_key_12345",
                    "name": "测试用户",
                    "gender": "男",
                    "year": "1990",
                    "month": "5",
                    "day": "15"
                },
                'expected_status': 401,
                'expected_error_contains': '卡密'
            },
            {
                'name': '无效的日期',
                'method': 'POST',
                'url': '/webhook/bazi-analysis',
                'data': {
                    "cardKey": "wdd",
                    "name": "测试用户",
                    "gender": "男",
                    "year": "1990",
                    "month": "13",  # 无效月份
                    "day": "32"     # 无效日期
                },
                'expected_status': [400, 500],  # 可能是400或500
                'expected_error_contains': None  # 不检查具体错误内容
            },
            {
                'name': '查询不存在的分析状态',
                'method': 'GET',
                'url': '/api/analysis/status/nonexistent_request_id',
                'data': None,
                'expected_status': 404,
                'expected_error_contains': None
            },
            {
                'name': '查询不存在的LLM进度',
                'method': 'GET',
                'url': '/api/analysis/llm-progress/nonexistent_request_id',
                'data': None,
                'expected_status': 404,
                'expected_error_contains': None
            },
            {
                'name': '无效的JSON数据',
                'method': 'POST',
                'url': '/webhook/bazi-analysis',
                'data': "invalid json data",  # 不是JSON格式
                'expected_status': 400,
                'expected_error_contains': None,
                'raw_data': True  # 标记为原始数据，不进行JSON编码
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(error_test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            
            try:
                # 准备请求数据
                if test_case['data'] is None:
                    # GET请求，无数据
                    response = client.get(test_case['url'])
                elif test_case.get('raw_data'):
                    # 原始数据，不进行JSON编码
                    response = client.post(test_case['url'],
                                         data=test_case['data'],
                                         content_type='application/json')
                else:
                    # 正常JSON数据
                    if test_case['method'] == 'POST':
                        response = client.post(test_case['url'],
                                             data=json.dumps(test_case['data']),
                                             content_type='application/json')
                    else:
                        response = client.get(test_case['url'])
                
                status_code = response.status_code
                expected_status = test_case['expected_status']
                
                # 检查状态码
                if isinstance(expected_status, list):
                    status_ok = status_code in expected_status
                else:
                    status_ok = status_code == expected_status
                
                print(f"   状态码: {status_code} (期望: {expected_status})")
                
                if status_ok:
                    print(f"   ✅ 状态码正确")
                    
                    # 检查错误消息内容（如果指定了）
                    if test_case['expected_error_contains']:
                        try:
                            response_data = response.get_json()
                            error_msg = response_data.get('error', '') if response_data else ''
                            
                            if test_case['expected_error_contains'].lower() in error_msg.lower():
                                print(f"   ✅ 错误消息包含期望内容: '{test_case['expected_error_contains']}'")
                                results.append({'test_name': test_case['name'], 'success': True})
                            else:
                                print(f"   ❌ 错误消息不包含期望内容")
                                print(f"   实际错误消息: {error_msg}")
                                results.append({'test_name': test_case['name'], 'success': False, 'error': '错误消息内容不匹配'})
                        except:
                            print(f"   ⚠️ 无法解析响应为JSON，但状态码正确")
                            results.append({'test_name': test_case['name'], 'success': True})
                    else:
                        results.append({'test_name': test_case['name'], 'success': True})
                        
                else:
                    print(f"   ❌ 状态码不正确")
                    results.append({'test_name': test_case['name'], 'success': False, 'error': f'状态码 {status_code} != {expected_status}'})
                
                # 显示响应内容（前100字符）
                try:
                    response_text = response.get_data(as_text=True)
                    if response_text:
                        print(f"   响应内容: {response_text[:100]}{'...' if len(response_text) > 100 else ''}")
                except:
                    print(f"   响应内容: 无法读取")
                    
            except Exception as e:
                print(f"   💥 测试异常: {str(e)}")
                results.append({'test_name': test_case['name'], 'success': False, 'error': f'测试异常: {str(e)}'})
        
        # 输出测试结果
        print(f"\n=== 错误处理测试结果 ===")
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        
        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}")
            if not result['success']:
                print(f"   错误: {result.get('error', '未知错误')}")
        
        return success_count == total_count

if __name__ == '__main__':
    try:
        success = test_error_handling()
        print(f"\n{'✅ 错误处理测试全部通过' if success else '❌ 部分错误处理测试失败'}")
    except Exception as e:
        print(f"\n💥 错误处理测试出现异常: {e}")
        import traceback
        traceback.print_exc()
