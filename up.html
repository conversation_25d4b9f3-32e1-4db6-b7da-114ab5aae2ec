<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>☯ 八字分析系统 - 输入信息</title>
    <link rel="stylesheet" href="css/styles.css">
    <!-- Google Fonts 已替换为本地字体文件，通过 css/fonts.css 加载 -->
    <link rel="stylesheet" href="css/up/main.css">

</head>
<body>
    <div class="container">
        <a href="index.html" class="nav-link">
            <span>返回首页</span>
        </a>
        
        <div class="page-header">
            <h1>八字分析系统</h1>
            <p class="header-subtitle">探索命运奥秘，解读人生密码</p>
        </div>
        
        <div id="requestForm" class="form-container">
        
            <form id="textForm">
                <div class="form-section birth-info-section">
                    <h3 class="section-title">📅 出生信息（阳历）</h3>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="input-group">
                                <label for="yearInput">⚌ 出生年份：</label>
                                <input type="number" id="yearInput" min="1900" max="2100" placeholder="例如: 1990" required name="year">
                                <span class="input-icon">📅</span>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="input-group">
                                <label for="monthInput">📅 出生月份：</label>
                                <input type="number" id="monthInput" min="1" max="12" placeholder="例如: 5" required name="month">
                                <span class="input-icon">📅</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="input-group">
                                <label for="dayInput">⚊ 出生日期：</label>
                                <input type="number" id="dayInput" min="1" max="31" placeholder="例如: 15" required name="day">
                                <span class="input-icon">📅</span>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="input-group">
                                <label for="hourInput">☾ 出生时辰：</label>
                                <select id="hourInput" required name="hour">
                                    <option value="">请选择出生时辰</option>
                                    <option value="早子时">早子时 (00:00-01:00)</option>
                                    <option value="丑时">丑时 (01:00-03:00)</option>
                                    <option value="寅时">寅时 (03:00-05:00)</option>
                                    <option value="卯时">卯时 (05:00-07:00)</option>
                                    <option value="辰时">辰时 (07:00-09:00)</option>
                                    <option value="巳时">巳时 (09:00-11:00)</option>
                                    <option value="午时">午时 (11:00-13:00)</option>
                                    <option value="未时">未时 (13:00-15:00)</option>
                                    <option value="申时">申时 (15:00-17:00)</option>
                                    <option value="酉时">酉时 (17:00-19:00)</option>
                                    <option value="戌时">戌时 (19:00-21:00)</option>
                                    <option value="亥时">亥时 (21:00-23:00)</option>
                                    <option value="夜子时">夜子时 (23:00-24:00)</option>
                                </select>
                                <span class="input-icon">⏰</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-section birth-info-section">
                    <h3 class="section-title">👤 个人信息</h3>
                    <div class="input-group">
                        <label for="genderInput">性别</label>
                        <select id="genderInput" required>
                            <option value="">请选择性别</option>
                            <option value="1">乾造 男</option>
                            <option value="0">坤造 女</option>
                        </select>
                        <span class="input-icon">👤</span>
                    </div>
                    
                    <div class="input-group">
                        <label for="cardKeyInput">⚌ 卡密：</label>
                        <input type="text" id="cardKeyInput" placeholder="请输入您的卡密" required>
                        <span class="input-icon">🔑</span>
                    </div>
                    
                    <div class="card-purchase-section">
                        <p class="purchase-hint">还没有卡密？</p>
                        <a href="http://175.24.228.204?from=1000&cid=9&mid=30" target="_blank" class="card-purchase-button">
                            <span>🔑</span> 购买卡密
                        </a>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="submit-btn">
                        <span class="btn-icon">☯</span>
                        <span class="btn-text">开始分析</span>
                        <div class="btn-loading" style="display: none;">
                            <div class="spinner"></div>
                        </div>
                    </button>
                </div>
            </form>
        
            <div id="result" class="result-container">
                <div class="progress-section" style="display: none;">
                    <div class="progress-header">
                        <h4>🔄 分析进度</h4>
                        <span class="elapsed-time"></span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-status"></div>
                    <div class="llm-progress" style="display: none;">
                        <div class="llm-dimensions"></div>
                    </div>
                </div>
                
                <div class="result-content"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 八字分析系统 | 专业命理分析服务</p>
        </div>
    </div>

    <!-- 模块化JavaScript文件 -->
    <script src="js/utils_logger.js"></script>
    <script src="js/ui_custom_dialogs.js"></script>
    <script src="js/console_cleanup.js"></script>
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/api.js"></script>
    <script src="js/analyzer.js"></script>
    <script src="js/main.js"></script>
    <script src="js/bazi-integration.js"></script>
    <script src="js/auto-convert.js"></script>
    <script src="js/ui_reports_polling_functions.js"></script>
    
    <script>
        // 卡密自动填写功能
        function getValidatedCards() {
            try {
                const cardsJson = localStorage.getItem('validatedCards');
                if (cardsJson) {
                    const cards = JSON.parse(cardsJson);
                    // 按时间戳降序排序，最新的在前面
                    return cards.sort((a, b) => b.timestamp - a.timestamp);
                }
            } catch (error) {
                const logger = window.BaziLogger ? window.BaziLogger.forModule('CardFill') : { error: console.error };
                logger.error('解析已验证卡密失败:', error);
                localStorage.removeItem('validatedCards');
            }
            return [];
        }
        
        // 页面加载完成后自动填写卡密
        document.addEventListener('DOMContentLoaded', function() {
            const logger = window.BaziLogger ? window.BaziLogger.forModule('CardFill') : { 
                info: console.log,
                debug: console.log
            };
            
            const cardKeyInput = document.getElementById('cardKeyInput');
            if (cardKeyInput) {
                const validatedCards = getValidatedCards();
                if (validatedCards.length > 0) {
                    // 使用最近验证过的卡密自动填写
                    const latestCard = validatedCards[0];
                    cardKeyInput.value = latestCard.cardKey;
                    logger.debug('自动填写卡密:', latestCard.cardKey);
                    
                    // 触发输入事件以便进行格式验证
                    const inputEvent = new Event('input', { bubbles: true });
                    cardKeyInput.dispatchEvent(inputEvent);
                }
            }
        });
    </script>
</body>
</html>