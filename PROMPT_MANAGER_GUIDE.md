# 提示词管理系统使用指南

## 🎯 系统概述

提示词管理系统是为八字分析系统设计的专业提示词管理工具，提供了Web界面、桌面GUI和API接口三种使用方式，支持提示词的统一管理、版本控制、质量评估和A/B测试。

## 🚀 快速开始

### 1. 环境准备

确保你的系统已安装Python 3.7+和必要的依赖：

```bash
pip install flask flask-cors requests
```

### 2. 启动系统

使用统一启动脚本：

```bash
# 启动Web界面
python start_prompt_manager.py web

# 启动桌面GUI
python start_prompt_manager.py gui

# 同时启动Web和GUI
python start_prompt_manager.py both

# 运行系统测试
python start_prompt_manager.py test

# 查看系统状态
python start_prompt_manager.py status
```

## 🌐 Web界面使用

### 访问地址
- 提示词管理界面：http://localhost:5000/prompt_manager.html
- 系统管理后台：http://localhost:5000/admin.html

### 主要功能

#### 1. 提示词管理
- **编辑提示词**：选择维度后可直接编辑提示词内容
- **预览效果**：实时预览构建后的完整提示词
- **测试分析**：使用测试数据验证提示词效果
- **保存版本**：保存修改并创建版本记录

#### 2. 版本控制
- **创建版本**：为当前提示词配置创建新版本
- **切换版本**：在不同版本间快速切换
- **版本对比**：比较不同版本间的差异
- **版本回滚**：快速回滚到历史版本

#### 3. 质量评估
- **自动评估**：从四个维度自动评估提示词质量
  - 十神使用准确性 (40%)
  - 逻辑一致性 (30%)
  - 格式规范性 (20%)
  - 内容完整性 (10%)
- **问题识别**：自动识别常见问题
- **改进建议**：提供具体的优化建议

#### 4. A/B测试
- **创建实验**：设置不同版本的对比测试
- **流量分配**：灵活配置用户流量分配
- **结果分析**：实时查看测试效果

## 🖥️ 桌面GUI使用

### 启动方式
```bash
python backend/prompt_gui.py
# 或
python start_prompt_manager.py gui
```

### 界面功能

#### 左侧导航
- **📝 提示词编辑**：编辑和管理提示词
- **🔄 版本管理**：版本控制操作
- **📊 质量评估**：质量检查和评估
- **🧪 A/B测试**：实验管理
- **⚙️ 系统设置**：系统配置

#### 主要操作
1. **选择维度**：从下拉菜单选择要编辑的分析维度
2. **加载提示词**：点击"加载"按钮获取当前提示词
3. **编辑内容**：在文本区域直接编辑提示词
4. **预览测试**：使用预览和测试功能验证效果
5. **保存更改**：保存修改并添加版本描述

## 🔧 API接口使用

### 基础API

```python
# 获取可用维度
GET /api/prompt/dimensions

# 获取指定维度的提示词
GET /api/prompt/prompt/<dimension>

# 保存提示词
POST /api/prompt/prompt
{
    "dimension": "性格",
    "content": "提示词内容",
    "description": "修改描述"
}

# 预览提示词效果
POST /api/prompt/prompt/preview
{
    "dimension": "性格",
    "content": "提示词内容"
}
```

### 版本管理API

```python
# 获取版本列表
GET /api/prompt/versions

# 创建新版本
POST /api/prompt/versions
{
    "name": "v1.1.0",
    "description": "优化性格分析逻辑"
}

# 切换版本
POST /api/prompt/versions/<version_name>/switch

# 删除版本
DELETE /api/prompt/versions/<version_name>
```

### 质量评估API

```python
# 运行质量评估
POST /api/prompt/evaluation
{
    "dimension": "性格",
    "bazi_data": "八字测试数据"
}
```

## 📊 系统管理后台

访问 http://localhost:5000/admin.html 进入管理后台

### 功能模块

#### 1. 仪表盘
- 系统运行状态概览
- 关键指标统计
- 快速操作入口

#### 2. 提示词管理
- 提示词系统状态监控
- 快速质量检查
- 备份和恢复操作

#### 3. 系统日志
- 实时日志查看
- 日志下载和清理
- 错误监控

#### 4. 系统设置
- 配置参数管理
- 系统重启和更新
- 数据备份

## 🔄 集成到现有系统

### 1. 后端集成

在你的Flask应用中添加：

```python
# 在app.py中添加
try:
    from prompt_api import init_prompt_api
    init_prompt_api(app)
    print("✅ 提示词管理API已集成")
except ImportError:
    print("⚠️ 提示词管理API不可用")
```

### 2. 前端集成

在现有页面中添加管理入口：

```html
<!-- 添加到导航菜单 -->
<a href="prompt_manager.html" target="_blank">
    🧠 提示词管理
</a>

<a href="admin.html" target="_blank">
    ⚙️ 系统管理
</a>
```

### 3. 使用优化后的分析器

```python
from optimized_bazi_analyzer import OptimizedBaziAnalyzer

# 创建分析器实例
analyzer = OptimizedBaziAnalyzer(
    bazi_data=bazi_data,
    enable_evaluation=True,
    enable_ab_testing=True
)

# 执行分析
result = analyzer.analyze_dimension("性格", user_id="user123")

# 获取分析结果和质量评估
analysis_content = result['analysis_content']
evaluation = result['evaluation']
```

## 🛠️ 高级配置

### 1. 自定义提示词模板

创建JSON格式的提示词配置：

```json
{
  "version": "2.0.0",
  "dimension": "性格",
  "metadata": {
    "author": "system",
    "created_at": "2025-01-30",
    "description": "优化后的性格分析提示词"
  },
  "system_prompt": {
    "role": "你是一位专业的八字命理分析师",
    "constraints": [
      "必须严格按照十神理论进行分析",
      "只能根据八字中实际出现的十神进行分析"
    ]
  },
  "analysis_template": {
    "structure": [
      {
        "section": "总论",
        "title": "## 一、性格总论",
        "content": "**核心特点：** {基于实际十神配置的性格核心特征}"
      }
    ]
  },
  "output_format": {
    "format": "markdown",
    "max_length": 1000,
    "tone": "professional"
  }
}
```

### 2. 质量评估配置

自定义评估权重：

```python
evaluation_config = {
    "weights": {
        "shishen_accuracy": 0.4,
        "logic_consistency": 0.3,
        "format_compliance": 0.2,
        "content_completeness": 0.1
    },
    "thresholds": {
        "excellent": 0.9,
        "good": 0.7,
        "fair": 0.5
    }
}
```

### 3. A/B测试配置

```python
# 创建A/B测试实验
experiment = {
    "name": "personality_optimization_v2",
    "variants": ["v1.0", "v1.1", "v1.2"],
    "traffic_split": [0.33, 0.33, 0.34],
    "description": "性格分析提示词优化测试"
}
```

## 🐛 故障排除

### 常见问题

1. **模块导入错误**
   ```bash
   # 确保在正确的目录下运行
   cd /path/to/your/project
   python start_prompt_manager.py status
   ```

2. **端口被占用**
   ```bash
   # 使用不同端口启动
   python start_prompt_manager.py web --port 5001
   ```

3. **GUI无法启动**
   ```bash
   # 检查tkinter是否安装
   python -c "import tkinter; print('tkinter可用')"
   ```

4. **API调用失败**
   - 检查后端服务是否正常运行
   - 确认API端点地址正确
   - 查看浏览器控制台错误信息

### 日志查看

- Web服务器日志：查看终端输出
- 系统日志：访问管理后台的日志页面
- 提示词日志：检查 `backend/llm_logs/` 目录

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 运行 `python start_prompt_manager.py status` 检查系统状态
3. 查看系统日志获取详细错误信息
4. 检查是否有最新的系统更新

## 🔄 更新日志

### v2.0.0 (2025-01-30)
- ✨ 新增Web管理界面
- ✨ 新增桌面GUI工具
- ✨ 新增系统管理后台
- ✨ 新增版本控制功能
- ✨ 新增质量评估系统
- ✨ 新增A/B测试框架
- 🔧 优化提示词管理架构
- 🔧 改进API接口设计

### v1.0.0
- 🎉 初始版本发布
- ✨ 基础提示词管理功能
