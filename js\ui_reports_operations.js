/**
 * 报告页面操作函数
 * 处理用户与页面的交互功能
 */

// 显示错误信息
function showError(message) {
    const reportsList = document.getElementById('reportsList');
    if (reportsList) {
        reportsList.innerHTML = `
            <div class="error-message">
                <div class="error-icon">⚏</div>
                <h3>加载失败</h3>
                <p>${message}</p>
                <button onclick="refreshReports()" class="action-btn secondary">⚌ 重试</button>
            </div>
        `;
    }
}

// 刷新报告
async function refreshReports() {
    console.log('开始刷新报告列表...');
    
    // 强制清除所有报告缓存
    const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
    cacheKeys.forEach(key => sessionStorage.removeItem(key));
    console.log('已清除所有报告缓存，强制重新获取数据');
    
    // 清除卡密筛选
    document.getElementById('cardKeyFilter').value = '';
    
    // 检查是否有正在进行的分析
    const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
    if (ongoingAnalysis) {
        try {
            const analysis = JSON.parse(ongoingAnalysis);
            // 检查是否超过30分钟，如果是则认为已失效
            if (Date.now() - analysis.startTime < 30 * 60 * 1000) {
                // 如果有正在进行的分析，显示分析状态和历史报告
                const reports = await window.reportsManager.fetchHistoryReports();
                displayAnalysisAndReports(analysis, reports);
                return;
            } else {
                localStorage.removeItem('ongoingAnalysis');
            }
        } catch (error) {
            console.error('解析进行中分析记录失败:', error);
            localStorage.removeItem('ongoingAnalysis');
        }
    }
    
    // 显示所有已验证卡密的报告
    await showAllReports();
}

// 查看报告
function viewReport(reportId) {
    // 从报告列表中找到对应的报告，获取其卡密信息
    const reports = window.reportsManager.getLocalReports();
    const report = reports.find(r => r.id === reportId);
    
    if (report && report.cardKey) {
        // 传递卡密+id参数
        window.location.href = `detailed_result.html?cardKey=${encodeURIComponent(report.cardKey)}&id=${encodeURIComponent(reportId)}`;
    } else {
        // 如果找不到卡密信息，只传递id（向后兼容）
        window.location.href = `detailed_result.html?id=${reportId}`;
    }
}

// 删除报告
async function deleteReport(reportId) {
    if (!reportId) {
            showError('无效的报告ID');
            return;
        }
    
    // 初始化日志记录器
    const operationsLogger = window.BaziLogger ? window.BaziLogger.forModule('ReportOperations') : {
        debug: console.log,
        info: console.log,
        warn: console.warn,
        error: console.error
    };
    
    operationsLogger.debug('尝试删除报告:', reportId);
    
    const confirmed = await customConfirm(
        '确定要删除这份报告吗？此操作无法撤销。',
        '删除确认',
        {
            confirmText: '确定删除',
            cancelText: '取消'
        }
    );
    if (confirmed) {
        try {
            // 删除本地报告
            const reportsJson = localStorage.getItem('baziReports');
            if (reportsJson) {
                let reports = JSON.parse(reportsJson);
                operationsLogger.debug('当前报告数量:', reports.length);
                
                // 查找报告
                const reportIndex = reports.findIndex(report => report.id === reportId);
                if (reportIndex === -1) {
                    operationsLogger.warn('在本地报告列表中未找到要删除的报告:', reportId);
                    
                    // 尝试使用已验证的卡密删除服务器报告
                    const validatedCards = getAllValidatedCardKeys();
                    if (validatedCards.length > 0) {
                        operationsLogger.debug('尝试使用已验证的卡密删除服务器报告');
                        tryDeleteFromServerWithCards(reportId, validatedCards, operationsLogger);
                    } else {
                        // 没有已验证的卡密，询问用户
                        const cardKey = prompt('本地找不到该报告，请输入卡密以从服务器删除：');
                        if (cardKey && cardKey.trim()) {
                            tryDeleteFromServerWithCards(reportId, [cardKey.trim()], operationsLogger);
                        }
                    }
                    return;
                }
                
                // 保存报告的卡密信息（用于服务器删除）
                const cardKey = reports[reportIndex].cardKey;
                
                // 删除报告
                reports.splice(reportIndex, 1);
                operationsLogger.debug('删除后报告数量:', reports.length);
                
                // 保存更新后的报告列表
                localStorage.setItem('baziReports', JSON.stringify(reports));
                operationsLogger.info('已从本地存储删除报告:', reportId);
                
                // 删除服务器报告（如果有API的话）
                if (cardKey) {
                    operationsLogger.debug('尝试从服务器删除报告，卡密:', cardKey);
                    const apiBaseUrl = getApiBaseUrl();
                    fetch(`${apiBaseUrl}/api/reports/delete/${reportId}?cardKey=${encodeURIComponent(cardKey)}`, {
                        method: 'DELETE'
                    })
                    .then(response => {
                        if (response.ok) {
                            operationsLogger.debug('服务器报告删除成功');
                        } else if (response.status === 404) {
                            // 报告已经不存在，可能已被删除，这是正常情况
                            operationsLogger.debug('报告已不存在，可能已被删除');
                        } else if (response.status === 401 || response.status === 403) {
                            // 卡密无效或无权限，这是正常情况，不需要显示错误
                            operationsLogger.debug('卡密无效或无权限，但本地删除已成功');
                        } else {
                            operationsLogger.warn(`服务器删除失败，状态码: ${response.status}`);
                        }
                    })
                    .catch(error => {
                        operationsLogger.warn('删除服务器报告失败:', error);
                    });
                }
                
                // 清除相关缓存
                const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
                cacheKeys.forEach(key => sessionStorage.removeItem(key));
                operationsLogger.debug('已清除相关缓存');
                
                // 刷新列表
                setTimeout(() => {
                    operationsLogger.debug('刷新报告列表');
                    refreshReports();
                }, 100);
                
                // 操作成功提示
                showSuccess('报告已成功删除');
            } else {
                operationsLogger.warn('本地存储中没有找到报告列表');
                
                // 尝试使用已验证的卡密删除服务器报告
                const validatedCards = getAllValidatedCardKeys();
                if (validatedCards.length > 0) {
                    operationsLogger.debug('本地无报告数据，尝试使用已验证的卡密删除服务器报告');
                    tryDeleteFromServerWithCards(reportId, validatedCards, operationsLogger);
                } else {
                    // 没有已验证的卡密，询问用户
                    const cardKey = prompt('本地没有报告数据，请输入卡密以从服务器删除：');
                    if (cardKey && cardKey.trim()) {
                        tryDeleteFromServerWithCards(reportId, [cardKey.trim()], operationsLogger);
                    }
                }
            }
        } catch (error) {
                operationsLogger.error('删除报告失败:', error);
                showError('删除报告失败，请稍后重试');
            }
    } else {
        operationsLogger.debug('用户取消了删除操作');
    }
}

// 根据卡密筛选报告
async function filterReportsByCard() {
    const cardKey = document.getElementById('cardKeyFilter').value.trim();
    if (!cardKey) {
        showWarning('请输入要筛选的卡密');
        return;
    }
    
    document.getElementById('reportsList').innerHTML = `
        <div class="loading-message">
            <div class="loading-icon">⚊</div>
            <h3>正在查询卡密 ${cardKey} 的报告...</h3>
        </div>
    `;
    
    try {
        // 添加到已验证卡密列表（如果还没有的话）
        addValidatedCard(cardKey);
        
        // 获取卡密报告
        const result = await window.reportsManager.fetchReportsByCardKey(cardKey);
        const reports = result.reports || [];
        
        // 显示结果
        if (reports.length > 0) {
            const reportsHtml = window.reportsManager.generateReportsHtml(reports);
            document.getElementById('reportsList').innerHTML = `
                <div class="filtered-reports">
                    <div class="filter-info">
                        <span>⚍ 显示卡密 <strong>${cardKey}</strong> 的报告 (${reports.length}条)</span>
                        <button onclick="clearCardFilter()" class="clear-filter-btn">显示全部</button>
                    </div>
                    ${reportsHtml}
                </div>
            `;
        } else {
            document.getElementById('reportsList').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">⚍</div>
                    <h3>未找到报告</h3>
                    <p>卡密 <strong>${cardKey}</strong> 没有相关的分析报告</p>
                    <button onclick="clearCardFilter()" class="action-btn secondary">显示全部报告</button>
                </div>
            `;
        }
    } catch (error) {
        console.error('筛选报告失败:', error);
        document.getElementById('reportsList').innerHTML = `
            <div class="error-message">
                <div class="error-icon">⚏</div>
                <h3>查询失败</h3>
                <p>筛选卡密报告时出错，请稍后重试</p>
                <button onclick="refreshReports()" class="action-btn secondary">重试</button>
            </div>
        `;
    }
}

// 清除卡密筛选
function clearCardFilter() {
    document.getElementById('cardKeyFilter').value = '';
    showAllReports();
}

// 尝试使用卡密列表从服务器删除报告
async function tryDeleteFromServerWithCards(reportId, cardKeys, logger) {
    const apiBaseUrl = getApiBaseUrl();
    
    for (const cardKey of cardKeys) {
        try {
            logger.debug('尝试使用卡密删除服务器报告:', cardKey);
            
            const response = await fetch(`${apiBaseUrl}/api/reports/delete/${reportId}?cardKey=${encodeURIComponent(cardKey)}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                logger.info('服务器报告删除成功，使用卡密:', cardKey);
                
                // 清除相关缓存
                const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
                cacheKeys.forEach(key => sessionStorage.removeItem(key));
                
                // 刷新列表
                setTimeout(() => {
                    refreshReports();
                }, 100);
                
                showSuccess('报告已成功从服务器删除');
                return true;
            } else if (response.status === 404) {
                logger.debug('报告不存在或已被删除');
                continue; // 尝试下一个卡密
            } else if (response.status === 401 || response.status === 403) {
                logger.debug('卡密无效或无权限:', cardKey);
                continue; // 尝试下一个卡密
            } else {
                logger.warn('删除失败，状态码:', response.status);
                continue;
            }
        } catch (error) {
            logger.warn('删除请求失败:', error);
            continue;
        }
    }
    
    // 所有卡密都尝试失败
    logger.error('所有卡密都无法删除该报告');
    showError('无法删除该报告，可能报告不存在或卡密无效');
    return false;
}

// 显示所有已验证卡密的报告
async function showAllReports() {
    document.getElementById('reportsList').innerHTML = `
        <div class="loading-message">
            <div class="loading-icon">⚊</div>
            <h3>正在加载所有报告...</h3>
        </div>
    `;
    
    try {
        const allReports = await window.reportsManager.fetchHistoryReports();
        
        if (allReports.length > 0) {
            const reportsHtml = window.reportsManager.generateReportsHtml(allReports);
            const validatedCards = getValidatedCards();
            const cardCount = validatedCards.length;
            
            document.getElementById('reportsList').innerHTML = `
                <div class="all-reports">
                    <div class="reports-info">
                        <span>📊 显示所有报告 (${allReports.length}条，来自${cardCount}个卡密)</span>
                    </div>
                    ${reportsHtml}
                </div>
            `;
        } else {
            document.getElementById('reportsList').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📝</div>
                    <h3>暂无报告</h3>
                    <p>还没有任何分析报告</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载报告失败:', error);
        document.getElementById('reportsList').innerHTML = `
            <div class="error-message">
                <div class="error-icon">⚏</div>
                <h3>加载失败</h3>
                <p>加载报告时出错，请稍后重试</p>
                <button onclick="showAllReports()" class="action-btn secondary">重试</button>
            </div>
        `;
    }
}