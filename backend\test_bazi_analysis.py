#!/usr/bin/env python3
"""
测试八字分析流程
"""

from app import app
import json
import time

def test_bazi_analysis():
    with app.test_client() as client:
        print("=== 测试八字分析流程 ===")
        
        # 测试数据
        test_data = {
            "name": "测试用户",
            "gender": "男",
            "year": "1990",
            "month": "5",
            "day": "15",
            "hour": "10",
            "minute": "30",
            "cardKey": "wdd"  # 使用有效的测试卡密
        }
        
        print(f"\n1. 提交八字分析请求...")
        print(f"测试数据: {test_data}")
        
        # 提交分析请求
        response = client.post('/webhook/bazi-analysis', 
                             data=json.dumps(test_data),
                             content_type='application/json')
        
        print(f'POST /webhook/bazi-analysis - Status: {response.status_code}')
        
        if response.status_code == 200:
            result = response.get_json()
            print(f'Analysis submitted: {result.get("success", False)}')
            
            if result.get("success") and "requestId" in result:
                analysis_id = result["requestId"]
                print(f'Request ID: {analysis_id}')
                
                # 测试状态查询
                print(f"\n2. 查询分析状态...")
                for i in range(3):  # 查询3次状态
                    response = client.get(f'/api/analysis/status/{analysis_id}')
                    print(f'GET /api/analysis/status/{analysis_id} - Status: {response.status_code}')
                    
                    if response.status_code == 200:
                        status = response.get_json()
                        print(f'  Status: {status.get("status", "unknown")}')
                        print(f'  Message: {status.get("message", "N/A")}')
                        
                        if status.get("status") == "completed":
                            print("  ✅ 分析已完成")
                            break
                        elif status.get("status") == "failed":
                            print("  ❌ 分析失败")
                            break
                    
                    time.sleep(1)  # 等待1秒
                
                # 测试LLM进度查询
                print(f"\n3. 查询LLM进度...")
                response = client.get(f'/api/analysis/llm-progress/{analysis_id}')
                print(f'GET /api/analysis/llm-progress/{analysis_id} - Status: {response.status_code}')
                
                if response.status_code == 200:
                    progress = response.get_json()
                    print(f'  LLM Progress available: {progress.get("has_llm_progress", False)}')
            else:
                print("❌ 分析请求失败或没有返回requestId")
        else:
            print(f"❌ 分析请求失败: {response.status_code}")
            if response.data:
                print(f"错误信息: {response.get_data(as_text=True)}")
        
        return True

if __name__ == '__main__':
    try:
        test_bazi_analysis()
        print("\n✅ 八字分析流程测试完成")
    except Exception as e:
        print(f"\n❌ 八字分析流程测试失败: {e}")
        import traceback
        traceback.print_exc()
