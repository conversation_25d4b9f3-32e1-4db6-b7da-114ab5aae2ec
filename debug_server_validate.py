import sys
sys.path.append('backend')

from card_manager import CardManager
from datetime import datetime
import json

# 创建与服务器相同的CardManager实例
print("=== 调试服务器端validate_card函数 ===")

cm = CardManager()
target_card = 'NWDT1fTupGJiyy7B'

print(f"\n1. 检查卡密是否存在")
exists = target_card in cm.cards_data
print(f"卡密存在: {exists}")

if exists:
    card_info = cm.cards_data[target_card]
    print(f"\n2. 卡密详细信息:")
    print(json.dumps(card_info, indent=2, ensure_ascii=False))
    
    print(f"\n3. 逐步验证过程:")
    
    # 检查valid字段
    valid_field = card_info.get('valid', False)
    print(f"valid字段: {valid_field}")
    
    # 检查过期时间
    expire_time_str = card_info.get('expire_time')
    print(f"过期时间字符串: {expire_time_str}")
    
    try:
        expire_time = datetime.fromisoformat(expire_time_str)
        current_time = datetime.now()
        is_expired = current_time > expire_time
        print(f"当前时间: {current_time}")
        print(f"过期时间: {expire_time}")
        print(f"是否过期: {is_expired}")
    except Exception as e:
        print(f"解析过期时间失败: {e}")
        is_expired = True
    
    # 检查使用次数
    usage_count = card_info.get('usage_count', 0)
    max_usage = card_info.get('max_usage', 1)
    usage_exceeded = usage_count >= max_usage
    print(f"使用次数: {usage_count}/{max_usage}")
    print(f"使用次数超限: {usage_exceeded}")
    
    # 最终验证结果
    final_result = valid_field and not is_expired and not usage_exceeded
    print(f"\n4. 最终验证结果: {final_result}")
    
    # 调用实际的validate_card函数
    print(f"\n5. 调用validate_card函数:")
    actual_result = cm.validate_card(target_card)
    print(f"validate_card返回: {actual_result}")
    
    # 比较结果
    if final_result != actual_result:
        print(f"\n⚠️ 警告: 手动计算结果({final_result})与函数返回结果({actual_result})不一致!")
    else:
        print(f"\n✅ 手动计算结果与函数返回结果一致")
else:
    print("卡密不存在，无法进行验证")

# 测试其他几个卡密
print(f"\n=== 测试其他卡密 ===")
all_cards = list(cm.cards_data.keys())[:3]
for card in all_cards:
    result = cm.validate_card(card)
    print(f"{card}: {result}")