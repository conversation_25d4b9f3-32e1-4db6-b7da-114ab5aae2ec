/* 结果页面样式集合 */

/* 导入所有CSS组件 */
@import url('variables.css');
@import url('base.css');
@import url('navigation.css');
@import url('buttons.css');
@import url('cards.css');
@import url('modal.css');
@import url('loading.css');
@import url('error.css');
@import url('empty.css');
@import url('responsive.css');
@import url('dark-mode.css');
@import url('animation/keyframes.css');

/* 额外的全局样式 */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }
.ml-5 { margin-left: 2rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }
.mr-5 { margin-right: 2rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 2rem; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }
.gap-5 { gap: 2rem; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }

.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-full { border-radius: 9999px; }

/* 操作区域 */
.actions-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin: 24px 0;
    background: var(--bg-card, white);
    border-radius: var(--radius-large, 12px);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color-light);
}

/* 卡片筛选器 */
.card-filter {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    justify-content: flex-end;
}

.card-filter-input {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-medium, 8px);
    padding: 10px 16px;
    min-width: 240px;
    font-size: 0.95rem;
    outline: none;
    transition: all 0.3s ease;
}

.card-filter-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 卡片样式 */
.report-card {
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-large, 12px);
    overflow: hidden;
    background: var(--bg-card, white);
    border: 1px solid var(--border-color-light);
    transition: all 0.3s ease;
    position: relative;
    margin-bottom: 20px;
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

.report-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color-light);
    background: linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.1));
}

.report-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.report-source {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.75rem;
    background: var(--bg-tertiary, #f3f4f6);
    color: var(--text-secondary);
    margin-top: 8px;
    border: 1px solid var(--border-color-light);
}

.report-source.local {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.report-source.server {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.report-meta {
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-date {
    font-size: 0.9rem;
    color: var(--text-light);
}

.report-summary {
    padding: 0 20px 16px;
}

.report-summary p {
    margin: 8px 0;
    font-size: 0.95rem;
    color: var(--text-secondary);
}

.report-actions {
    padding: 16px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid var(--border-color-light);
}

.view-btn, 
.delete-btn {
    padding: 8px 16px;
    border-radius: var(--radius-medium, 8px);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.view-btn {
    background: var(--primary-gradient, linear-gradient(135deg, #3b82f6 0%, #2563eb 100%));
    color: white;
    border: none;
}

.view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(59, 130, 246, 0.2);
}

.delete-btn {
    background: transparent;
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.delete-btn:hover {
    background: #fee2e2;
    border-color: #ef4444;
}

/* 报告列表 */
.reports-container {
    margin-top: 30px;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .card-filter {
        justify-content: space-between;
        margin-top: 16px;
    }
    
    .card-filter-input {
        flex: 1;
        min-width: 0;
    }
    
    .report-actions {
        flex-direction: column;
    }
    
    .view-btn, 
    .delete-btn {
        width: 100%;
        padding: 12px;
    }
}

/* 清除筛选按钮样式 */
.clear-filter-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: var(--radius-medium, 8px);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.clear-filter-btn:hover {
    border-color: var(--primary-color, #3b82f6);
    color: var(--primary-color, #3b82f6);
}

/* 深色模式全局样式 */
@media (prefers-color-scheme: dark) {
    /* 确保所有元素在深色模式下有适当的背景和前景色 */
    body * {
        background-color: transparent;
    }
    
    /* 全局白色背景覆盖 */
    [style*="background: white"],
    [style*="background: #fff"],
    [style*="background: #ffffff"],
    [style*="background-color: white"],
    [style*="background-color: #fff"],
    [style*="background-color: #ffffff"],
    [style*="background:#fff"],
    [style*="background:#ffffff"],
    [style*="background-color:#fff"],
    [style*="background-color:#ffffff"],
    div[style*="background"],
    span[style*="background"],
    p[style*="background"] {
        background: rgba(15, 23, 42, 0.8) !important;
        background-color: rgba(15, 23, 42, 0.8) !important;
        color: #f1f5f9 !important;
    }
    
    /* 全局白色文本颜色覆盖 - 排除特定元素 */
    [style*="color: white"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color: #fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color: #ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color:#fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color:#ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn) {
        color: #0f172a !important;
        background-color: #f1f5f9 !important;
        padding: 0 4px !important;
        border-radius: 4px !important;
    }

    /* 确保特定元素的白色文字不被覆盖 */
    .force-complete-btn,
    .item-title:hover .title-text,
    .item-title:hover .title-icon,
    .item-title:hover .title-level,
    .item-title:hover *,
    .detailed-action-btn,
    .special-title *,
    .number,
    .term {
        color: white !important;
        background-color: transparent !important;
        padding: initial !important;
        -webkit-text-fill-color: white !important;
        text-fill-color: white !important;
    }

    /* 确保 action-btn.secondary 按钮样式不被覆盖 */
    .action-btn.secondary {
        background: white !important;
        color: var(--primary-color) !important;
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }

    .action-btn.secondary:hover {
        background: rgba(102, 126, 234, 0.05) !important;
        color: var(--primary-color) !important;
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }
    
    /* 链接和按钮在深色模式下的样式 */
    a:not(.btn):not(.nav-link) {
        color: #60a5fa;
    }
    
    a:not(.btn):not(.nav-link):hover {
        color: #93c5fd;
    }
    
    /* 表格深色模式 */
    table {
        border-color: #334155;
    }
    
    th {
        background-color: #334155;
        color: #f8fafc;
    }
    
    td {
        border-color: #475569;
    }
    
    tr:nth-child(even) {
        background-color: rgba(51, 65, 85, 0.5);
    }
} 