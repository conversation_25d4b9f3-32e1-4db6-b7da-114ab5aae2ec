#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from card_manager import CardManager
from datetime import datetime
import json

def debug_card_validation():
    print("=== 卡密验证调试 ===")
    
    # 初始化卡密管理器
    cm = CardManager()
    card_key = 'NWDT1fTupGJiyy7B'
    
    print(f"\n1. 检查卡密是否存在")
    print(f"卡密 '{card_key}' 存在: {card_key in cm.cards_data}")
    
    if card_key not in cm.cards_data:
        print("❌ 卡密不存在于数据库中")
        return
    
    # 获取卡密信息
    info = cm.cards_data[card_key]
    print(f"\n2. 卡密详细信息:")
    print(json.dumps(info, indent=2, ensure_ascii=False))
    
    print(f"\n3. 验证各项条件:")
    
    # 检查有效状态
    valid_status = info.get('valid', False)
    print(f"有效状态: {valid_status} {'✅' if valid_status else '❌'}")
    
    # 检查过期时间
    expire_time_str = info.get('expire_time')
    print(f"过期时间字符串: {expire_time_str}")
    
    try:
        expire_time = datetime.fromisoformat(expire_time_str)
        current_time = datetime.now()
        is_expired = current_time > expire_time
        
        print(f"过期时间: {expire_time}")
        print(f"当前时间: {current_time}")
        print(f"是否过期: {is_expired} {'❌' if is_expired else '✅'}")
    except Exception as e:
        print(f"❌ 解析过期时间失败: {e}")
        return
    
    # 检查使用次数
    usage_count = info.get('usage_count', 0)
    max_usage = info.get('max_usage', 0)
    usage_exceeded = usage_count >= max_usage
    
    print(f"使用次数: {usage_count}/{max_usage}")
    print(f"使用次数超限: {usage_exceeded} {'❌' if usage_exceeded else '✅'}")
    
    print(f"\n4. 最终验证结果:")
    validation_result = cm.validate_card(card_key)
    print(f"validate_card() 返回: {validation_result} {'✅' if validation_result else '❌'}")
    
    # 分析失败原因
    if not validation_result:
        print(f"\n❌ 验证失败原因分析:")
        if not valid_status:
            print("- 卡密状态为无效")
        if is_expired:
            print("- 卡密已过期")
        if usage_exceeded:
            print("- 使用次数已达上限")
    else:
        print(f"\n✅ 卡密验证通过")

if __name__ == "__main__":
    debug_card_validation()