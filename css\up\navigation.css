/* 导航链接样式 */
.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 20px;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color-light);
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.nav-link:hover {
    transform: translateX(-5px) translateY(-2px);
    color: var(--accent-color);
    background: white;
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.nav-link::before {
    content: '←';
    font-size: 1.2em;
    transition: transform 0.3s ease;
}

.nav-link:hover::before {
    transform: translateX(-3px);
} 