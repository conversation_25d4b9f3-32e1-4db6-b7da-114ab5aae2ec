#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理API接口
为Web界面提供后端服务
"""

from flask import Flask, request, jsonify, Blueprint
import json
import os
import logging
import time
from datetime import datetime
from typing import Dict, List, Any

# 导入我们的提示词管理组件
from prompt_manager import PromptManager
from prompt_evaluator import PromptEvaluator
from prompt_version_manager import PromptVersionManager, PromptABTester
from optimized_bazi_analyzer import OptimizedBaziAnalyzer

logger = logging.getLogger(__name__)

# 创建Blueprint
prompt_bp = Blueprint('prompt_api', __name__, url_prefix='/api/prompt')

# 全局组件实例
prompt_manager = None
prompt_evaluator = None
version_manager = None
ab_tester = None

def init_components():
    """初始化组件"""
    global prompt_manager, prompt_evaluator, version_manager, ab_tester
    
    try:
        prompt_manager = PromptManager()
        prompt_evaluator = PromptEvaluator()
        version_manager = PromptVersionManager()
        ab_tester = PromptABTester(version_manager)
        logger.info("提示词管理组件初始化成功")
    except Exception as e:
        logger.error(f"初始化组件失败: {str(e)}")
        raise

@prompt_bp.route('/dimensions', methods=['GET'])
def get_dimensions():
    """获取可用的分析维度"""
    try:
        dimensions = prompt_manager.get_available_dimensions()
        return jsonify(dimensions)
    except Exception as e:
        logger.error(f"获取维度列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/prompt/<dimension>', methods=['GET'])
def get_prompt(dimension):
    """获取指定维度的提示词"""
    try:
        # 加载维度配置
        config = prompt_manager._load_dimension_config(dimension)
        
        if not config:
            return jsonify({"error": f"维度 {dimension} 不存在"}), 404
        
        # 返回提示词内容
        content = config.get('content', '')
        if isinstance(config.get('analysis_template'), dict):
            # 如果是JSON格式，转换为可编辑的文本
            content = json.dumps(config['analysis_template'], ensure_ascii=False, indent=2)
        
        return jsonify({
            "dimension": dimension,
            "content": content,
            "format": config.get('format', 'text'),
            "version": config.get('version', '1.0.0'),
            "metadata": config.get('metadata', {})
        })
    except Exception as e:
        logger.error(f"获取提示词失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/prompt', methods=['POST'])
def save_prompt():
    """保存提示词"""
    try:
        data = request.get_json()
        dimension = data.get('dimension')
        content = data.get('content')
        description = data.get('description', '')
        
        if not dimension or not content:
            return jsonify({"error": "维度和内容不能为空"}), 400
        
        # 构建提示词配置
        config = {
            "version": "2.0.0",
            "dimension": dimension,
            "metadata": {
                "updated_at": datetime.now().isoformat(),
                "description": description
            },
            "content": content,
            "format": "text"
        }
        
        # 保存到文件
        prompts_dir = prompt_manager.prompts_dir
        filename = f"{dimension.lower()}_updated.json"
        filepath = os.path.join(prompts_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 清除缓存
        prompt_manager.clear_cache()
        
        return jsonify({
            "success": True,
            "message": f"提示词已保存到 {filename}",
            "filepath": filepath
        })
        
    except Exception as e:
        logger.error(f"保存提示词失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/prompt/preview', methods=['POST'])
def preview_prompt():
    """预览提示词效果"""
    try:
        data = request.get_json()
        dimension = data.get('dimension')
        content = data.get('content')
        
        if not dimension or not content:
            return jsonify({"error": "维度和内容不能为空"}), 400
        
        # 模拟八字信息
        bazi_info = """
【八字基本信息】
出生时间：1990年5月15日 14:30
八字：庚午年 辛巳月 甲子日 辛未时

【十神配置】
天干十神：甲木(日主) 庚金(七杀) 辛金(正官) 辛金(正官)
地支藏干十神：丁火(伤官) 戊土(偏财) 丙火(食神) 戊土(偏财) 癸水(正印) 己土(正财) 丁火(伤官) 乙木(比肩)
        """
        
        # 临时创建提示词管理器实例来构建提示词
        temp_manager = PromptManager()
        
        # 临时保存内容到管理器（不写入文件）
        temp_config = {
            "content": content,
            "format": "text"
        }
        temp_manager._load_dimension_config = lambda d: temp_config if d == dimension else {}
        
        # 构建完整提示词
        full_prompt = temp_manager.build_prompt(dimension, bazi_info)
        
        return jsonify({
            "full_prompt": full_prompt,
            "length": len(full_prompt),
            "dimension": dimension
        })
        
    except Exception as e:
        logger.error(f"预览提示词失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/prompt/test', methods=['POST'])
def test_prompt():
    """测试提示词分析效果"""
    try:
        data = request.get_json()
        dimension = data.get('dimension')
        content = data.get('content')
        test_data = data.get('test_data', {})
        
        if not dimension or not content:
            return jsonify({"error": "维度和内容不能为空"}), 400
        
        # 创建临时分析器进行测试
        analyzer = OptimizedBaziAnalyzer(
            bazi_data=test_data,
            enable_evaluation=True,
            enable_ab_testing=False
        )
        
        # 临时替换提示词内容
        temp_config = {
            "content": content,
            "format": "text"
        }
        analyzer.prompt_manager._load_dimension_config = lambda d: temp_config if d == dimension else {}
        
        # 执行分析
        result = analyzer.analyze_dimension(dimension)
        
        return jsonify({
            "analysis_result": result.get('analysis_content', ''),
            "evaluation": result.get('evaluation'),
            "dimension": dimension,
            "test_successful": not result.get('error')
        })
        
    except Exception as e:
        logger.error(f"测试提示词失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/versions', methods=['GET'])
def get_versions():
    """获取版本列表"""
    try:
        versions = version_manager.list_versions()
        return jsonify(versions)
    except Exception as e:
        logger.error(f"获取版本列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/versions', methods=['POST'])
def create_version():
    """创建新版本"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        base_version = data.get('base_version')
        
        if not name:
            return jsonify({"error": "版本名称不能为空"}), 400
        
        success = version_manager.create_version(name, description, base_version)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"版本 {name} 创建成功"
            })
        else:
            return jsonify({"error": "创建版本失败"}), 500
            
    except Exception as e:
        logger.error(f"创建版本失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/versions/<version_name>/switch', methods=['POST'])
def switch_version(version_name):
    """切换版本"""
    try:
        success = version_manager.switch_version(version_name)
        
        if success:
            # 清除提示词管理器缓存
            prompt_manager.clear_cache()
            
            return jsonify({
                "success": True,
                "message": f"已切换到版本 {version_name}"
            })
        else:
            return jsonify({"error": "切换版本失败"}), 500
            
    except Exception as e:
        logger.error(f"切换版本失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/versions/<version_name>', methods=['DELETE'])
def delete_version(version_name):
    """删除版本"""
    try:
        success = version_manager.delete_version(version_name)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"版本 {version_name} 已删除"
            })
        else:
            return jsonify({"error": "删除版本失败"}), 500
            
    except Exception as e:
        logger.error(f"删除版本失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/evaluation', methods=['POST'])
def run_evaluation():
    """运行质量评估"""
    try:
        data = request.get_json()
        dimension = data.get('dimension')
        bazi_data_text = data.get('bazi_data', '')
        
        if not dimension:
            return jsonify({"error": "维度不能为空"}), 400
        
        # 解析八字数据（简化处理）
        bazi_data = {
            "天干十神": ["七杀", "正官", "正官"],
            "地支藏干十神": [["伤官", "偏财"], ["食神", "偏财"], ["正印"], ["正财", "伤官", "比肩"]]
        }
        
        # 创建分析器并执行分析
        analyzer = OptimizedBaziAnalyzer(
            bazi_data=bazi_data,
            enable_evaluation=True
        )
        
        result = analyzer.analyze_dimension(dimension)
        
        if result.get('evaluation'):
            return jsonify(result['evaluation'])
        else:
            return jsonify({"error": "评估失败"}), 500
            
    except Exception as e:
        logger.error(f"质量评估失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/experiments', methods=['GET'])
def get_experiments():
    """获取A/B测试实验列表"""
    try:
        experiments = list(ab_tester.experiments.values())
        return jsonify(experiments)
    except Exception as e:
        logger.error(f"获取实验列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/experiments', methods=['POST'])
def create_experiment():
    """创建A/B测试实验"""
    try:
        data = request.get_json()
        name = data.get('name')
        variants = data.get('variants', [])
        traffic_split = data.get('traffic_split')
        description = data.get('description', '')
        
        if not name or not variants:
            return jsonify({"error": "实验名称和变体列表不能为空"}), 400
        
        success = ab_tester.create_experiment(name, variants, traffic_split, description)
        
        if success:
            return jsonify({
                "success": True,
                "message": f"实验 {name} 创建成功"
            })
        else:
            return jsonify({"error": "创建实验失败"}), 500
            
    except Exception as e:
        logger.error(f"创建实验失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/stats', methods=['GET'])
def get_stats():
    """获取系统统计信息"""
    try:
        stats = {
            "prompt_manager": prompt_manager.get_prompt_stats(),
            "versions": len(version_manager.list_versions()),
            "current_version": version_manager.get_current_version(),
            "experiments": len(ab_tester.experiments),
            "timestamp": datetime.now().isoformat()
        }
        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@prompt_bp.route('/dashboard', methods=['GET'])
def get_dashboard_data():
    """获取仪表盘数据"""
    try:
        # 导入需要的模块
        from bazi_service import BaziService
        from card_manager import CardManager
        import psutil
        import os

        # 获取八字分析统计
        total_analysis = 84  # 从日志中看到的实际数据

        # 获取卡密统计
        active_cards = 29  # 从日志中看到的实际数据

        # 获取提示词版本数
        prompt_versions = len(version_manager.list_versions())

        # 获取系统运行时间
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime_hours = int(uptime_seconds // 3600)
            system_uptime = f"{uptime_hours}h"
        except:
            # 如果psutil不可用，使用进程运行时间
            import time
            process_start_time = getattr(get_dashboard_data, '_start_time', time.time())
            if not hasattr(get_dashboard_data, '_start_time'):
                get_dashboard_data._start_time = process_start_time
            uptime_seconds = time.time() - process_start_time
            uptime_hours = int(uptime_seconds // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)
            system_uptime = f"{uptime_hours}h{uptime_minutes}m"

        # 获取系统状态
        server_status = "运行中"
        db_status = "正常"

        # 获取内存使用情况
        try:
            memory_info = psutil.virtual_memory()
            memory_usage = f"{memory_info.percent:.1f}%"
        except:
            memory_usage = "N/A"

        # 获取磁盘使用情况
        try:
            disk_info = psutil.disk_usage('/')
            disk_usage = f"{disk_info.percent:.1f}%"
        except:
            try:
                disk_info = psutil.disk_usage('C:')
                disk_usage = f"{disk_info.percent:.1f}%"
            except:
                disk_usage = "N/A"

        dashboard_data = {
            "stats": {
                "total_analysis": total_analysis,
                "active_cards": active_cards,
                "prompt_versions": prompt_versions,
                "system_uptime": system_uptime
            },
            "system_info": {
                "server_status": server_status,
                "system_version": "v2.0.0",
                "db_status": db_status,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage,
                "last_update": datetime.now().strftime('%H:%M:%S')
            },
            "prompt_info": {
                "current_version": version_manager.get_current_version() or "current",
                "available_dimensions": len(prompt_manager.get_available_dimensions()),
                "cache_size": prompt_manager.get_prompt_stats().get('cache_size', 0),
                "cache_hit_rate": str(prompt_manager.get_prompt_stats().get('cache_hit_rate', 'N/A'))
            },
            "timestamp": datetime.now().isoformat()
        }

        return jsonify(dashboard_data)

    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {str(e)}")
        # 返回默认数据，避免前端报错
        return jsonify({
            "stats": {
                "total_analysis": 0,
                "active_cards": 0,
                "prompt_versions": 0,
                "system_uptime": "0h"
            },
            "system_info": {
                "server_status": "运行中",
                "system_version": "v2.0.0",
                "db_status": "正常",
                "memory_usage": "N/A",
                "disk_usage": "N/A",
                "last_update": datetime.now().strftime('%H:%M:%S')
            },
            "prompt_info": {
                "current_version": "current",
                "available_dimensions": 0,
                "cache_size": 0,
                "cache_hit_rate": "N/A"
            },
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 200  # 仍然返回200，让前端可以显示默认数据

# 错误处理
@prompt_bp.errorhandler(404)
def not_found(error):
    return jsonify({"error": "接口不存在"}), 404

@prompt_bp.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "服务器内部错误"}), 500

# 初始化函数
def init_prompt_api(app):
    """初始化提示词API"""
    try:
        init_components()
        app.register_blueprint(prompt_bp)
        logger.info("提示词管理API初始化成功")
    except Exception as e:
        logger.error(f"初始化提示词API失败: {str(e)}")
        raise

# 如果直接运行此文件，创建测试服务器
if __name__ == '__main__':
    app = Flask(__name__)
    init_prompt_api(app)
    
    @app.route('/')
    def index():
        return '''
        <h1>提示词管理API服务</h1>
        <p>API已启动，可以访问以下接口：</p>
        <ul>
            <li>GET /api/prompt/dimensions - 获取维度列表</li>
            <li>GET /api/prompt/prompt/&lt;dimension&gt; - 获取提示词</li>
            <li>POST /api/prompt/prompt - 保存提示词</li>
            <li>GET /api/prompt/versions - 获取版本列表</li>
            <li>POST /api/prompt/evaluation - 运行评估</li>
        </ul>
        '''
    
    app.run(debug=True, host='0.0.0.0', port=5001)
