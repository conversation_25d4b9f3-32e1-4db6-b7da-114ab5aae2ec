#!/usr/bin/env python3
"""
测试前后端数据传输完整性
"""

from app import app
import json
import time

def test_data_transmission():
    with app.test_client() as client:
        print("=== 测试前后端数据传输 ===")
        
        # 测试用例：包含各种数据类型和特殊字符
        test_cases = [
            {
                'name': '基本数据传输',
                'data': {
                    "cardKey": "wdd",
                    "name": "测试用户",
                    "gender": "男",
                    "year": "1990",
                    "month": "5",
                    "day": "15",
                    "hour": "10",
                    "minute": "30"
                }
            },
            {
                'name': '特殊字符数据传输',
                'data': {
                    "cardKey": "wdd",
                    "name": "张三李四王五赵六",
                    "gender": "女",
                    "year": "1985",
                    "month": "12",
                    "day": "25",
                    "hour": "23",
                    "minute": "59",
                    "location": "北京市朝阳区"
                }
            },
            {
                'name': '边界值数据传输',
                'data': {
                    "cardKey": "wdd",
                    "name": "A" * 50,  # 长名字
                    "gender": "男",
                    "year": "1900",    # 最小年份
                    "month": "1",      # 最小月份
                    "day": "1",        # 最小日期
                    "hour": "0",       # 最小小时
                    "minute": "0"      # 最小分钟
                }
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            
            try:
                # 发送POST请求
                response = client.post('/webhook/bazi-analysis',
                                     data=json.dumps(test_case['data']),
                                     content_type='application/json')
                
                print(f"   请求状态: {response.status_code}")
                
                if response.status_code == 200:
                    response_data = response.get_json()
                    print(f"   响应成功: {response_data.get('success', False)}")
                    
                    if response_data.get('success'):
                        request_id = response_data.get('requestId')
                        print(f"   请求ID: {request_id}")
                        
                        # 验证数据是否正确传输
                        if request_id:
                            # 等待一下让处理开始
                            time.sleep(1)
                            
                            # 查询状态
                            status_response = client.get(f'/api/analysis/status/{request_id}')
                            if status_response.status_code == 200:
                                status_data = status_response.get_json()
                                print(f"   状态查询成功: {status_data.get('status', 'unknown')}")
                                
                                results.append({
                                    'test_name': test_case['name'],
                                    'success': True,
                                    'request_id': request_id,
                                    'status': status_data.get('status', 'unknown')
                                })
                            else:
                                print(f"   状态查询失败: {status_response.status_code}")
                                results.append({
                                    'test_name': test_case['name'],
                                    'success': False,
                                    'error': f'状态查询失败: {status_response.status_code}'
                                })
                        else:
                            print("   ❌ 响应中缺少requestId")
                            results.append({
                                'test_name': test_case['name'],
                                'success': False,
                                'error': '响应中缺少requestId'
                            })
                    else:
                        error_msg = response_data.get('error', '未知错误')
                        print(f"   ❌ 请求失败: {error_msg}")
                        results.append({
                            'test_name': test_case['name'],
                            'success': False,
                            'error': error_msg
                        })
                else:
                    error_text = response.get_data(as_text=True)
                    print(f"   ❌ HTTP错误: {response.status_code}")
                    print(f"   错误内容: {error_text[:200]}...")
                    results.append({
                        'test_name': test_case['name'],
                        'success': False,
                        'error': f'HTTP {response.status_code}: {error_text[:100]}'
                    })
                    
            except Exception as e:
                print(f"   💥 异常: {str(e)}")
                results.append({
                    'test_name': test_case['name'],
                    'success': False,
                    'error': f'异常: {str(e)}'
                })
        
        # 输出测试结果
        print(f"\n=== 数据传输测试结果 ===")
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        
        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}")
            if not result['success']:
                print(f"   错误: {result['error']}")
            elif 'request_id' in result:
                print(f"   请求ID: {result['request_id']}")
                print(f"   状态: {result['status']}")
        
        return success_count == total_count

if __name__ == '__main__':
    try:
        success = test_data_transmission()
        print(f"\n{'✅ 数据传输测试全部通过' if success else '❌ 部分数据传输测试失败'}")
    except Exception as e:
        print(f"\n💥 数据传输测试出现异常: {e}")
        import traceback
        traceback.print_exc()
