/* 响应式媒体查询 */

/* 超大屏幕 (1400px+) */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
        padding: 32px;
    }
    
    .report-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 大屏幕 (1200px - 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        padding: 28px;
    }
    
    .report-list {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 平板横屏 (992px - 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        padding: 24px 20px;
    }
    
    .report-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 平板竖屏 (768px - 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        padding: 20px;
    }
    
    .report-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
}

/* 手机横屏 (576px - 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        padding: 16px;
    }
    
    .report-list {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

/* 手机竖屏 (最大 575px) */
@media (max-width: 575px) {
    .container {
        padding: 12px;
        border-radius: 12px;
    }
    
    .report-list {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .action-btn,
    .report-actions .btn,
    .nav-link {
        min-height: 44px;
        touch-action: manipulation;
    }
    
    .card-filter-input {
        min-height: 44px;
    }
    
    .report-item {
        touch-action: manipulation;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-primary: #000;
        --bg-card: #fff;
    }
    
    button, a {
        border: 2px solid currentColor;
        color: #000;
    }
    
    .report-item {
        border: 2px solid #000;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .nav-link,
    .action-buttons,
    .card-key-modal {
        display: none !important;
    }
    
    .container,
    .report-item {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
        background: white !important;
    }
    
    .report-list {
        grid-template-columns: 1fr !important;
    }
} 