/* 统一配色系统 - 纯白色系 */

:root {
    /* 主要颜色 - 灰色系 */
    --color-primary: #64748b;  /* 使用灰色作为主色 */
    --color-primary-hover: #475569;
    
    /* 灰色色阶 */
    --primary-50: #f8fafc;
    --primary-100: #f1f5f9;
    --primary-200: #e2e8f0;
    --primary-300: #cbd5e1;
    --primary-400: #94a3b8;
    --primary-500: #64748b;  /* 中灰色 */
    --primary-600: #475569;
    --primary-700: #334155;
    --primary-800: #1e293b;
    --primary-900: #0f172a;
    
    /* 灰度色阶 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 功能色 */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #94a3b8;  /* 使用灰色替代蓝色 */
    
    /* 扩展功能色 - 用于Toast消息 */
    --success-color: #10b981;
    --success-color-light: #e6f7e6;
    --success-color-dark: #2e7d32;
    
    --warning-color: #f59e0b;
    --warning-color-light: #fff8e1;
    --warning-color-dark: #ff8f00;
    
    --error-color: #ef4444;
    --error-color-light: #ffebee;
    --error-color-dark: #c62828;
    
    --info-color: #94a3b8;
    --info-color-light: #e3f2fd;
    --info-color-dark: #0277bd;
    
    /* 文本颜色 */
    --text-primary: #111827;
    --text-secondary: #374151;
    --text-tertiary: #6b7280;
    --text-muted: #9ca3af;
    
    /* 背景颜色 - 确保白色背景 */
    --bg-primary: #ffffff;  /* 纯白背景 */
    --bg-secondary: #ffffff;
    --bg-tertiary: #f9fafb;
    --bg-hover: #f1f5f9;
    --bg-card: #ffffff;
    --bg-overlay: rgba(17, 24, 39, 0.7);
    --bg-glass: rgba(255, 255, 255, 0.95);
    
    /* 边框颜色 */
    --border-primary: #e5e7eb;
    --border-secondary: #f3f4f6;
    
    /* 圆角 */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-card-hover: 0 20px 40px rgba(0, 0, 0, 0.1);  /* 纯黑阴影，低透明度 */
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, #64748b 0%, #475569 100%);  /* 灰色渐变 */
    --gradient-hero: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);  /* 白色到浅灰色渐变 */
    --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);  /* 纯白到近白渐变 */
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-info: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);  /* 灰色渐变 */
    
    /* 过渡 */
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 字体 */
    --font-family-primary: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
} 