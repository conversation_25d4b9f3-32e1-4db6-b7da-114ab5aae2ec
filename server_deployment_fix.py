#!/usr/bin/env python3
"""
服务器部署问题诊断和修复工具
专门解决本地正常但服务器部署失败的问题
"""

import os
import sys
import subprocess
import time
import socket
import requests
from datetime import datetime

def check_server_environment():
    """检查服务器环境"""
    print("🔍 服务器环境检查")
    print("=" * 50)
    
    # 基本信息
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    
    # 检查项目结构
    project_files = ['up.html', 'index.html', 'styles.css', 'backend/app.py']
    print(f"\n📁 项目文件检查:")
    for file_path in project_files:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")
    
    # 检查Python依赖
    print(f"\n📦 Python依赖检查:")
    required_modules = ['flask', 'flask_cors', 'requests']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 未安装")
    
    # 检查端口占用
    print(f"\n🔌 端口检查:")
    ports_to_check = [5000, 80, 443]
    for port in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            status = "占用" if result == 0 else "空闲"
            print(f"端口 {port}: {status}")
        except:
            print(f"端口 {port}: 检查失败")

def kill_flask_processes():
    """终止所有Flask进程"""
    print("\n🔄 终止现有Flask进程")
    print("=" * 50)
    
    try:
        # Windows
        if os.name == 'nt':
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            if 'python.exe' in result.stdout:
                subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                             capture_output=True)
                print("✅ 已终止Windows上的Python进程")
            else:
                print("ℹ️ 没有发现Python进程")
        # Linux/Unix
        else:
            subprocess.run(['pkill', '-f', 'python.*app.py'], capture_output=True)
            print("✅ 已终止Linux上的Flask进程")
            
        time.sleep(2)
        
    except Exception as e:
        print(f"⚠️ 终止进程时出错: {e}")

def create_server_specific_app():
    """创建服务器专用的Flask应用"""
    print("\n📝 创建服务器专用Flask应用")
    print("=" * 50)
    
    server_app_content = '''#!/usr/bin/env python3
"""
服务器专用Flask应用
解决部署环境的路径和配置问题
"""

import os
import sys
from datetime import datetime
from flask import Flask, send_file, jsonify, request
from flask_cors import CORS

# 强制设置工作目录为脚本所在的项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir.endswith('backend'):
    project_dir = os.path.dirname(script_dir)
    os.chdir(project_dir)
else:
    project_dir = script_dir

print(f"🔧 服务器Flask应用启动")
print(f"📁 项目目录: {project_dir}")
print(f"📁 工作目录: {os.getcwd()}")

# 创建Flask应用
app = Flask(__name__, 
           static_folder=project_dir,
           static_url_path='/static')

# 配置CORS - 允许所有来源
CORS(app, resources={
    r"/*": {"origins": "*", "methods": ["GET", "POST", "OPTIONS", "DELETE"]},
    r"/api/*": {"origins": "*", "methods": ["GET", "POST", "OPTIONS", "DELETE"]},
    r"/webhook/*": {"origins": "*", "methods": ["GET", "POST", "OPTIONS", "DELETE"]}
})

@app.route('/')
def index():
    """主页"""
    print("📍 访问根路径")
    
    # 查找HTML文件
    html_files = ['index.html', 'up.html']
    for filename in html_files:
        file_path = os.path.join(project_dir, filename)
        print(f"🔍 检查文件: {file_path}")
        if os.path.exists(file_path):
            print(f"✅ 找到文件: {file_path}")
            return send_file(file_path)
    
    # 返回默认页面
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>八字分析系统 - 服务器版</title>
        <meta charset="UTF-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }}
            .status {{ background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; }}
            .error {{ background: #ffe8e8; padding: 20px; border-radius: 5px; margin: 20px 0; }}
            .links a {{ display: inline-block; margin: 10px; padding: 10px 20px; 
                       background: #007cba; color: white; text-decoration: none; border-radius: 3px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 八字分析系统 - 服务器版</h1>
            <div class="status">
                <h3>✅ Flask服务器运行正常</h3>
                <p>服务器时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>项目目录: {project_dir}</p>
                <p>工作目录: {os.getcwd()}</p>
            </div>
            <div class="error">
                <h3>⚠️ 前端文件未找到</h3>
                <p>系统无法找到 index.html 或 up.html 文件</p>
                <p>请确保前端文件已正确上传到服务器</p>
            </div>
            <div>
                <a href="/test">测试页面</a>
                <a href="/api/health">健康检查</a>
                <a href="/static/up.html">分析页面(静态)</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/test')
def test():
    """测试页面"""
    return """
    <h1>🎉 服务器测试页面</h1>
    <p>Flask服务器在服务器环境中运行正常！</p>
    <ul>
        <li><a href="/">返回主页</a></li>
        <li><a href="/api/health">健康检查</a></li>
    </ul>
    """

@app.route('/api/health')
def health():
    """健康检查"""
    return jsonify({
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "message": "服务器Flask应用运行正常",
        "environment": "server",
        "project_dir": project_dir,
        "working_dir": os.getcwd()
    })

# 尝试导入原始应用的路由
try:
    sys.path.insert(0, os.path.join(project_dir, 'backend'))
    
    # 导入必要的模块
    from config_manager import ConfigManager
    from card_manager import CardManager
    from bazi_service import BaziService
    
    config = ConfigManager()
    card_manager = CardManager()
    bazi_service = BaziService()
    
    @app.route('/webhook/bazi-analysis', methods=['POST', 'OPTIONS'])
    def bazi_analysis():
        """八字分析接口"""
        if request.method == 'OPTIONS':
            return '', 200
        
        try:
            data = request.get_json()
            
            # 验证卡密
            card_code = data.get('cardCode')
            if not card_manager.validate_card(card_code):
                return jsonify({"error": "无效的卡密"}), 401
            
            # 执行分析
            result = bazi_service.analyze(data)
            return jsonify(result)
            
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    
    print("✅ 成功导入业务逻辑模块")
    
except Exception as e:
    print(f"⚠️ 导入业务模块失败: {e}")
    print("🔄 使用基础功能模式")

if __name__ == '__main__':
    print("🚀 启动服务器专用Flask应用")
    print("🌐 监听地址: 0.0.0.0:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
'''
    
    # 写入服务器专用应用文件
    with open('server_app.py', 'w', encoding='utf-8') as f:
        f.write(server_app_content)
    
    print("✅ 已创建 server_app.py")

def test_server_deployment():
    """测试服务器部署"""
    print("\n🧪 测试服务器部署")
    print("=" * 50)
    
    # 启动服务器应用
    print("🚀 启动服务器应用...")
    
    try:
        # 使用subprocess启动
        process = subprocess.Popen([
            sys.executable, 'server_app.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
           text=True, bufsize=1)
        
        # 等待启动
        time.sleep(5)
        
        # 测试连接
        test_urls = [
            'http://127.0.0.1:5000/',
            'http://127.0.0.1:5000/test',
            'http://127.0.0.1:5000/api/health'
        ]
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=5)
                print(f"✅ {url} -> {response.status_code}")
            except Exception as e:
                print(f"❌ {url} -> {e}")
        
        # 终止测试进程
        process.terminate()
        process.wait()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 服务器部署问题诊断和修复工具")
    print("=" * 60)
    
    # 检查环境
    check_server_environment()
    
    # 终止现有进程
    kill_flask_processes()
    
    # 创建服务器专用应用
    create_server_specific_app()
    
    # 测试部署
    test_server_deployment()
    
    print("\n" + "=" * 60)
    print("🎯 修复完成！")
    print("\n💡 现在可以启动服务器应用:")
    print("python server_app.py")
    print("\n🌐 然后访问:")
    print("http://175.24.228.204:5000/")

if __name__ == "__main__":
    main()
