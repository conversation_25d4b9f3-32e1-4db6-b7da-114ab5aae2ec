/**
 * 八字反推集成模块
 * 将Calculate目录的八字反推功能集成到up.html中
 */

class BaziIntegration {
    constructor() {
        this.isCalculateLoaded = false;
        this.init();
    }

    /**
     * 初始化八字反推功能
     */
    async init() {
        try {
            // 动态加载lunar1.js库（用于八字反推）
            await this.loadScript('js/lunar1.js');
            console.log('lunar1.js库加载成功');
            
            // 动态加载paipan.js库（用于农历转换）
            await this.loadLunarLibrary();
            
            this.isCalculateLoaded = true;
            
            // 初始化UI
            this.initUI();
        } catch (error) {
            console.error('八字反推功能初始化失败:', error);
            showError('八字反推功能加载失败，请刷新页面重试');
        }
    }

    /**
     * 加载Calculate目录的库文件
     */
    async loadCalculateLibrary() {
        try {
            // 动态加载lunar1.js库
            await this.loadScript('js/lunar1.js');
            this.isCalculateLoaded = true;
            console.log('Calculate库加载成功');
        } catch (error) {
            console.error('加载Calculate库失败:', error);
        }
    }

    /**
     * 动态加载脚本
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            if (document.querySelector(`script[src="${src}"]`)) {
                resolve();
                return;
            }
            
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * 加载农历转换库
     */
    async loadLunarLibrary() {
        try {
            // 加载paipan.js库用于农历转换
            await this.loadScript('js/paipan.js');
            console.log('农历转换库加载成功');
            return true;
        } catch (error) {
            console.error('农历转换库加载失败:', error);
            return false;
        }
    }

    /**
     * 初始化UI界面
     */
    initUI() {
        // 在表单中添加八字反推选项
        this.addBaziReverseOption();
        // 绑定切换事件
        this.bindToggleEvents();
        // 确保表单输入有name属性
        setTimeout(() => {
            this.ensureFormInputsHaveNames();
            // 绑定自动触发阳历反推的事件
            this.bindAutoConvertEvents();
        }, 1500); // 延迟执行，确保所有元素已加载
    }

    /**
     * 添加八字反推选项到表单
     */
    addBaziReverseOption() {
        const formSection = document.querySelector('.form-section');
        if (!formSection) return;

        // 创建输入方式选择器
        const inputModeSection = document.createElement('div');
        inputModeSection.className = 'form-section input-mode-section';
        inputModeSection.style.opacity = '0';
        inputModeSection.innerHTML = `
            <h3 class="section-title">📝 输入方式</h3>
            <div class="input-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="solar" checked>
                    <span class="radio-label">🌞 阳历输入</span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="lunar">
                    <span class="radio-label">🌙 农历输入</span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="bazi">
                    <span class="radio-label">☯ 八字反推</span>
                </label>
            </div>
        `;

        // 插入到第一个表单部分之前
        formSection.parentNode.insertBefore(inputModeSection, formSection);
        
        // 添加淡入动画，与其他元素同步
        setTimeout(() => {
            inputModeSection.style.opacity = '1';
            inputModeSection.style.animation = 'fadeInUp 0.6s ease-out';
        }, 100);

        // 创建八字输入区域
        const baziSection = document.createElement('div');
        baziSection.className = 'form-section bazi-input-section';
        baziSection.style.display = 'none';
        baziSection.innerHTML = `
            <h3 class="section-title">☯ 八字信息</h3>
            <div class="input-mode-toggle">
                <label class="toggle-option">
                    <input type="radio" name="baziInputMode" value="separate" checked>
                    <span>分别输入</span>
                </label>
                <label class="toggle-option">
                    <input type="radio" name="baziInputMode" value="combined">
                    <span>整体输入</span>
                </label>
            </div>
            <div id="separateInputs" class="bazi-input-mode">
                <div class="bazi-selector-container">
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">年柱</div>
                        <div class="tiangan-selector" data-pillar="year">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="year">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="yearPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">月柱</div>
                        <div class="tiangan-selector" data-pillar="month">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="month">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="monthPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">日柱</div>
                        <div class="tiangan-selector" data-pillar="day">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="day">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="dayPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">时柱</div>
                        <div class="tiangan-selector" data-pillar="time">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="time">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="timePillarPreview">未选择</div>
                    </div>
                </div>
                
                <!-- 隐藏的输入字段，用于存储选择的值 -->
                <input type="hidden" id="yearTiangan" value="">
                <input type="hidden" id="yearDizhi" value="">
                <input type="hidden" id="monthTiangan" value="">
                <input type="hidden" id="monthDizhi" value="">
                <input type="hidden" id="dayTiangan" value="">
                <input type="hidden" id="dayDizhi" value="">
                <input type="hidden" id="timeTiangan" value="">
                <input type="hidden" id="timeDizhi" value="">
            </div>
            <div id="combinedInputs" class="bazi-input-mode" style="display: none;">
                <div class="input-group">
                    <label for="baziString">完整八字：</label>
                    <textarea id="baziString" placeholder="请输入完整八字，支持以下格式：&#10;甲子乙丑丙寅丁卯&#10;甲子：乙丑：丙寅：丁卯&#10;甲子:乙丑:丙寅:丁卯" rows="3"></textarea>
                    <span class="input-icon">📝</span>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" id="reverseBtn" class="reverse-btn">
                    <span>🔄</span> 反推阳历时间
                </button>
            </div>
            <div id="reverseResults" class="reverse-results" style="display: none;"></div>
        `;

        // 创建农历输入区域
        const lunarSection = document.createElement('div');
        lunarSection.className = 'form-section lunar-input-section';
        lunarSection.style.display = 'none';
        lunarSection.innerHTML = `
            <h3 class="section-title">🌙 农历信息</h3>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarYear">农历年份：</label>
                        <input type="number" id="lunarYear" min="1900" max="2100" placeholder="例如: 2002">
                        <span class="input-icon">📅</span>
                    </div>
                </div>
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarMonth">农历月份：</label>
                        <input type="number" id="lunarMonth" min="1" max="12" placeholder="例如: 5">
                        <span class="input-icon">🌙</span>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarDay">农历日期：</label>
                        <input type="number" id="lunarDay" min="1" max="30" placeholder="例如: 27">
                        <span class="input-icon">📅</span>
                    </div>
                </div>
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarHour">时辰：</label>
                        <select id="lunarHour">
                            <option value="">请选择时辰</option>
                            <option value="早子时">早子时 (00:00-01:00)</option>
                            <option value="丑时">丑时 (01:00-03:00)</option>
                            <option value="寅时">寅时 (03:00-05:00)</option>
                            <option value="卯时">卯时 (05:00-07:00)</option>
                            <option value="辰时">辰时 (07:00-09:00)</option>
                            <option value="巳时">巳时 (09:00-11:00)</option>
                            <option value="午时">午时 (11:00-13:00)</option>
                            <option value="未时">未时 (13:00-15:00)</option>
                            <option value="申时">申时 (15:00-17:00)</option>
                            <option value="酉时">酉时 (17:00-19:00)</option>
                            <option value="戌时">戌时 (19:00-21:00)</option>
                            <option value="亥时">亥时 (21:00-23:00)</option>
                            <option value="夜子时">夜子时 (23:00-24:00)</option>
                        </select>
                        <span class="input-icon">⏰</span>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="isLeapMonth">
                            <input type="checkbox" id="isLeapMonth">
                            闰月
                        </label>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-actions">
                        <button type="button" id="convertBtn" class="convert-btn">
                            <span>🔄</span> 转换为阳历
                        </button>
                    </div>
                </div>
            </div>
            <div id="lunarConvertResult" class="convert-result" style="display: none;"></div>
        `;

        // 插入到个人信息部分之前
        const personalSection = document.querySelector('.form-section:nth-child(3)');
        if (personalSection) {
            personalSection.parentNode.insertBefore(baziSection, personalSection);
            personalSection.parentNode.insertBefore(lunarSection, personalSection);
        }
        
        // 为农历输入框添加自动转换事件监听器
        this.addLunarAutoConvertListeners();
    }

    /**
     * 添加八字反推选项到表单
     */
    addBaziReverseOption() {
        const formSection = document.querySelector('.form-section');
        if (!formSection) return;

        // 创建输入方式选择器
        const inputModeSection = document.createElement('div');
        inputModeSection.className = 'form-section input-mode-section';
        inputModeSection.style.opacity = '0';
        inputModeSection.innerHTML = `
            <h3 class="section-title">📝 输入方式</h3>
            <div class="input-mode-selector">
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="solar" checked>
                    <span class="radio-label">🌞 阳历输入</span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="lunar">
                    <span class="radio-label">🌙 农历输入</span>
                </label>
                <label class="radio-option">
                    <input type="radio" name="inputMode" value="bazi">
                    <span class="radio-label">☯ 八字反推</span>
                </label>
            </div>
        `;

        // 插入到第一个表单部分之前
        formSection.parentNode.insertBefore(inputModeSection, formSection);
        
        // 添加淡入动画，与其他元素同步
        setTimeout(() => {
            inputModeSection.style.opacity = '1';
            inputModeSection.style.animation = 'fadeInUp 0.6s ease-out';
        }, 100);

        // 创建八字输入区域
        const baziSection = document.createElement('div');
        baziSection.className = 'form-section bazi-input-section';
        baziSection.style.display = 'none';
        baziSection.innerHTML = `
            <h3 class="section-title">☯ 八字信息</h3>
            <div class="input-mode-toggle">
                <label class="toggle-option">
                    <input type="radio" name="baziInputMode" value="separate" checked>
                    <span>分别输入</span>
                </label>
                <label class="toggle-option">
                    <input type="radio" name="baziInputMode" value="combined">
                    <span>整体输入</span>
                </label>
            </div>
            <div id="separateInputs" class="bazi-input-mode">
                <div class="bazi-selector-container">
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">年柱</div>
                        <div class="tiangan-selector" data-pillar="year">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="year">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="yearPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">月柱</div>
                        <div class="tiangan-selector" data-pillar="month">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="month">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="monthPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">日柱</div>
                        <div class="tiangan-selector" data-pillar="day">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="day">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="dayPillarPreview">未选择</div>
                    </div>
                    
                    <div class="bazi-pillar-section">
                        <div class="pillar-header">时柱</div>
                        <div class="tiangan-selector" data-pillar="time">
                            <div class="selector-label">天干</div>
                            <div class="selector-options">
                                <span class="tiangan-option" data-value="甲">甲</span>
                                <span class="tiangan-option" data-value="乙">乙</span>
                                <span class="tiangan-option" data-value="丙">丙</span>
                                <span class="tiangan-option" data-value="丁">丁</span>
                                <span class="tiangan-option" data-value="戊">戊</span>
                                <span class="tiangan-option" data-value="己">己</span>
                                <span class="tiangan-option" data-value="庚">庚</span>
                                <span class="tiangan-option" data-value="辛">辛</span>
                                <span class="tiangan-option" data-value="壬">壬</span>
                                <span class="tiangan-option" data-value="癸">癸</span>
                            </div>
                        </div>
                        <div class="dizhi-selector" data-pillar="time">
                            <div class="selector-label">地支</div>
                            <div class="selector-options">
                                <span class="dizhi-option" data-value="子">子</span>
                                <span class="dizhi-option" data-value="丑">丑</span>
                                <span class="dizhi-option" data-value="寅">寅</span>
                                <span class="dizhi-option" data-value="卯">卯</span>
                                <span class="dizhi-option" data-value="辰">辰</span>
                                <span class="dizhi-option" data-value="巳">巳</span>
                                <span class="dizhi-option" data-value="午">午</span>
                                <span class="dizhi-option" data-value="未">未</span>
                                <span class="dizhi-option" data-value="申">申</span>
                                <span class="dizhi-option" data-value="酉">酉</span>
                                <span class="dizhi-option" data-value="戌">戌</span>
                                <span class="dizhi-option" data-value="亥">亥</span>
                            </div>
                        </div>
                        <div class="pillar-preview" id="timePillarPreview">未选择</div>
                    </div>
                </div>
                
                <!-- 隐藏的输入字段，用于存储选择的值 -->
                <input type="hidden" id="yearTiangan" value="">
                <input type="hidden" id="yearDizhi" value="">
                <input type="hidden" id="monthTiangan" value="">
                <input type="hidden" id="monthDizhi" value="">
                <input type="hidden" id="dayTiangan" value="">
                <input type="hidden" id="dayDizhi" value="">
                <input type="hidden" id="timeTiangan" value="">
                <input type="hidden" id="timeDizhi" value="">
            </div>
            <div id="combinedInputs" class="bazi-input-mode" style="display: none;">
                <div class="input-group">
                    <label for="baziString">完整八字：</label>
                    <textarea id="baziString" placeholder="请输入完整八字，支持以下格式：&#10;甲子乙丑丙寅丁卯&#10;甲子：乙丑：丙寅：丁卯&#10;甲子:乙丑:丙寅:丁卯" rows="3"></textarea>
                    <span class="input-icon">📝</span>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" id="reverseBtn" class="reverse-btn">
                    <span>🔄</span> 反推阳历时间
                </button>
            </div>
            <div id="reverseResults" class="reverse-results" style="display: none;"></div>
        `;

        // 创建农历输入区域
        const lunarSection = document.createElement('div');
        lunarSection.className = 'form-section lunar-input-section';
        lunarSection.style.display = 'none';
        lunarSection.innerHTML = `
            <h3 class="section-title">🌙 农历信息</h3>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarYear">农历年份：</label>
                        <input type="number" id="lunarYear" min="1900" max="2100" placeholder="例如: 2002">
                        <span class="input-icon">📅</span>
                    </div>
                </div>
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarMonth">农历月份：</label>
                        <input type="number" id="lunarMonth" min="1" max="12" placeholder="例如: 5">
                        <span class="input-icon">🌙</span>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarDay">农历日期：</label>
                        <input type="number" id="lunarDay" min="1" max="30" placeholder="例如: 27">
                        <span class="input-icon">📅</span>
                    </div>
                </div>
                <div class="form-col">
                    <div class="input-group">
                        <label for="lunarHour">时辰：</label>
                        <select id="lunarHour">
                            <option value="">请选择时辰</option>
                            <option value="早子时">早子时 (00:00-01:00)</option>
                            <option value="丑时">丑时 (01:00-03:00)</option>
                            <option value="寅时">寅时 (03:00-05:00)</option>
                            <option value="卯时">卯时 (05:00-07:00)</option>
                            <option value="辰时">辰时 (07:00-09:00)</option>
                            <option value="巳时">巳时 (09:00-11:00)</option>
                            <option value="午时">午时 (11:00-13:00)</option>
                            <option value="未时">未时 (13:00-15:00)</option>
                            <option value="申时">申时 (15:00-17:00)</option>
                            <option value="酉时">酉时 (17:00-19:00)</option>
                            <option value="戌时">戌时 (19:00-21:00)</option>
                            <option value="亥时">亥时 (21:00-23:00)</option>
                            <option value="夜子时">夜子时 (23:00-24:00)</option>
                        </select>
                        <span class="input-icon">⏰</span>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <div class="input-group">
                        <label for="isLeapMonth">
                            <input type="checkbox" id="isLeapMonth">
                            闰月
                        </label>
                    </div>
                </div>
                <div class="form-col">
                    <div class="form-actions">
                        <button type="button" id="convertBtn" class="convert-btn">
                            <span>🔄</span> 转换为阳历
                        </button>
                    </div>
                </div>
            </div>
            <div id="lunarConvertResult" class="convert-result" style="display: none;"></div>
        `;

        // 插入到个人信息部分之前
        const personalSection = document.querySelector('.form-section:nth-child(3)');
        if (personalSection) {
            personalSection.parentNode.insertBefore(baziSection, personalSection);
            personalSection.parentNode.insertBefore(lunarSection, personalSection);
        }
        
        // 为农历输入框添加自动转换事件监听器
        this.addLunarAutoConvertListeners();
    }

    /**
     * 添加八字输入模式切换监听器
     */
    addBaziInputModeListeners() {
        document.addEventListener('change', (e) => {
            if (e.target.name === 'baziInputMode') {
                this.toggleBaziInputMode(e.target.value);
            }
        });
        
        // 绑定天干地支选择事件
        this.bindBaziSelectorEvents();
        
        // 为整体输入模式添加自动触发功能
        const baziStringInput = document.getElementById('baziString');
        if (baziStringInput) {
            baziStringInput.addEventListener('input', () => {
                // 使用防抖函数避免频繁触发
                clearTimeout(baziStringInput.timer);
                baziStringInput.timer = setTimeout(() => {
                    // 检查输入是否有效且足够长度
                    const inputValue = baziStringInput.value.trim();
                    if (this.validateBaziString(inputValue)) {
                        console.log('八字输入完成，自动开始反推分析');
                        // 延迟执行以确保UI更新完成
                        setTimeout(() => {
                            this.autoAnalyzeFromBazi();
                        }, 300);
                    }
                }, 800); // 800ms的延迟
            });
        }
    }
    
    /**
     * 验证八字字符串是否有效
     * @param {string} input - 用户输入的八字字符串
     * @returns {boolean} 是否有效
     */
    validateBaziString(input) {
        if (!input || input.length < 8) return false;
        
        // 移除所有空格、换行符和分隔符
        let cleanInput = input.replace(/\s+/g, '')
                             .replace(/：|:/g, '');
        
        // 验证长度，必须是8个字符（四柱八字）
        if (cleanInput.length !== 8) return false;
        
        // 验证是否包含有效的天干地支
        const validTiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        const validDizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        
        // 验证每个天干（偶数位置：0,2,4,6）
        for (let i = 0; i < 8; i += 2) {
            if (!validTiangan.includes(cleanInput[i])) {
                return false;
            }
        }
        
        // 验证每个地支（奇数位置：1,3,5,7）
        for (let i = 1; i < 8; i += 2) {
            if (!validDizhi.includes(cleanInput[i])) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 绑定八字选择器的事件
     */
    bindBaziSelectorEvents() {
        // 天干选择事件
        document.querySelectorAll('.tiangan-option').forEach(option => {
            option.addEventListener('click', e => {
                const pillar = e.target.closest('.tiangan-selector').dataset.pillar;
                const value = e.target.dataset.value;
                
                // 移除同组其他选项的选中状态
                document.querySelectorAll(`.tiangan-selector[data-pillar="${pillar}"] .tiangan-option`).forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 添加当前选项的选中状态
                e.target.classList.add('selected');
                
                // 更新隐藏字段的值
                document.getElementById(`${pillar}Tiangan`).value = value;
                
                // 更新预览
                this.updatePillarPreview(pillar);
                
                // 检查是否所有八字组件都已选择完成，如果是则自动触发反推
                this.checkAndAutoReverse();
            });
        });
        
        // 地支选择事件
        document.querySelectorAll('.dizhi-option').forEach(option => {
            option.addEventListener('click', e => {
                const pillar = e.target.closest('.dizhi-selector').dataset.pillar;
                const value = e.target.dataset.value;
                
                // 移除同组其他选项的选中状态
                document.querySelectorAll(`.dizhi-selector[data-pillar="${pillar}"] .dizhi-option`).forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 添加当前选项的选中状态
                e.target.classList.add('selected');
                
                // 更新隐藏字段的值
                document.getElementById(`${pillar}Dizhi`).value = value;
                
                // 更新预览
                this.updatePillarPreview(pillar);
                
                // 检查是否所有八字组件都已选择完成，如果是则自动触发反推
                this.checkAndAutoReverse();
            });
        });
    }
    
    /**
     * 更新柱子预览
     */
    updatePillarPreview(pillar) {
        const tiangan = document.getElementById(`${pillar}Tiangan`).value;
        const dizhi = document.getElementById(`${pillar}Dizhi`).value;
        const previewElement = document.getElementById(`${pillar}PillarPreview`);
        
        if (tiangan && dizhi) {
            const pillarValue = tiangan + dizhi;
            previewElement.textContent = pillarValue;
            previewElement.classList.add('completed');
            
            // 获取五行颜色并应用
            if (window.getComputedWuxingColor) {
                try {
                    // 尝试根据天干获取五行颜色
                    const tianganWuxing = TIANGAN_WUXING[tiangan];
                    if (tianganWuxing) {
                        const color = getComputedWuxingColor(tianganWuxing);
                        previewElement.style.background = `linear-gradient(135deg, ${color} 0%, ${color}dd 100%)`;
                    }
                } catch (e) {
                    console.log('五行颜色获取失败', e);
                }
            }
        } else {
            previewElement.textContent = '未选择';
            previewElement.classList.remove('completed');
            previewElement.style.background = '';
        }
    }

    /**
     * 切换八字输入模式
     */
    toggleBaziInputMode(mode) {
        const separateInputs = document.getElementById('separateInputs');
        const combinedInputs = document.getElementById('combinedInputs');
        
        if (mode === 'combined') {
            separateInputs.style.display = 'none';
            combinedInputs.style.display = 'block';
        } else {
            separateInputs.style.display = 'block';
            combinedInputs.style.display = 'none';
        }
    }

    /**
     * 绑定切换事件
     */
    bindToggleEvents() {
        // 输入方式切换
        document.addEventListener('change', (e) => {
            if (e.target.name === 'inputMode') {
                this.toggleInputMode(e.target.value);
            }
        });
        
        // 添加八字输入模式切换监听器
        this.addBaziInputModeListeners();

        // 农历转换按钮
        document.addEventListener('click', (e) => {
            if (e.target.id === 'convertBtn' || e.target.closest('#convertBtn')) {
                this.performLunarConvert();
            }
        });

        // 八字反推按钮
        document.addEventListener('click', (e) => {
            if (e.target.id === 'reverseBtn' || e.target.closest('#reverseBtn')) {
                this.performBaziReverse();
            }
        });

        // 反推结果选择
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('result-item')) {
                this.selectReverseResult(e.target);
            }
        });
        
        // 监听表单提交（开始分析按钮）
        const form = document.querySelector('form');
        if (form) {
            // 保存原始按钮文本，用于恢复
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.setAttribute('data-original-text', submitBtn.innerHTML);
            }
            
            form.addEventListener('submit', (e) => {
                // 在表单提交前处理HTML5验证问题
                this.handleFormValidation();
                
                // 检查当前是否在八字输入模式
                const currentMode = document.querySelector('input[name="inputMode"]:checked')?.value;
                if (currentMode === 'bazi') {
                    // 如果当前是八字输入模式，检查是否所有四柱都已选择完毕
                    if (this.checkBaziComplete()) {
                        e.preventDefault(); // 阻止表单提交
                        // 显示处理中状态
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<span class="spinner"></span><span>处理中...</span>';
                        }
                        // 自动执行反推并分析
                        this.autoAnalyzeFromBazi();
                    } else {
                        e.preventDefault();
                        showWarning('请先完成四柱八字的选择');
                    }
                } else {
                    // 如果不是八字模式，检查表单完整性
                    if (!this.validateForm()) {
                        e.preventDefault();
                    }
                }
            });
        }
    }

    /**
     * 切换输入模式
     */
    toggleInputMode(mode) {
        const solarSection = document.querySelector('.form-section:nth-child(2)');
        const baziSection = document.querySelector('.bazi-input-section');
        const lunarSection = document.querySelector('.lunar-input-section');

        // 隐藏所有输入区域
        solarSection.style.display = 'none';
        baziSection.style.display = 'none';
        lunarSection.style.display = 'none';

        // 根据选择显示对应区域
        if (mode === 'bazi') {
            baziSection.style.display = 'block';
        } else if (mode === 'lunar') {
            lunarSection.style.display = 'block';
        } else {
            solarSection.style.display = 'block';
        }
        
        // 处理表单验证问题
        setTimeout(() => {
            this.handleFormValidation();
        }, 100); // 延迟执行，确保DOM更新完成
    }

    /**
     * 检查八字是否已全部选择完毕
     */
    checkBaziComplete() {
        // 检查分别输入模式
        const inputMode = document.querySelector('input[name="baziInputMode"]:checked')?.value;
        
        if (inputMode === 'combined') {
            // 整体输入模式
            return document.getElementById('baziString').value.trim().length > 0;
        } else {
            // 分别输入模式 - 检查所有天干地支是否已选择
            const yearTiangan = document.getElementById('yearTiangan').value;
            const yearDizhi = document.getElementById('yearDizhi').value;
            const monthTiangan = document.getElementById('monthTiangan').value;
            const monthDizhi = document.getElementById('monthDizhi').value;
            const dayTiangan = document.getElementById('dayTiangan').value;
            const dayDizhi = document.getElementById('dayDizhi').value;
            const timeTiangan = document.getElementById('timeTiangan').value;
            const timeDizhi = document.getElementById('timeDizhi').value;
            
            return yearTiangan && yearDizhi && monthTiangan && monthDizhi && 
                   dayTiangan && dayDizhi && timeTiangan && timeDizhi;
        }
    }
    
    /**
     * 自动从八字反推并执行分析
     */
    async autoAnalyzeFromBazi() {
        try {
            // 显示加载状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner"></span><span>处理中...</span>';
            
            // 执行八字反推
            const reverseResults = await this.performBaziReverseAndGetFirst();
            
            if (!reverseResults) {
                console.error('八字反推失败，请检查输入是否正确');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
                return;
            }
            
            // 填充表单
            document.getElementById('yearInput').value = reverseResults.year;
            document.getElementById('monthInput').value = reverseResults.month;
            document.getElementById('dayInput').value = reverseResults.day;
            document.getElementById('hourInput').value = reverseResults.hour;
            
            // 确保表单输入有name属性
            this.ensureFormInputsHaveNames();
            
            // 确保其他必填字段有默认值
            this.ensureRequiredFieldsHaveValues();
            
            // 验证表单完整性
            if (!this.validateForm()) {
                console.error('表单验证失败：请填写完整的个人信息');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
                return;
            }
            
            // 切换到阳历模式（确保表单显示正确）
            document.querySelector('input[name="inputMode"][value="solar"]').checked = true;
            this.toggleInputMode('solar');
            
            // 延迟提交表单，确保UI更新
            setTimeout(() => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
                
                // 自动提交表单进行分析
                const form = document.querySelector('form');
                
                // 使用编程方式提交表单
                const submitEvent = new Event('submit', {
                    'bubbles': true,
                    'cancelable': true
                });
                
                // 手动触发提交事件
                form.dispatchEvent(submitEvent);
            }, 500);
            
        } catch (error) {
            console.error('自动分析失败:', error);
            // 恢复按钮状态
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || '开始分析';
            }
        }
    }
    
    /**
     * 执行八字反推并返回距离2000年最近的结果
     */
    async performBaziReverseAndGetFirst() {
        if (!this.isCalculateLoaded) {
            await this.loadCalculateLibrary();
            if (!this.isCalculateLoaded) {
                console.error('八字库加载失败，请刷新页面重试');
                return null;
            }
        }

        try {
            let baziString = '';
            
            // 检查当前输入模式
            const inputMode = document.querySelector('input[name="baziInputMode"]:checked').value;
            
            if (inputMode === 'combined') {
                // 整体输入模式
                const baziInput = document.getElementById('baziString').value.trim();
                if (!baziInput) {
                    console.error('请输入完整的八字信息');
                    return null;
                }
                
                // 解析八字字符串
                baziString = this.parseBaziString(baziInput);
                if (!baziString) {
                    console.error('八字格式不正确，请检查输入格式');
                    return null;
                }
            } else {
                // 分别输入模式 - 从下拉框获取天干地支
                const yearTiangan = document.getElementById('yearTiangan').value;
                const yearDizhi = document.getElementById('yearDizhi').value;
                const monthTiangan = document.getElementById('monthTiangan').value;
                const monthDizhi = document.getElementById('monthDizhi').value;
                const dayTiangan = document.getElementById('dayTiangan').value;
                const dayDizhi = document.getElementById('dayDizhi').value;
                const timeTiangan = document.getElementById('timeTiangan').value;
                const timeDizhi = document.getElementById('timeDizhi').value;

                // 验证输入
                if (!yearTiangan || !yearDizhi || !monthTiangan || !monthDizhi || 
                    !dayTiangan || !dayDizhi || !timeTiangan || !timeDizhi) {
                    console.error('请选择完整的四柱八字信息');
                    return null;
                }

                // 构建八字字符串
                const yearPillar = yearTiangan + yearDizhi;
                const monthPillar = monthTiangan + monthDizhi;
                const dayPillar = dayTiangan + dayDizhi;
                const timePillar = timeTiangan + timeDizhi;
                
                baziString = yearPillar + monthPillar + dayPillar + timePillar;
            }

            // 使用默认的流派和基准年值
            const sect = 2; // 默认使用流派2
            const baseYear = 1824; // 默认使用1824年作为基准年

            // 将八字字符串拆分成四柱
            const yearGanZhi = baziString.substr(0, 2);
            const monthGanZhi = baziString.substr(2, 2);
            const dayGanZhi = baziString.substr(4, 2);
            const timeGanZhi = baziString.substr(6, 2);

            // 调用Solar.fromBaZi进行反推
            const results = Solar.fromBaZi(yearGanZhi, monthGanZhi, dayGanZhi, timeGanZhi, sect, baseYear);
            
            if (!results || results.length === 0) {
                console.error('未找到匹配的阳历日期，请检查八字输入');
                return null;
            }
            
            // 查找距离2000年最近的结果
            const targetYear = 2000;
            let closestResult = results[0];
            let minYearDiff = Math.abs(results[0].getYear() - targetYear);
            
            for (let i = 1; i < results.length; i++) {
                const yearDiff = Math.abs(results[i].getYear() - targetYear);
                if (yearDiff < minYearDiff) {
                    minYearDiff = yearDiff;
                    closestResult = results[i];
                }
            }
            
            // 根据小时选择对应的时辰
            const hour = parseInt(closestResult.getHour());
            let timeText = '午时'; // 默认值
            
            const timeMapping = {
                23: '夜子时', 0: '早子时', 1: '丑时', 2: '丑时',
                3: '寅时', 4: '寅时', 5: '卯时', 6: '卯时',
                7: '辰时', 8: '辰时', 9: '巳时', 10: '巳时',
                11: '午时', 12: '午时', 13: '未时', 14: '未时',
                15: '申时', 16: '申时', 17: '酉时', 18: '酉时',
                19: '戌时', 20: '戌时', 21: '亥时', 22: '亥时'
            };
            
            if (hour === 23) {
                timeText = '夜子时';
            } else if (hour === 0) {
                timeText = '早子时';
            } else {
                timeText = timeMapping[hour] || '午时';
            }
            
            // 如果存在多个结果，记录日志但不影响用户体验
            if (results.length > 1) {
                console.log(`找到${results.length}个匹配结果，自动选择距离2000年最近的一个（${closestResult.getYear()}年）`);
            }
            
            // 自动显示反推结果，但不要求用户点击选择
            this.displayReverseResults(results, true);
            
            return {
                year: closestResult.getYear(),
                month: closestResult.getMonth(),
                day: closestResult.getDay(),
                hour: timeText
            };
            
        } catch (error) {
            console.error('八字反推失败:', error);
            return null;
        }
    }

    /**
     * 解析八字字符串
     */
    parseBaziString(baziInput) {
        // 移除所有空格和换行符
        let cleanInput = baziInput.replace(/\s+/g, '');
        
        // 支持多种分隔符格式
        if (cleanInput.includes('：')) {
            cleanInput = cleanInput.replace(/：/g, '');
        } else if (cleanInput.includes(':')) {
            cleanInput = cleanInput.replace(/:/g, '');
        }
        
        // 验证长度
        if (cleanInput.length !== 8) {
            return null;
        }
        
        return cleanInput;
    }

    /**
     * 选择反推结果
     */
    selectReverseResult(element) {
        // 移除其他选中状态
        document.querySelectorAll('.result-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 添加选中状态
        element.classList.add('selected');
        
        // 填充到阳历表单
        const year = element.dataset.year;
        const month = element.dataset.month;
        const day = element.dataset.day;
        const hour = parseInt(element.dataset.hour);
        
        document.getElementById('yearInput').value = year;
        document.getElementById('monthInput').value = month;
        document.getElementById('dayInput').value = day;
        
        // 根据小时选择对应的时辰
        const hourSelect = document.getElementById('hourInput');
        const timeMapping = {
            23: '早子时', 0: '早子时', 1: '丑时', 2: '丑时',
            3: '寅时', 4: '寅时', 5: '卯时', 6: '卯时',
            7: '辰时', 8: '辰时', 9: '巳时', 10: '巳时',
            11: '午时', 12: '午时', 13: '未时', 14: '未时',
            15: '申时', 16: '申时', 17: '酉时', 18: '酉时',
            19: '戌时', 20: '戌时', 21: '亥时', 22: '亥时'
        };
        
        if (hour === 23) {
            hourSelect.value = '夜子时';
        } else {
            hourSelect.value = timeMapping[hour] || '午时';
        }
        
        // 切换回阳历输入模式
        document.querySelector('input[name="inputMode"][value="solar"]').checked = true;
        this.toggleInputMode('solar');
        
        // 确保表单输入有name属性
        this.ensureFormInputsHaveNames();
    }

    /**
     * 绑定自动触发阳历反推的事件
     */
    bindAutoConvertEvents() {
        // 阳历输入模式下的年月日时字段
        const yearInput = document.getElementById('yearInput');
        const monthInput = document.getElementById('monthInput');
        const dayInput = document.getElementById('dayInput');
        const hourInput = document.getElementById('hourInput');
        
        if (yearInput && monthInput && dayInput && hourInput) {
            // 为每个字段添加change事件监听
            const checkAndConvert = () => {
                // 检查当前输入模式，只有在非阳历模式下才执行转换
                const currentMode = document.querySelector('input[name="inputMode"]:checked')?.value;
                
                // 如果当前是阳历输入模式，不执行转换
                if (currentMode === 'solar') {
                    return;
                }
                
                // 检查所有字段是否已填写
                if (yearInput.value && monthInput.value && dayInput.value && hourInput.value) {
                    // 自动执行转换，无需手动点击，使用静默模式
                    this.performLunarConvert(true);
                }
            };
            
            // 为年月日输入添加事件监听
            yearInput.addEventListener('change', checkAndConvert);
            monthInput.addEventListener('change', checkAndConvert);
            dayInput.addEventListener('change', checkAndConvert);
            hourInput.addEventListener('change', checkAndConvert);
            
            // 为时辰下拉框添加特殊处理
            hourInput.addEventListener('input', checkAndConvert);
        }
    }

    /**
     * 确保表单输入字段都有name属性
     */
    ensureFormInputsHaveNames() {
        // 为主要输入字段添加name属性（如果尚未设置）
        const inputFields = [
            { id: 'yearInput', name: 'year' },
            { id: 'monthInput', name: 'month' },
            { id: 'dayInput', name: 'day' },
            { id: 'hourInput', name: 'hour' },
            { id: 'genderInput', name: 'gender' },
            { id: 'nameInput', name: 'name' },
            { id: 'cardKeyInput', name: 'cardKey' } // 添加卡密字段的name属性
        ];
        
        inputFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element && !element.hasAttribute('name')) {
                element.setAttribute('name', field.name);
                console.log(`为 ${field.id} 添加 name 属性: ${field.name}`);
            }
        });
    }

    /**
     * 验证表单是否填写完整
     * @returns {boolean} 是否完整
     */
    validateForm() {
        const requiredFields = [
            'yearInput', 'monthInput', 'dayInput', 'hourInput', 'genderInput'
        ];
        
        let isValid = true;
        let firstInvalidField = null;
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && field.required && !field.value) {
                isValid = false;
                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
            }
        });
        
        // 如果有无效字段，尝试聚焦并滚动到视图
        if (firstInvalidField) {
            setTimeout(() => {
                firstInvalidField.focus();
                firstInvalidField.scrollIntoView({behavior: 'smooth', block: 'center'});
            }, 100);
        }
        
        return isValid;
    }
    
    /**
     * 确保必填字段有默认值（如性别等）
     */
    ensureRequiredFieldsHaveValues() {
        // 设置性别默认值（如果未设置）
        const genderInput = document.getElementById('genderInput');
        if (genderInput && !genderInput.value) {
            genderInput.value = '男'; // 默认设置为男性
        }
        
        // 如果有名字输入字段但为空，设置默认值
        const nameInput = document.getElementById('nameInput');
        if (nameInput && nameInput.required && !nameInput.value) {
            nameInput.value = '佚名'; // 默认设置为佚名
        }
    }

    /**
     * 处理表单验证问题
     * 移除隐藏字段的required属性以避免HTML5验证错误
     */
    handleFormValidation() {
        const currentMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'solar';
        
        // 获取所有可能的输入字段
        const allFields = [
            'yearInput', 'monthInput', 'dayInput', 'hourInput',
            'lunarYearInput', 'lunarMonthInput', 'lunarDayInput', 'lunarHourInput',
            'yearPillar', 'monthPillar', 'dayPillar', 'timePillar',
            'baziString'
        ];
        
        allFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                const fieldContainer = field.closest('.form-section, .input-group, .bazi-input-section, .lunar-input-section');
                
                // 如果字段的容器被隐藏，临时移除required属性
                if (fieldContainer && (fieldContainer.style.display === 'none' || 
                    window.getComputedStyle(fieldContainer).display === 'none')) {
                    
                    // 保存原始required状态
                    if (field.hasAttribute('required')) {
                        field.setAttribute('data-original-required', 'true');
                        field.removeAttribute('required');
                    }
                } else {
                    // 如果字段可见，恢复required属性
                    if (field.hasAttribute('data-original-required')) {
                        field.setAttribute('required', '');
                        field.removeAttribute('data-original-required');
                    }
                }
            }
        });
        
        // 根据当前模式设置正确的required属性
        if (currentMode === 'solar') {
            // 阳历模式：年月日时必填
            ['yearInput', 'monthInput', 'dayInput', 'hourInput'].forEach(id => {
                const field = document.getElementById(id);
                if (field) field.setAttribute('required', '');
            });
        } else if (currentMode === 'lunar') {
            // 农历模式：农历年月日时必填
            ['lunarYearInput', 'lunarMonthInput', 'lunarDayInput', 'lunarHourInput'].forEach(id => {
                const field = document.getElementById(id);
                if (field) field.setAttribute('required', '');
            });
        } else if (currentMode === 'bazi') {
            // 八字模式：四柱必填
            ['yearPillar', 'monthPillar', 'dayPillar', 'timePillar'].forEach(id => {
                const field = document.getElementById(id);
                if (field) field.setAttribute('required', '');
            });
        }
    }

    /**
     * 执行八字反推（用于手动反推按钮）
     */
    async performBaziReverse() {
        if (!this.isCalculateLoaded) {
            showError('八字库尚未加载完成，请稍后重试');
            return;
        }

        try {
            let baziString = '';
            
            // 检查当前输入模式
            const inputMode = document.querySelector('input[name="baziInputMode"]:checked').value;
            
            if (inputMode === 'combined') {
                // 整体输入模式
                const baziInput = document.getElementById('baziString').value.trim();
                if (!baziInput) {
                    showWarning('请输入完整的八字信息');
                    return;
                }
                
                // 解析八字字符串
                baziString = this.parseBaziString(baziInput);
                if (!baziString) {
                    showError('八字格式不正确，请检查输入格式');
                    return;
                }
            } else {
                // 分别输入模式 - 从下拉框获取天干地支
                const yearTiangan = document.getElementById('yearTiangan').value;
                const yearDizhi = document.getElementById('yearDizhi').value;
                const monthTiangan = document.getElementById('monthTiangan').value;
                const monthDizhi = document.getElementById('monthDizhi').value;
                const dayTiangan = document.getElementById('dayTiangan').value;
                const dayDizhi = document.getElementById('dayDizhi').value;
                const timeTiangan = document.getElementById('timeTiangan').value;
                const timeDizhi = document.getElementById('timeDizhi').value;

                // 验证输入
                if (!yearTiangan || !yearDizhi || !monthTiangan || !monthDizhi || 
                    !dayTiangan || !dayDizhi || !timeTiangan || !timeDizhi) {
                    showWarning('请选择完整的四柱八字信息');
                    return;
                }

                // 构建八字字符串
                const yearPillar = yearTiangan + yearDizhi;
                const monthPillar = monthTiangan + monthDizhi;
                const dayPillar = dayTiangan + dayDizhi;
                const timePillar = timeTiangan + timeDizhi;
                
                baziString = yearPillar + monthPillar + dayPillar + timePillar;
            }

            // 使用默认的流派和基准年值
            const sect = 2; // 默认使用流派2
            const baseYear = 1824; // 默认使用1824年作为基准年

            // 将八字字符串拆分成四柱
            const yearGanZhi = baziString.substr(0, 2);
            const monthGanZhi = baziString.substr(2, 2);
            const dayGanZhi = baziString.substr(4, 2);
            const timeGanZhi = baziString.substr(6, 2);

            // 调用Solar.fromBaZi进行反推
            const results = Solar.fromBaZi(yearGanZhi, monthGanZhi, dayGanZhi, timeGanZhi, sect, baseYear);
            this.displayReverseResults(results);
        } catch (error) {
            console.error('八字反推失败:', error);
            showError('八字反推失败，请检查输入的八字是否正确');
        }
    }

    /**
     * 显示反推结果
     * @param {Array} results - 反推结果数组
     * @param {boolean} autoSelect - 是否自动选择最佳结果
     */
    displayReverseResults(results, autoSelect = false) {
        const resultsDiv = document.getElementById('reverseResults');
        if (!results || results.length === 0) {
            resultsDiv.innerHTML = '<p class="no-results">未找到匹配的阳历日期，请检查八字输入</p>';
            resultsDiv.style.display = 'block';
            return;
        }

        let html = '<h4>🔍 反推结果' + (autoSelect ? '（已自动选择最佳匹配）' : '（点击选择）') + '：</h4><div class="results-list">';
        
        // 查找距离2000年最近的结果
        const targetYear = 2000;
        let closestResult = results[0];
        let minYearDiff = Math.abs(results[0].getYear() - targetYear);
        let closestIndex = 0;
        
        for (let i = 1; i < results.length; i++) {
            const yearDiff = Math.abs(results[i].getYear() - targetYear);
            if (yearDiff < minYearDiff) {
                minYearDiff = yearDiff;
                closestResult = results[i];
                closestIndex = i;
            }
        }
        
        results.forEach((solar, index) => {
            const date = `${solar.getYear()}-${solar.getMonth().toString().padStart(2, '0')}-${solar.getDay().toString().padStart(2, '0')}`;
            const time = `${solar.getHour().toString().padStart(2, '0')}:${solar.getMinute().toString().padStart(2, '0')}`;
            
            // 如果是自动选择模式，为最佳结果添加选中样式
            const isSelected = autoSelect && index === closestIndex;
            
            html += `
                <div class="result-item ${isSelected ? 'selected' : ''}" data-year="${solar.getYear()}" data-month="${solar.getMonth()}" data-day="${solar.getDay()}" data-hour="${solar.getHour()}">
                    <span class="result-date">${date}</span>
                    <span class="result-time">${time}</span>
                    <span class="result-weekday">${solar.getWeekInChinese()}</span>
                    ${isSelected ? '<span class="result-best-match">✓ 最佳匹配</span>' : ''}
                </div>
            `;
        });
        
        html += '</div>';
        resultsDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    }

    /**
     * 执行农历转换
     * @param {boolean} silent - 是否静默执行，不显示结果弹窗
     */
    async performLunarConvert(silent = false) {
        // 检查当前输入模式，如果是阳历输入模式则直接返回
        const currentMode = document.querySelector('input[name="inputMode"]:checked')?.value;
        if (currentMode === 'solar') {
            console.log('当前是阳历输入模式，跳过农历转换');
            return;
        }
        
        if (!this.isCalculateLoaded) {
            if (!silent) {
                showError('农历库尚未加载完成，请稍后重试');
            } else {
                console.log('农历库尚未加载完成，稍后自动重试');
            }
            return;
        }

        // 获取农历输入
        const lunarYear = parseInt(document.getElementById('lunarYear')?.value);
        const lunarMonth = parseInt(document.getElementById('lunarMonth')?.value);
        const lunarDay = parseInt(document.getElementById('lunarDay')?.value);
        const lunarHour = document.getElementById('lunarHour')?.value;
        const isLeapMonth = document.getElementById('isLeapMonth')?.checked;
        
        // 如果是从农历输入模式调用
        if (!isNaN(lunarYear) && !isNaN(lunarMonth) && !isNaN(lunarDay) && lunarHour) {
            try {
                // 创建paipan实例并调用Lunar2Solar方法进行转换
                const paipanInstance = new paipan();
                const solarResult = paipanInstance.Lunar2Solar(lunarYear, lunarMonth, lunarDay, isLeapMonth ? 1 : 0);
                
                if (solarResult && solarResult.length >= 3) {
                    const solarData = {
                        year: solarResult[0],
                        month: solarResult[1],
                        day: solarResult[2]
                    };
                    // 将农历时辰转换为对应的阳历小时
                    let solarHour = 12; // 默认中午12点
                    if (lunarHour.includes('子时')) {
                        solarHour = 0; // 子时对应0点
                    } else if (lunarHour.includes('丑时')) solarHour = 1;
                    else if (lunarHour.includes('寅时')) solarHour = 3;
                    else if (lunarHour.includes('卯时')) solarHour = 5;
                    else if (lunarHour.includes('辰时')) solarHour = 7;
                    else if (lunarHour.includes('巳时')) solarHour = 9;
                    else if (lunarHour.includes('午时')) solarHour = 11;
                    else if (lunarHour.includes('未时')) solarHour = 13;
                    else if (lunarHour.includes('申时')) solarHour = 15;
                    else if (lunarHour.includes('酉时')) solarHour = 17;
                    else if (lunarHour.includes('戌时')) solarHour = 19;
                    else if (lunarHour.includes('亥时')) solarHour = 21;
                    
                    if (silent) {
                        // 静默模式：直接应用结果而不显示弹窗
                        this.applyLunarConvertResult({
                            year: solarData.year,
                            month: solarData.month,
                            day: solarData.day,
                            hour: solarHour
                        }, false); // 不显示提示
                    } else {
                        // 正常模式：显示转换结果弹窗
                        this.displayLunarConvertResult(solarData, lunarHour);
                    }
                } else {
                    // 根据输入参数提供更具体的错误信息
                    let errorMessage = '农历转换失败：';
                    if (isLeapMonth) {
                        errorMessage += `${lunarYear}年${lunarMonth}月不是闰月，请取消闰月选择或选择正确的闰月`;
                    } else {
                        errorMessage += '输入的农历日期无效，请检查年、月、日是否正确';
                    }
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('农历转换失败:', error);
                if (!silent) {
                    showError(error.message || '农历转换失败，请检查输入的农历日期是否正确');
                }
            }
            return;
        }
        
        // 如果是从阳历输入模式调用（自动转换）
        const year = parseInt(document.getElementById('yearInput')?.value);
        const month = parseInt(document.getElementById('monthInput')?.value);
        const day = parseInt(document.getElementById('dayInput')?.value);
        const hourSelect = document.getElementById('hourInput');
        const hourText = hourSelect?.options[hourSelect.selectedIndex]?.text || '';
        
        // 验证输入
        if (isNaN(year) || isNaN(month) || isNaN(day) || !hourText) {
            if (!silent) {
                showWarning('请填写完整的阳历信息');
            } else {
                console.log('阳历信息不完整，跳过自动转换');
            }
            return;
        }

        try {
            // 确保Solar和Lunar已加载
            if (typeof Solar === 'undefined' || typeof Lunar === 'undefined') {
                if (!silent) {
                    showError('农历转换库尚未加载完成，请稍后重试');
                } else {
                    console.log('农历转换库尚未加载完成，稍后自动重试');
                }
                return;
            }
            
            // 从时辰文本提取小时值
            let hour = 12; // 默认中午12点
            if (hourText.includes('子时')) {
                if (hourText.includes('早子时')) {
                    hour = 0;
                } else {
                    hour = 23;
                }
            } else if (hourText.includes('丑时')) hour = 2;
            else if (hourText.includes('寅时')) hour = 4;
            else if (hourText.includes('卯时')) hour = 6;
            else if (hourText.includes('辰时')) hour = 8;
            else if (hourText.includes('巳时')) hour = 10;
            else if (hourText.includes('午时')) hour = 12;
            else if (hourText.includes('未时')) hour = 14;
            else if (hourText.includes('申时')) hour = 16;
            else if (hourText.includes('酉时')) hour = 18;
            else if (hourText.includes('戌时')) hour = 20;
            else if (hourText.includes('亥时')) hour = 22;
            
            // 获取农历数据
            const solar = Solar.fromYmdHms(year, month, day, hour, 0, 0);
            const lunar = solar.getLunar();
            
            if (lunar) {
                // 构建农历时辰
                const lunarHour = lunar.getTimeZhi() + '时';
                
                // 如果是静默模式，仅在控制台输出结果，不显示UI
                if (silent) {
                    console.log('阳历转农历结果:', lunar.getYear() + '年' + lunar.getMonthInChinese() + lunar.getDayInChinese() + ' ' + lunarHour);
                    
                    // 静默模式下，更新相关隐藏字段
                    const lunarYearInput = document.getElementById('lunarYear');
                    const lunarMonthInput = document.getElementById('lunarMonth');
                    const lunarDayInput = document.getElementById('lunarDay');
                    const lunarHourInput = document.getElementById('lunarHour');
                    
                    if (lunarYearInput) lunarYearInput.value = lunar.getYear();
                    if (lunarMonthInput) lunarMonthInput.value = lunar.getMonth();
                    if (lunarDayInput) lunarDayInput.value = lunar.getDay();
                    if (lunarHourInput) lunarHourInput.value = lunarHour;
                } else {
                    // 非静默模式，显示转换结果
                    const resultDiv = document.getElementById('lunarConvertResult');
                    const date = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                    
                    // 计算星期
                    const dateObj = new Date(year, month - 1, day);
                    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                    const weekday = weekdays[dateObj.getDay()];
                    
                    let html = `
                        <h4>🔄 转换结果：</h4>
                        <div class="convert-result-item">
                            <p>
                                <span class="result-label">阳历：</span>
                                <span class="result-value">${date} ${hourText} ${weekday}</span>
                            </p>
                            <p>
                                <span class="result-label">农历：</span>
                                <span class="result-value">${lunar.getYear()}年${lunar.getMonthInChinese()}${lunar.getDayInChinese()} ${lunarHour}</span>
                            </p>
                        </div>
                    `;
                    
                    resultDiv.innerHTML = html;
                    resultDiv.style.display = 'block';
                }
            } else {
                if (!silent) {
                    showError('阳历转农历失败，请检查日期是否有效');
                } else {
                    console.error('阳历转农历失败：无效日期');
                }
            }
        } catch (error) {
            console.error('阳历转农历失败:', error);
            if (!silent) {
                showError('阳历转农历失败: ' + (error.message || '未知错误'));
            }
        }
    }

    /**
     * 检查八字是否全部选择完成，如果是则自动触发反推分析
     */
    checkAndAutoReverse() {
        // 只在分别输入模式下工作
        const inputMode = document.querySelector('input[name="baziInputMode"]:checked')?.value;
        if (inputMode !== 'separate') return;
        
        // 检查是否所有八字组件都已选择完成
        if (this.checkBaziComplete()) {
            console.log('八字已全部选择完成，自动开始反推分析');
            // 延迟执行以确保UI更新完成
            setTimeout(() => {
                this.autoAnalyzeFromBazi();
            }, 300);
        }
    }

    /**
     * 显示农历转换结果
     * @param {Object} solarData 阳历日期数据
     * @param {string} lunarHour 农历时辰
     */
    displayLunarConvertResult(solarData, lunarHour) {
        try {
            // 构建阳历日期
            const date = `${solarData.year}-${solarData.month.toString().padStart(2, '0')}-${solarData.day.toString().padStart(2, '0')}`;
            
            // 获取时辰对应的阳历小时
            let hour = 12; // 默认中午12点
            let hourText = '';
            
            if (lunarHour.includes('子')) {
                hour = 23;
                hourText = '子时 (23:00-01:00)';
            } else if (lunarHour.includes('丑')) {
                hour = 1;
                hourText = '丑时 (01:00-03:00)';
            } else if (lunarHour.includes('寅')) {
                hour = 3;
                hourText = '寅时 (03:00-05:00)';
            } else if (lunarHour.includes('卯')) {
                hour = 5;
                hourText = '卯时 (05:00-07:00)';
            } else if (lunarHour.includes('辰')) {
                hour = 7;
                hourText = '辰时 (07:00-09:00)';
            } else if (lunarHour.includes('巳')) {
                hour = 9;
                hourText = '巳时 (09:00-11:00)';
            } else if (lunarHour.includes('午')) {
                hour = 11;
                hourText = '午时 (11:00-13:00)';
            } else if (lunarHour.includes('未')) {
                hour = 13;
                hourText = '未时 (13:00-15:00)';
            } else if (lunarHour.includes('申')) {
                hour = 15;
                hourText = '申时 (15:00-17:00)';
            } else if (lunarHour.includes('酉')) {
                hour = 17;
                hourText = '酉时 (17:00-19:00)';
            } else if (lunarHour.includes('戌')) {
                hour = 19;
                hourText = '戌时 (19:00-21:00)';
            } else if (lunarHour.includes('亥')) {
                hour = 21;
                hourText = '亥时 (21:00-23:00)';
            }
            
            // 计算星期
            const dateObj = new Date(solarData.year, solarData.month - 1, solarData.day);
            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            const weekday = weekdays[dateObj.getDay()];
            
            // 显示结果弹窗
            const resultDiv = document.getElementById('lunarConvertResult');
            if (resultDiv) {
                let html = `
                    <h4>🔄 转换结果：</h4>
                    <div class="convert-result-item">
                        <p>
                            <span class="result-label">农历输入：</span>
                            <span class="result-value">${document.getElementById('lunarYear').value}年${document.getElementById('lunarMonth').value}月${document.getElementById('lunarDay').value}日 ${lunarHour}</span>
                        </p>
                        <p>
                            <span class="result-label">阳历结果：</span>
                            <span class="result-value">${date} ${hourText} ${weekday}</span>
                        </p>
                    </div>
                    <div class="convert-actions">
                        <button id="applyConvertBtn" class="action-btn primary">应用此结果</button>
                        <button id="cancelConvertBtn" class="action-btn outline">取消</button>
                    </div>
                `;
                
                resultDiv.innerHTML = html;
                resultDiv.style.display = 'block';
                
                // 绑定应用按钮事件
                document.getElementById('applyConvertBtn').addEventListener('click', () => {
                    this.applyLunarConvertResult({
                        year: solarData.year,
                        month: solarData.month,
                        day: solarData.day,
                        hour: hour
                    }, true); // 明确指定显示提示
                    resultDiv.style.display = 'none';
                });
                
                // 绑定取消按钮事件
                document.getElementById('cancelConvertBtn').addEventListener('click', () => {
                    resultDiv.style.display = 'none';
                });
            }
        } catch (error) {
            console.error('显示农历转换结果失败:', error);
            showError('显示农历转换结果失败: ' + (error.message || '未知错误'));
        }
    }

    /**
     * 应用农历转换结果到阳历输入框
     * @param {Object} solarDate 阳历日期对象
     * @param {boolean} showAlert 是否显示提示信息，默认为true
     */
    applyLunarConvertResult(solarDate, showAlert = true) {
        try {
            // 填充阳历输入框
            document.getElementById('yearInput').value = solarDate.year;
            document.getElementById('monthInput').value = solarDate.month;
            document.getElementById('dayInput').value = solarDate.day;
            
            // 设置时辰
            const hourSelect = document.getElementById('hourInput');
            if (hourSelect && typeof solarDate.hour === 'number') {
                // 根据小时找到最接近的时辰选项
                const hourMap = {
                    0: '子', 1: '丑', 2: '丑', 3: '寅', 4: '寅', 5: '卯',
                    6: '卯', 7: '辰', 8: '辰', 9: '巳', 10: '巳', 11: '午',
                    12: '午', 13: '未', 14: '未', 15: '申', 16: '申', 17: '酉',
                    18: '酉', 19: '戌', 20: '戌', 21: '亥', 22: '亥', 23: '子'
                };
                
                const targetHourChar = hourMap[solarDate.hour] || '午';
                
                // 寻找包含此时辰的选项
                for (let i = 0; i < hourSelect.options.length; i++) {
                    if (hourSelect.options[i].text.includes(targetHourChar + '时')) {
                        hourSelect.selectedIndex = i;
                        console.log(`设置时辰: ${solarDate.hour}点 -> ${targetHourChar}时 (选项${i})`);
                        break;
                    }
                }
            }
            
            // 切换到阳历输入模式
            const solarRadio = document.querySelector('input[name="inputMode"][value="solar"]');
            if (solarRadio) {
                solarRadio.checked = true;
                this.toggleInputMode('solar');
            }
            
            // 触发变更事件
            const event = new Event('change');
            document.getElementById('yearInput').dispatchEvent(event);
            document.getElementById('monthInput').dispatchEvent(event);
            document.getElementById('dayInput').dispatchEvent(event);
            if (hourSelect) hourSelect.dispatchEvent(event);
            
            // 只在需要时显示提示
            if (showAlert) {
                showSuccess('已应用农历转换结果到阳历输入框');
            }
        } catch (error) {
            console.error('应用农历转换结果失败:', error);
            if (showAlert) {
                showError('应用农历转换结果失败: ' + (error.message || '未知错误'));
            }
        }
    }
    
    /**
     * 为农历输入框添加自动转换事件监听器
     */
    addLunarAutoConvertListeners() {
        // 检查农历输入框是否存在
        const lunarYear = document.getElementById('lunarYear');
        const lunarMonth = document.getElementById('lunarMonth');
        const lunarDay = document.getElementById('lunarDay');
        const lunarHour = document.getElementById('lunarHour');
        
        if (!lunarYear || !lunarMonth || !lunarDay || !lunarHour) {
            console.log('农历输入框未找到，跳过自动转换监听器绑定');
            return;
        }
        
        // 检查农历输入是否完整的函数
        const checkLunarInputComplete = () => {
            const year = lunarYear.value.trim();
            const month = lunarMonth.value.trim();
            const day = lunarDay.value.trim();
            const hour = lunarHour.value.trim();
            
            // 检查所有字段是否都已填写
            if (year && month && day && hour) {
                console.log('农历输入完成，自动转换为阳历');
                // 延迟执行转换，确保输入完成
                setTimeout(() => {
                    this.performLunarConvert(true); // 使用静默模式
                }, 300);
            }
        };
        
        // 为农历输入框添加事件监听器
        lunarYear.addEventListener('change', checkLunarInputComplete);
        lunarYear.addEventListener('input', checkLunarInputComplete);
        
        lunarMonth.addEventListener('change', checkLunarInputComplete);
        lunarMonth.addEventListener('input', checkLunarInputComplete);
        
        lunarDay.addEventListener('change', checkLunarInputComplete);
        lunarDay.addEventListener('input', checkLunarInputComplete);
        
        lunarHour.addEventListener('change', checkLunarInputComplete);
        lunarHour.addEventListener('input', checkLunarInputComplete);
        
        console.log('农历输入框自动转换监听器已绑定');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本加载完成
    setTimeout(() => {
        window.baziIntegration = new BaziIntegration();
    }, 1000);
});