#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理桌面GUI应用
使用tkinter创建用户友好的桌面界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import json
import os
import threading
from datetime import datetime
from typing import Dict, List, Any

# 导入我们的提示词管理组件
try:
    from prompt_manager import PromptManager
    from prompt_evaluator import PromptEvaluator
    from prompt_version_manager import PromptVersionManager, PromptABTester
    from optimized_bazi_analyzer import OptimizedBaziAnalyzer
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的模块都在同一目录下")

class PromptManagerGUI:
    """提示词管理桌面GUI应用"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🧠 八字分析提示词管理系统")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 初始化组件
        self.init_components()
        
        # 初始化状态变量
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")

        # 创建界面
        self.create_widgets()

        # 加载初始数据
        self.load_initial_data()
    
    def init_components(self):
        """初始化后端组件"""
        try:
            self.prompt_manager = PromptManager()
            self.prompt_evaluator = PromptEvaluator()
            self.version_manager = PromptVersionManager()
            self.ab_tester = PromptABTester(self.version_manager)
            
            self.current_dimension = ""
            self.current_version = "current"
            
            print("✅ 组件初始化成功")
        except Exception as e:
            messagebox.showerror("初始化错误", f"组件初始化失败: {str(e)}")
            self.root.quit()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建标题
        title_label = ttk.Label(main_frame, text="🧠 八字分析提示词管理系统", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 创建左侧面板（导航）
        self.create_navigation_panel(main_frame)
        
        # 创建右侧面板（内容）
        self.create_content_panel(main_frame)
        
        # 创建状态栏
        self.create_status_bar(main_frame)
    
    def create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        nav_frame = ttk.LabelFrame(parent, text="功能导航", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 导航按钮
        nav_buttons = [
            ("📝 提示词编辑", self.show_prompt_editor),
            ("🔄 版本管理", self.show_version_manager),
            ("📊 质量评估", self.show_evaluation_panel),
            ("🧪 A/B测试", self.show_ab_testing),
            ("⚙️ 系统设置", self.show_settings),
        ]
        
        for i, (text, command) in enumerate(nav_buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # 系统信息
        info_frame = ttk.LabelFrame(nav_frame, text="系统信息", padding="5")
        info_frame.grid(row=len(nav_buttons), column=0, pady=(20, 0), sticky=(tk.W, tk.E))
        
        self.info_text = tk.Text(info_frame, height=8, width=25, wrap=tk.WORD, 
                                font=('Courier', 9))
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 配置导航面板网格权重
        nav_frame.columnconfigure(0, weight=1)
    
    def create_content_panel(self, parent):
        """创建右侧内容面板"""
        self.content_frame = ttk.Frame(parent)
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)
        
        # 默认显示提示词编辑器
        self.show_prompt_editor()
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 版本信息
        version_label = ttk.Label(status_frame, text=f"当前版本: {self.current_version}")
        version_label.grid(row=0, column=1, sticky=tk.E)
        
        status_frame.columnconfigure(0, weight=1)
    
    def clear_content_frame(self):
        """清空内容面板"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_prompt_editor(self):
        """显示提示词编辑器"""
        self.clear_content_frame()
        
        editor_frame = ttk.LabelFrame(self.content_frame, text="提示词编辑器", padding="10")
        editor_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        editor_frame.columnconfigure(1, weight=1)
        editor_frame.rowconfigure(2, weight=1)
        
        # 维度选择
        ttk.Label(editor_frame, text="分析维度:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.dimension_var = tk.StringVar()
        dimension_combo = ttk.Combobox(editor_frame, textvariable=self.dimension_var, 
                                      values=self.prompt_manager.get_available_dimensions(),
                                      state="readonly")
        dimension_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        dimension_combo.bind('<<ComboboxSelected>>', self.on_dimension_selected)
        
        # 操作按钮
        button_frame = ttk.Frame(editor_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="📁 加载", command=self.load_prompt).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="💾 保存", command=self.save_prompt).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="👁️ 预览", command=self.preview_prompt).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 测试", command=self.test_prompt).pack(side=tk.LEFT, padx=5)
        
        # 提示词编辑区域
        self.prompt_text = scrolledtext.ScrolledText(editor_frame, wrap=tk.WORD, 
                                                    font=('Courier', 10))
        self.prompt_text.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                             pady=(10, 0))
        
        self.update_status("提示词编辑器已加载")
    
    def show_version_manager(self):
        """显示版本管理器"""
        self.clear_content_frame()
        
        version_frame = ttk.LabelFrame(self.content_frame, text="版本管理", padding="10")
        version_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        version_frame.columnconfigure(0, weight=1)
        version_frame.rowconfigure(1, weight=1)
        
        # 操作按钮
        button_frame = ttk.Frame(version_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="➕ 创建版本", command=self.create_version).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 刷新", command=self.refresh_versions).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 对比", command=self.compare_versions).pack(side=tk.LEFT, padx=5)
        
        # 版本列表
        columns = ('名称', '描述', '创建时间', '状态')
        self.version_tree = ttk.Treeview(version_frame, columns=columns, show='headings')
        
        for col in columns:
            self.version_tree.heading(col, text=col)
            self.version_tree.column(col, width=150)
        
        # 添加滚动条
        version_scrollbar = ttk.Scrollbar(version_frame, orient=tk.VERTICAL, 
                                         command=self.version_tree.yview)
        self.version_tree.configure(yscrollcommand=version_scrollbar.set)
        
        self.version_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        version_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.version_tree.bind('<Double-1>', self.on_version_double_click)
        
        # 加载版本列表
        self.refresh_versions()
        
        self.update_status("版本管理器已加载")
    
    def show_evaluation_panel(self):
        """显示质量评估面板"""
        self.clear_content_frame()
        
        eval_frame = ttk.LabelFrame(self.content_frame, text="质量评估", padding="10")
        eval_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        eval_frame.columnconfigure(0, weight=1)
        eval_frame.rowconfigure(2, weight=1)
        
        # 评估配置
        config_frame = ttk.Frame(eval_frame)
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        ttk.Label(config_frame, text="评估维度:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.eval_dimension_var = tk.StringVar()
        eval_combo = ttk.Combobox(config_frame, textvariable=self.eval_dimension_var,
                                 values=self.prompt_manager.get_available_dimensions(),
                                 state="readonly")
        eval_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        
        # 测试数据输入
        ttk.Label(eval_frame, text="测试八字数据:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.test_data_text = scrolledtext.ScrolledText(eval_frame, height=8, wrap=tk.WORD)
        self.test_data_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 默认测试数据
        default_data = """【八字基本信息】
出生时间：1990年5月15日 14:30
八字：庚午年 辛巳月 甲子日 辛未时

【十神配置】
天干十神：甲木(日主) 庚金(七杀) 辛金(正官) 辛金(正官)
地支藏干十神：丁火(伤官) 戊土(偏财) 丙火(食神) 戊土(偏财) 癸水(正印) 己土(正财) 丁火(伤官) 乙木(比肩)"""
        
        self.test_data_text.insert(tk.END, default_data)
        
        # 评估按钮
        button_frame = ttk.Frame(eval_frame)
        button_frame.grid(row=3, column=0, pady=10)
        
        ttk.Button(button_frame, text="🔍 开始评估", command=self.run_evaluation).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📊 批量评估", command=self.batch_evaluation).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 导出报告", command=self.export_evaluation).pack(side=tk.LEFT, padx=5)
        
        self.update_status("质量评估面板已加载")
    
    def show_ab_testing(self):
        """显示A/B测试面板"""
        self.clear_content_frame()
        
        ab_frame = ttk.LabelFrame(self.content_frame, text="A/B测试", padding="10")
        ab_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 实验创建区域
        create_frame = ttk.LabelFrame(ab_frame, text="创建实验", padding="10")
        create_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        create_frame.columnconfigure(1, weight=1)
        
        ttk.Label(create_frame, text="实验名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.exp_name_var = tk.StringVar()
        ttk.Entry(create_frame, textvariable=self.exp_name_var).grid(row=0, column=1, 
                                                                    sticky=(tk.W, tk.E), 
                                                                    padx=(10, 0), pady=5)
        
        ttk.Label(create_frame, text="测试版本:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.exp_versions_var = tk.StringVar()
        ttk.Entry(create_frame, textvariable=self.exp_versions_var).grid(row=1, column=1, 
                                                                        sticky=(tk.W, tk.E), 
                                                                        padx=(10, 0), pady=5)
        
        ttk.Button(create_frame, text="🚀 创建实验", command=self.create_experiment).grid(row=2, column=1, 
                                                                                      sticky=tk.E, pady=10)
        
        # 实验列表区域
        list_frame = ttk.LabelFrame(ab_frame, text="实验列表", padding="10")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 实验列表
        exp_columns = ('名称', '变体', '状态', '创建时间')
        self.experiment_tree = ttk.Treeview(list_frame, columns=exp_columns, show='headings')
        
        for col in exp_columns:
            self.experiment_tree.heading(col, text=col)
            self.experiment_tree.column(col, width=120)
        
        self.experiment_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        ab_frame.columnconfigure(0, weight=1)
        ab_frame.rowconfigure(1, weight=1)
        
        self.update_status("A/B测试面板已加载")
    
    def show_settings(self):
        """显示系统设置"""
        self.clear_content_frame()
        
        settings_frame = ttk.LabelFrame(self.content_frame, text="系统设置", padding="10")
        settings_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设置选项
        ttk.Label(settings_frame, text="提示词目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        dir_frame = ttk.Frame(settings_frame)
        dir_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
        dir_frame.columnconfigure(0, weight=1)
        
        self.prompts_dir_var = tk.StringVar(value=self.prompt_manager.prompts_dir)
        ttk.Entry(dir_frame, textvariable=self.prompts_dir_var).grid(row=0, column=0, 
                                                                    sticky=(tk.W, tk.E))
        ttk.Button(dir_frame, text="浏览", command=self.browse_prompts_dir).grid(row=0, column=1, 
                                                                               padx=(5, 0))
        
        # 其他设置
        ttk.Checkbutton(settings_frame, text="启用自动备份").grid(row=1, column=0, columnspan=2, 
                                                              sticky=tk.W, pady=5)
        ttk.Checkbutton(settings_frame, text="启用质量评估").grid(row=2, column=0, columnspan=2, 
                                                              sticky=tk.W, pady=5)
        
        # 保存按钮
        ttk.Button(settings_frame, text="💾 保存设置", command=self.save_settings).grid(row=3, column=1, 
                                                                                     sticky=tk.E, pady=20)
        
        settings_frame.columnconfigure(1, weight=1)
        
        self.update_status("系统设置已加载")
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 更新系统信息
            self.update_system_info()
            self.update_status("系统初始化完成")
        except Exception as e:
            messagebox.showerror("加载错误", f"加载初始数据失败: {str(e)}")
    
    def update_system_info(self):
        """更新系统信息显示"""
        try:
            stats = self.prompt_manager.get_prompt_stats()
            versions = self.version_manager.list_versions()
            
            info_text = f"""系统状态: 运行中
当前版本: {self.current_version}
可用维度: {stats.get('available_dimensions', 0)}
缓存大小: {stats.get('cache_size', 0)}
版本数量: {len(versions)}
更新时间: {datetime.now().strftime('%H:%M:%S')}"""
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(tk.END, info_text)
        except Exception as e:
            print(f"更新系统信息失败: {e}")
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(f"{message} - {datetime.now().strftime('%H:%M:%S')}")
        self.root.update_idletasks()
    
    # 事件处理方法
    def on_dimension_selected(self, event):
        """维度选择事件"""
        self.current_dimension = self.dimension_var.get()
        self.update_status(f"选择维度: {self.current_dimension}")
    
    def load_prompt(self):
        """加载提示词"""
        if not self.current_dimension:
            messagebox.showwarning("警告", "请先选择分析维度")
            return
        
        try:
            config = self.prompt_manager._load_dimension_config(self.current_dimension)
            content = config.get('content', '')
            
            self.prompt_text.delete(1.0, tk.END)
            self.prompt_text.insert(tk.END, content)
            
            self.update_status(f"已加载 {self.current_dimension} 的提示词")
        except Exception as e:
            messagebox.showerror("错误", f"加载提示词失败: {str(e)}")
    
    def save_prompt(self):
        """保存提示词"""
        if not self.current_dimension:
            messagebox.showwarning("警告", "请先选择分析维度")
            return
        
        content = self.prompt_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "提示词内容不能为空")
            return
        
        try:
            # 这里应该调用保存逻辑
            messagebox.showinfo("成功", f"{self.current_dimension} 的提示词已保存")
            self.update_status(f"已保存 {self.current_dimension} 的提示词")
        except Exception as e:
            messagebox.showerror("错误", f"保存提示词失败: {str(e)}")
    
    def preview_prompt(self):
        """预览提示词"""
        if not self.current_dimension:
            messagebox.showwarning("警告", "请先选择分析维度")
            return
        
        content = self.prompt_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "提示词内容不能为空")
            return
        
        # 创建预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("提示词预览")
        preview_window.geometry("800x600")
        
        preview_text = scrolledtext.ScrolledText(preview_window, wrap=tk.WORD, 
                                               font=('Courier', 10))
        preview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 构建完整提示词进行预览
        try:
            bazi_info = "【测试八字信息】\n出生时间：1990年5月15日 14:30\n八字：庚午年 辛巳月 甲子日 辛未时"
            full_prompt = f"维度: {self.current_dimension}\n\n{content}\n\n八字信息:\n{bazi_info}"
            preview_text.insert(tk.END, full_prompt)
        except Exception as e:
            preview_text.insert(tk.END, f"预览生成失败: {str(e)}")
    
    def test_prompt(self):
        """测试提示词"""
        messagebox.showinfo("提示", "测试功能正在开发中...")
    
    def refresh_versions(self):
        """刷新版本列表"""
        try:
            versions = self.version_manager.list_versions()
            
            # 清空现有项目
            for item in self.version_tree.get_children():
                self.version_tree.delete(item)
            
            # 添加版本信息
            for version in versions:
                status = "当前版本" if version.get('is_current') else "历史版本"
                created_at = version.get('created_at', '')
                if created_at:
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
                
                self.version_tree.insert('', tk.END, values=(
                    version.get('name', ''),
                    version.get('description', ''),
                    created_at,
                    status
                ))
            
            self.update_status(f"已加载 {len(versions)} 个版本")
        except Exception as e:
            messagebox.showerror("错误", f"刷新版本列表失败: {str(e)}")
    
    def create_version(self):
        """创建新版本"""
        # 创建输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("创建新版本")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="版本名称:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=10)
        
        ttk.Label(dialog, text="版本描述:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        desc_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=desc_var, width=30).grid(row=1, column=1, padx=10, pady=10)
        
        def create():
            name = name_var.get().strip()
            desc = desc_var.get().strip()
            
            if not name:
                messagebox.showwarning("警告", "版本名称不能为空")
                return
            
            try:
                success = self.version_manager.create_version(name, desc)
                if success:
                    messagebox.showinfo("成功", f"版本 {name} 创建成功")
                    dialog.destroy()
                    self.refresh_versions()
                else:
                    messagebox.showerror("错误", "创建版本失败")
            except Exception as e:
                messagebox.showerror("错误", f"创建版本失败: {str(e)}")
        
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="创建", command=create).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def on_version_double_click(self, event):
        """版本双击事件"""
        selection = self.version_tree.selection()
        if selection:
            item = self.version_tree.item(selection[0])
            version_name = item['values'][0]
            
            if messagebox.askyesno("确认", f"是否切换到版本 {version_name}？"):
                try:
                    success = self.version_manager.switch_version(version_name)
                    if success:
                        self.current_version = version_name
                        messagebox.showinfo("成功", f"已切换到版本 {version_name}")
                        self.refresh_versions()
                        self.update_system_info()
                    else:
                        messagebox.showerror("错误", "切换版本失败")
                except Exception as e:
                    messagebox.showerror("错误", f"切换版本失败: {str(e)}")
    
    def compare_versions(self):
        """对比版本"""
        messagebox.showinfo("提示", "版本对比功能正在开发中...")
    
    def run_evaluation(self):
        """运行质量评估"""
        dimension = self.eval_dimension_var.get()
        if not dimension:
            messagebox.showwarning("警告", "请选择评估维度")
            return
        
        test_data = self.test_data_text.get(1.0, tk.END).strip()
        if not test_data:
            messagebox.showwarning("警告", "请输入测试数据")
            return
        
        # 在新线程中运行评估
        def run_eval():
            try:
                self.update_status("正在运行质量评估...")
                
                # 这里应该调用实际的评估逻辑
                # evaluation = self.prompt_evaluator.evaluate_analysis_result(...)
                
                # 模拟评估结果
                result = {
                    "overall_score": 0.85,
                    "detailed_scores": {
                        "shishen_accuracy": 0.9,
                        "logic_consistency": 0.8,
                        "format_compliance": 0.85,
                        "content_completeness": 0.85
                    },
                    "issues": ["示例问题1", "示例问题2"],
                    "suggestions": ["建议1", "建议2"]
                }
                
                # 在主线程中显示结果
                self.root.after(0, lambda: self.show_evaluation_result(result))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("错误", f"评估失败: {str(e)}"))
        
        threading.Thread(target=run_eval, daemon=True).start()
    
    def show_evaluation_result(self, result):
        """显示评估结果"""
        # 创建结果窗口
        result_window = tk.Toplevel(self.root)
        result_window.title("质量评估结果")
        result_window.geometry("600x500")
        
        # 创建结果显示区域
        result_text = scrolledtext.ScrolledText(result_window, wrap=tk.WORD, font=('Arial', 10))
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 格式化显示结果
        result_content = f"""质量评估结果
================

整体评分: {result['overall_score']:.2f}

详细评分:
- 十神准确性: {result['detailed_scores']['shishen_accuracy']:.2f}
- 逻辑一致性: {result['detailed_scores']['logic_consistency']:.2f}
- 格式规范性: {result['detailed_scores']['format_compliance']:.2f}
- 内容完整性: {result['detailed_scores']['content_completeness']:.2f}

发现的问题:
{chr(10).join([f"- {issue}" for issue in result['issues']])}

改进建议:
{chr(10).join([f"- {suggestion}" for suggestion in result['suggestions']])}
"""
        
        result_text.insert(tk.END, result_content)
        result_text.config(state=tk.DISABLED)
        
        self.update_status("质量评估完成")
    
    def batch_evaluation(self):
        """批量评估"""
        messagebox.showinfo("提示", "批量评估功能正在开发中...")
    
    def export_evaluation(self):
        """导出评估报告"""
        messagebox.showinfo("提示", "导出功能正在开发中...")
    
    def create_experiment(self):
        """创建A/B测试实验"""
        messagebox.showinfo("提示", "A/B测试功能正在开发中...")
    
    def browse_prompts_dir(self):
        """浏览提示词目录"""
        directory = filedialog.askdirectory(initialdir=self.prompts_dir_var.get())
        if directory:
            self.prompts_dir_var.set(directory)
    
    def save_settings(self):
        """保存设置"""
        messagebox.showinfo("成功", "设置已保存")

def main():
    """主函数"""
    root = tk.Tk()
    app = PromptManagerGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
