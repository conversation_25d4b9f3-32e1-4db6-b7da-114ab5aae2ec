/* 变量定义文件 - 存储颜色、字体等通用变量 */
/* 导入统一配色系统 */
@import url('../unified-colors.css');

:root {
    /* 字体 */
    --main-font: var(--font-family-primary);
    --emoji-font: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Segoe UI Symbol', 'Android Emoji', 'EmojiSymbols', 'Noto Sans SC', sans-serif;
    
    /* 颜色别名 - 使用统一配色系统 */
    --text-primary: var(--text-primary);
    --text-secondary: var(--text-secondary);
    --text-light: var(--text-tertiary);
    
    --primary-color: var(--color-primary);
    --primary-color-rgb: 14, 165, 233;
    --accent-color: var(--color-accent);
    
    --bg-primary: var(--bg-primary);
    --bg-secondary: var(--bg-secondary);
    --bg-tertiary: var(--bg-tertiary);
    --bg-card: var(--bg-card);
    --bg-glass: var(--bg-glass);
    
    --border-color: var(--border-primary);
    --border-color-light: var(--border-secondary);
    
    /* 渐变别名 */
    --hero-gradient: var(--gradient-primary);
    --primary-gradient: var(--gradient-primary);
    --card-gradient: var(--gradient-card);
    --success-gradient: var(--gradient-success);
    
    /* 阴影别名 */
    --shadow-sm: var(--shadow-sm);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    
    /* 边框别名 */
    --border-radius: var(--radius-md);
    --border-radius-lg: var(--radius-lg);
    
    /* 动画别名 */
    --transition: var(--transition-normal);
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #f1f5f9;
        --text-light: #e2e8f0;
        
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --bg-card: #2d3748;
        --bg-tertiary: #4a5568;
        
        --border-color: #4a5568;
        --border-color-light: #718096;
        
        --bg-glass: rgba(45, 55, 72, 0.8);
    }
}