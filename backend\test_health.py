#!/usr/bin/env python3
"""
测试健康检查端点
"""

from app import app

def test_health():
    with app.test_client() as client:
        response = client.get('/api/health')
        print(f'Health check status: {response.status_code}')
        print(f'Response: {response.get_json()}')
        return response.status_code == 200

if __name__ == '__main__':
    success = test_health()
    print(f'Health check {"PASSED" if success else "FAILED"}')
