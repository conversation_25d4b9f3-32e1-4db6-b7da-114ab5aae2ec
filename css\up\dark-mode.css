/* 深色模式样式覆盖 */
@media (prefers-color-scheme: dark) {
    /* 导航链接深色模式 */
    .nav-link {
        color: var(--primary-color);
        background: var(--bg-glass);
        border-color: var(--border-color);
    }
    
    .nav-link:hover {
        background: var(--bg-secondary);
        color: #667eea;
    }
    
    /* 页面标题深色模式 */
    .page-header h1 {
        color: var(--text-primary);
    }
    
    .header-subtitle {
        color: var(--text-secondary);
    }
    
    /* 表单容器深色模式 */
    .form-container {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .form-section {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }
    
    .section-title {
        color: var(--text-primary);
    }
    
    /* 输入框深色模式 */
    .input-group label {
        color: var(--text-primary);
    }
    
    .input-group input,
    .input-group select {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .input-group input::placeholder {
        color: var(--text-secondary);
    }
    
    .input-group input:focus,
    .input-group select:focus {
        border-color: var(--primary-color);
        background: var(--bg-secondary);
    }
    
    /* 按钮深色模式 */
    .submit-btn {
        background: var(--primary-gradient);
        color: white;
    }
    
    .submit-btn:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
    }
    
    /* 进度容器深色模式 */
    .progress-container {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .progress-text {
        color: var(--text-primary);
    }
    
    .progress-bar {
        background: var(--bg-tertiary);
    }
    
    /* 结果容器深色模式 */
    .result-container {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .result-title {
        color: var(--text-primary);
    }
    
    .result-content {
        color: var(--text-secondary);
    }
    
    /* 购买卡密区域深色模式 */
    .purchase-card {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .purchase-title {
        color: var(--text-primary);
    }
    
    .purchase-description {
        color: var(--text-secondary);
    }
    
    .purchase-button {
        background: var(--success-gradient);
        color: white;
    }
    
    .purchase-button:hover {
        background: linear-gradient(135deg, #38a169 0%, #48bb78 100%);
    }
    
    /* 页脚深色模式 */
    .footer {
        background: var(--bg-secondary);
        border-color: var(--border-color);
    }
    
    .footer p {
        color: var(--text-secondary);
    }
    
    /* 等待和错误消息深色模式 */
    .waiting-message,
    .error-message {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .waiting-message h3,
    .error-message h3 {
        color: var(--text-primary);
    }
    
    .waiting-message p,
    .error-message p {
        color: var(--text-secondary);
    }
    
    /* 选择框选项深色模式 */
    #hourInput option {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }
    
    /* 输入图标深色模式 */
    .input-icon {
        color: var(--text-secondary);
    }
    
    /* 移动端深色模式优化 */
    @media (max-width: 768px) {
        .nav-link:active {
            background: rgba(102, 126, 234, 0.2);
        }
        
        .card-purchase-button:active {
            background: rgba(102, 126, 234, 0.2);
        }
    }
} 