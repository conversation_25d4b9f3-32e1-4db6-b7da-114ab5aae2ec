#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理八字分析结果中的Markdown格式
"""

import os
import json
import re
import glob
import argparse
from pathlib import Path

def clean_markdown_formatting(content):
    """清理文本中的Markdown格式
    
    Args:
        content: 包含Markdown格式的原始文本
        
    Returns:
        str: 清理后的纯文本
    """
    if content is None or content == "":
        return ""
    
    # 1. 清除所有Markdown标题标记
    content = re.sub(r'^#+\s+', '', content, flags=re.MULTILINE)
    
    # 2. 清除粗体、斜体标记
    content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 清除粗体 **text**
    content = re.sub(r'\*(.*?)\*', r'\1', content)      # 清除斜体 *text*
    content = re.sub(r'__(.*?)__', r'\1', content)      # 清除粗体 __text__
    content = re.sub(r'_(.*?)_', r'\1', content)        # 清除斜体 _text_
    
    # 3. 清除列表标记
    content = re.sub(r'^\s*[-*+•]\s+', '', content, flags=re.MULTILINE)  # 无序列表
    content = re.sub(r'^\s*\d+\.\s+', '', content, flags=re.MULTILINE)   # 有序列表
    
    # 4. 清除引用标记
    content = re.sub(r'^\s*>\s+', '', content, flags=re.MULTILINE)
    
    # 5. 清除代码段标记
    content = re.sub(r'```.*?\n(.*?)```', r'\1', content, flags=re.DOTALL)  # 代码块
    content = re.sub(r'`([^`]+)`', r'\1', content)  # 内联代码
    
    # 6. 清除水平线
    content = re.sub(r'^\s*[-*_]{3,}\s*$', '', content, flags=re.MULTILINE)
    
    # 7. 清除链接标记
    content = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', content)
    
    # 8. 清除图片标记
    content = re.sub(r'!\[(.*?)\]\(.*?\)', r'\1', content)
    
    # 9. 清除HTML标签
    content = re.sub(r'<[^>]+>', '', content)
    
    # 10. 清除表格标记
    content = re.sub(r'^\|.*\|$', '', content, flags=re.MULTILINE)
    content = re.sub(r'^[-|:]+$', '', content, flags=re.MULTILINE)
    
    # 11. 处理连续多个换行为单个换行
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    # 12. 清除markdown风格的注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 13. 清除任何剩余的特殊标记字符序列
    content = re.sub(r'[\\`*_#{}\[\]()>#+\-.!](\s*[\\`*_#{}\[\]()>#+\-.!])+', ' ', content)
    
    # 14. 清除多余空格
    content = re.sub(r' {2,}', ' ', content)
    
    return content.strip()

def process_json_file(file_path, backup=True):
    """处理单个JSON文件，清理其中的Markdown格式
    
    Args:
        file_path: JSON文件路径
        backup: 是否创建备份
        
    Returns:
        bool: 是否成功处理
    """
    try:
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建备份
        if backup:
            backup_path = f"{file_path}.bak"
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 已创建备份: {backup_path}")
        
        # 处理每个维度的内容
        modified = False
        for key, value in data.items():
            if isinstance(value, str) and any(md_char in value for md_char in ['#', '*', '_', '`', '>', '-', '|', '[']):
                data[key] = clean_markdown_formatting(value)
                modified = True
        
        # 保存处理后的文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 已处理文件: {file_path}")
            return True
        else:
            print(f"ℹ️ 文件无需处理: {file_path}")
            return False
    
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="清理八字分析结果中的Markdown格式")
    parser.add_argument("--dir", "-d", default="analysis_results", help="分析结果目录")
    parser.add_argument("--pattern", "-p", default="llm_analysis_*.json", help="文件匹配模式")
    parser.add_argument("--no-backup", "-n", action="store_true", help="不创建备份")
    parser.add_argument("--file", "-f", help="处理单个文件")
    args = parser.parse_args()
    
    # 设置工作目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    results_dir = os.path.join(script_dir, args.dir)
    
    # 处理单个文件
    if args.file:
        file_path = args.file if os.path.isabs(args.file) else os.path.join(script_dir, args.file)
        if os.path.exists(file_path):
            process_json_file(file_path, not args.no_backup)
        else:
            print(f"❌ 文件不存在: {file_path}")
        return
    
    # 处理目录中的所有匹配文件
    if os.path.exists(results_dir):
        pattern = os.path.join(results_dir, args.pattern)
        files = glob.glob(pattern)
        
        if not files:
            print(f"⚠️ 未找到匹配的文件: {pattern}")
            return
        
        processed = 0
        total = len(files)
        print(f"🔍 找到 {total} 个文件，开始处理...")
        
        for file_path in files:
            if process_json_file(file_path, not args.no_backup):
                processed += 1
        
        print(f"✅ 处理完成，共处理 {processed}/{total} 个文件")
    else:
        print(f"❌ 目录不存在: {results_dir}")

if __name__ == "__main__":
    main() 