/* 骨架屏样式 - 用于报告加载时显示 */
.skeleton {
    position: relative;
    overflow: hidden;
}

.skeleton::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0) 100%);
    animation: shimmer 2s infinite;
}

.skeleton-title {
    height: 28px;
    width: 200px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 16px;
}

.skeleton-line {
    height: 16px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
}

.skeleton-line.title {
    height: 24px;
    width: 70%;
}

.skeleton-line.date {
    width: 40%;
}

.skeleton-badge {
    width: 40px;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
}

.skeleton-button {
    height: 36px;
    width: 80px;
    background-color: #f0f0f0;
    border-radius: 4px;
    display: inline-block;
    margin-right: 8px;
}

.loading-more {
    text-align: center;
    padding: 15px;
    color: var(--text-color-secondary);
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
    .skeleton-line, .skeleton-badge, .skeleton-button, .skeleton-title {
        background-color: #2a2a2a;
    }
    
    .skeleton::after {
        background: linear-gradient(90deg, 
            rgba(50, 50, 50, 0) 0%, 
            rgba(50, 50, 50, 0.3) 50%, 
            rgba(50, 50, 50, 0) 100%);
    }
} 