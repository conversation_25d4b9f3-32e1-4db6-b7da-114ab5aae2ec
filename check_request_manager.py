#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 request_manager 中的数据
"""

import sys
sys.path.append('backend')

from bazi_service import BaziService

def check_request_manager():
    """检查 request_manager 中的数据"""
    bs = BaziService()
    
    print("检查 request_manager 数据:")
    print("=" * 60)
    
    # 检查 request_manager 的数据
    rm = bs.request_manager
    
    print(f"request_manager 类型: {type(rm)}")
    
    # 检查是否有 get_latest_request_by_card 方法
    if hasattr(rm, 'get_latest_request_by_card'):
        print("✅ 有 get_latest_request_by_card 方法")
        
        # 测试查找 Xs1MR9iVx9RNZOk6
        latest_request = rm.get_latest_request_by_card('Xs1MR9iVx9RNZOk6')
        print(f"查找 Xs1MR9iVx9RNZOk6 结果: {latest_request}")
        
        if latest_request:
            request_id = latest_request[0]
            print(f"  请求ID: {request_id}")
            
            # 检查这个请求ID是否在 results 中
            if request_id in bs.results:
                result = bs.results[request_id]
                print(f"  在 results 中找到: {type(result)}")
                
                # 检查八字数据
                data = result.get('data', {})
                bz = data.get('bz', {})
                if bz:
                    birth_info = bz.get('8', '未知生辰')
                    print(f"  生辰: {birth_info}")
            else:
                print(f"  ❌ 请求ID {request_id} 不在 results 中")
        else:
            print("  ❌ 未找到请求")
    else:
        print("❌ 没有 get_latest_request_by_card 方法")
    
    # 检查 request_manager 的其他属性
    print(f"\nrequest_manager 属性:")
    for attr in dir(rm):
        if not attr.startswith('_'):
            print(f"  {attr}: {type(getattr(rm, attr, None))}")

def test_get_result():
    """测试 get_result 方法"""
    bs = BaziService()
    
    print("\n测试 get_result 方法:")
    print("=" * 60)
    
    result = bs.get_result('Xs1MR9iVx9RNZOk6')
    print(f"get_result('Xs1MR9iVx9RNZOk6') 结果: {type(result)}")
    
    if result:
        print("✅ 找到结果")
        
        # 检查八字数据
        data = result.get('data', {})
        bz = data.get('bz', {})
        if bz:
            birth_info = bz.get('8', '未知生辰')
            print(f"  生辰: {birth_info}")
            
            # 显示八字四柱
            if len(bz) >= 8:
                bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                print(f"  八字: {bazi_str}")
        
        # 检查处理时间
        processed_time = result.get('processed_time', '未知')
        print(f"  处理时间: {processed_time}")
        
        # 检查请求ID
        request_id = result.get('request_id', '未知')
        print(f"  请求ID: {request_id}")
    else:
        print("❌ 未找到结果")

if __name__ == "__main__":
    check_request_manager()
    test_get_result()
