#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找 Xs1MR9iVx9RNZOk6 相关的请求
"""

import sys
sys.path.append('backend')

from bazi_service import BaziService

def find_xs1mr_requests():
    """查找 Xs1MR9iVx9RNZOk6 相关的请求"""
    bs = BaziService()
    
    print(f"内存中结果总数: {len(bs.results)}")
    print("\n查找 Xs1MR9iVx9RNZOk6 相关请求:")
    print("=" * 60)
    
    found = []
    for req_id, result in bs.results.items():
        if 'Xs1MR9iVx9RNZOk6' in req_id:
            found.append((req_id, result))
    
    print(f"找到 {len(found)} 个匹配请求:")
    
    for req_id, result in found:
        print(f"\n请求ID: {req_id}")
        
        # 基本信息
        card_key = result.get('card_key', '未知')
        print(f"  卡密: {card_key}")
        
        # 八字数据
        data = result.get('data', {})
        bz = data.get('bz', {})
        
        if bz:
            birth_info = bz.get('8', '未知生辰')
            print(f"  生辰: {birth_info}")
            
            # 显示八字四柱
            if len(bz) >= 8:
                bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                print(f"  八字: {bazi_str}")
        else:
            print("  八字: 无数据")
        
        # 处理时间
        processed_time = result.get('processed_time', '未知')
        print(f"  处理时间: {processed_time}")
        
        # 成功状态
        success = result.get('success', False)
        print(f"  成功状态: {success}")

if __name__ == "__main__":
    find_xs1mr_requests()
