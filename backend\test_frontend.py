#!/usr/bin/env python3
"""
测试前端文件访问
"""

from app import app

def test_frontend_files():
    with app.test_client() as client:
        print("=== 测试前端文件访问 ===")
        
        # 测试主页
        print("\n1. 测试主页...")
        response = client.get('/')
        print(f'GET / - Status: {response.status_code}')
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            print(f'Content length: {len(content)} characters')
            print(f'Contains HTML: {"<html" in content.lower()}')
        
        # 测试index.html
        print("\n2. 测试index.html...")
        response = client.get('/bazi/index.html')
        print(f'GET /bazi/index.html - Status: {response.status_code}')
        
        # 测试CSS文件
        print("\n3. 测试CSS文件...")
        response = client.get('/static/styles.css')
        print(f'GET /static/styles.css - Status: {response.status_code}')
        if response.status_code == 200:
            content = response.get_data(as_text=True)
            print(f'CSS content length: {len(content)} characters')
        
        # 测试result.html
        print("\n4. 测试result.html...")
        response = client.get('/static/result.html')
        print(f'GET /static/result.html - Status: {response.status_code}')
        
        # 测试JS文件
        print("\n5. 测试JS文件...")
        response = client.get('/static/js/ui_reports_polling_functions.js')
        print(f'GET /static/js/ui_reports_polling_functions.js - Status: {response.status_code}')
        
        return True

if __name__ == '__main__':
    try:
        test_frontend_files()
        print("\n✅ 前端文件访问测试完成")
    except Exception as e:
        print(f"\n❌ 前端文件访问测试失败: {e}")
        import traceback
        traceback.print_exc()
