/**
 * 模态框UI组件
 * 负责处理卡密输入和确认分析等模态框交互
 */

// 卡密输入模态框相关函数
function showCardKeyModal() {
    return new Promise((resolve, reject) => {
        const modal = document.getElementById('cardKeyModal');
        const input = document.getElementById('cardKeyInput');
        const confirmBtn = modal.querySelector('.card-key-modal-btn.primary');
        const statusDiv = document.getElementById('cardKeyStatus');
        
        // 重置状态
        input.value = '';
        confirmBtn.disabled = true;
        statusDiv.style.display = 'none';
        statusDiv.className = 'card-key-status';
        
        // 显示模态框
        modal.classList.add('show');
        setTimeout(() => input.focus(), 300);
        
        // 存储resolve和reject函数
        window.cardKeyModalResolve = resolve;
        window.cardKeyModalReject = reject;
        
        // 输入验证
        input.addEventListener('input', async function() {
            const cardKey = this.value.trim();
            confirmBtn.disabled = cardKey.length < 6;
            
            if (cardKey.length >= 6) {
                statusDiv.style.display = 'block';
                statusDiv.className = 'card-key-status warning';
                statusDiv.textContent = '正在验证卡密...';
                
                try {
                    const isValid = await validateCardKey(cardKey);
                    if (isValid) {
                        statusDiv.className = 'card-key-status success';
                        statusDiv.textContent = '✓ 卡密验证成功';
                        confirmBtn.disabled = false;
                    } else {
                        statusDiv.className = 'card-key-status error';
                        statusDiv.textContent = '✗ 卡密无效，请检查后重试';
                        confirmBtn.disabled = true;
                    }
                } catch (error) {
                    statusDiv.className = 'card-key-status error';
                    statusDiv.textContent = '✗ 验证失败，请稍后重试';
                    confirmBtn.disabled = true;
                }
            } else {
                statusDiv.style.display = 'none';
            }
        });
        
        // 回车键确认
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !confirmBtn.disabled) {
                confirmCardKeyInput();
            }
        });
    });
}

function confirmCardKeyInput() {
    const input = document.getElementById('cardKeyInput');
    const cardKey = input.value.trim();
    
    if (cardKey && window.cardKeyModalResolve) {
        hideCardKeyModal();
        window.cardKeyModalResolve(cardKey);
    }
}

function cancelCardKeyInput() {
    hideCardKeyModal();
    if (window.cardKeyModalReject) {
        window.cardKeyModalReject(new Error('用户取消输入'));
    }
}

function hideCardKeyModal() {
    const modal = document.getElementById('cardKeyModal');
    modal.classList.remove('show');
}

// 继续分析确认模态框
function showContinueAnalysisModal(analysis) {
    return new Promise((resolve) => {
        const startTime = new Date(analysis.startTime).toLocaleString();
        const elapsedMinutes = Math.floor((Date.now() - analysis.startTime) / (1000 * 60));
        
        const modalHtml = `
            <div id="continueAnalysisModal" class="card-key-modal show">
                <div class="card-key-modal-content">
                    <div class="card-key-modal-header">
                        <h3 class="card-key-modal-title">检测到未完成的分析</h3>
                        <p class="card-key-modal-subtitle">发现一个进行中的分析任务</p>
                    </div>
                    
                    <div class="analysis-info" style="padding: 15px; background: var(--card-bg); border-radius: 8px; margin: 15px 0;">
                        <p><strong>开始时间：</strong>${startTime}</p>
                        <p><strong>已用时：</strong>${elapsedMinutes}分钟</p>
                        <p><strong>分析类型：</strong>${analysis.type === 'request' ? '请求分析' : '卡密分析'}</p>
                    </div>
                    
                    <div class="card-key-modal-actions">
                        <button class="card-key-modal-btn primary" onclick="confirmContinueAnalysis(true)">继续分析</button>
                        <button class="card-key-modal-btn secondary" onclick="confirmContinueAnalysis(false)">放弃分析</button>
                    </div>
                </div>
            </div>
        `;
        
        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 存储resolve函数
        window.continueAnalysisResolve = resolve;
    });
}

function confirmContinueAnalysis(shouldContinue) {
    const modal = document.getElementById('continueAnalysisModal');
    if (modal) {
        modal.remove();
    }
    
    if (window.continueAnalysisResolve) {
        window.continueAnalysisResolve(shouldContinue);
        window.continueAnalysisResolve = null;
    }
} 