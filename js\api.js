// API相关函数文件

/**
 * 判断错误是否应该重试
 * @param {string} errorMsg - 错误消息
 * @returns {boolean} 是否应该重试
 */
function shouldRetryError(errorMsg) {
    const retryableErrors = [
        'Internal Server Error',
        '服务器内部错误',
        '网络请求失败',
        '请求超时',
        '服务器暂时不可用',
        'timeout',
        'network error',
        '500'
    ];

    return retryableErrors.some(error =>
        errorMsg.toLowerCase().includes(error.toLowerCase())
    );
}

/**
 * 获取重试延迟时间（指数退避）
 * @param {number} retryCount - 当前重试次数
 * @returns {number} 延迟时间（秒）
 */
function getRetryDelay(retryCount) {
    // 指数退避：2, 4, 8 秒
    return Math.min(2 ** (retryCount + 1), 8);
}

/**
 * 发送八字分析请求（带自动重试机制）
 * @param {Object} formData - 表单数据
 * @param {Function} onSuccess - 成功回调
 * @param {Function} onError - 错误回调
 * @param {number} retryCount - 当前重试次数
 * @param {number} maxRetries - 最大重试次数
 */
function sendBaziRequest(formData, onSuccess, onError, retryCount = 0, maxRetries = 3) {
    const xhr = new XMLHttpRequest();
    const apiBaseUrl = getApiBaseUrl();

    console.log(`🚀 发送八字分析请求 (尝试 ${retryCount + 1}/${maxRetries + 1})`);

    xhr.open('POST', `${apiBaseUrl}/webhook/bazi-analysis`, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('请求成功响应:', response);
                    
                    if (response.success) {
                        // 保存正在进行的分析信息到本地存储
                        const ongoingAnalysis = {
                            cardKey: formData.cardKey,
                            requestId: response.requestId,
                            startTime: new Date().toISOString(),
                            formData: formData
                        };
                        localStorage.setItem('ongoingAnalysis', JSON.stringify(ongoingAnalysis));
                        
                        if (onSuccess) {
                            onSuccess(response);
                        }
                    } else {
                        const errorMsg = response.error || '请求失败，请重试';
                        console.error('请求失败:', errorMsg);

                        // 检查是否需要重试
                        if (shouldRetryError(errorMsg) && retryCount < maxRetries) {
                            console.log(`⚠️ 请求失败，${getRetryDelay(retryCount)}秒后重试: ${errorMsg}`);
                            setTimeout(() => {
                                sendBaziRequest(formData, onSuccess, onError, retryCount + 1, maxRetries);
                            }, getRetryDelay(retryCount) * 1000);
                        } else {
                            if (onError) {
                                onError(retryCount >= maxRetries ? `${errorMsg} (已重试${maxRetries}次)` : errorMsg);
                            }
                        }
                    }
                } catch (e) {
                    console.error('解析响应失败:', e);
                    if (onError) {
                        onError('服务器响应格式错误');
                    }
                }
            } else if (xhr.status === 401) {
                console.error('卡密无效:', xhr.responseText);
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    const errorMsg = `🔑 ${errorResponse.error || '卡密验证失败，请检查卡密是否正确'}`;
                    if (onError) {
                        onError(errorMsg);
                    }
                } catch (e) {
                    if (onError) {
                        onError('🔑 卡密验证失败，请检查卡密是否正确');
                    }
                }
            } else {
                console.error('请求失败:', xhr.status, xhr.responseText);
                let errorMsg = '网络请求失败，请检查网络连接';
                let shouldRetry = false;

                if (xhr.status === 0) {
                    errorMsg = '无法连接到服务器，请检查网络连接';
                    shouldRetry = true;
                } else if (xhr.status >= 500) {
                    errorMsg = '服务器内部错误，请稍后重试';
                    shouldRetry = true;
                } else if (xhr.status === 429) {
                    errorMsg = '请求过于频繁，请稍后重试';
                    shouldRetry = true;
                } else if (xhr.status === 502 || xhr.status === 503 || xhr.status === 504) {
                    errorMsg = '服务器暂时不可用，请稍后重试';
                    shouldRetry = true;
                }

                // 自动重试逻辑
                if (shouldRetry && retryCount < maxRetries) {
                    const delay = getRetryDelay(retryCount);
                    console.log(`🔄 HTTP ${xhr.status} 错误，${delay}秒后重试 (${retryCount + 1}/${maxRetries})`);
                    setTimeout(() => {
                        sendBaziRequest(formData, onSuccess, onError, retryCount + 1, maxRetries);
                    }, delay * 1000);
                } else {
                    if (onError) {
                        onError(retryCount >= maxRetries ? `${errorMsg} (已重试${maxRetries}次)` : errorMsg);
                    }
                }
            }
        }
    };
    
    xhr.onerror = function() {
        console.error('网络错误');

        // 网络错误自动重试
        if (retryCount < maxRetries) {
            const delay = getRetryDelay(retryCount);
            console.log(`🌐 网络错误，${delay}秒后重试 (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => {
                sendBaziRequest(formData, onSuccess, onError, retryCount + 1, maxRetries);
            }, delay * 1000);
        } else {
            if (onError) {
                onError(`网络连接失败，请检查网络设置 (已重试${maxRetries}次)`);
            }
        }
    };

    xhr.ontimeout = function() {
        console.error('请求超时');

        // 超时错误自动重试
        if (retryCount < maxRetries) {
            const delay = getRetryDelay(retryCount);
            console.log(`⏰ 请求超时，${delay}秒后重试 (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => {
                sendBaziRequest(formData, onSuccess, onError, retryCount + 1, maxRetries);
            }, delay * 1000);
        } else {
            if (onError) {
                onError(`请求超时，请重试 (已重试${maxRetries}次)`);
            }
        }
    };
    
    // 设置超时时间（30秒）
    xhr.timeout = 30000;
    
    console.log('发送请求:', formData);
    xhr.send(JSON.stringify(formData));
}

/**
 * 检查完成状态
 * @param {string} identifier - 标识符（卡密或请求ID）
 * @param {Function} callback - 回调函数
 * @param {boolean} isRequestId - 是否为请求ID
 */
function checkCompletionStatus(identifier, callback, isRequestId = false) {
    const xhr = new XMLHttpRequest();
    const apiBaseUrl = getApiBaseUrl();
    
    // 构建查询参数，优先使用requestId
    const queryParam = isRequestId ? `requestId=${encodeURIComponent(identifier)}` : `cardKey=${encodeURIComponent(identifier)}`;
    xhr.open('GET', `${apiBaseUrl}/webhook/check-status?${queryParam}`, true);
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('状态检查响应:', response);
                    const isCompleted = response.completed === true || response.status === 'completed';
                    console.log('是否完成:', isCompleted);
                    callback(isCompleted, response.result, response);
                } catch (e) {
                    console.error('解析响应失败:', e);
                    callback(false, null, null);
                }
            } else if (xhr.status === 401) {
                console.error('卡密验证失败:', xhr.responseText);
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    // 检查是否是请求已完成但卡密验证失败的情况
                    // 这种情况下应该停止轮询而不是报错
                    console.log('停止轮询，可能请求已完成或卡密状态异常');
                    callback(false, null, { error: errorResponse.error || '请求状态检查失败，请刷新页面查看结果', status: 'auth_failed' });
                } catch (e) {
                    console.log('停止轮询，卡密验证异常');
                    callback(false, null, { error: '请求状态检查失败，请刷新页面查看结果', status: 'auth_failed' });
                }
            } else {
                console.error('检查状态失败:', xhr.status);
                callback(false, null, null);
            }
        }
    };
    
    xhr.send();
}

// 导出函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        sendBaziRequest,
        checkCompletionStatus
    };
}