/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 32px;
}

.button-group .btn {
    min-width: 140px;
    position: relative;
    overflow: hidden;
}

.button-group .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.button-group .btn:hover::before {
    left: 100%;
}

/* 提交按钮样式 */
.form-actions {
    text-align: center;
    margin-top: 32px;
}

.submit-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 50px;
    background: var(--hero-gradient);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.submit-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.submit-btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon {
    transform: rotate(360deg);
}

/* 购买卡密按钮样式 */
.card-purchase-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color-light);
}

.purchase-hint {
    margin: 0 0 12px 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 400;
}

.card-purchase-button {
    display: inline-block;
    padding: 12px 24px;
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.card-purchase-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.card-purchase-button:hover {
    background: rgba(var(--primary-color-rgb), 0.05);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
    border-color: #764ba2;
}

.card-purchase-button:hover::before {
    left: 100%;
}

.card-purchase-button span {
    margin-right: 6px;
    transition: transform 0.3s ease;
}

.card-purchase-button:hover span {
    transform: scale(1.1) rotate(8deg);
}

/* 加载动画 */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 