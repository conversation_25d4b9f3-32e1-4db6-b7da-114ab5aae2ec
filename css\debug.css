/* 调试页面样式 */
.debug-page {
    font-family: var(--font-family-primary);
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    background: var(--bg-primary);
    min-height: 100vh;
}

.debug-panel {
    background: var(--bg-card);
    padding: 28px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: 24px;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.debug-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.debug-panel h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.debug-status-item {
    padding: 16px 20px;
    margin: 12px 0;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    background: var(--bg-secondary);
    transition: var(--transition);
    position: relative;
}

.debug-status-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.debug-status-item.success {
    border-left-color: var(--success-color);
    background: rgba(72, 187, 120, 0.1);
}

.debug-status-item.error {
    border-left-color: var(--danger-color);
    background: rgba(245, 101, 101, 0.1);
}

.debug-status-item.warning {
    border-left-color: var(--warning-color);
    background: rgba(251, 191, 36, 0.1);
}

.debug-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    margin: 8px;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.debug-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.debug-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.debug-btn:hover::before {
    left: 100%;
}

.debug-link-button {
    display: inline-block;
    background: var(--success-gradient);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    margin: 8px;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.debug-link-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.debug-link-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.debug-link-button:hover::before {
    left: 100%;
}

.debug-code {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    font-family: var(--font-family-mono);
    font-size: 0.9rem;
    line-height: 1.5;
    overflow-x: auto;
    margin: 16px 0;
}

.debug-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .debug-page {
        padding: 16px;
    }
    
    .debug-panel {
        padding: 20px;
    }
    
    .debug-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}