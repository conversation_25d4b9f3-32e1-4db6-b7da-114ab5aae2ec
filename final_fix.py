import json
import re

# 读取原文件
with open(r'c:\Users\<USER>\Desktop\project\articles\articles.json','r',encoding='utf-8') as f:
    content = f.read()

print('开始修复JSON格式问题...')

# 修复1: 将数字id改为字符串id
content = re.sub(r'"id":\s*(\d+),', r'"id": "\1",', content)

# 修复2: 转义content字段中的双引号
# 使用更安全的方法
def fix_content_quotes(match):
    full_match = match.group(0)
    content_value = match.group(1)
    # 转义内部的双引号
    fixed_content = content_value.replace('"', '\\"')
    return '"content": "' + fixed_content + '"'

# 匹配content字段的内容
pattern = r'"content":\s*"(.*?)"(?=\s*[,}])'
content = re.sub(pattern, fix_content_quotes, content, flags=re.DOTALL)

print('验证修复后的JSON...')
try:
    data = json.loads(content)
    print('JSON格式修复成功！包含', len(data), '个文章')
    
    # 保存修复后的文件
    with open(r'c:\Users\<USER>\Desktop\project\articles\articles_fixed.json','w',encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print('已保存修复后的文件为 articles_fixed.json')
    
    # 替换原文件
    with open(r'c:\Users\<USER>\Desktop\project\articles\articles.json','w',encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print('已更新原文件 articles.json')
    
except json.JSONDecodeError as e:
    print(f'仍有JSON错误: {e}')
    
    # 显示错误位置
    lines = content.split('\n')
    char_count = 0
    for i, line in enumerate(lines):
        if char_count + len(line) + 1 >= e.pos:
            pos_in_line = e.pos - char_count
            print(f'错误在第{i+1}行，第{pos_in_line}个字符')
            print(f'错误行: {line}')
            if pos_in_line > 5:
                print(f'错误附近: "{line[max(0,pos_in_line-10):pos_in_line+10]}"')
            break
        char_count += len(line) + 1
    
    # 保存部分修复的内容用于调试
    with open(r'c:\Users\<USER>\Desktop\project\articles\debug_content.txt','w',encoding='utf-8') as f:
        f.write(content)
    print('已保存调试内容到 debug_content.txt')