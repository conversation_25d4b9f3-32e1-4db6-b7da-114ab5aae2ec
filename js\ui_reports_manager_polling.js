/**
 * 报告管理器 - 轮询与状态更新
 * 扩展HistoryReportsManager类，处理分析轮询和状态更新
 */

// 扩展HistoryReportsManager类的轮询功能
HistoryReportsManager.prototype.startPollingAnalysis = function(analysis) {
    // 存储分析状态，方便调试
    localStorage.setItem('ongoingAnalysis', JSON.stringify(analysis));
    console.log('开始轮询分析状态，分析信息:', analysis);
    
    // 更新已用时间
    this.updateElapsedTime(analysis.startTime);
    this.timeInterval = setInterval(() => {
        this.updateElapsedTime(analysis.startTime);
    }, 1000);
    
    // 轮询分析状态
    this.pollingInterval = setInterval(async () => {
        try {
            console.log('正在轮询分析状态...');
            console.log('使用cardKey:', analysis.cardKey, '使用requestId:', analysis.requestId);
            
            // 构建查询参数，优先使用原始的cardKey，不使用带有时间戳的requestId
            let params = new URLSearchParams();
            if (analysis.cardKey) {
                params.append('cardKey', analysis.cardKey);
            }
            if (analysis.requestId) {
                params.append('requestId', analysis.requestId);
            }
            
            // 查询webhook状态，使用正确的参数
            const queryString = params.toString();
            console.log('查询参数:', queryString);
            
            const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
                ? 'http://localhost:5000' 
                : '';
                
            console.log('使用API基础URL:', apiBaseUrl);
            
            const webhookResponse = await fetch(`${apiBaseUrl}/webhook/check-status?${queryString}`);
            
            // 添加状态码日志
            console.log('状态检查响应状态码:', webhookResponse.status);
            
            if (webhookResponse.ok) {
                const webhookStatus = await webhookResponse.json();
                console.log('Webhook状态:', webhookStatus);
                
                // 更新调试信息（安全检查）
                if (typeof this.updateDebugInfo === 'function') {
                    this.updateDebugInfo({
                        time: new Date().toLocaleTimeString(),
                        type: 'webhook',
                        status: webhookStatus
                    });
                }
                
                // 更新进度显示
                this.updateAnalysisProgress(webhookStatus);
                
                // 如果完成，处理完成逻辑
                if (webhookStatus.completed === true) {
                    console.log('分析已完成，停止轮询');
                    
                    // 分析结果保存由 api_analysis_service.js 处理
                    
                    // 记录已完成的分析，防止重复启动
                    this.recordCompletedAnalysis(analysis);
                    
                    // 停止轮询，并清理资源
                    this.completeAnalysis();
                    
                    // 显示完成消息，然后自动刷新页面
                    const runningStatus = document.getElementById('runningStatus');
                    const progressText = document.getElementById('progressText');
                    const progressFill = document.getElementById('progressFill');
                    
                    if (runningStatus && progressText && progressFill) {
                        runningStatus.textContent = '分析完成!';
                        progressText.textContent = '分析已完成，页面将自动刷新...';
                        progressFill.style.backgroundColor = 'var(--success-color)';
                        progressFill.style.width = '100%';
                    }
                    
                    // 设置一个标志，表示需要刷新报告列表
                    localStorage.setItem('needRefreshReports', 'true');
                    
                    // 1秒后自动刷新页面以显示最新结果
                    setTimeout(async () => {
                        console.log('分析完成，自动刷新报告列表...');
                        
                        // 清除缓存，确保获取最新数据
                        const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
                        cacheKeys.forEach(key => sessionStorage.removeItem(key));
                        
                        try {
                            // 直接获取最新报告数据
                            const reports = await this.fetchHistoryReports();
                            console.log('获取到最新报告数据:', reports);
                            
                            // 更新显示
                            displayReportsOnly(reports);
                        } catch (refreshError) {
                            console.error('自动刷新报告失败:', refreshError);
                            
                            // 如果自动刷新失败，提示用户手动刷新
                            if (runningStatus && progressText) {
                                runningStatus.textContent = '分析完成!';
                                progressText.textContent = '获取最新报告失败，请手动刷新页面';
                            }
                            
                            // 添加刷新按钮
                            const actionsDiv = document.querySelector('.analysis-actions');
                            if (actionsDiv) {
                                const refreshButton = document.createElement('button');
                                refreshButton.className = 'action-btn primary';
                                refreshButton.textContent = '刷新报告列表';
                                refreshButton.onclick = refreshReports;
                                actionsDiv.prepend(refreshButton);
                            }
                        }
                    }, 1000);
                    
                    return;
                }
            } else if (webhookResponse.status === 401) {
                // 卡密无效处理
                const errorData = await webhookResponse.json();
                console.error('卡密无效:', errorData);
                
                // 更新调试信息（安全检查）
                if (typeof this.updateDebugInfo === 'function') {
                    this.updateDebugInfo({
                        time: new Date().toLocaleTimeString(),
                        type: 'error',
                        status: errorData
                    });
                }
                
                // 更新状态显示
                const runningStatus = document.getElementById('runningStatus');
                const progressText = document.getElementById('progressText');
                const progressFill = document.getElementById('progressFill');
                
                if (runningStatus && progressText && progressFill) {
                    runningStatus.textContent = '验证失败';
                    progressText.textContent = errorData.error || '卡密不存在或已失效';
                    progressFill.style.backgroundColor = 'var(--error-color)';
                    progressFill.style.width = '100%';
                }
                
                // 停止轮询
                this.completeAnalysis();
                return;
            } else {
                // 其他错误处理
                console.error('状态检查请求失败:', webhookResponse.status);
                
                // 更新调试信息（安全检查）
                if (typeof this.updateDebugInfo === 'function') {
                    this.updateDebugInfo({
                        time: new Date().toLocaleTimeString(),
                        type: 'error',
                        message: `状态检查请求失败: ${webhookResponse.status}`,
                        statusCode: webhookResponse.status
                    });
                }
            }
            
            // 检查是否超过最长等待时间（30分钟）
            if (Date.now() - analysis.startTime > 30 * 60 * 1000) {
                console.log('分析超过30分钟，自动停止轮询');
                this.completeAnalysis();
            }
        } catch (error) {
            console.error('轮询分析状态失败:', error);

            // 更新调试信息（安全检查）
            if (typeof this.updateDebugInfo === 'function') {
                this.updateDebugInfo({
                    time: new Date().toLocaleTimeString(),
                    type: 'error',
                    message: '轮询状态异常',
                    error: error.toString()
                });
            } else {
                console.warn('updateDebugInfo 函数不存在，跳过调试信息更新');
            }
        }
    }, 3000);
};

// 更新已用时间
HistoryReportsManager.prototype.updateElapsedTime = function(startTime) {
    const elapsedTimeElement = document.getElementById('elapsedTime');
    if (!elapsedTimeElement) return;
    
    const elapsedSeconds = Math.floor((Date.now() - startTime) / 1000);
    let timeText = '';
    
    if (elapsedSeconds < 60) {
        timeText = `${elapsedSeconds}秒`;
    } else if (elapsedSeconds < 3600) {
        const minutes = Math.floor(elapsedSeconds / 60);
        const seconds = elapsedSeconds % 60;
        timeText = `${minutes}分${seconds}秒`;
    } else {
        const hours = Math.floor(elapsedSeconds / 3600);
        const minutes = Math.floor((elapsedSeconds % 3600) / 60);
        timeText = `${hours}小时${minutes}分`;
    }
    
    elapsedTimeElement.textContent = `已用时：${timeText}`;
};

// 注意：updateAnalysisProgress 和 updateDebugInfo 函数已移至 ui_reports_polling_functions.js
// 避免重复定义，这里不再重复声明这些函数

// 完成分析，集中处理完成逻辑
HistoryReportsManager.prototype.completeAnalysis = function() {
    // 停止所有定时器
    clearInterval(this.pollingInterval);
    clearInterval(this.timeInterval);
    
    // 清除本地存储的正在进行的分析状态
    localStorage.removeItem('ongoingAnalysis');
    
    // 清除URL参数，防止刷新页面时重新启动分析
    this.clearAnalysisParams();
};

// 清除URL中的分析参数，防止重复分析
HistoryReportsManager.prototype.clearAnalysisParams = function() {
    // 如果URL中有requestId或cardKey参数，移除它们
    if (window.location.search.includes('requestId') || 
        window.location.search.includes('cardKey')) {
        
        // 获取当前URL，不包含参数
        const cleanUrl = window.location.pathname;
        
        // 使用history API更新URL，不刷新页面
        window.history.replaceState({}, document.title, cleanUrl);
        
        console.log('已清除URL分析参数，防止重复分析');
    }
};

// 记录已完成的分析，防止重复启动
HistoryReportsManager.prototype.recordCompletedAnalysis = function(analysis) {
    const completed = {
        ...analysis,
        completedTime: new Date().toLocaleString(),
        timestamp: Date.now()
    };
    localStorage.setItem('completedAnalysis', JSON.stringify(completed));
    console.log('已记录完成的分析，防止重复启动');
};