#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from card_manager import CardManager
from config_manager import get_config

print("=== 调试API流程 ===")

try:
    # 1. 初始化CardManager
    print("1. 初始化CardManager...")
    card_manager = CardManager()
    print(f"   CardManager加载了 {len(card_manager.cards_data)} 个卡密")
    
    # 2. 获取配置
    print("\n2. 获取配置...")
    config = get_config()
    
    # 3. 从卡密文件中获取最新卡密列表
    print("\n3. 从卡密文件中获取最新卡密列表...")
    cards_in_file = list(card_manager.cards_data.keys())
    print(f"   卡密文件中有 {len(cards_in_file)} 个卡密")
    print(f"   前10个: {cards_in_file[:10]}")
    
    # 4. 获取配置中的卡密列表
    print("\n4. 获取配置中的卡密列表...")
    cards_in_config = config.get_valid_cards()
    print(f"   配置中有 {len(cards_in_config)} 个卡密")
    print(f"   前10个: {cards_in_config[:10] if cards_in_config else []}")
    
    # 5. 检查是否需要同步
    print("\n5. 检查是否需要同步...")
    if set(cards_in_file) != set(cards_in_config):
        print(f"   需要同步: 卡密文件中有 {len(cards_in_file)} 个卡密，配置中有 {len(cards_in_config)} 个卡密")
        print("   执行同步...")
        config.set('card_validation.valid_cards', cards_in_file)
        config.save()
        print("   同步完成")
    else:
        print("   不需要同步")
    
    # 6. 获取最终返回的数据
    print("\n6. 获取最终返回的数据...")
    cards_data = card_manager.cards_data
    print(f"   最终返回 {len(cards_data)} 个卡密")
    print(f"   卡密列表: {list(cards_data.keys())[:10]}")
    
    # 7. 模拟JSON序列化
    print("\n7. 模拟JSON序列化...")
    json_str = json.dumps(cards_data, ensure_ascii=False, indent=2)
    print(f"   JSON字符串长度: {len(json_str)}")
    print(f"   JSON包含的卡密数量: {len(json.loads(json_str))}")
    
except Exception as e:
    print(f"调试过程中出错: {e}")
    import traceback
    traceback.print_exc()