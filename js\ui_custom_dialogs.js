/**
 * 自定义美化弹窗组件
 * 替代原生的alert、confirm、prompt弹窗
 */

// 通用弹窗样式
const DIALOG_STYLES = {
    overlay: {
        position: 'fixed',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        background: 'rgba(0, 0, 0, 0.7)',
        backdropFilter: 'blur(8px)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: '10000',
        opacity: '0',
        visibility: 'hidden',
        transition: 'all 0.3s ease'
    },
    content: {
        background: 'var(--bg-card, #ffffff)',
        borderRadius: 'var(--radius-large, 16px)',
        padding: '32px',
        maxWidth: '480px',
        width: '90%',
        boxShadow: 'var(--shadow-lg, 0 20px 25px -5px rgba(0, 0, 0, 0.1))',
        border: '1px solid var(--border-color-light, #e5e7eb)',
        transform: 'translateY(-50px) scale(0.9)',
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'hidden'
    }
};

// 创建弹窗基础结构
function createDialogBase(type, title, message, options = {}) {
    const dialogId = `custom-dialog-${Date.now()}`;
    
    const overlay = document.createElement('div');
    overlay.id = dialogId;
    overlay.className = 'custom-dialog-overlay';
    
    // 应用样式
    Object.assign(overlay.style, DIALOG_STYLES.overlay);
    
    const content = document.createElement('div');
    content.className = 'custom-dialog-content';
    Object.assign(content.style, DIALOG_STYLES.content);
    
    // 顶部装饰条
    const topBar = document.createElement('div');
    topBar.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
    `;
    content.appendChild(topBar);
    
    // 图标
    const icon = document.createElement('div');
    icon.style.cssText = `
        text-align: center;
        margin-bottom: 16px;
        font-size: 48px;
        line-height: 1;
    `;
    
    const iconMap = {
        alert: '⚠️',
        confirm: '❓',
        prompt: '✏️',
        success: '✅',
        error: '❌',
        info: 'ℹ️'
    };
    
    icon.textContent = iconMap[type] || iconMap.info;
    content.appendChild(icon);
    
    // 标题
    if (title) {
        const titleEl = document.createElement('h3');
        titleEl.style.cssText = `
            margin: 0 0 12px 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary, #1f2937);
            text-align: center;
        `;
        titleEl.textContent = title;
        content.appendChild(titleEl);
    }
    
    // 消息内容
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
        margin-bottom: 24px;
        color: var(--text-secondary, #6b7280);
        line-height: 1.6;
        text-align: center;
        white-space: pre-wrap;
    `;
    messageEl.textContent = message;
    content.appendChild(messageEl);
    
    overlay.appendChild(content);
    
    return { overlay, content, dialogId };
}

// 显示弹窗动画
function showDialog(overlay) {
    document.body.appendChild(overlay);
    
    // 强制重绘
    overlay.offsetHeight;
    
    // 显示动画
    overlay.style.opacity = '1';
    overlay.style.visibility = 'visible';
    
    const content = overlay.querySelector('.custom-dialog-content');
    content.style.transform = 'translateY(0) scale(1)';
}

// 隐藏弹窗动画
function hideDialog(overlay) {
    return new Promise((resolve) => {
        overlay.style.opacity = '0';
        overlay.style.visibility = 'hidden';
        
        const content = overlay.querySelector('.custom-dialog-content');
        content.style.transform = 'translateY(-50px) scale(0.9)';
        
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
            resolve();
        }, 300);
    });
}

// 创建按钮
function createButton(text, type = 'secondary', onClick) {
    const button = document.createElement('button');
    button.textContent = text;
    button.className = `custom-dialog-btn ${type}`;
    
    const baseStyle = `
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 80px;
        margin: 0 8px;
    `;
    
    if (type === 'primary') {
        button.style.cssText = baseStyle + `
            background: var(--primary-gradient, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        `;
        
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 6px 16px rgba(102, 126, 234, 0.5)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.4)';
        });
    } else {
        button.style.cssText = baseStyle + `
            background: var(--bg-secondary, #f3f4f6);
            color: var(--text-secondary, #6b7280);
            border: 1px solid var(--border-color, #d1d5db);
        `;
        
        button.addEventListener('mouseenter', () => {
            button.style.background = 'var(--bg-hover, #e5e7eb)';
            button.style.transform = 'translateY(-1px)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.background = 'var(--bg-secondary, #f3f4f6)';
            button.style.transform = 'translateY(0)';
        });
    }
    
    if (onClick) {
        button.addEventListener('click', onClick);
    }
    
    return button;
}

// 美化的Alert弹窗
function customAlert(message, title = '提示', type = 'info') {
    return new Promise((resolve) => {
        const { overlay, content } = createDialogBase(type, title, message);
        
        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-top: 24px;
        `;
        
        const okButton = createButton('确定', 'primary', () => {
            hideDialog(overlay).then(resolve);
        });
        
        buttonContainer.appendChild(okButton);
        content.appendChild(buttonContainer);
        
        showDialog(overlay);
        
        // ESC键关闭
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleEsc);
                hideDialog(overlay).then(resolve);
            }
        };
        document.addEventListener('keydown', handleEsc);
        
        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                hideDialog(overlay).then(resolve);
            }
        });
        
        // 聚焦确定按钮
        setTimeout(() => okButton.focus(), 300);
    });
}

// 美化的Confirm弹窗
function customConfirm(message, title = '确认', options = {}) {
    return new Promise((resolve) => {
        const { overlay, content } = createDialogBase('confirm', title, message);
        
        const confirmText = options.confirmText || '确定';
        const cancelText = options.cancelText || '取消';
        
        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-top: 24px;
        `;
        
        const cancelButton = createButton(cancelText, 'secondary', () => {
            hideDialog(overlay).then(() => resolve(false));
        });
        
        const confirmButton = createButton(confirmText, 'primary', () => {
            hideDialog(overlay).then(() => resolve(true));
        });
        
        buttonContainer.appendChild(cancelButton);
        buttonContainer.appendChild(confirmButton);
        content.appendChild(buttonContainer);
        
        showDialog(overlay);
        
        // ESC键取消
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleEsc);
                hideDialog(overlay).then(() => resolve(false));
            }
        };
        document.addEventListener('keydown', handleEsc);
        
        // 点击遮罩取消
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                hideDialog(overlay).then(() => resolve(false));
            }
        });
        
        // 聚焦确定按钮
        setTimeout(() => confirmButton.focus(), 300);
    });
}

// 美化的Prompt弹窗
function customPrompt(message, title = '输入', defaultValue = '', options = {}) {
    return new Promise((resolve) => {
        const { overlay, content } = createDialogBase('prompt', title, message);
        
        const placeholder = options.placeholder || '请输入...';
        const confirmText = options.confirmText || '确定';
        const cancelText = options.cancelText || '取消';
        const inputType = options.inputType || 'text';
        
        // 输入框
        const inputContainer = document.createElement('div');
        inputContainer.style.cssText = `
            margin: 16px 0 24px 0;
        `;
        
        const input = document.createElement('input');
        input.type = inputType;
        input.value = defaultValue;
        input.placeholder = placeholder;
        input.style.cssText = `
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color, #d1d5db);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: var(--bg-input, #ffffff);
            color: var(--text-primary, #1f2937);
            box-sizing: border-box;
        `;
        
        // 输入框焦点效果
        input.addEventListener('focus', () => {
            input.style.borderColor = 'var(--primary-color, #667eea)';
            input.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
        });
        
        input.addEventListener('blur', () => {
            input.style.borderColor = 'var(--border-color, #d1d5db)';
            input.style.boxShadow = 'none';
        });
        
        inputContainer.appendChild(input);
        content.appendChild(inputContainer);
        
        // 按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            justify-content: center;
            gap: 12px;
        `;
        
        const cancelButton = createButton(cancelText, 'secondary', () => {
            hideDialog(overlay).then(() => resolve(null));
        });
        
        const confirmButton = createButton(confirmText, 'primary', () => {
            const value = input.value.trim();
            hideDialog(overlay).then(() => resolve(value));
        });
        
        buttonContainer.appendChild(cancelButton);
        buttonContainer.appendChild(confirmButton);
        content.appendChild(buttonContainer);
        
        showDialog(overlay);
        
        // 回车确认
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const value = input.value.trim();
                hideDialog(overlay).then(() => resolve(value));
            }
        });
        
        // ESC键取消
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', handleEsc);
                hideDialog(overlay).then(() => resolve(null));
            }
        };
        document.addEventListener('keydown', handleEsc);
        
        // 点击遮罩取消
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                hideDialog(overlay).then(() => resolve(null));
            }
        });
        
        // 聚焦输入框
        setTimeout(() => {
            input.focus();
            input.select();
        }, 300);
    });
}

// 成功提示
function showSuccess(message, title = '成功') {
    return customAlert(message, title, 'success');
}

// 错误提示
function showError(message, title = '错误') {
    return customAlert(message, title, 'error');
}

// 警告提示
function showWarning(message, title = '警告') {
    return customAlert(message, title, 'alert');
}

// 信息提示
function showInfo(message, title = '信息') {
    return customAlert(message, title, 'info');
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.customAlert = customAlert;
    window.customConfirm = customConfirm;
    window.customPrompt = customPrompt;
    window.showSuccess = showSuccess;
    window.showError = showError;
    window.showWarning = showWarning;
    window.showInfo = showInfo;
    
    // 提供简化的全局函数
    window.alert = customAlert;
    window.confirm = customConfirm;
    window.prompt = customPrompt;
}