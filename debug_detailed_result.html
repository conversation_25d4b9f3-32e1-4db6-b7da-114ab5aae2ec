<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试详细结果页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        #console-output {
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            padding: 10px;
            border-radius: 3px;
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 详细结果页面调试工具</h1>
        
        <div class="section">
            <h3>🎯 快速测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <button onclick="testAPI()">测试API</button>
            <button onclick="testProgressiveLoader()">测试渐进式加载器</button>
            <button onclick="testFunctions()">测试函数</button>
            <button onclick="clearConsole()">清空控制台</button>
            <div id="quickStatus"></div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>📡 API 响应</h3>
                <pre id="apiResponse">等待测试...</pre>
            </div>
            
            <div class="section">
                <h3>🔧 函数状态</h3>
                <pre id="functionStatus">等待测试...</pre>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 控制台输出</h3>
            <div id="console-output">等待日志...</div>
        </div>
        
        <div class="section">
            <h3>🧪 测试结果</h3>
            <pre id="testResults">等待测试...</pre>
        </div>
    </div>

    <!-- 加载所有必要的JavaScript文件 -->
    <script src="js/ui_detailed_helpers.js"></script>
    <script src="js/ui_detailed_states.js"></script>
    <script src="js/detailed_result/detailed_result_bazi_info.js"></script>
    <script src="js/detailed_result/detailed_result_utils.js"></script>
    <script src="js/detailed_result/progressive_loader.js"></script>
    <script src="js/detailed_result/image_exporter.js"></script>

    <script>
        // 拦截控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toISOString().substr(11, 12);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#f00' : type === 'warn' ? '#ff0' : '#0f0';
            div.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('warn', ...args);
        };
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '控制台已清空...';
        }
        
        async function testAPI() {
            console.log('🧪 [TEST] 开始测试API...');
            try {
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const data = await response.json();
                
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                console.log('✅ [TEST] API测试成功');
                return data;
            } catch (error) {
                console.error('❌ [TEST] API测试失败:', error);
                document.getElementById('apiResponse').textContent = `错误: ${error.message}`;
                return null;
            }
        }
        
        function testFunctions() {
            console.log('🧪 [TEST] 开始测试函数...');
            const results = [];
            
            const functions = [
                'generateBaziInfoSection',
                'initializeToggleFeatures',
                'toggleSection',
                'downloadReport'
            ];
            
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                results.push(`${exists ? '✅' : '❌'} ${funcName}: ${exists ? '存在' : '不存在'}`);
                console.log(`🔍 [TEST] ${funcName}: ${exists ? '存在' : '不存在'}`);
            });
            
            // 检查类和实例
            const progressiveLoaderExists = typeof window.ProgressiveLoader === 'function';
            const progressiveLoaderInstanceExists = !!window.progressiveLoader;
            
            results.push(`${progressiveLoaderExists ? '✅' : '❌'} ProgressiveLoader类: ${progressiveLoaderExists ? '存在' : '不存在'}`);
            results.push(`${progressiveLoaderInstanceExists ? '✅' : '❌'} progressiveLoader实例: ${progressiveLoaderInstanceExists ? '存在' : '不存在'}`);
            
            console.log(`🔍 [TEST] ProgressiveLoader类: ${progressiveLoaderExists ? '存在' : '不存在'}`);
            console.log(`🔍 [TEST] progressiveLoader实例: ${progressiveLoaderInstanceExists ? '存在' : '不存在'}`);
            
            document.getElementById('functionStatus').textContent = results.join('\n');
        }
        
        async function testProgressiveLoader() {
            console.log('🧪 [TEST] 开始测试渐进式加载器...');
            
            if (!window.progressiveLoader) {
                console.error('❌ [TEST] progressiveLoader 不存在');
                return;
            }
            
            try {
                // 测试 loadBasicData
                console.log('🔍 [TEST] 测试 loadBasicData...');
                const basicData = await window.progressiveLoader.loadBasicData('Xs1MR9iVx9RNZOk6', null);
                console.log('📊 [TEST] loadBasicData 返回:', basicData);
                
                if (basicData) {
                    // 测试 displayBasicInfo
                    console.log('🔍 [TEST] 测试 displayBasicInfo...');
                    // 注意：这里不实际调用 displayBasicInfo，因为页面结构不同
                    console.log('✅ [TEST] basicData 存在，可以调用 displayBasicInfo');
                } else {
                    console.log('❌ [TEST] basicData 为 null');
                }
                
            } catch (error) {
                console.error('❌ [TEST] 渐进式加载器测试失败:', error);
            }
        }
        
        async function runFullTest() {
            console.log('🚀 [TEST] === 开始完整测试 ===');
            showStatus('quickStatus', '🧪 正在运行完整测试...', 'info');
            
            const results = [];
            
            // 1. 测试API
            console.log('📡 [TEST] 步骤1: 测试API...');
            const apiData = await testAPI();
            if (apiData) {
                results.push('✅ API测试通过');
                results.push(`   - completed: ${apiData.completed}`);
                results.push(`   - 有result.data: ${!!(apiData.result && apiData.result.data)}`);
            } else {
                results.push('❌ API测试失败');
            }
            
            // 2. 测试函数
            console.log('🔧 [TEST] 步骤2: 测试函数...');
            testFunctions();
            results.push('✅ 函数测试完成');
            
            // 3. 测试渐进式加载器
            console.log('🔄 [TEST] 步骤3: 测试渐进式加载器...');
            await testProgressiveLoader();
            results.push('✅ 渐进式加载器测试完成');
            
            // 4. 模拟完整流程
            console.log('🎯 [TEST] 步骤4: 模拟完整流程...');
            if (window.progressiveLoader && apiData) {
                try {
                    console.log('🔄 [TEST] 调用 startProgressiveLoad...');
                    // 注意：这里不实际调用，因为会修改页面
                    // await window.progressiveLoader.startProgressiveLoad('Xs1MR9iVx9RNZOk6', null);
                    results.push('✅ 完整流程模拟准备就绪');
                } catch (error) {
                    console.error('❌ [TEST] 完整流程测试失败:', error);
                    results.push('❌ 完整流程测试失败');
                }
            }
            
            document.getElementById('testResults').textContent = results.join('\n');
            showStatus('quickStatus', '✅ 完整测试完成', 'success');
            console.log('🎉 [TEST] === 完整测试完成 ===');
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            console.log('🚀 [DEBUG] 调试页面加载完成');
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
