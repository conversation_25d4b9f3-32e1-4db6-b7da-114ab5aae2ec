/**
 * 报告管理器 - 数据提取工具(2)
 * 处理报告数据中各种信息的提取
 */

// 从文件路径中提取信息
HistoryReportsManager.prototype.extractInfoFromFilePath = function(filePath) {
    if (!filePath) return null;
    
    // 尝试匹配 llm_analysis_YYYY[农历日期]_[时辰]_[性别].json 格式
    const fileNameMatch = filePath.match(/llm_analysis_(\d{4})([^_]+)_([^_]+)_([^\.]+)\.json/);
    if (fileNameMatch) {
        const year = fileNameMatch[1];
        const lunarDate = fileNameMatch[2];
        const timeSlot = fileNameMatch[3];
        const gender = fileNameMatch[4];
        
        // 处理农历日期，确保"月"字不会被省略
        let formattedLunarDate = lunarDate;
        
        // 检查是否包含"月"字，如果没有，尝试添加
        if (!lunarDate.includes('月')) {
            // 常见的农历月份表示，如"正"、"二"、"三"、"四"、"五"等
            const monthChars = ['正', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '冬', '腊'];
            
            for (const monthChar of monthChars) {
                if (lunarDate.startsWith(monthChar)) {
                    formattedLunarDate = monthChar + '月' + lunarDate.substring(monthChar.length);
                    console.log('修正农历日期，添加"月"字:', formattedLunarDate);
                    break;
                }
            }
            
            // 处理特殊情况：十一月和十二月
            if (lunarDate.startsWith('十一')) {
                formattedLunarDate = '十一月' + lunarDate.substring(2);
                console.log('修正农历日期，添加"月"字:', formattedLunarDate);
            } else if (lunarDate.startsWith('十二')) {
                formattedLunarDate = '十二月' + lunarDate.substring(2);
                console.log('修正农历日期，添加"月"字:', formattedLunarDate);
            }
        }
        
        // 检查日期中是否包含"日"字，如果没有，尝试添加
        if (!formattedLunarDate.includes('日')) {
            // 查找最后一个数字或汉字数字
            const dayPattern = /[初一二三四五六七八九十廿卅]+$/;
            const dayMatch = formattedLunarDate.match(dayPattern);
            
            if (dayMatch) {
                // 在匹配到的日期后添加"日"字
                const dayPart = dayMatch[0];
                formattedLunarDate = formattedLunarDate.replace(dayPattern, dayPart + '日');
                console.log('修正农历日期，添加"日"字:', formattedLunarDate);
            }
        }
        
        // 处理时辰，确保格式统一
        let formattedTimeSlot = timeSlot;
        if (!timeSlot.endsWith('时')) {
            formattedTimeSlot = timeSlot + '时';
        }
        
        // 构建完整的日期字符串，包含农历日期和时辰
        const fullDate = `${year}年${formattedLunarDate} ${formattedTimeSlot}`;
        
        console.log('从文件路径提取的信息:', {
            year: year + '年',
            lunarDate: formattedLunarDate,
            timeSlot: formattedTimeSlot,
            fullDate,
            gender
        });
        
        return {
            year: year + '年',
            lunarDate: formattedLunarDate,
            timeSlot: formattedTimeSlot,
            fullDate,
            gender
        };
    }
    
    return null;
}; 