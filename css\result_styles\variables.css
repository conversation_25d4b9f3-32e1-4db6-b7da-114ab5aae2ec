/* CSS变量定义 */
/* 导入统一配色系统 */
@import url('./unified-colors.css');

:root {
    /* 兼容性别名 - 保持向后兼容 */
    --primary-color: var(--color-primary);
    --primary-dark: var(--color-primary-hover);
    --primary-light: var(--primary-300);
    --primary-gradient: var(--gradient-primary);
    --primary-color-rgb: 100, 116, 139;
    --hero-gradient: var(--gradient-hero);
    
    /* 背景色别名 */
    --background-light: var(--bg-secondary);
    --bg-input: var(--bg-tertiary);
    
    /* 文本颜色别名 */
    --text-primary: var(--text-primary);
    --text-secondary: var(--text-secondary);
    --text-light: var(--text-tertiary);
    --text-muted: var(--text-muted);
    
    /* 边框和阴影别名 */
    --border-color: var(--border-primary);
    --border-color-light: var(--border-secondary);
    --border-color-dark: var(--border-secondary);
    --shadow-sm: var(--shadow-sm);
    --shadow-md: var(--shadow-md);
    --card-hover-shadow: var(--shadow-card-hover);
    
    /* 圆角别名 */
    --radius-small: var(--radius-md);
    --radius-medium: var(--radius-lg);
    --radius-large: var(--radius-xl);
    
    /* 动效别名 */
    --transition-normal: var(--transition-normal);
    --font-family-primary: var(--font-family-primary);
    --line-height-base: 1.6;
    
    /* 渐变色别名 */
    --bg-hover: var(--bg-hover);
    --bg-glass: var(--bg-glass);
    --bg-overlay: var(--bg-overlay);
    --card-gradient: var(--gradient-card);
    --success-gradient: var(--gradient-success);
    --warning-gradient: var(--gradient-warning);
    --info-gradient: var(--gradient-info);
    
    /* 动画 */
    --pulse-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --float-animation: float 6s ease-in-out infinite;
    --glow-animation: glow 3s ease-in-out infinite alternate;
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
    :root {
        /* 主色调 - 深色模式下的灰色系 */
        --primary-color: #94a3b8;  /* 更浅的灰色在深色模式中 */
        --primary-dark: #64748b;
        --primary-light: #cbd5e1;
        --primary-gradient: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        --hero-gradient: linear-gradient(135deg, #1e293b 0%, #334155 30%, #475569 70%, #64748b 100%);
        
        /* 文本颜色 */
        --text-primary: #f8fafc;
        --text-secondary: #e2e8f0;
        --text-light: #cbd5e1;
        --text-muted: #94a3b8;
        
        /* 背景颜色 - 深色蓝紫色系背景 */
        --bg-primary: #1e293b;  /* 与统一配色的深色模式匹配 */
        --bg-secondary: #0f172a;
        --bg-tertiary: #334155;
        --bg-card: #1e293b;
        --bg-overlay: rgba(15, 23, 42, 0.9);
        --bg-glass: rgba(15, 23, 42, 0.95);
        --bg-hover: #334155;
        
        /* 边框颜色 */
        --border-color: #334155;
        --border-color-light: #1e293b;
        --border-color-dark: #475569;
        
        /* 阴影 - 增加对比度 */
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4);
        --shadow-md: 0 4px 8px -1px rgba(0, 0, 0, 0.5), 0 2px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 12px 20px -3px rgba(0, 0, 0, 0.5), 0 6px 10px -2px rgba(0, 0, 0, 0.4);
        --shadow-xl: 0 25px 30px -5px rgba(0, 0, 0, 0.6), 0 12px 15px -5px rgba(0, 0, 0, 0.5);
        
        /* 卡片悬停阴影 */
        --card-hover-shadow: 0 20px 40px rgba(99, 102, 241, 0.25);
        
        /* 渐变色 */
        --card-gradient: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
        --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
        --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        --info-gradient: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    }
}