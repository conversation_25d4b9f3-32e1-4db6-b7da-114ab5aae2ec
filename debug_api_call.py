import sys
sys.path.append('backend')

# 模拟与app.py完全相同的导入和初始化过程
from flask import Flask, request, jsonify
from card_manager import CardManager
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

print("=== 模拟API调用过程 ===")

# 创建CardManager实例（与app.py中相同）
card_manager = CardManager()

# 模拟API调用的数据处理过程
def simulate_validate_card_api(card_key_input):
    print(f"\n--- 模拟验证卡密: {card_key_input} ---")
    
    try:
        # 模拟request.get_json()的数据
        data = {'cardKey': card_key_input}
        print(f"1. 接收到的数据: {data}")
        
        if not data or 'cardKey' not in data:
            print("2. 数据验证失败: 缺少卡密参数")
            return {'success': False, 'error': '缺少卡密参数'}, 400
        
        card_key = data['cardKey'].strip()
        print(f"2. 处理后的卡密: '{card_key}'")
        
        if not card_key:
            print("3. 卡密验证失败: 卡密为空")
            return {'success': False, 'error': '卡密不能为空'}, 400
        
        # 验证卡密
        print(f"3. 开始验证卡密...")
        is_valid = card_manager.validate_card(card_key)
        print(f"4. 验证结果: {is_valid}")
        
        if is_valid:
            # 获取卡密信息
            card_info = card_manager.get_card_info(card_key)
            print(f"5. 卡密信息: {card_info}")
            
            result = {
                'success': True,
                'message': '卡密验证成功',
                'data': {
                    'valid': True,
                    'remaining_usage': card_info.get('max_usage', 0) - card_info.get('usage_count', 0),
                    'expire_time': card_info.get('expire_time')
                }
            }
            print(f"6. 返回成功结果: {result}")
            return result, 200
        else:
            result = {
                'success': False,
                'error': '卡密无效或已过期'
            }
            print(f"6. 返回失败结果: {result}")
            return result, 401
            
    except Exception as e:
        error_msg = f"验证卡密时出错: {str(e)}"
        logger.error(error_msg)
        print(f"异常: {error_msg}")
        return {
            'success': False,
            'error': '服务器内部错误'
        }, 500

# 测试目标卡密
result, status_code = simulate_validate_card_api('NWDT1fTupGJiyy7B')
print(f"\n=== 最终结果 ===")
print(f"状态码: {status_code}")
print(f"响应: {result}")

# 测试其他卡密
print(f"\n=== 测试其他卡密 ===")
all_cards = list(card_manager.cards_data.keys())[:2]
for card in all_cards:
    result, status_code = simulate_validate_card_api(card)
    print(f"{card} -> 状态码: {status_code}, 成功: {result.get('success')}")