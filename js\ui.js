// UI相关函数文件

/**
 * 设置提交状态
 * @param {boolean} isSubmitting - 是否正在提交
 */
function setSubmitting(isSubmitting) {
    const submitButton = document.getElementById('submitButton');
    const form = document.querySelector('.form-container');
    
    if (submitButton) {
        submitButton.disabled = isSubmitting;
        submitButton.textContent = isSubmitting ? '分析中...' : '开始分析';
    }
    
    if (form) {
        form.style.opacity = isSubmitting ? '0.7' : '1';
    }
}

/**
 * 显示进度
 * @param {string} message - 进度消息
 * @param {number} progress - 进度百分比
 * @param {string} elapsedTime - 已用时间
 */
function showProgress(message, progress, elapsedTime = '') {
    const resultDiv = document.getElementById('result');
    if (!resultDiv) return;
    
    const progressSection = resultDiv.querySelector('.progress-section') || createProgressSection(resultDiv);
    
    // 更新进度条
    const progressBar = progressSection.querySelector('.progress-bar');
    const progressText = progressSection.querySelector('.progress-text');
    const timeText = progressSection.querySelector('.time-text');
    
    if (progressBar) {
        progressBar.style.width = `${Math.min(progress, 100)}%`;
    }
    
    if (progressText) {
        progressText.textContent = message;
    }
    
    if (timeText && elapsedTime) {
        timeText.textContent = `已用时: ${elapsedTime}`;
    }
    
    console.log(`进度更新: ${message} (${progress}%)`);
}

/**
 * 创建进度显示区域
 * @param {HTMLElement} container - 容器元素
 * @returns {HTMLElement} 进度区域元素
 */
function createProgressSection(container) {
    const progressSection = document.createElement('div');
    progressSection.className = 'progress-section';
    progressSection.innerHTML = `
        <div class="progress-container">
            <div class="progress-text">正在处理...</div>
            <div class="progress-bar-container">
                <div class="progress-bar"></div>
            </div>
            <div class="time-text"></div>
        </div>
        <div class="llm-progress" style="display: none;">
            <div class="llm-dimensions"></div>
        </div>
    `;
    
    container.innerHTML = '';
    container.appendChild(progressSection);
    
    return progressSection;
}

/**
 * 显示错误信息
 * @param {string} message - 错误消息
 */
function showError(message) {
    const resultDiv = document.getElementById('result');
    if (!resultDiv) return;
    
    resultDiv.innerHTML = `
        <div class="error-message">
            <div class="error-icon">❌</div>
            <div class="error-text">${message}</div>
            <button onclick="location.reload()" class="retry-button">重新开始</button>
        </div>
    `;
    
    resultDiv.classList.remove('success');
    resultDiv.classList.add('error');
    
    console.error('显示错误:', message);
}

/**
 * 显示成功信息并跳转
 * @param {string} cardKey - 卡密
 */
function showSuccess(cardKey) {
    const resultDiv = document.getElementById('result');
    if (!resultDiv) return;
    
    resultDiv.innerHTML = `
        <div class="success-message">
            <div class="success-icon">✅</div>
            <div class="success-text">分析完成！正在跳转到结果页面...</div>
        </div>
        
        <div style="
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 16px;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text-secondary);
        ">
            <div style="margin-bottom: 8px;">💡 <strong>温馨提示</strong></div>
            <div>• 分析报告包含多个维度的详细解读</div>
            <div>• 建议收藏页面链接以便随时查看</div>
            <div>• 如有疑问，欢迎联系客服咨询</div>
        </div>
    `;
    
    resultDiv.classList.add('success');
    
    // 添加成功动画的CSS
    if (!document.getElementById('success-animations')) {
        const style = document.createElement('style');
        style.id = 'success-animations';
        style.textContent = `
            @keyframes successFadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes successBounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }
        `;
        document.head.appendChild(style);
    }
    
    // 延迟跳转到结果页面
    setTimeout(() => {
        window.location.href = `result.html?cardKey=${encodeURIComponent(cardKey)}`;
    }, 2000);
}

/**
 * 添加脉冲动画
 * @param {HTMLElement} element - 要添加动画的元素
 */
function addPulseAnimation(element) {
    if (!element) return;
    
    element.style.animation = 'pulse 2s infinite';
    
    // 添加脉冲动画CSS（如果不存在）
    if (!document.getElementById('pulse-animation')) {
        const style = document.createElement('style');
        style.id = 'pulse-animation';
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 显示状态变化
 * @param {string} oldStatus - 旧状态
 * @param {string} newStatus - 新状态
 */
function showStatusChange(oldStatus, newStatus) {
    if (oldStatus && oldStatus !== newStatus) {
        console.log(`状态变化: ${oldStatus} → ${newStatus}`);
        
        // 可以在这里添加状态变化的视觉反馈
        const progressSection = document.querySelector('.progress-section');
        if (progressSection) {
            addPulseAnimation(progressSection);
            
            // 移除动画
            setTimeout(() => {
                progressSection.style.animation = '';
            }, 2000);
        }
    }
}

// 导出函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        setSubmitting,
        showProgress,
        createProgressSection,
        showError,
        showSuccess,
        addPulseAnimation,
        showStatusChange
    };
}