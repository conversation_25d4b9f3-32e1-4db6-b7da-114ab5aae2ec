# CardManager 单例模式迁移指南

## 问题背景
之前的代码中存在多个 `CardManager()` 实例化，导致数据不一致的问题。为了解决这个问题，我们引入了单例模式。

## 解决方案
创建了 `card_manager_singleton.py` 模块，提供全局唯一的 CardManager 实例。

## 使用方法

### 旧的使用方式（已废弃）
```python
from card_manager import CardManager
card_manager = CardManager()  # 创建新实例，可能导致数据不一致
```

### 新的使用方式（推荐）
```python
from card_manager_singleton import get_card_manager
card_manager = get_card_manager()  # 获取全局唯一实例
```

### 重新加载数据
```python
from card_manager_singleton import reload_card_manager
card_manager = reload_card_manager()  # 重新加载数据并返回实例
```

## 已修改的文件

### 核心应用文件
- ✅ `app.py` - Flask主应用
- ✅ `app_debug.py` - Flask调试版本
- ✅ `config_api.py` - 配置API（3个实例化位置）
- ✅ `request_manager.py` - 请求管理器

### 需要手动修改的文件
以下文件仍使用旧的实例化方式，建议在使用时进行修改：

#### 调试和测试文件
- `debug_api.py` - API调试接口
- `add_cards.py` - 卡密添加工具
- `debug_card_manager.py` - 卡密管理调试工具

#### 项目根目录的调试文件
- `debug_card_issue.py`
- `debug_server_validate.py`
- `debug_card_manager_direct.py`
- `debug_card_validation.py`
- `debug_api_call.py`

## 修改示例

### 对于简单的脚本文件
```python
# 旧代码
from card_manager import CardManager
card_manager = CardManager()

# 新代码
from card_manager_singleton import get_card_manager
card_manager = get_card_manager()
```

### 对于需要重新加载数据的场景
```python
# 旧代码
card_manager = CardManager()
card_manager._load_cards()

# 新代码
from card_manager_singleton import reload_card_manager
card_manager = reload_card_manager()
```

## 优势
1. **数据一致性**: 确保整个应用使用同一个 CardManager 实例
2. **内存效率**: 避免重复加载卡密数据
3. **线程安全**: 使用线程锁确保单例模式的线程安全
4. **易于维护**: 统一的实例管理，便于调试和维护

## 注意事项
1. 所有新代码都应该使用 `get_card_manager()` 而不是直接实例化 `CardManager()`
2. 如果需要重新加载数据，使用 `reload_card_manager()` 而不是调用 `_load_cards()`
3. 调试和测试文件可以根据需要逐步迁移
4. 确保在导入时使用正确的模块路径