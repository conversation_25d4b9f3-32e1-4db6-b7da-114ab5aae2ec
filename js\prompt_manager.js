/**
 * 提示词管理系统 JavaScript
 * 处理前端交互和后端API调用
 */

class PromptManagerUI {
    constructor() {
        this.currentDimension = '';
        this.currentVersion = 'current';
        this.apiBase = '/api/prompt';
        this.init();
    }
    
    init() {
        this.loadVersions();
        this.loadDimensions();
    }
    
    // API调用封装
    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || '请求失败');
            }
            
            return result;
        } catch (error) {
            console.error('API调用错误:', error);
            this.showAlert('error', `API调用失败: ${error.message}`);
            throw error;
        }
    }
    
    // 显示提示信息
    showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        // 插入到当前活动的tab-content顶部
        const activeTab = document.querySelector('.tab-content.active');
        activeTab.insertBefore(alertDiv, activeTab.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
    
    // 加载维度列表
    async loadDimensions() {
        try {
            const dimensions = await this.apiCall('/dimensions');
            const select = document.getElementById('dimension-select');
            const evalSelect = document.getElementById('eval-dimension');
            
            // 清空现有选项（保留第一个默认选项）
            select.innerHTML = '<option value="">请选择维度</option>';
            evalSelect.innerHTML = '';
            
            dimensions.forEach(dim => {
                const option1 = new Option(dim, dim);
                const option2 = new Option(dim, dim);
                select.appendChild(option1);
                evalSelect.appendChild(option2);
            });
        } catch (error) {
            console.error('加载维度列表失败:', error);
        }
    }
    
    // 加载提示词内容
    async loadPrompt() {
        const dimension = document.getElementById('dimension-select').value;
        if (!dimension) return;
        
        this.currentDimension = dimension;
        
        try {
            const prompt = await this.apiCall(`/prompt/${dimension}`);
            document.getElementById('prompt-content').value = prompt.content || '';
            this.showAlert('info', `已加载 ${dimension} 的提示词`);
        } catch (error) {
            console.error('加载提示词失败:', error);
        }
    }
    
    // 保存提示词
    async savePrompt() {
        const dimension = this.currentDimension;
        const content = document.getElementById('prompt-content').value;
        const description = document.getElementById('prompt-description').value;
        
        if (!dimension) {
            this.showAlert('error', '请先选择分析维度');
            return;
        }
        
        if (!content.trim()) {
            this.showAlert('error', '提示词内容不能为空');
            return;
        }
        
        try {
            await this.apiCall('/prompt', 'POST', {
                dimension: dimension,
                content: content,
                description: description || '更新提示词内容'
            });
            
            this.showAlert('success', '提示词保存成功');
            document.getElementById('prompt-description').value = '';
        } catch (error) {
            console.error('保存提示词失败:', error);
        }
    }
    
    // 预览提示词效果
    async previewPrompt() {
        const dimension = this.currentDimension;
        const content = document.getElementById('prompt-content').value;
        
        if (!dimension || !content.trim()) {
            this.showAlert('error', '请选择维度并输入提示词内容');
            return;
        }
        
        try {
            const preview = await this.apiCall('/prompt/preview', 'POST', {
                dimension: dimension,
                content: content
            });
            
            const previewDiv = document.getElementById('prompt-preview');
            const previewContent = document.getElementById('preview-content');
            
            previewContent.innerHTML = `
                <h4>构建后的完整提示词</h4>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 8px; white-space: pre-wrap; max-height: 400px; overflow-y: auto;">${preview.full_prompt}</pre>
                <div style="margin-top: 15px;">
                    <strong>提示词长度:</strong> ${preview.length} 字符<br>
                    <strong>预估tokens:</strong> ${Math.ceil(preview.length / 2.5)}
                </div>
            `;
            
            previewDiv.style.display = 'block';
        } catch (error) {
            console.error('预览提示词失败:', error);
        }
    }
    
    // 测试提示词分析效果
    async testPrompt() {
        const dimension = this.currentDimension;
        const content = document.getElementById('prompt-content').value;
        
        if (!dimension || !content.trim()) {
            this.showAlert('error', '请选择维度并输入提示词内容');
            return;
        }
        
        this.showAlert('info', '正在测试分析效果，请稍候...');
        
        try {
            const testResult = await this.apiCall('/prompt/test', 'POST', {
                dimension: dimension,
                content: content,
                test_data: {
                    "天干十神": ["七杀", "正官", "正官"],
                    "地支藏干十神": [["伤官", "偏财"], ["食神", "偏财"], ["正印"], ["正财", "伤官", "比肩"]]
                }
            });
            
            const previewDiv = document.getElementById('prompt-preview');
            const previewContent = document.getElementById('preview-content');
            
            previewContent.innerHTML = `
                <h4>测试分析结果</h4>
                <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    ${testResult.analysis_result.replace(/\n/g, '<br>')}
                </div>
                ${testResult.evaluation ? this.renderEvaluation(testResult.evaluation) : ''}
            `;
            
            previewDiv.style.display = 'block';
            this.showAlert('success', '测试完成');
        } catch (error) {
            console.error('测试提示词失败:', error);
        }
    }
    
    // 加载版本列表
    async loadVersions() {
        try {
            const versions = await this.apiCall('/versions');
            const versionsList = document.getElementById('versions-list');
            
            if (versions.length === 0) {
                versionsList.innerHTML = '<div class="alert alert-info">暂无版本记录</div>';
                return;
            }
            
            versionsList.innerHTML = versions.map(version => `
                <div class="version-item ${version.is_current ? 'current' : ''}">
                    <div class="version-info">
                        <h4>${version.name} ${version.is_current ? '(当前版本)' : ''}</h4>
                        <div class="meta">
                            创建时间: ${new Date(version.created_at).toLocaleString()}<br>
                            描述: ${version.description || '无描述'}<br>
                            校验和: ${version.checksum ? version.checksum.substring(0, 8) : 'N/A'}
                        </div>
                    </div>
                    <div class="version-actions">
                        ${!version.is_current ? `<button class="btn btn-primary" onclick="promptManager.switchVersion('${version.name}')">切换</button>` : ''}
                        <button class="btn btn-secondary" onclick="promptManager.compareVersion('${version.name}')">对比</button>
                        ${!version.is_current ? `<button class="btn btn-danger" onclick="promptManager.deleteVersion('${version.name}')">删除</button>` : ''}
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('加载版本列表失败:', error);
            document.getElementById('versions-list').innerHTML = '<div class="alert alert-error">加载版本列表失败</div>';
        }
    }
    
    // 创建新版本
    async createVersion() {
        const versionName = prompt('请输入版本名称:');
        if (!versionName) return;
        
        const description = prompt('请输入版本描述:') || '';
        
        try {
            await this.apiCall('/versions', 'POST', {
                name: versionName,
                description: description
            });
            
            this.showAlert('success', `版本 ${versionName} 创建成功`);
            this.loadVersions();
        } catch (error) {
            console.error('创建版本失败:', error);
        }
    }
    
    // 切换版本
    async switchVersion(versionName) {
        if (!confirm(`确定要切换到版本 ${versionName} 吗？`)) return;
        
        try {
            await this.apiCall(`/versions/${versionName}/switch`, 'POST');
            this.showAlert('success', `已切换到版本 ${versionName}`);
            this.loadVersions();
        } catch (error) {
            console.error('切换版本失败:', error);
        }
    }
    
    // 删除版本
    async deleteVersion(versionName) {
        if (!confirm(`确定要删除版本 ${versionName} 吗？此操作不可恢复！`)) return;
        
        try {
            await this.apiCall(`/versions/${versionName}`, 'DELETE');
            this.showAlert('success', `版本 ${versionName} 已删除`);
            this.loadVersions();
        } catch (error) {
            console.error('删除版本失败:', error);
        }
    }
    
    // 运行质量评估
    async runEvaluation() {
        const dimension = document.getElementById('eval-dimension').value;
        const baziData = document.getElementById('test-bazi').value;
        
        if (!dimension || !baziData.trim()) {
            this.showAlert('error', '请选择维度并输入测试数据');
            return;
        }
        
        this.showAlert('info', '正在进行质量评估，请稍候...');
        
        try {
            const evaluation = await this.apiCall('/evaluation', 'POST', {
                dimension: dimension,
                bazi_data: baziData
            });
            
            const resultsDiv = document.getElementById('evaluation-results');
            resultsDiv.innerHTML = this.renderEvaluation(evaluation);
            resultsDiv.style.display = 'block';
            
            this.showAlert('success', '质量评估完成');
        } catch (error) {
            console.error('质量评估失败:', error);
        }
    }
    
    // 渲染评估结果
    renderEvaluation(evaluation) {
        const score = evaluation.overall_score;
        let scoreClass = 'score-poor';
        let scoreText = '需要改进';
        
        if (score >= 0.9) {
            scoreClass = 'score-excellent';
            scoreText = '优秀';
        } else if (score >= 0.7) {
            scoreClass = 'score-good';
            scoreText = '良好';
        } else if (score >= 0.5) {
            scoreClass = 'score-fair';
            scoreText = '一般';
        }
        
        return `
            <div class="evaluation-result">
                <div class="score-display">
                    <div>
                        <h3>整体质量评分</h3>
                        <p>维度: ${evaluation.dimension}</p>
                        <p>评估时间: ${new Date(evaluation.timestamp).toLocaleString()}</p>
                    </div>
                    <div class="score-circle ${scoreClass}">
                        ${Math.round(score * 100)}
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <h4>十神准确性</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #667eea;">
                            ${Math.round((evaluation.detailed_scores.shishen_accuracy || 0) * 100)}%
                        </div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <h4>逻辑一致性</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #28a745;">
                            ${Math.round((evaluation.detailed_scores.logic_consistency || 0) * 100)}%
                        </div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <h4>格式规范性</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">
                            ${Math.round((evaluation.detailed_scores.format_compliance || 0) * 100)}%
                        </div>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px;">
                        <h4>内容完整性</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #ffc107;">
                            ${Math.round((evaluation.detailed_scores.content_completeness || 0) * 100)}%
                        </div>
                    </div>
                </div>
                
                ${evaluation.issues && evaluation.issues.length > 0 ? `
                    <div class="issues-list">
                        <h4>发现的问题</h4>
                        <ul>
                            ${evaluation.issues.map(issue => `<li>${issue}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
                
                ${evaluation.suggestions && evaluation.suggestions.length > 0 ? `
                    <div style="background: white; border-radius: 8px; padding: 15px; margin-top: 15px;">
                        <h4 style="color: #28a745; margin: 0 0 10px 0;">改进建议</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            ${evaluation.suggestions.map(suggestion => `<li style="margin-bottom: 5px; color: #666;">${suggestion}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }
}

// 全局变量和函数
let promptManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    promptManager = new PromptManagerUI();
});

// Tab切换功能
function switchTab(tabName) {
    // 隐藏所有tab内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 移除所有tab的active状态
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的tab内容
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // 设置选中的tab为active
    event.target.classList.add('active');
    
    // 根据tab执行特定的加载逻辑
    switch(tabName) {
        case 'versions':
            promptManager.loadVersions();
            break;
        case 'evaluation':
            // 可以在这里加载评估历史等
            break;
        case 'testing':
            // 可以在这里加载实验列表等
            break;
    }
}

// 导出到全局作用域的函数
window.switchTab = switchTab;
window.loadPrompt = () => promptManager.loadPrompt();
window.savePrompt = () => promptManager.savePrompt();
window.previewPrompt = () => promptManager.previewPrompt();
window.testPrompt = () => promptManager.testPrompt();
window.createVersion = () => promptManager.createVersion();
window.loadVersions = () => promptManager.loadVersions();
window.runEvaluation = () => promptManager.runEvaluation();
