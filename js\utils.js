// 工具函数文件

/**
 * 获取表单元素的值
 * @param {string} elementId - 元素ID
 * @returns {string} 元素的值
 */
function getFormValue(elementId) {
    const element = document.getElementById(elementId);
    return element ? element.value : '';
}

/**
 * 验证卡密格式和有效性
 * @param {string} cardKey - 卡密
 * @returns {Promise<boolean>} 是否有效
 */
async function validateCardKey(cardKey) {
    if (!cardKey || cardKey.trim() === '') {
        return false;
    }
    
    // 基本长度检查（可根据实际需求调整）
    if (cardKey.length < 6) {
        return false;
    }
    
    try {
        // 向服务器验证卡密
        const response = await fetch('/api/validate-card', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ cardKey: cardKey })
        });
        
        const data = await response.json();
        return data.success === true;
    } catch (error) {
        console.error('验证卡密时出错:', error);
        return false;
    }
}

/**
 * 清除卡密状态显示
 */
function clearCardKeyStatus() {
    const statusElement = document.getElementById('cardKeyStatus');
    if (statusElement) {
        statusElement.textContent = '';
        statusElement.className = 'card-key-status';
    }
}

/**
 * 显示卡密状态
 * @param {string} message - 状态消息
 * @param {string} type - 状态类型 ('success', 'error', 'warning')
 */
function showCardKeyStatus(message, type = 'info') {
    const statusElement = document.getElementById('cardKeyStatus');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `card-key-status ${type}`;
    }
}

/**
 * 获取表单数据
 * @returns {Object} 表单数据对象
 */
function getFormData() {
    const inputMode = document.querySelector('input[name="inputMode"]:checked')?.value || 'solar';
    
    // 详细调试表单元素的存在性和值
    console.log('=== getFormData 调试信息 ===');
    console.log('输入模式:', inputMode);
    
    // 检查关键元素是否存在
    const yearElement = document.getElementById('yearInput');
    const monthElement = document.getElementById('monthInput');
    const dayElement = document.getElementById('dayInput');
    const hourElement = document.getElementById('hourInput');
    
    console.log('表单元素存在性检查:', {
        yearElement: !!yearElement,
        monthElement: !!monthElement,
        dayElement: !!dayElement,
        hourElement: !!hourElement
    });
    
    if (yearElement) console.log('年份元素值:', yearElement.value, '类型:', typeof yearElement.value);
    if (monthElement) console.log('月份元素值:', monthElement.value, '类型:', typeof monthElement.value);
    if (dayElement) console.log('日期元素值:', dayElement.value, '类型:', typeof dayElement.value);
    if (hourElement) console.log('时辰元素值:', hourElement.value, '类型:', typeof hourElement.value);
    
    const baseData = {
        cardKey: getFormValue('cardKeyInput'),
        name: getFormValue('nameInput'),
        gender: getFormValue('genderInput'),
        location: getFormValue('locationInput'),
        inputMode: inputMode
    };
    
    console.log('基础数据:', baseData);
    
    if (inputMode === 'bazi') {
        // 八字反推模式，包含八字信息
        const baziData = {
            ...baseData,
            yearPillar: getFormValue('yearPillar'),
            monthPillar: getFormValue('monthPillar'),
            dayPillar: getFormValue('dayPillar'),
            timePillar: getFormValue('timePillar'),
            sect: getFormValue('sect'),
            baseYear: getFormValue('baseYear'),
            // 如果已经反推出阳历时间，也包含进去
            year: getFormValue('yearInput'),
            month: getFormValue('monthInput'),
            day: getFormValue('dayInput'),
            hour: getFormValue('hourInput')
        };
        console.log('八字模式完整数据:', baziData);
        return baziData;
    } else {
        // 阳历输入模式
        const solarData = {
            ...baseData,
            year: getFormValue('yearInput'),
            month: getFormValue('monthInput'),
            day: getFormValue('dayInput'),
            hour: getFormValue('hourInput')
        };
        console.log('阳历模式完整数据:', solarData);
        return solarData;
    }
}

// 注意：saveAnalysisResult 函数已移至 api_analysis_service.js
// 该函数有更完整的实现，包含重复检测和更好的数据提取逻辑

// 导出函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getFormValue,
        validateCardKey,
        clearCardKeyStatus,
        showCardKeyStatus,
        getFormData
        // 注意：saveAnalysisResult 已移至 api_analysis_service.js
    };
}