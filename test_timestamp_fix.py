#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间戳修复效果
"""

import requests
import time

def test_timestamp_fix():
    """测试时间戳修复效果"""
    print("🎯 测试时间戳修复效果")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 发送第一个请求
    print("\n1. 发送第一个请求：1995年6月20日午时")
    print("-" * 40)
    
    payload1 = {
        "cardKey": "wdd",
        "year": "1995",
        "month": "6",
        "day": "20",
        "hour": "午时",
        "gender": "1"
    }
    
    response1 = requests.post('http://localhost:5000/webhook/bazi-analysis', json=payload1)
    if response1.status_code == 200:
        result1 = response1.json()
        request_id1 = result1.get('requestId')
        print(f"✅ 第一个请求成功，ID: {request_id1}")
    else:
        print(f"❌ 第一个请求失败: {response1.status_code}")
        return False
    
    # 等待一秒，确保时间戳不同
    time.sleep(2)
    
    # 发送第二个请求
    print("\n2. 发送第二个请求：1988年2月14日卯时")
    print("-" * 40)
    
    payload2 = {
        "cardKey": "wdd",
        "year": "1988",
        "month": "2",
        "day": "14",
        "hour": "卯时",
        "gender": "1"
    }
    
    response2 = requests.post('http://localhost:5000/webhook/bazi-analysis', json=payload2)
    if response2.status_code == 200:
        result2 = response2.json()
        request_id2 = result2.get('requestId')
        print(f"✅ 第二个请求成功，ID: {request_id2}")
    else:
        print(f"❌ 第二个请求失败: {response2.status_code}")
        return False
    
    # 等待处理完成
    print("\n3. 等待处理完成...")
    print("-" * 40)
    time.sleep(15)
    
    # 查询结果
    print("\n4. 查询最新结果")
    print("-" * 40)
    
    result_response = requests.get('http://localhost:5000/api/get_result/wdd')
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        
        if result_data.get('completed'):
            result_obj = result_data.get('result', {})
            bz = result_obj.get('data', {}).get('bz', {})
            
            if bz:
                birth_info = bz.get('8', '未知')
                bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                
                print(f"📅 返回的生辰: {birth_info}")
                print(f"🔮 返回的八字: {bazi_str}")
                
                # 检查是否是最新的请求（1988年的）
                if '1987年腊月' in birth_info or '1988' in birth_info:
                    print("✅ 成功！返回了最新请求的结果（1988年2月14日卯时）")
                    return True
                elif '1995年五月' in birth_info:
                    print("❌ 返回了第一个请求的结果，而不是最新的")
                    return False
                else:
                    print(f"❓ 返回了未知的结果: {birth_info}")
                    return False
            else:
                print("❌ 没有八字数据")
                return False
        else:
            print("⏳ 仍在处理中...")
            return False
    else:
        print(f"❌ 查询失败: {result_response.status_code}")
        return False

def main():
    """主函数"""
    print("🎯 时间戳修复效果测试")
    print("=" * 60)
    
    success = test_timestamp_fix()
    
    if success:
        print("\n🎉 修复成功！")
        print("💡 API现在能够正确返回最新请求的结果")
        print("🔗 你可以在 http://localhost:5000/ 测试不同的日期时间")
    else:
        print("\n⚠️ 还需要进一步修复")
        print("💡 请检查服务器日志以了解具体问题")

if __name__ == "__main__":
    main()
