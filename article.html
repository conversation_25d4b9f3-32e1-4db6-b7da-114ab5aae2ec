<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询解答博客 - 文章详情</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/fonts.css">
    <!-- Google Fonts 已替换为本地字体文件 -->
    <link rel="stylesheet" href="css/article.css">
</head>
<body>
    <div class="container">
        <a href="blog.html" class="back-to-blog">
            ← 返回博客
        </a>
        
        <article>
            <header class="article-header">
                <div class="article-category">加载中...</div>
                <h1 class="article-title">文章加载中...</h1>
                <div class="article-meta">
                    <div class="meta-item">
                        📅 发布时间：加载中...
                    </div>
                    <div class="meta-item">
                        👁️ 阅读量：--
                    </div>
                    <div class="meta-item">
                        ⏱️ 阅读时长：--
                    </div>
                </div>
            </header>
            
            <div class="article-content">
                <p>文章内容加载中，请稍候...</p>
            </div>
            
            <div class="article-tags">
                <!-- 标签将动态加载 -->
            </div>
            
            <div class="article-footer">
                <div class="share-buttons">
                    <a href="#" class="share-button share-wechat">
                        💬 分享到微信
                    </a>
                    <a href="#" class="share-button share-weibo">
                        📱 分享到微博
                    </a>
                    <a href="#" class="share-button share-qq">
                        🐧 分享到QQ
                    </a>
                </div>
            </div>
        </article>
        
        <div class="related-articles">
            <h3 class="related-title">相关文章推荐</h3>
            <div class="related-list" id="related-articles-container">
                <!-- 相关文章将动态加载 -->
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 八字分析系统 | 专业命理分析服务</p>
        </div>
    </div>
    
    <script>
        // 文章页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 根据URL参数加载文章内容
            loadArticleContent();
            
            async function loadArticleContent() {
                try {
                    // 获取URL参数中的文章ID
                    const urlParams = new URLSearchParams(window.location.search);
                    const articleId = urlParams.get('id');
                    
                    if (!articleId) {
                        console.error('未找到文章ID参数');
                        document.querySelector('.article-title').textContent = '文章未找到';
                        document.querySelector('.article-content').innerHTML = '<p>抱歉，未提供文章ID参数。</p>';
                        return;
                    }
                    
                    // 加载文章数据
                    const response = await fetch('./articles/articles.json');
                    if (!response.ok) {
                        throw new Error(`加载文章数据失败: ${response.status}`);
                    }
                    
                    const articles = await response.json();
                    const article = articles.find(a => a.id === articleId);
                    
                    if (!article) {
                        console.error('未找到对应的文章，ID:', articleId);
                        document.querySelector('.article-title').textContent = '文章未找到';
                        document.querySelector('.article-content').innerHTML = '<p>抱歉，您访问的文章不存在。</p>';
                        return;
                    }
                    
                    console.log('找到文章:', article.title);
                    
                    // 更新页面标题
                    document.title = article.title + ' - 咨询解答博客';
                    
                    // 更新文章内容
                    document.querySelector('.article-category').textContent = getCategoryName(article.category);
                    document.querySelector('.article-title').textContent = article.title;
                    document.querySelector('.article-content').innerHTML = article.content;
                    
                    // 更新发布时间
                    const metaItems = document.querySelectorAll('.meta-item');
                    if (metaItems[0]) {
                        metaItems[0].innerHTML = `📅 发布时间：${article.date}`;
                    }
                    
                    // 添加标签
                    const tagsContainer = document.querySelector('.article-tags');
                    tagsContainer.innerHTML = article.tags ? article.tags.map(tag => `<span class="tag">${tag}</span>`).join('') : '';
                    
                    // 加载相关文章
                    loadRelatedArticles(article.category, articleId);
                    
                } catch (error) {
                    console.error('加载文章失败:', error);
                    document.querySelector('.article-title').textContent = '加载失败';
                    document.querySelector('.article-content').innerHTML = '<p>文章加载失败，请稍后重试。</p>';
                }
            }
            
            // 加载相关文章
            async function loadRelatedArticles(category, currentId) {
                try {
                    const response = await fetch('./articles/articles.json');
                    if (!response.ok) throw new Error('加载文章数据失败');
                    
                    const articles = await response.json();
                    // 过滤出同类别但不是当前文章的文章，最多显示3篇
                    const relatedArticles = articles
                        .filter(a => a.category === category && a.id !== currentId)
                        .slice(0, 3);
                    
                    // 如果相关文章不足3篇，添加其他分类的文章
                    if (relatedArticles.length < 3) {
                        const otherArticles = articles
                            .filter(a => a.category !== category && a.id !== currentId)
                            .slice(0, 3 - relatedArticles.length);
                        
                        relatedArticles.push(...otherArticles);
                    }
                    
                    // 渲染相关文章
                    const container = document.getElementById('related-articles-container');
                    container.innerHTML = relatedArticles.map(article => `
                        <div class="related-item" data-id="${article.id}">
                            <div class="related-item-title">${article.title}</div>
                            <div class="related-item-excerpt">${article.excerpt}</div>
                        </div>
                    `).join('');
                    
                    // 添加点击事件
                    document.querySelectorAll('.related-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const id = this.dataset.id;
                            window.location.href = `article.html?id=${id}`;
                        });
                    });
                    
                } catch (error) {
                    console.error('加载相关文章失败:', error);
                    document.getElementById('related-articles-container').innerHTML = 
                        '<p>相关文章加载失败</p>';
                }
            }
            
            // 获取分类中文名称
            function getCategoryName(categoryId) {
                const categoryMap = {
                    'analysis': '案例分析',
                    'theory': '理论知识',
                    'fortune': '运势解读',
                    'qa': '问答精选',
                    'guide': '开运指南',
                    '婚姻分析': '婚姻分析',
                    '基础知识': '基础知识',
                    '年运分析': '年运分析',
                    '答疑解惑': '答疑解惑'
                };
                return categoryMap[categoryId] || categoryId;
            }
            
            // 分享按钮功能
            const shareButtons = document.querySelectorAll('.share-button');
            shareButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const platform = this.classList.contains('share-wechat') ? '微信' :
                                   this.classList.contains('share-weibo') ? '微博' : 'QQ';
                    alert(`分享功能开发中，您点击了分享到${platform}`);
                });
            });
        });
    </script>
</body>
</html>