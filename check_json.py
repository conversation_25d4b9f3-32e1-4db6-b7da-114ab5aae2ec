import json

with open(r'c:\Users\<USER>\Desktop\project\articles\articles.json','r',encoding='utf-8') as f:
    content = f.read()
    lines = content.split('\n')
    print(f'总行数: {len(lines)}')
    print(f'第9行内容长度: {len(lines[8]) if len(lines) > 8 else 0}')
    
    # 查找第1437个字符的位置
    char_count = 0
    for i, line in enumerate(lines):
        if char_count + len(line) + 1 >= 1690:  # +1 for newline
            pos_in_line = 1690 - char_count
            print(f'错误位置在第{i+1}行，第{pos_in_line}个字符')
            print(f'该行内容: {line}')
            if pos_in_line > 10:
                print(f'错误附近内容: {line[max(0,pos_in_line-10):pos_in_line+10]}')
            break
        char_count += len(line) + 1
    
    try:
        json.loads(content)
        print('JSON格式正确')
    except json.JSONDecodeError as e:
        print(f'JSON错误: {e}')