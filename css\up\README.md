# up.css 文件拆分说明

根据八字分析系统文件拆分规则，原始的 `up.css` 文件（约23KB，1090行）已被拆分为以下多个较小的CSS文件：

## 目录结构

```
css/up/
├── main.css          # 主入口文件，导入所有其他CSS文件
├── variables.css     # 变量定义文件
├── reset.css         # 基础样式重置
├── navigation.css    # 导航组件样式
├── form.css          # 表单样式
├── buttons.css       # 按钮样式
├── progress.css      # 进度条样式
├── header.css        # 页面头部样式
├── decorations.css   # 背景装饰效果
├── dark-mode.css     # 深色模式样式
└── responsive.css    # 响应式设计
```

## 使用方法

在HTML文件中，只需引入主入口文件 `main.css` 即可：

```html
<link rel="stylesheet" href="css/up/main.css">
```

这将自动导入所有拆分后的CSS文件。

## 文件说明

- **variables.css**: 存储颜色、字体等通用变量
- **reset.css**: 基础样式重置和符号样式修复
- **navigation.css**: 导航链接样式
- **form.css**: 表单容器、输入框和分组样式
- **buttons.css**: 按钮样式、提交按钮和购买卡密按钮
- **progress.css**: 进度条和进度指示器样式
- **header.css**: 页面头部和标题样式
- **decorations.css**: 背景装饰和动画效果
- **dark-mode.css**: 深色模式样式覆盖
- **responsive.css**: 移动端和各种屏幕尺寸的响应式设计

## 优势

1. **更好的可维护性**：每个文件专注于特定功能
2. **更快的加载速度**：按需加载，减少首屏加载时间
3. **更清晰的组织结构**：代码按功能组织，便于团队协作
4. **更容易的调试**：问题隔离更简单
5. **符合文件大小控制规则**：每个文件都控制在适当大小范围内 