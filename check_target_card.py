import requests
import json

response = requests.get('http://localhost:5000/api/cards/json')
data = response.json()

print(f'Total cards: {len(data)}')
target = 'NWDT1fTupGJiyy7B'
print(f'Target card exists: {target in data}')

if target in data:
    print(f'Target card info: {json.dumps(data[target], indent=2)}')
else:
    print('Target card not found in server data')
    print('First 5 cards in server:')
    for i, (key, value) in enumerate(list(data.items())[:5]):
        print(f'  {key}: {value}')