#!/usr/bin/env python3
"""
调试请求状态脚本
检查正在处理的请求是否有基础八字数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from bazi_service import BaziService
from request_manager import RequestManager

# 创建服务实例
bazi_service = BaziService()
import json

def debug_request_status():
    """调试请求状态"""
    print("🔍 调试请求状态")
    print("=" * 50)
    
    # 检查所有活跃请求
    print(f"📊 活跃请求数量: {len(bazi_service.request_manager.active_requests)}")
    
    for request_id, request_info in bazi_service.request_manager.active_requests.items():
        print(f"\n🎯 请求ID: {request_id}")
        print(f"   卡密: {request_info.get('card_key', 'N/A')}")
        print(f"   状态: {request_info.get('status', 'N/A')}")
        print(f"   开始时间: {request_info.get('start_time', 'N/A')}")
        print(f"   有基础数据: {'basic_data' in request_info}")
        
        if 'basic_data' in request_info:
            basic_data = request_info['basic_data']
            print(f"   基础数据类型: {type(basic_data)}")
            if isinstance(basic_data, dict):
                print(f"   基础数据键: {list(basic_data.keys())}")
                if 'bz' in basic_data:
                    print(f"   八字数据: {basic_data['bz']}")
        else:
            print(f"   ❌ 没有基础数据")
    
    print("\n" + "=" * 50)
    
    # 检查特定卡密的请求
    card_key = "NI45OgqRJEf7KZBonjMXLX7W"
    print(f"🎯 检查卡密 {card_key} 的请求:")
    
    latest_request = bazi_service.request_manager.get_latest_request_by_card(card_key)
    if latest_request:
        request_id, request_info = latest_request
        print(f"   最新请求ID: {request_id}")
        print(f"   请求状态: {request_info.get('status', 'N/A')}")
        print(f"   有基础数据: {'basic_data' in request_info}")
        
        # 测试 get_basic_result 方法
        print(f"\n🧪 测试 get_basic_result 方法:")
        basic_result = bazi_service.get_basic_result(request_id)
        print(f"   返回结果: {basic_result is not None}")
        if basic_result:
            print(f"   结果类型: {type(basic_result)}")
            print(f"   结果键: {list(basic_result.keys()) if isinstance(basic_result, dict) else 'N/A'}")
        else:
            print(f"   ❌ get_basic_result 返回 None")
            
            # 详细调试
            print(f"\n🔍 详细调试:")
            print(f"   请求信息存在: {request_info is not None}")
            if request_info:
                print(f"   请求信息键: {list(request_info.keys())}")
                print(f"   basic_data 键存在: {'basic_data' in request_info}")
                if 'basic_data' in request_info:
                    print(f"   basic_data 内容: {json.dumps(request_info['basic_data'], ensure_ascii=False, indent=2)}")
    else:
        print(f"   ❌ 没有找到该卡密的请求")
    
    print("\n" + "=" * 50)
    print("✅ 调试完成")

if __name__ == "__main__":
    debug_request_status()
