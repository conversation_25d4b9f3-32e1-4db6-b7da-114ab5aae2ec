/* 渐进式加载样式 */

/* 八字基础信息样式 */
.bazi-basic-info {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.bazi-header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e2e8f0;
}

.bazi-header h2 {
    color: #2d3748;
    margin: 0 0 12px 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.bazi-meta {
    display: flex;
    justify-content: center;
    gap: 24px;
    font-size: 0.9rem;
    color: #718096;
}

.bazi-meta span {
    background: white;
    padding: 4px 12px;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

/* 四柱八字样式 */
.bazi-pillars {
    margin-bottom: 24px;
}

.bazi-pillars h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 1.2rem;
    text-align: center;
}

.pillars-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    max-width: 400px;
    margin: 0 auto;
}

.pillar-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: transform 0.2s ease;
}

.pillar-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.pillar-name {
    font-size: 0.8rem;
    color: #718096;
    margin-bottom: 4px;
}

.pillar-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
}

/* 十神配置样式 */
.bazi-shishen {
    margin-bottom: 24px;
}

.bazi-shishen h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 1.2rem;
    text-align: center;
}

.shishen-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-width: 400px;
    margin: 0 auto;
}

.shishen-item {
    background: white;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.shishen-position {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 2px;
}

.shishen-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4a5568;
}

/* 大运信息样式 */
.bazi-dayun h3 {
    color: #4a5568;
    margin-bottom: 16px;
    font-size: 1.2rem;
    text-align: center;
}

.dayun-meta {
    text-align: center;
    margin-bottom: 16px;
    color: #718096;
    font-size: 0.9rem;
}

.dayun-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    max-width: 500px;
    margin: 0 auto;
}

.dayun-item {
    background: white;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.dayun-period {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 2px;
}

.dayun-ganzhi {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4a5568;
}

/* 渐进式分析区域样式 */
.progressive-analysis-section {
    margin-top: 32px;
}

.progressive-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    text-align: center;
}

.progressive-header h3 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
}

.progressive-status {
    font-size: 0.9rem;
    opacity: 0.9;
}

.progressive-status.completed {
    color: #68d391;
}

.progressive-status.timeout {
    color: #fed7d7;
}

.progressive-status.error {
    color: #feb2b2;
}

/* 维度占位符样式 */
.progressive-dimensions {
    background: white;
    border-radius: 0 0 12px 12px;
    border: 1px solid #e2e8f0;
    border-top: none;
}

.dimension-placeholder {
    border-bottom: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.dimension-placeholder:last-child {
    border-bottom: none;
}

.dimension-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dimension-header:hover {
    background-color: #f7fafc;
}

.dimension-icon {
    font-size: 1.2rem;
    margin-right: 12px;
}

.dimension-title {
    flex: 1;
    font-weight: 600;
    color: #2d3748;
}

.dimension-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
}

.dimension-status.loading {
    color: #718096;
}

.dimension-status.completed {
    color: #38a169;
}

.dimension-status.error {
    color: #e53e3e;
}

.dimension-content {
    padding: 0 20px 20px 20px;
    border-top: 1px solid #f1f5f9;
    background-color: #fafafa;
}

.dimension-content p {
    margin: 12px 0;
    line-height: 1.6;
    color: #4a5568;
}

/* 加载动画 */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 重试按钮样式 */
.retry-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-left: 8px;
    transition: background-color 0.2s ease;
}

.retry-btn:hover {
    background: #5a67d8;
}

/* 渐进式加载按钮样式 */
.progressive-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.progressive-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.progressive-btn:active {
    transform: translateY(0);
}

/* 分析错误样式 */
.analysis-error {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #fed7d7;
    border: 1px solid #feb2b2;
    border-radius: 8px;
    margin: 12px 0;
}

.analysis-error .error-icon {
    font-size: 1.2rem;
}

.analysis-error .error-message {
    flex: 1;
    color: #742a2a;
}

/* 展开/收起图标 */
.toggle-icon {
    margin-left: 8px;
    color: #718096;
    font-size: 0.8rem;
    transition: transform 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .bazi-basic-info {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .bazi-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .pillars-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .shishen-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dayun-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dimension-header {
        padding: 12px 16px;
    }
    
    .dimension-content {
        padding: 0 16px 16px 16px;
    }
    
    .progressive-header {
        padding: 16px;
    }
    
    .progressive-header h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .pillars-grid,
    .shishen-grid,
    .dayun-grid {
        grid-template-columns: 1fr;
        max-width: 200px;
    }
    
    .dimension-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .dimension-status {
        align-self: flex-end;
    }
}

/* 打印样式 */
@media print {
    .progressive-analysis-section {
        break-inside: avoid;
    }
    
    .dimension-placeholder {
        break-inside: avoid;
    }
    
    .loading-spinner {
        display: none;
    }
    
    .retry-btn {
        display: none;
    }
    
    .dimension-status.loading {
        display: none;
    }
}
