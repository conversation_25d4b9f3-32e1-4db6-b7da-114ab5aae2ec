/**
 * 报告显示功能
 * 处理报告在页面中的显示逻辑
 */

// 初始化模块日志记录器
const displayLogger = window.BaziLogger ? window.BaziLogger.forModule('ReportsDisplay') : {
    debug: console.log,
    info: console.log,
    warn: console.warn,
    error: console.error
};

// 显示分析状态和历史报告
async function displayAnalysisAndReports(analysis, reportsResult) {
    displayLogger.info('显示分析状态和历史报告', reportsResult);
    const reportsContainer = document.getElementById('reportsList');
    
    // 先显示骨架屏
    window.reportUIHelper.showSkeletonScreen(reportsContainer);
    
    let contentHtml = '';
    
    // 先添加分析状态部分
    contentHtml += window.reportsManager.generateRunningAnalysisHtml(analysis);
    
    // 获取报告数据
    const reports = reportsResult.reports || reportsResult;
    
    // 添加历史报告部分
    if (reports && reports.length > 0) {
        contentHtml += window.reportsManager.generateReportsHtml(reports);
    } else {
        contentHtml += `
            <div class="no-reports-section">
                <div class="empty-state" style="padding: 20px; margin-top: 20px;">
                    <div class="empty-icon">📋</div>
                    <h3>暂无历史报告</h3>
                    <p>分析完成后，您的报告将显示在这里</p>
                </div>
            </div>
        `;
    }
    
    // 延迟更新，减少界面卡顿
    setTimeout(() => {
        // 更新页面内容
        reportsContainer.innerHTML = contentHtml;
        
        // 如果有分页信息，初始化无限滚动
        if (reportsResult && reportsResult.hasMore) {
            window.infiniteScrollController = window.reportUIHelper.initInfiniteScroll();
        }
    }, 100);
}

// 只显示历史报告
async function displayReportsOnly(reportsResult) {
    displayLogger.info('只显示历史报告', reportsResult);
    const reportsContainer = document.getElementById('reportsList');
    
    // 先显示骨架屏
    window.reportUIHelper.showSkeletonScreen(reportsContainer);
    
    // 获取报告数据
    const reports = reportsResult && reportsResult.reports ? reportsResult.reports : reportsResult;
    
    // 检查报告是否有效
    const hasReports = reports && Array.isArray(reports) && reports.length > 0;
    displayLogger.debug('是否有报告数据:', hasReports, '报告数量:', hasReports ? reports.length : 0);
    
    // 延迟加载真实内容，减少界面卡顿
    setTimeout(() => {
        let contentHtml = '';
        
        if (hasReports) {
            contentHtml = window.reportsManager.generateReportsHtml(reports);
        } else {
            contentHtml = window.reportsManager.generateEmptyStateHtml();
        }
        
        reportsContainer.innerHTML = contentHtml;
        
        // 如果有分页信息，初始化无限滚动
        if (reportsResult && reportsResult.hasMore) {
            window.infiniteScrollController = window.reportUIHelper.initInfiniteScroll();
        }
    }, 100);
}

// 刷新报告列表
async function refreshReports() {
    displayLogger.info('刷新报告列表');
    const reportsContainer = document.getElementById('reportsList');
    
    // 显示骨架屏
    window.reportUIHelper.showSkeletonScreen(reportsContainer);
    
    try {
        // 强制清除所有报告缓存
        const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
        cacheKeys.forEach(key => sessionStorage.removeItem(key));
        displayLogger.debug('已清除所有报告缓存，强制重新获取数据');
        
        // 重置滚动控制器
        if (window.infiniteScrollController) {
            window.infiniteScrollController.reset();
        }
        
        // 设置短暂延迟，确保缓存已被完全清除
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 获取第一页报告
        const result = await window.reportsManager.fetchHistoryReports(1);
        displayLogger.debug('刷新获取到的报告:', result);
        
        // 额外检查：如果在刷新前新添加了报告，但结果中没有显示
        // 通过检查本地存储中的报告，确保新添加的报告被显示
        if (result && result.reports) {
            // 尝试从localStorage获取报告
            try {
                const localReports = JSON.parse(localStorage.getItem('baziReports') || '[]');
                if (localReports.length > 0) {
                    // 检查最新报告是否在结果中
                    const latestLocalReport = localReports[0]; // 最新的本地报告
                    const isLatestIncluded = result.reports.some(report => report.id === latestLocalReport.id);
                    
                    if (!isLatestIncluded) {
                        displayLogger.warn('刷新结果中未包含最新本地报告，尝试手动添加');
                        
                        // 手动添加最新报告到结果中
                        result.reports.unshift({
                            ...latestLocalReport,
                            source: 'local'
                        });
                        
                        // 重新排序
                        result.reports.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    }
                }
            } catch (e) {
                displayLogger.error('处理本地报告时出错:', e);
            }
        }
        
        // 显示报告
        await displayReportsOnly(result);
        
        // 显示刷新成功消息
        const toast = document.createElement('div');
        toast.className = 'toast-message success';
        toast.textContent = '刷新成功！';
        document.body.appendChild(toast);
        
        // 3秒后自动移除消息
        setTimeout(() => {
            toast.classList.add('toast-fade-out');
            setTimeout(() => toast.remove(), 500);
        }, 3000);
    } catch (error) {
        displayLogger.error('刷新报告失败:', error);
        
        // 显示错误信息
        reportsContainer.innerHTML = `
            <div class="error-state">
                <div class="error-icon">❌</div>
                <h3>加载失败</h3>
                <p>刷新报告列表时出错，请稍后重试</p>
                <button onclick="refreshReports()" class="action-btn primary">重试</button>
            </div>
        `;
    }
}