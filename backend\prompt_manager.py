#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理器
统一管理和优化提示词系统
"""

import json
import os
import logging
import hashlib
import time
from typing import Dict, Any, Optional, List
from functools import lru_cache
from datetime import datetime

logger = logging.getLogger(__name__)

class PromptManager:
    """提示词管理器 - 统一管理所有分析维度的提示词"""
    
    def __init__(self, prompts_dir: str = None, version: str = "current"):
        """初始化提示词管理器
        
        Args:
            prompts_dir: 提示词目录路径
            version: 使用的提示词版本
        """
        self.prompts_dir = prompts_dir or os.path.join(os.path.dirname(__file__), "prompts")
        self.version = version
        self.cache = {}
        self.cache_ttl = 3600  # 缓存1小时
        
        # 加载基础配置
        self._load_base_config()
    
    def _load_base_config(self):
        """加载基础配置"""
        try:
            # 加载系统级提示词
            self.system_prompts = self._load_json_file("base/system_prompts.json", {
                "role": "你是一位专业的八字命理分析师，精通十神理论",
                "principles": [
                    "必须严格按照十神理论进行分析，禁止使用五行理论",
                    "只能根据八字中实际出现的十神进行分析，不得添加未出现的十神特点",
                    "天干有什么十神就只说该十神的特点，没有的十神特点不允许提及"
                ]
            })
            
            # 加载通用约束
            self.global_constraints = self._load_json_file("base/constraints.json", {
                "output_format": "markdown",
                "max_length": 800,
                "tone": "professional",
                "person": "third_person"
            })
            
            # 加载维度索引
            self.dimension_index = self._load_json_file("index.json", {})
            
        except Exception as e:
            logger.error(f"加载基础配置失败: {str(e)}")
            # 使用默认配置
            self.system_prompts = {"role": "你是一位专业的八字命理分析师"}
            self.global_constraints = {"output_format": "markdown"}
            self.dimension_index = {}
    
    def _load_json_file(self, relative_path: str, default: Any = None) -> Any:
        """加载JSON文件"""
        file_path = os.path.join(self.prompts_dir, relative_path)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载文件 {relative_path} 失败: {str(e)}")
        return default or {}
    
    def _get_cache_key(self, dimension: str, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{dimension}_{self.version}_{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @lru_cache(maxsize=100)
    def _load_dimension_config(self, dimension: str) -> Dict[str, Any]:
        """加载维度特定配置（带缓存）"""
        # 从索引获取文件名
        if "prompts_index" in self.dimension_index:
            filename = self.dimension_index["prompts_index"].get(dimension)
        else:
            # 使用默认映射
            dimension_map = {
                "性格": "personality.txt",
                "感情": "relationship.txt", 
                "职业": "career.txt",
                "健康": "health.txt",
                "日主强弱": "strength.txt",
                "学业": "education.txt",
                "2025运势": "fortune_2025.txt"
            }
            filename = dimension_map.get(dimension, "common.txt")
        
        if not filename:
            return {}
        
        # 尝试加载JSON格式
        json_path = filename.replace('.txt', '.json')
        config = self._load_json_file(json_path)
        
        if config:
            return config
        
        # 回退到文本格式
        txt_path = os.path.join(self.prompts_dir, filename)
        if os.path.exists(txt_path):
            try:
                with open(txt_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {
                    "version": "1.0.0",
                    "dimension": dimension,
                    "content": content,
                    "format": "text"
                }
            except Exception as e:
                logger.error(f"加载文本文件 {filename} 失败: {str(e)}")
        
        return {}
    
    def build_prompt(self, dimension: str, bazi_info: str, **kwargs) -> str:
        """构建完整的分析提示词
        
        Args:
            dimension: 分析维度
            bazi_info: 八字信息
            **kwargs: 其他参数
            
        Returns:
            str: 完整的提示词
        """
        try:
            # 检查缓存
            cache_key = self._get_cache_key(dimension, bazi_info=bazi_info[:100], **kwargs)
            if cache_key in self.cache:
                cached_item = self.cache[cache_key]
                if time.time() - cached_item['timestamp'] < self.cache_ttl:
                    return cached_item['prompt']
            
            # 构建提示词
            prompt_parts = []
            
            # 1. 系统角色设定
            if "role" in self.system_prompts:
                prompt_parts.append(f"【角色设定】\n{self.system_prompts['role']}")
            
            # 2. 分析原则
            if "principles" in self.system_prompts:
                principles_text = "\n".join([f"- {p}" for p in self.system_prompts["principles"]])
                prompt_parts.append(f"【分析原则】\n{principles_text}")
            
            # 3. 八字信息
            prompt_parts.append(f"【八字信息】\n{bazi_info}")
            
            # 4. 维度特定要求
            dimension_config = self._load_dimension_config(dimension)
            if dimension_config:
                if dimension_config.get("format") == "text":
                    # 文本格式的提示词
                    prompt_parts.append(f"【分析要求】\n{dimension_config['content']}")
                else:
                    # JSON格式的提示词
                    if "analysis_template" in dimension_config:
                        template = dimension_config["analysis_template"]
                        prompt_parts.append(f"【分析要求】\n请对【{dimension}】维度进行分析")
                        
                        # 添加结构化要求
                        if "structure" in template:
                            structure_text = self._build_structure_prompt(template["structure"], dimension)
                            prompt_parts.append(f"【输出结构】\n{structure_text}")
            
            # 5. 输出格式要求
            format_requirements = self._build_format_requirements(dimension_config)
            if format_requirements:
                prompt_parts.append(f"【格式要求】\n{format_requirements}")
            
            # 组装最终提示词
            final_prompt = "\n\n".join(prompt_parts)
            
            # 缓存结果
            self.cache[cache_key] = {
                'prompt': final_prompt,
                'timestamp': time.time()
            }
            
            return final_prompt
            
        except Exception as e:
            logger.error(f"构建提示词失败 - 维度: {dimension}, 错误: {str(e)}")
            # 返回简化的提示词
            return f"请根据以下八字信息分析【{dimension}】：\n\n{bazi_info}\n\n请使用Markdown格式输出分析结果。"
    
    def _build_structure_prompt(self, structure: List[Dict], dimension: str) -> str:
        """构建结构化提示词"""
        structure_parts = []
        for section in structure:
            title = section.get("title", "").replace("{dimension}", dimension)
            structure_parts.append(title)
            
            if "content" in section:
                content = section["content"].replace("{dimension}", dimension)
                structure_parts.append(content)
            
            if "subsections" in section:
                for subsection in section["subsections"]:
                    sub_title = subsection.get("title", "")
                    structure_parts.append(sub_title)
                    if "prompt" in subsection:
                        sub_prompt = subsection["prompt"].replace("{dimension}", dimension)
                        structure_parts.append(f"分析要求：{sub_prompt}")
        
        return "\n".join(structure_parts)
    
    def _build_format_requirements(self, dimension_config: Dict) -> str:
        """构建格式要求"""
        requirements = []
        
        # 从维度配置获取格式要求
        if "output_format" in dimension_config:
            format_config = dimension_config["output_format"]
            if format_config.get("format") == "markdown":
                requirements.append("- 必须使用Markdown格式输出")
            if "max_length" in format_config:
                requirements.append(f"- 控制在{format_config['max_length']}字以内")
            if format_config.get("person") == "third_person":
                requirements.append("- 使用第三人称描述，避免使用'你'")
        
        # 添加全局约束
        if self.global_constraints.get("output_format") == "markdown":
            requirements.append("- 使用`##`表示一级标题，`###`表示二级标题")
            requirements.append("- 使用`**文字**`进行重点强调")
            requirements.append("- 段落之间保留空行以确保排版正确")
        
        return "\n".join(requirements) if requirements else ""
    
    def validate_prompt_config(self, config: Dict[str, Any]) -> List[str]:
        """验证提示词配置
        
        Args:
            config: 提示词配置
            
        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []
        
        # 检查必需字段
        required_fields = ["version", "dimension"]
        for field in required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        # 检查版本格式
        if "version" in config:
            version = config["version"]
            if not isinstance(version, str) or not version:
                errors.append("版本号必须是非空字符串")
        
        # 检查维度名称
        if "dimension" in config:
            dimension = config["dimension"]
            valid_dimensions = ["性格", "感情", "职业", "健康", "日主强弱", "学业", "2025运势"]
            if dimension not in valid_dimensions:
                errors.append(f"无效的维度名称: {dimension}")
        
        return errors
    
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
        # 清除lru_cache
        self._load_dimension_config.cache_clear()
    
    def get_available_dimensions(self) -> List[str]:
        """获取可用的分析维度"""
        if "prompts_index" in self.dimension_index:
            return list(self.dimension_index["prompts_index"].keys())
        return ["性格", "感情", "职业", "健康", "日主强弱", "学业", "2025运势"]
    
    def get_prompt_stats(self) -> Dict[str, Any]:
        """获取提示词统计信息"""
        return {
            "cache_size": len(self.cache),
            "available_dimensions": len(self.get_available_dimensions()),
            "version": self.version,
            "cache_hit_rate": getattr(self._load_dimension_config, 'cache_info', lambda: None)()
        }
