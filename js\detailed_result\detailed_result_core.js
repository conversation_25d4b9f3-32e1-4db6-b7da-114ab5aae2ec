/**
 * @file-group detailed_result
 * @description 八字分析结果查看器核心模块
 * @related-files 
 * - detailed_result_loader.js
 * - detailed_result_display.js
 * - detailed_result_bazi_info.js
 * - detailed_result_utils.js
 * - detailed_result_events.js
 */

// 注意：这些函数在其他文件中定义，通过全局作用域访问
// loadAnalysisResult, loadResultFromAPI, loadResultFromFile, loadResultFromLocal, loadHistoryReport, loadAdminReport 在 detailed_result_loader.js 中定义
// displayResult, showError, showEmptyState, updateGenerateTime, showAnalysisProgress 在 detailed_result_display.js 中定义
// setupEventListeners 在 detailed_result_events.js 中定义
// loadCustomTitle, saveTitle, getCurrentReportKey, exportSeparateFiles 在 detailed_result_utils.js 中定义

/**
 * 八字分析结果查看器类
 */
class DetailedResultViewer {
    /**
     * 构造函数
     */
    constructor() {
        this.init();
    }
    
    /**
     * 初始化函数
     */
    init() {
        this.loadAnalysisResult();
        this.setupEventListeners();
        this.loadCustomTitle();
    }
    
    // 委托给相应模块的方法
    loadAnalysisResult() { return loadAnalysisResult.call(this); }
    loadResultFromAPI(cardKey, reportId) { return loadResultFromAPI.call(this, cardKey, reportId); }
    loadResultFromFile(filePath) { return loadResultFromFile.call(this, filePath); }
    loadResultFromLocal(reportId) { return loadResultFromLocal.call(this, reportId); }
    loadHistoryReport(cardKey, reportId) { return loadHistoryReport.call(this, cardKey, reportId); }
    loadAdminReport(reportId, adminToken) { return loadAdminReport.call(this, reportId, adminToken); }
    displayResult(data) { return displayResult.call(this, data); }
    showError(message, details) { return showError.call(this, message, details); }
    showEmptyState() { return showEmptyState.call(this); }
    updateGenerateTime() { return updateGenerateTime.call(this); }
    showAnalysisProgress(progressData) { return showAnalysisProgress.call(this, progressData); }
    setupEventListeners() { return setupEventListeners.call(this); }
    loadCustomTitle() { return loadCustomTitle.call(this); }
    saveTitle(title) { return saveTitle.call(this, title); }
    getCurrentReportKey() { return getCurrentReportKey.call(this); }
    exportSeparateFiles() { return exportSeparateFiles.call(this); }
}

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', function() {
    window.detailedResultViewer = new DetailedResultViewer();
});

// 将类暴露到全局作用域
window.DetailedResultViewer = DetailedResultViewer;