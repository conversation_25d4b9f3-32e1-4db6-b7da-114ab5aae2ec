// 清理localStorage中的无效卡密
function clearInvalidCards() {
    console.log('开始清理localStorage中的无效卡密...');
    
    // 清理已验证的卡密
    const validatedCards = localStorage.getItem('validatedCards');
    if (validatedCards) {
        try {
            const cards = JSON.parse(validatedCards);
            console.log('找到已验证卡密:', cards.length, '个');
            
            // 移除无效的卡密
            const invalidCards = ['2twly9CSBgXzi9iH', '2Djh5a6lNAaukw5q'];
            const filteredCards = cards.filter(card => !invalidCards.includes(card.cardKey));
            
            if (filteredCards.length !== cards.length) {
                localStorage.setItem('validatedCards', JSON.stringify(filteredCards));
                console.log('已清理无效卡密，剩余:', filteredCards.length, '个');
            } else {
                console.log('没有找到需要清理的无效卡密');
            }
        } catch (error) {
            console.error('解析卡密数据失败:', error);
            localStorage.removeItem('validatedCards');
            console.log('已清空所有卡密数据');
        }
    } else {
        console.log('localStorage中没有找到卡密数据');
    }
    
    // 清理其他可能的缓存
    const keysToCheck = [
        'ongoingAnalysis',
        'completedAnalysis',
        'baziReports',
        'latestBaziResult'
    ];
    
    keysToCheck.forEach(key => {
        const data = localStorage.getItem(key);
        if (data) {
            try {
                const parsed = JSON.parse(data);
                console.log(`检查 ${key}:`, typeof parsed, parsed);
            } catch (error) {
                console.log(`${key} 不是JSON格式:`, data);
            }
        }
    });
    
    // 清理sessionStorage中的缓存
    const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
    if (cacheKeys.length > 0) {
        cacheKeys.forEach(key => sessionStorage.removeItem(key));
        console.log('已清理sessionStorage缓存:', cacheKeys.length, '个');
    }
    
    console.log('清理完成！');
}

// 立即执行清理
clearInvalidCards();
