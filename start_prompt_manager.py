#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词管理系统启动脚本
提供多种启动方式的统一入口
"""

import os
import sys
import argparse
import subprocess
import webbrowser
import time
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    return Path(__file__).parent

def check_dependencies():
    """检查依赖是否安装"""
    required_modules = [
        'flask', 'flask_cors', 'requests', 'tkinter'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
            else:
                __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def start_web_server():
    """启动Web服务器"""
    print("🚀 启动Web服务器...")
    
    backend_dir = get_project_root() / 'backend'
    os.chdir(backend_dir)
    
    try:
        # 尝试启动主应用
        if (backend_dir / 'app.py').exists():
            subprocess.run([sys.executable, 'app.py'], check=True)
        else:
            # 如果主应用不存在，启动提示词API服务器
            subprocess.run([sys.executable, 'prompt_api.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ Web服务器已停止")
        return True

def start_desktop_gui():
    """启动桌面GUI"""
    print("🖥️ 启动桌面GUI...")
    
    backend_dir = get_project_root() / 'backend'
    gui_script = backend_dir / 'prompt_gui.py'
    
    if not gui_script.exists():
        print(f"❌ GUI脚本不存在: {gui_script}")
        return False
    
    try:
        os.chdir(backend_dir)
        subprocess.run([sys.executable, 'prompt_gui.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动桌面GUI失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ 桌面GUI已关闭")
        return True

def open_web_interface():
    """打开Web界面"""
    print("🌐 打开Web管理界面...")
    
    # 等待服务器启动
    time.sleep(2)
    
    urls = [
        'http://localhost:5000/prompt_manager.html',
        'http://localhost:5000/admin.html',
        'http://localhost:5001/prompt_manager.html',  # 如果使用独立的提示词API服务器
    ]
    
    for url in urls:
        try:
            webbrowser.open(url)
            print(f"✅ 已打开: {url}")
            break
        except Exception as e:
            print(f"⚠️ 无法打开 {url}: {e}")
            continue
    else:
        print("❌ 无法打开Web界面，请手动访问 http://localhost:5000/prompt_manager.html")

def run_tests():
    """运行测试"""
    print("🧪 运行系统测试...")
    
    backend_dir = get_project_root() / 'backend'
    test_script = backend_dir / 'test_optimized_prompt_system.py'
    
    if not test_script.exists():
        print(f"❌ 测试脚本不存在: {test_script}")
        return False
    
    try:
        os.chdir(backend_dir)
        subprocess.run([sys.executable, 'test_optimized_prompt_system.py'], check=True)
        print("✅ 测试完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_status():
    """显示系统状态"""
    print("📊 系统状态检查...")
    
    project_root = get_project_root()
    backend_dir = project_root / 'backend'
    
    # 检查关键文件
    key_files = [
        'backend/prompt_manager.py',
        'backend/prompt_evaluator.py',
        'backend/prompt_version_manager.py',
        'backend/prompt_api.py',
        'backend/prompt_gui.py',
        'prompt_manager.html',
        'admin.html',
        'js/prompt_manager.js',
        'js/admin.js'
    ]
    
    print("\n📁 文件检查:")
    for file_path in key_files:
        full_path = project_root / file_path
        status = "✅" if full_path.exists() else "❌"
        print(f"  {status} {file_path}")
    
    # 检查依赖
    print("\n📦 依赖检查:")
    deps_ok = check_dependencies()
    print(f"  {'✅' if deps_ok else '❌'} Python依赖")
    
    # 检查端口
    print("\n🌐 端口检查:")
    import socket
    
    ports_to_check = [5000, 5001]
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"  ⚠️ 端口 {port} 已被占用")
        else:
            print(f"  ✅ 端口 {port} 可用")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="提示词管理系统启动工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python start_prompt_manager.py web          # 启动Web服务器
  python start_prompt_manager.py gui          # 启动桌面GUI
  python start_prompt_manager.py both         # 同时启动Web和GUI
  python start_prompt_manager.py test         # 运行测试
  python start_prompt_manager.py status       # 显示系统状态
        """
    )
    
    parser.add_argument(
        'mode',
        choices=['web', 'gui', 'both', 'test', 'status'],
        help='启动模式'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=5000,
        help='Web服务器端口 (默认: 5000)'
    )
    
    args = parser.parse_args()
    
    print("🧠 提示词管理系统启动工具")
    print("=" * 50)
    
    # 检查依赖
    if args.mode in ['web', 'gui', 'both', 'test']:
        if not check_dependencies():
            sys.exit(1)
    
    # 根据模式执行相应操作
    if args.mode == 'status':
        show_status()
    
    elif args.mode == 'test':
        success = run_tests()
        sys.exit(0 if success else 1)
    
    elif args.mode == 'gui':
        success = start_desktop_gui()
        sys.exit(0 if success else 1)
    
    elif args.mode == 'web':
        if not args.no_browser:
            # 在后台启动浏览器打开
            import threading
            browser_thread = threading.Thread(target=open_web_interface)
            browser_thread.daemon = True
            browser_thread.start()
        
        success = start_web_server()
        sys.exit(0 if success else 1)
    
    elif args.mode == 'both':
        print("🚀 同时启动Web服务器和桌面GUI...")
        
        # 在后台启动Web服务器
        import threading
        import multiprocessing
        
        def start_web_background():
            start_web_server()
        
        web_process = multiprocessing.Process(target=start_web_background)
        web_process.start()
        
        # 等待Web服务器启动
        time.sleep(3)
        
        if not args.no_browser:
            open_web_interface()
        
        # 启动桌面GUI
        try:
            start_desktop_gui()
        finally:
            # 清理Web服务器进程
            web_process.terminate()
            web_process.join()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 程序已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        sys.exit(1)
