/* Noto Sans SC 本地字体定义 */

/* Light 300 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url('../fonts/noto-sans-sc-300.ttf') format('truetype');
}

/* Regular 400 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('../fonts/noto-sans-sc-400.ttf') format('truetype');
}

/* Medium 500 - 使用 400 字重作为回退 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url('../fonts/noto-sans-sc-400.ttf') format('truetype');
}

/* SemiBold 600 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('../fonts/noto-sans-sc-600.ttf') format('truetype');
}

/* Bold 700 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('../fonts/noto-sans-sc-700.ttf') format('truetype');
}

/* 字体回退定义 */
body {
  font-family: 'Noto Sans SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
}