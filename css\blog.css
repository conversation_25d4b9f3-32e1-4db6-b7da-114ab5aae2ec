/* 博客页面特定样式 */
        .blog-header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 0.8s ease-out;
        }
        
        .blog-hero {
            background: var(--primary-gradient);
            color: white;
            padding: 60px 40px;
            border-radius: var(--border-radius);
            margin-bottom: 40px;
            text-align: center;
        }
        
        .blog-hero h1 {
            font-size: 2.5rem;
            margin-bottom: 16px;
            font-weight: 700;
        }
        
        .blog-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .blog-nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }
        
        .nav-button {
            padding: 12px 24px;
            background: var(--bg-card);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .nav-button:hover,
        .nav-button.active {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }
        
        .blog-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .articles-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .article-card {
            background: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            cursor: pointer;
            animation: fadeInUp 0.5s ease-out;
        }
        
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }
        
        .article-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .article-category {
            background: var(--primary-gradient);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .article-date {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .article-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .article-excerpt {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 16px;
        }
        
        .article-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        
        .tag {
            background: var(--bg-primary);
            color: var(--text-secondary);
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 0.8rem;
            border: 1px solid var(--border-color);
        }
        
        .read-more {
            color: var(--primary-color);
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: var(--transition);
        }
        
        .read-more:hover {
            opacity: 0.8;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .sidebar-card {
            background: var(--bg-card);
            border-radius: var(--border-radius);
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
        }
        
        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .popular-articles {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .popular-item {
            display: flex;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .popular-item:hover {
            background: var(--bg-primary);
        }
        
        .popular-number {
            background: var(--primary-gradient);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .popular-content {
            flex: 1;
        }
        
        .popular-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
            line-height: 1.4;
            margin-bottom: 4px;
        }
        
        .popular-date {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        .categories-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-radius: 8px;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .category-item:hover {
            background: var(--bg-primary);
        }
        
        .category-name {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .category-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .back-to-home {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: var(--transition);
        }
        
        .back-to-home:hover {
            opacity: 0.8;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            gap: 5px;
        }
        
        .page-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 12px;
            border-radius: 8px;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.2s ease;
        }
        
        .page-button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .page-button.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }
        
        .page-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
            background-color: var(--bg-secondary);
            color: var(--text-secondary);
        }
        
        .page-ellipsis {
            margin: 0 5px;
            color: var(--text-secondary);
        }
        
        /* 专家简介卡片样式 */
        .expert-profile {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            border: 2px solid rgba(102, 126, 234, 0.2);
        }
        
        .expert-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .expert-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            box-shadow: var(--shadow-md);
        }
        
        .expert-details h4 {
            margin: 0 0 5px 0;
            color: var(--text-primary);
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .expert-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .expert-credentials {
            margin-bottom: 20px;
        }
        
        .credential-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 6px;
            transition: var(--transition);
        }
        
        .credential-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        
        .credential-icon {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }
        
        .credential-text {
            font-size: 0.9rem;
            color: var(--text-primary);
            line-height: 1.4;
        }
        
        .contact-info {
            margin-bottom: 20px;
        }
        
        .contact-title {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .contact-icon {
            font-size: 1rem;
            width: 20px;
            text-align: center;
        }
        
        .contact-text {
            flex: 1;
            font-size: 0.9rem;
            color: var(--text-primary);
        }
        
        .copy-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .copy-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }
        
        .consultation-cta {
            text-align: center;
        }
        
        .consultation-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            width: 100%;
            box-shadow: var(--shadow-md);
        }
        
        .consultation-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .blog-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .blog-hero {
                padding: 40px 20px;
            }
            
            .blog-hero h1 {
                font-size: 2rem;
            }
            
            .article-card {
                padding: 20px;
            }
            
            .nav-button {
                padding: 10px 16px;
                font-size: 0.9rem;
            }
            
            .expert-info {
                flex-direction: column;
                text-align: center;
            }
            
            .contact-item {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }
            
            .pagination {
                gap: 2px;
            }
            
            .page-button {
                min-width: 32px;
                height: 32px;
                padding: 0 8px;
                font-size: 0.8rem;
            }
        }
        
        .container {
            max-width: 1200px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }