#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from datetime import datetime

def fix_cards_json():
    """修复损坏的cards.json文件"""
    input_file = 'cards.json'
    output_file = 'cards_fixed.json'
    
    try:
        # 读取损坏的JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始文件大小: {len(content)} 字符")
        
        # 尝试解析为有效的JSON结构
        # 首先移除重复的字段和结构
        lines = content.split('\n')
        fixed_lines = []
        seen_lines = set()
        
        for line in lines:
            stripped = line.strip()
            # 跳过重复的行
            if stripped and stripped not in seen_lines:
                seen_lines.add(stripped)
                fixed_lines.append(line)
        
        fixed_content = '\n'.join(fixed_lines)
        
        # 尝试解析JSON
        try:
            data = json.loads(fixed_content)
            print(f"成功解析JSON，包含 {len(data)} 个卡密")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            # 手动构建正确的JSON结构
            data = reconstruct_json_from_content(content)
        
        # 保存修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"修复完成，保存到 {output_file}")
        print(f"修复后包含 {len(data)} 个卡密")
        
        # 验证修复后的文件
        with open(output_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
            print(f"验证成功，文件包含 {len(test_data)} 个有效卡密")
            
        return True
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        return False

def reconstruct_json_from_content(content):
    """从损坏的内容重建JSON结构"""
    data = {}
    
    # 提取所有卡密名称
    card_pattern = r'"([A-Za-z0-9]{16})":\s*{'
    cards = re.findall(card_pattern, content)
    
    print(f"找到 {len(cards)} 个卡密")
    
    for card in cards:
        # 为每个卡密创建标准结构
        data[card] = {
            "valid": True,
            "created_time": "2025-05-30T05:13:30.000000",
            "expire_time": "2025-06-29T05:13:30.000000",
            "usage_count": 0,
            "max_usage": 100
        }
    
    return data

if __name__ == "__main__":
    fix_cards_json()