/* 补充样式，确保基本功能在主CSS加载失败时也能工作 */
.toast-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

/* 分隔符样式 - 用于替换单独的短横线 */
.section-spacer {
    height: 20px;
    margin: 5px 0;
    position: relative;
}

/* 防止a. b. c.标记段落内部不恰当换行 */
.no-break-paragraph {
    margin-bottom: 10px; /* 确保段落之间有足够间距 */
}

.no-break-paragraph strong {
    display: inline-block; /* 确保a. b. c.标记与其内容不分离 */
    margin-right: 0; /* 减少a.和后面内容的间距 */
}

/* 英文免责声明样式，防止单词被断行 */
.english-disclaimer {
    white-space: normal;
    word-break: keep-all; /* 防止单词内断行 */
    word-wrap: normal; /* 保持完整单词 */
    hyphens: none; /* 禁用连字符 */
    padding: 15px;
    margin: 15px 0;
    background-color: #f8f9fa;
    border-left: 4px solid #6366f1;
    border-radius: 4px;
    font-style: italic;
    color: #4b5563;
}

.toast-message.fade-out {
    animation: fadeOut 0.5s ease forwards;
}

.edit-hint {
    font-size: 0.8rem;
    color: var(--text-secondary, #666);
    margin-top: 5px;
    font-style: italic;
}

.detailed-footer {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: var(--bg-card, #fff);
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 100;
    padding: 8px 0;
    justify-content: space-around;
}

.detailed-footer-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none;
    color: var(--text-primary, #333);
    padding: 5px 0;
    cursor: pointer;
}

.footer-icon {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.footer-text {
    font-size: 0.8rem;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, 20px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translate(-50%, 0); }
    to { opacity: 0; transform: translate(-50%, 20px); }
}

@media (max-width: 768px) {
    .detailed-footer {
        display: flex;
    }
    
    /* 确保按钮文本始终显示 */
    .btn-text {
        display: inline-block !important;
        margin-left: 6px;
    }
    
    .detailed-action-btn {
        padding: 8px 12px;
        min-height: 44px;
        font-size: 14px;
    }
    
    .detailed-container {
        padding-bottom: 60px;
        padding-left: 0;
        padding-right: 0;
    }
    
    .detailed-result-container {
        margin: 0;
        padding: 16px;
        border-radius: 0;
    }
    
    .detailed-result-header h2 {
        font-size: 22px;
        line-height: 1.3;
    }
    
    .detailed-dimension-section {
        margin-bottom: 16px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }
    
    .detailed-dimension-header {
        padding: 16px 18px;
        font-size: 15px;
        min-height: 52px;
        font-weight: 600;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .detailed-dimension-content {
        padding: 18px;
        font-size: 15px;
        line-height: 1.7;
        background: white;
    }
}

@media (max-width: 480px) {
    .detailed-action-btn {
        padding: 8px 10px;
        min-height: 40px;
    }
    
    .detailed-result-container {
        padding: 12px;
    }
    
    .detailed-dimension-header {
        padding: 14px 16px;
        font-size: 14px;
        min-height: 48px;
    }
    
    .detailed-dimension-content {
        padding: 16px;
        font-size: 14px;
    }
}

/* 删除独特的导出按钮样式，使用通用按钮样式 */
/* .detailed-export-btn 样式已在layout.css中统一定义，此处删除 */

.export-icon {
    font-size: 16px;
} 