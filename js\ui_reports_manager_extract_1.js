/**
 * 报告管理器 - 数据提取工具(1)
 * 处理报告数据中各种信息的提取
 */

// 添加一个辅助方法，确保报告数据包含必要的信息
HistoryReportsManager.prototype.ensureReportData = function(report) {
    if (!report) return;
    
    console.log('确保报告数据完整:', report.id);
    
    // 从各种可能的位置提取生日和性别信息
    let birthDate, gender;
    
    // 检查顶层属性
    if (report.birthDate) birthDate = report.birthDate;
    if (report.birth_date) birthDate = report.birth_date;
    if (report.gender) gender = report.gender;
    if (report.sex !== undefined) {
        gender = report.sex === 1 ? '男' : '女';
    }
    
    // 检查 report.data
    if (report.data) {
        // 从llm_analysis文件路径提取信息
        if ((!birthDate || !gender) && report.data.llm_analysis && report.data.llm_analysis.file_path) {
            const filePath = report.data.llm_analysis.file_path;
            console.log('尝试从llm_analysis文件路径提取信息:', filePath);
            
            const extractedInfo = this.extractInfoFromFilePath(filePath);
            if (extractedInfo) {
                if (!birthDate) {
                    // 优先使用完整的日期信息
                    birthDate = extractedInfo.fullDate || extractedInfo.year;
                    console.log('从文件路径提取的日期信息:', birthDate);
                }
                if (!gender && extractedInfo.gender) {
                    gender = extractedInfo.gender;
                    console.log('从文件路径提取的性别:', gender);
                }
            }
        }
        
        // 从bazi_url提取日期信息
        if (!birthDate && report.data.bazi_url) {
            console.log('尝试从bazi_url提取日期:', report.data.bazi_url);
            const dateMatch = report.data.bazi_url.match(/d=([^&]+)/);
            if (dateMatch && dateMatch[1]) {
                try {
                    // URL解码
                    const decodedDate = decodeURIComponent(dateMatch[1]);
                    console.log('从URL解码的日期:', decodedDate);
                    
                    // 提取年月日
                    const dateTimeParts = decodedDate.split(' ');
                    if (dateTimeParts.length > 0) {
                        const datePart = dateTimeParts[0];
                        const dateParts = datePart.split('-');
                        if (dateParts.length >= 3) {
                            birthDate = `${dateParts[0]}年${dateParts[1]}月${dateParts[2]}日`;
                            
                            // 如果有时辰信息，添加到日期中
                            if (dateTimeParts.length > 1) {
                                const timePart = dateTimeParts[1];
                                // 确保时辰格式统一，添加"时"字
                                const formattedTime = timePart.endsWith('时') ? timePart : timePart + '时';
                                birthDate += ` ${formattedTime}`;
                            }
                            
                            console.log('从bazi_url提取的birthDate:', birthDate);
                        }
                    }
                    
                    // 检查性别参数 s=1 表示男性，s=0 表示女性
                    if (!gender) {
                        const sexMatch = report.data.bazi_url.match(/s=([01])/);
                        if (sexMatch && sexMatch[1]) {
                            const sexValue = parseInt(sexMatch[1]);
                            gender = sexValue === 1 ? '男' : '女';
                            console.log('从bazi_url提取的gender:', gender);
                        }
                    }
                } catch (e) {
                    console.error('解析bazi_url中的日期失败:', e);
                }
            }
        }
    }
    
    // 确保报告对象包含这些关键信息
    if (birthDate && !report.birthDate) {
        report.birthDate = birthDate;
        console.log('  添加缺失的 birthDate:', birthDate);
    }
    
    if (gender && !report.gender) {
        report.gender = gender;
        console.log('  添加缺失的 gender:', gender);
    }
    
    return report;
}; 