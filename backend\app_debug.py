# 创建一个带调试信息的app.py副本，用于调试validate_card问题
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, request, jsonify
from card_manager import CardManager
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_validate.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 初始化CardManager
logger.info("初始化CardManager...")
from card_manager_singleton import get_card_manager
card_manager = get_card_manager()
logger.info(f"CardManager初始化完成，加载了{len(card_manager.cards_data)}个卡密")

@app.route('/api/validate-card', methods=['POST'])
def validate_card():
    """验证卡密接口 - 调试版本"""
    logger.info("=== 开始处理卡密验证请求 ===")
    
    try:
        # 获取请求数据
        data = request.get_json()
        logger.info(f"接收到的原始数据: {data}")
        
        if not data or 'cardKey' not in data:
            logger.warning("请求数据验证失败: 缺少卡密参数")
            return jsonify({
                'success': False,
                'error': '缺少卡密参数'
            }), 400
        
        card_key = data['cardKey'].strip()
        logger.info(f"处理后的卡密: '{card_key}'")
        
        if not card_key:
            logger.warning("卡密验证失败: 卡密为空")
            return jsonify({
                'success': False,
                'error': '卡密不能为空'
            }), 400
        
        # 检查卡密是否存在
        exists = card_key in card_manager.cards_data
        logger.info(f"卡密是否存在于数据中: {exists}")
        
        if exists:
            card_info = card_manager.cards_data[card_key]
            logger.info(f"卡密详细信息: {card_info}")
        
        # 验证卡密
        logger.info("开始调用card_manager.validate_card()...")
        is_valid = card_manager.validate_card(card_key)
        logger.info(f"card_manager.validate_card()返回结果: {is_valid}")
        
        if is_valid:
            # 获取卡密信息
            card_info = card_manager.get_card_info(card_key)
            logger.info(f"获取到的卡密信息: {card_info}")
            
            result = {
                'success': True,
                'message': '卡密验证成功',
                'data': {
                    'valid': True,
                    'remaining_usage': card_info.get('max_usage', 0) - card_info.get('usage_count', 0),
                    'expire_time': card_info.get('expire_time')
                }
            }
            logger.info(f"返回成功结果: {result}")
            return jsonify(result)
        else:
            logger.warning(f"卡密验证失败: {card_key}")
            result = {
                'success': False,
                'error': '卡密无效或已过期'
            }
            logger.info(f"返回失败结果: {result}")
            return jsonify(result), 401
            
    except Exception as e:
        error_msg = f"验证卡密时出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

@app.route('/api/debug/card-info/<card_key>', methods=['GET'])
def debug_card_info(card_key):
    """调试接口：获取卡密详细信息"""
    try:
        exists = card_key in card_manager.cards_data
        if exists:
            card_info = card_manager.cards_data[card_key]
            is_valid = card_manager.validate_card(card_key)
            return jsonify({
                'exists': True,
                'card_info': card_info,
                'is_valid': is_valid,
                'total_cards': len(card_manager.cards_data)
            })
        else:
            return jsonify({
                'exists': False,
                'total_cards': len(card_manager.cards_data),
                'first_5_cards': list(card_manager.cards_data.keys())[:5]
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("启动调试服务器...")
    app.run(host='0.0.0.0', port=5001, debug=True)