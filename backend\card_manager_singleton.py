#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卡密管理器单例模式
确保整个应用只有一个CardManager实例，避免数据不一致问题
"""

import threading
from card_manager import CardManager

class CardManagerSingleton:
    """CardManager单例模式实现"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(CardManagerSingleton, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._card_manager = CardManager()
            self._initialized = True
    
    def get_instance(self):
        """获取CardManager实例"""
        return self._card_manager
    
    def reload_cards(self):
        """重新加载卡密数据"""
        self._card_manager._load_cards()
        return self._card_manager

# 全局单例实例
_singleton = CardManagerSingleton()

def get_card_manager():
    """获取全局唯一的CardManager实例"""
    return _singleton.get_instance()

def reload_card_manager():
    """重新加载卡密数据"""
    return _singleton.reload_cards()