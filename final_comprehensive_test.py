#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试所有修复的问题
"""

import requests
import time
import json

def test_cache_cleanup():
    """测试缓存清理效果"""
    print("🧹 测试缓存清理效果")
    print("=" * 60)
    
    # 检查服务器是否重新启动
    try:
        response = requests.get('http://localhost:5000/')
        if response.status_code == 200:
            print("✅ 服务器已重新启动")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {str(e)}")
        return False

def test_different_times():
    """测试不同时间是否生成不同结果"""
    print(f"\n🕐 测试不同时间生成不同结果")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "测试1：1988年2月14日卯时",
            "data": {
                "cardKey": "wdd",
                "year": "1988",
                "month": "2",
                "day": "14",
                "hour": "卯时",
                "gender": "1"
            }
        },
        {
            "name": "测试2：2000年12月31日亥时",
            "data": {
                "cardKey": "wdd",
                "year": "2000",
                "month": "12",
                "day": "31",
                "hour": "亥时",
                "gender": "0"
            }
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送分析请求
            response = requests.post(
                'http://localhost:5000/webhook/bazi-analysis',
                json=test_case['data']
            )
            
            if response.status_code == 200:
                result = response.json()
                request_id = result.get('requestId')
                print(f"✅ 请求提交成功，ID: {request_id}")
                
                # 等待处理完成
                print("⏳ 等待处理完成...")
                time.sleep(10)
                
                # 获取结果
                result_response = requests.get(f'http://localhost:5000/api/get_result/{request_id}')
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    
                    if result_data.get('completed'):
                        result_obj = result_data.get('result', {})
                        bz = result_obj.get('data', {}).get('bz', {})
                        
                        if bz:
                            birth_info = bz.get('8', '未知')
                            bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                            
                            print(f"✅ 处理完成")
                            print(f"   生辰: {birth_info}")
                            print(f"   八字: {bazi_str}")
                            
                            results.append({
                                'name': test_case['name'],
                                'birth_info': birth_info,
                                'bazi': bazi_str,
                                'success': True
                            })
                        else:
                            print(f"❌ 没有八字数据")
                            results.append({'name': test_case['name'], 'success': False})
                    else:
                        print(f"⏳ 仍在处理中...")
                        results.append({'name': test_case['name'], 'success': False})
                else:
                    print(f"❌ 获取结果失败: {result_response.status_code}")
                    results.append({'name': test_case['name'], 'success': False})
            else:
                print(f"❌ 请求失败: {response.status_code}")
                results.append({'name': test_case['name'], 'success': False})
                
        except Exception as e:
            print(f"💥 测试失败: {str(e)}")
            results.append({'name': test_case['name'], 'success': False})
    
    # 分析结果
    print(f"\n📊 结果分析")
    print("-" * 40)
    
    successful_results = [r for r in results if r.get('success')]
    
    if len(successful_results) >= 2:
        # 检查是否生成了不同的结果
        birth_infos = [r['birth_info'] for r in successful_results]
        bazi_strings = [r['bazi'] for r in successful_results]
        
        if len(set(birth_infos)) > 1 and len(set(bazi_strings)) > 1:
            print("✅ 不同时间生成了不同的结果")
            for r in successful_results:
                print(f"   - {r['name']}: {r['birth_info']}")
            return True
        else:
            print("❌ 不同时间生成了相同的结果")
            for r in successful_results:
                print(f"   - {r['name']}: {r['birth_info']}")
            return False
    else:
        print("❌ 测试失败，无法获得足够的结果进行比较")
        return False

def test_button_duplication():
    """测试按钮重复生成问题"""
    print(f"\n🔄 测试按钮重复生成问题")
    print("=" * 60)
    
    try:
        # 检查JavaScript修复
        response = requests.get('http://localhost:5000/js/ui_reports_polling_functions.js')
        if response.status_code == 200:
            content = response.text
            
            fixes = [
                ('防重复执行', 'viewCurrentResultsExecuting'),
                ('按钮禁用', 'disabled = true'),
                ('onclick检查', 'hasAttribute(\'onclick\')'),
                ('事件绑定标记', 'data-event-bound')
            ]
            
            all_fixed = True
            for fix_name, pattern in fixes:
                if pattern in content:
                    print(f"✅ {fix_name} - 已修复")
                else:
                    print(f"❌ {fix_name} - 未找到")
                    all_fixed = False
            
            return all_fixed
        else:
            print(f"❌ JavaScript文件访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_export_function():
    """测试导出长图功能"""
    print(f"\n🖼️ 测试导出长图功能")
    print("=" * 60)
    
    try:
        # 检查HTML页面
        response = requests.get('http://localhost:5000/detailed_result.html')
        if response.status_code == 200:
            content = response.text
            
            features = [
                ('导出按钮', 'exportAsImage()'),
                ('备用函数', 'window.exportAsImage = window.exportAsImage ||'),
                ('html2canvas库', 'html2canvas.min.js'),
                ('图片导出模块', 'image_exporter.js'),
                ('错误处理', 'html2canvas库未加载')
            ]
            
            all_present = True
            for feature_name, pattern in features:
                if pattern in content:
                    print(f"✅ {feature_name} - 已添加")
                else:
                    print(f"❌ {feature_name} - 未找到")
                    all_present = False
            
            return all_present
        else:
            print(f"❌ HTML页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎯 综合测试 - 所有问题修复验证")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 执行各项测试
    tests = [
        ("缓存清理", test_cache_cleanup),
        ("不同时间生成不同结果", test_different_times),
        ("按钮重复生成修复", test_button_duplication),
        ("导出长图功能", test_export_function)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 测试 {test_name} 出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 综合测试结果")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体状态: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有问题已成功修复！")
        print("💡 系统现在应该能够:")
        print("  - 正确处理不同时间的八字分析")
        print("  - 避免按钮重复生成问题")
        print("  - 正常使用导出长图功能")
        print("  - 清理了所有缓存文件")
    else:
        print(f"\n⚠️ 还有 {total - passed} 项需要进一步检查")
    
    print(f"\n🔗 测试页面:")
    print("  主页面: http://localhost:5000/")
    print("  详细报告: http://localhost:5000/detailed_result.html?cardKey=wdd&id=1")

if __name__ == "__main__":
    main()
