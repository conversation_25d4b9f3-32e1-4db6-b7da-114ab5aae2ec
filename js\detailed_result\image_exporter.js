/**
 * 图片导出功能模块
 * 用于将详细分析报告导出为长图
 */

class ImageExporter {
    constructor() {
        this.isExporting = false;
        this.exportOptions = {
            scale: 2, // 高清晰度
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            // 不设置固定宽度，让html2canvas自动检测
            scrollX: 0,
            scrollY: 0,
            logging: false, // 关闭调试日志
            imageTimeout: 15000, // 图片加载超时时间
            onclone: (clonedDoc) => {
                // 在克隆的文档中应用额外的样式优化
                this.optimizeClonedDocument(clonedDoc);
            }
        };
    }

    /**
     * 导出分析报告为长图
     */
    async exportAsImage(format = 'png') {
        if (this.isExporting) {
            console.log('正在导出中，请稍候...');
            return;
        }

        try {
            this.isExporting = true;
            this.showExportProgress('准备导出...');

            // 获取要导出的内容区域
            const contentElement = this.getExportContent();
            if (!contentElement) {
                throw new Error('未找到可导出的内容');
            }

            // 准备导出环境
            await this.prepareForExport(contentElement);

            this.showExportProgress('正在生成图片...');

            // 计算实际内容尺寸
            const actualOptions = this.calculateExportOptions(contentElement);

            // 使用html2canvas生成图片
            const canvas = await html2canvas(contentElement, actualOptions);

            this.showExportProgress('正在保存图片...');

            // 下载图片
            await this.downloadImage(canvas, format);

            this.showExportProgress('导出完成！', true);

        } catch (error) {
            console.error('导出图片失败:', error);
            this.showExportError('导出失败: ' + error.message);
        } finally {
            this.isExporting = false;
            // 恢复页面状态
            setTimeout(() => this.hideExportProgress(), 2000);
        }
    }

    /**
     * 计算导出选项
     */
    calculateExportOptions(element) {
        // 获取元素的实际尺寸
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);

        // 计算实际内容宽度（包括padding）
        const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
        let actualWidth = Math.max(rect.width, element.scrollWidth) + paddingLeft + paddingRight;

        // 如果有自定义宽度，使用自定义宽度
        if (this.customWidth) {
            actualWidth = Math.max(actualWidth, this.customWidth);
        }

        // 计算实际内容高度（包括padding）
        const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
        const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
        const actualHeight = Math.max(rect.height, element.scrollHeight) + paddingTop + paddingBottom;

        console.log('计算的导出尺寸:', {
            width: actualWidth,
            height: actualHeight,
            scrollWidth: element.scrollWidth,
            scrollHeight: element.scrollHeight,
            rectWidth: rect.width,
            rectHeight: rect.height
        });

        return {
            ...this.exportOptions,
            width: Math.ceil(actualWidth),
            height: Math.ceil(actualHeight)
        };
    }

    /**
     * 获取要导出的内容区域
     */
    getExportContent() {
        // 优先导出分析内容区域
        let contentElement = document.getElementById('analysisContent');
        
        if (!contentElement || contentElement.style.display === 'none') {
            // 如果分析内容不可见，导出整个结果容器
            contentElement = document.querySelector('.detailed-result-container');
        }

        if (!contentElement) {
            // 最后备选：导出整个详细容器
            contentElement = document.querySelector('.detailed-container');
        }

        return contentElement;
    }

    /**
     * 准备导出环境
     */
    async prepareForExport(element) {
        // 确保所有图片都已加载
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve; // 即使加载失败也继续
                }
            });
        });

        await Promise.all(imagePromises);

        // 临时调整样式以适合导出
        this.applyExportStyles(element);
    }

    /**
     * 应用导出专用样式
     */
    applyExportStyles(element) {
        // 添加导出样式类
        element.classList.add('exporting-image');

        // 创建导出专用样式
        if (!document.getElementById('export-styles')) {
            const style = document.createElement('style');
            style.id = 'export-styles';
            style.textContent = `
                .exporting-image {
                    background: white !important;
                    min-height: auto !important;
                    padding: 20px !important;
                    box-shadow: none !important;
                    width: auto !important;
                    max-width: none !important;
                    overflow: visible !important;
                }
                
                .exporting-image .detailed-header-actions,
                .exporting-image .detailed-footer,
                .exporting-image .loading-state,
                .exporting-image .error-state,
                .exporting-image .empty-state {
                    display: none !important;
                }
                
                .exporting-image .detailed-result-header h2 {
                    text-align: center !important;
                    margin-bottom: 20px !important;
                    color: #333 !important;
                }
                
                .exporting-image .analysis-section {
                    margin-bottom: 30px !important;
                    page-break-inside: avoid !important;
                }
                
                .exporting-image .section-title {
                    color: #4a00e0 !important;
                    border-bottom: 2px solid #4a00e0 !important;
                    padding-bottom: 10px !important;
                    margin-bottom: 15px !important;
                }
                
                .exporting-image .bazi-table,
                .exporting-image .dayun-table {
                    width: 100% !important;
                    margin: 15px 0 !important;
                }
                
                .exporting-image .analysis-content {
                    line-height: 1.6 !important;
                    color: #333 !important;
                }
                
                /* 确保表格在导出时显示正确 */
                .exporting-image table {
                    border-collapse: collapse !important;
                    width: 100% !important;
                    table-layout: auto !important;
                    min-width: 600px !important;
                }

                .exporting-image td, .exporting-image th {
                    border: 1px solid #ddd !important;
                    padding: 8px !important;
                    text-align: center !important;
                    white-space: nowrap !important;
                }

                /* 确保容器宽度足够 */
                .exporting-image .detailed-result-container,
                .exporting-image .detailed-analysis-content {
                    width: auto !important;
                    min-width: 800px !important;
                    max-width: none !important;
                }

                /* 确保八字表格完整显示 */
                .exporting-image .bazi-table,
                .exporting-image .dayun-table {
                    min-width: 600px !important;
                    margin: 15px auto !important;
                }
                
                /* 隐藏不必要的交互元素 */
                .exporting-image button,
                .exporting-image .interactive-element {
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 下载生成的图片
     */
    async downloadImage(canvas, format = 'png') {
        return new Promise((resolve, reject) => {
            try {
                // 生成文件名
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const title = document.getElementById('resultTitle')?.textContent || '八字分析报告';
                const extension = format === 'jpeg' ? 'jpg' : format;
                const filename = `${title}_${timestamp}.${extension}`;

                // 确定MIME类型和质量
                const mimeType = format === 'jpeg' ? 'image/jpeg' :
                               format === 'webp' ? 'image/webp' : 'image/png';
                const quality = format === 'png' ? 1.0 : 0.9; // PNG使用无损压缩

                // 转换为blob并下载
                canvas.toBlob((blob) => {
                    if (!blob) {
                        reject(new Error('无法生成图片文件'));
                        return;
                    }

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;

                    // 触发下载
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 清理URL对象
                    URL.revokeObjectURL(url);

                    resolve();
                }, mimeType, quality);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 显示导出进度
     */
    showExportProgress(message, isSuccess = false) {
        // 移除现有的进度提示
        this.hideExportProgress();

        // 创建进度提示
        const progressDiv = document.createElement('div');
        progressDiv.id = 'export-progress';
        progressDiv.className = `export-progress ${isSuccess ? 'success' : ''}`;
        progressDiv.innerHTML = `
            <div class="export-progress-content">
                <div class="export-progress-icon">${isSuccess ? '✅' : '🖼️'}</div>
                <div class="export-progress-text">${message}</div>
                ${!isSuccess ? '<div class="export-progress-spinner"></div>' : ''}
            </div>
        `;

        // 添加样式
        if (!document.getElementById('export-progress-styles')) {
            const style = document.createElement('style');
            style.id = 'export-progress-styles';
            style.textContent = `
                .export-progress {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.9);
                    color: white;
                    padding: 20px 30px;
                    border-radius: 10px;
                    z-index: 10000;
                    text-align: center;
                    min-width: 200px;
                }
                
                .export-progress.success {
                    background: rgba(76, 175, 80, 0.9);
                }
                
                .export-progress-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 10px;
                }
                
                .export-progress-icon {
                    font-size: 24px;
                }
                
                .export-progress-text {
                    font-size: 16px;
                    font-weight: 500;
                }
                
                .export-progress-spinner {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #ffffff40;
                    border-top: 2px solid #ffffff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(progressDiv);
    }

    /**
     * 显示导出错误
     */
    showExportError(message) {
        this.hideExportProgress();
        
        const errorDiv = document.createElement('div');
        errorDiv.id = 'export-error';
        errorDiv.className = 'export-progress error';
        errorDiv.innerHTML = `
            <div class="export-progress-content">
                <div class="export-progress-icon">❌</div>
                <div class="export-progress-text">${message}</div>
                <button onclick="document.getElementById('export-error').remove()" 
                        style="margin-top: 10px; padding: 5px 15px; background: #ff4444; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    确定
                </button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
    }

    /**
     * 隐藏导出进度
     */
    hideExportProgress() {
        const progressElement = document.getElementById('export-progress');
        const errorElement = document.getElementById('export-error');

        if (progressElement) {
            progressElement.remove();
        }

        if (errorElement) {
            errorElement.remove();
        }

        // 清理导出样式
        const exportingElements = document.querySelectorAll('.exporting-image');
        exportingElements.forEach(el => el.classList.remove('exporting-image'));
    }

    /**
     * 优化克隆的文档
     */
    optimizeClonedDocument(clonedDoc) {
        try {
            // 确保字体正确加载
            const style = clonedDoc.createElement('style');
            style.textContent = `
                * {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
                }

                /* 确保表格边框显示 */
                table, th, td {
                    border: 1px solid #ddd !important;
                    border-collapse: collapse !important;
                }

                /* 优化文本渲染 */
                .analysis-content {
                    line-height: 1.8 !important;
                    word-break: break-word !important;
                    white-space: pre-wrap !important;
                }

                /* 确保背景色 */
                body, .detailed-container, .detailed-result-container {
                    background: white !important;
                }

                /* 隐藏滚动条 */
                ::-webkit-scrollbar {
                    display: none !important;
                }
            `;
            clonedDoc.head.appendChild(style);

            // 移除可能影响导出的元素
            const elementsToRemove = [
                '.detailed-header-actions',
                '.detailed-footer',
                '.loading-state',
                '.error-state',
                '.empty-state',
                'button',
                '.interactive-element',
                'script'
            ];

            elementsToRemove.forEach(selector => {
                const elements = clonedDoc.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });

            // 优化图片
            const images = clonedDoc.querySelectorAll('img');
            images.forEach(img => {
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
            });

        } catch (error) {
            console.warn('优化克隆文档时出错:', error);
        }
    }

    /**
     * 显示导出选项对话框
     */
    showExportOptions() {
        // 创建选项对话框
        const optionsDialog = document.createElement('div');
        optionsDialog.id = 'export-options-dialog';
        optionsDialog.className = 'export-options-dialog';
        optionsDialog.innerHTML = `
            <div class="export-options-content">
                <div class="export-options-header">
                    <h3>导出设置</h3>
                    <button onclick="document.getElementById('export-options-dialog').remove()" class="close-btn">×</button>
                </div>
                <div class="export-options-body">
                    <div class="option-group">
                        <label>图片格式:</label>
                        <select id="export-format">
                            <option value="png">PNG (推荐)</option>
                            <option value="jpeg">JPEG</option>
                            <option value="webp">WebP</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>图片质量:</label>
                        <select id="export-quality">
                            <option value="2">超高清 (2x)</option>
                            <option value="1.5">高清 (1.5x)</option>
                            <option value="1">标准 (1x)</option>
                        </select>
                    </div>
                    <div class="option-group">
                        <label>图片宽度:</label>
                        <select id="export-width">
                            <option value="800">手机版 (800px)</option>
                            <option value="1200">平板版 (1200px)</option>
                            <option value="1600">桌面版 (1600px)</option>
                        </select>
                    </div>
                </div>
                <div class="export-options-footer">
                    <button onclick="document.getElementById('export-options-dialog').remove()" class="cancel-btn">取消</button>
                    <button onclick="window.imageExporter.exportWithOptions()" class="export-btn">导出</button>
                </div>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('export-options-styles')) {
            const style = document.createElement('style');
            style.id = 'export-options-styles';
            style.textContent = `
                .export-options-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                }

                .export-options-content {
                    background: white;
                    border-radius: 10px;
                    width: 400px;
                    max-width: 90vw;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                }

                .export-options-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                }

                .export-options-header h3 {
                    margin: 0;
                    color: #333;
                }

                .close-btn {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .close-btn:hover {
                    color: #333;
                }

                .export-options-body {
                    padding: 20px;
                }

                .option-group {
                    margin-bottom: 15px;
                }

                .option-group label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: 500;
                    color: #333;
                }

                .option-group select {
                    width: 100%;
                    padding: 8px 12px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    font-size: 14px;
                }

                .export-options-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                    padding: 20px;
                    border-top: 1px solid #eee;
                }

                .cancel-btn, .export-btn {
                    padding: 8px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                }

                .cancel-btn {
                    background: #f5f5f5;
                    color: #666;
                }

                .cancel-btn:hover {
                    background: #e5e5e5;
                }

                .export-btn {
                    background: #4a00e0;
                    color: white;
                }

                .export-btn:hover {
                    background: #3a00b0;
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(optionsDialog);
    }

    /**
     * 使用自定义选项导出
     */
    async exportWithOptions() {
        const format = document.getElementById('export-format').value;
        const quality = parseFloat(document.getElementById('export-quality').value);
        const width = parseInt(document.getElementById('export-width').value);

        // 临时保存原始选项
        const originalScale = this.exportOptions.scale;

        // 更新导出选项
        this.exportOptions.scale = quality;
        this.customWidth = width; // 保存自定义宽度

        // 关闭选项对话框
        document.getElementById('export-options-dialog').remove();

        try {
            // 执行导出
            await this.exportAsImage(format);
        } finally {
            // 恢复原始选项
            this.exportOptions.scale = originalScale;
            this.customWidth = null;
        }
    }
}

// 确保在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 创建全局实例
    window.imageExporter = new ImageExporter();

    // 全局导出函数
    window.exportAsImage = function() {
        console.log('导出长图功能被调用');
        if (window.imageExporter) {
            window.imageExporter.exportAsImage();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    // 高级导出函数（带选项）
    window.exportAsImageAdvanced = function() {
        console.log('高级导出功能被调用');
        if (window.imageExporter) {
            window.imageExporter.showExportOptions();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    console.log('图片导出功能已初始化');
});

// 如果DOM已经加载完成，立即初始化
if (document.readyState === 'loading') {
    // DOM还在加载中，等待DOMContentLoaded事件
} else {
    // DOM已经加载完成，立即初始化
    window.imageExporter = new ImageExporter();

    window.exportAsImage = function() {
        console.log('导出长图功能被调用');
        if (window.imageExporter) {
            window.imageExporter.exportAsImage();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    window.exportAsImageAdvanced = function() {
        console.log('高级导出功能被调用');
        if (window.imageExporter) {
            window.imageExporter.showExportOptions();
        } else {
            console.error('图片导出器未初始化');
        }
    };

    console.log('图片导出功能已立即初始化');
}
