#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
用于加载和管理应用程序配置
"""

import json
import os
from typing import Dict, Any, List
from pathlib import Path

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认为当前目录下的config.json
        """
        self.config_file = config_file or os.path.join(os.path.dirname(__file__), 'config.json')
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"警告: 配置文件 {self.config_file} 不存在，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "api": {
                "url": "https://openrouter.ai/api/v1/chat/completions",
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer your-api-key-here"
                },
                "model_name": "deepseek/deepseek-r1:free",
                "timeout": 60,
                "max_retries": 3,
                "retry_delay": 10
            },
            "analysis": {
                "personality_only": True,
                "available_dimensions": [
                    "性格特征", "事业发展", "财运分析", "感情婚姻",
                    "健康状况", "人际关系", "学业教育", "家庭关系"
                ],
                "enabled_dimensions": ["性格特征"]
            },
            "paths": {
                "prompts_dir": "prompts",
                "output_dir": "summaries",
                "logs_dir": "llm_logs"
            },
            "server": {
                "host": "0.0.0.0",
                "port": 5000,
                "debug": False
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "console_enabled": True
            },
            "card_validation": {
                "enabled": True,
                "valid_cards": ["test123", "demo456"]
            },
            "webhook": {
                "secret_key": "a7712e3b-7f96-4b2e-9573-a6f87d9fd848",
                "enabled": True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def save(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def reload(self) -> None:
        """重新加载配置文件"""
        self.config = self._load_config()
    
    # API相关配置
    def get_api_url(self) -> str:
        return self.get('api.url', 'https://openrouter.ai/api/v1/chat/completions')
    
    def get_api_headers(self) -> Dict[str, str]:
        return self.get('api.headers', {})
    
    def get_model_name(self) -> str:
        return self.get('api.model_name', 'deepseek/deepseek-r1:free')
    
    def get_api_timeout(self) -> int:
        return self.get('api.timeout', 60)
    
    def get_max_retries(self) -> int:
        return self.get('api.max_retries', 3)
    
    def get_retry_delay(self) -> int:
        return self.get('api.retry_delay', 10)
    
    # 分析相关配置
    def is_personality_only(self) -> bool:
        return self.get('analysis.personality_only', True)
    
    def get_available_dimensions(self) -> List[str]:
        return self.get('analysis.available_dimensions', ['性格特征'])
    
    def get_enabled_dimensions(self) -> List[str]:
        return self.get('analysis.enabled_dimensions', ['性格特征'])
    
    def set_enabled_dimensions(self, dimensions: List[str]) -> None:
        self.set('analysis.enabled_dimensions', dimensions)
    
    def toggle_personality_only(self) -> None:
        """切换是否只分析性格维度"""
        current = self.is_personality_only()
        self.set('analysis.personality_only', not current)
        if not current:
            # 如果切换到只分析性格，则只启用性格维度
            self.set_enabled_dimensions(['性格特征'])
        else:
            # 如果切换到全分析，则启用所有维度
            self.set_enabled_dimensions(self.get_available_dimensions())
    
    # 路径相关配置
    def get_prompts_dir(self) -> str:
        return self.get('paths.prompts_dir', 'prompts')
    
    def get_output_dir(self) -> str:
        return self.get('paths.output_dir', 'summaries')
    
    def get_logs_dir(self) -> str:
        return self.get('paths.logs_dir', 'llm_logs')
    
    # 服务器相关配置
    def get_server_host(self) -> str:
        return self.get('server.host', '0.0.0.0')
    
    def get_server_port(self) -> int:
        return self.get('server.port', 5000)
    
    def is_debug_mode(self) -> bool:
        return self.get('server.debug', False)
    
    # 卡密验证相关配置
    def is_card_validation_enabled(self) -> bool:
        return self.get('card_validation.enabled', True)
    
    def get_valid_cards(self) -> List[str]:
        return self.get('card_validation.valid_cards', [])
    
    def add_valid_card(self, card: str) -> None:
        """添加有效卡密"""
        cards = self.get_valid_cards()
        if card not in cards:
            cards.append(card)
            self.set('card_validation.valid_cards', cards)
    
    def remove_valid_card(self, card: str) -> None:
        """移除有效卡密"""
        cards = self.get_valid_cards()
        if card in cards:
            cards.remove(card)
            self.set('card_validation.valid_cards', cards)
    
    # Webhook相关配置
    def get_webhook_secret(self) -> str:
        return self.get('webhook.secret_key', '')
    
    def is_webhook_enabled(self) -> bool:
        return self.get('webhook.enabled', True)


# 全局配置实例
config = ConfigManager()


def get_config() -> ConfigManager:
    """获取全局配置实例"""
    return config


if __name__ == '__main__':
    # 测试配置管理器
    cm = ConfigManager()
    print("当前配置:")
    print(f"API URL: {cm.get_api_url()}")
    print(f"模型名称: {cm.get_model_name()}")
    print(f"只分析性格: {cm.is_personality_only()}")
    print(f"启用的维度: {cm.get_enabled_dimensions()}")
    print(f"服务器端口: {cm.get_server_port()}")