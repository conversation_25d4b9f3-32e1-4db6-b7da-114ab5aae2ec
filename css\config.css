/* 配置管理页面样式 */
:root {
    --primary-color: #4a6cf7;
    --accent-color: #6c5ce7;
    --success-color: #38b2ac;
    --warning-color: #ecc94b;
    --danger-color: #e53e3e;
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --border-color: #e2e8f0;
    --border-color-light: #edf2f7;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --transition: all 0.3s ease;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #38b2ac 0%, #2f855a 100%);
    --warning-gradient: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
    --hero-gradient: linear-gradient(135deg, #4a6cf7 0%, #6c5ce7 100%);
    --card-gradient: linear-gradient(to bottom, #ffffff 0%, #f8fafc 100%);
    --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: var(--hero-gradient);
    color: white;
    padding: 30px;
    text-align: center;
    border-radius: var(--border-radius-lg);
    margin-bottom: 30px;
    box-shadow: var(--shadow-lg);
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.nav-links {
    margin-top: 15px;
}

.nav-link {
    display: inline-block;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.content {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.section {
    margin-bottom: 30px;
    padding: 25px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.section h2 {
    color: var(--text-primary);
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"] {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.checkbox-item input[type="checkbox"] {
    margin-right: 10px;
}

.btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn:hover {
    background: #3a5bd9;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--accent-color);
}

.btn-secondary:hover {
    background: #5a4ad9;
}

.btn-success {
    background: var(--success-color);
}

.btn-success:hover {
    background: #2c9a8c;
}

.status {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: none;
    font-weight: 500;
}

.status.success {
    background: rgba(56, 178, 172, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(56, 178, 172, 0.3);
}

.status.error {
    background: rgba(229, 62, 62, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(229, 62, 62, 0.3);
}

.config-display {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    font-family: var(--font-family-mono);
    font-size: 0.9rem;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.5;
}

.card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 15px;
}

.card-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-item .remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.card-item .remove-btn:hover {
    background: #c53030;
}

.loading {
    text-align: center;
    padding: 30px;
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(74, 108, 247, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    margin: 0 auto 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 密码保护层样式 */
#passwordProtection {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .header {
        padding: 20px 16px;
        margin-bottom: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
        line-height: 1.3;
    }
    
    .nav-links {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }
    
    .nav-link {
        padding: 10px 16px;
        margin: 0;
        font-size: 0.9rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .content {
        padding: 20px 16px;
        margin: 0;
    }
    
    .section {
        padding: 20px 16px;
        margin-bottom: 20px;
    }
    
    .section h2 {
        font-size: 1.3rem;
        line-height: 1.3;
        margin-bottom: 16px;
    }
    
    .checkbox-group {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .checkbox-item {
        padding: 16px;
        border-radius: 10px;
    }
    
    .card-list {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .card {
        padding: 20px 16px;
        border-radius: 12px;
    }
    
    .btn {
        padding: 12px 24px;
        font-size: 1rem;
        min-height: 48px;
        border-radius: 10px;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        margin-bottom: 12px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 12px;
    }
    
    .header {
        padding: 16px 12px;
        margin-bottom: 16px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .nav-link {
        padding: 12px 16px;
        font-size: 0.85rem;
        flex: 1;
        min-width: 120px;
    }
    
    .content {
        padding: 16px 12px;
    }
    
    .section {
        padding: 16px 12px;
        margin-bottom: 16px;
    }
    
    .section h2 {
        font-size: 1.2rem;
    }
    
    .checkbox-item {
        padding: 14px 12px;
    }
    
    .card {
        padding: 16px 12px;
    }
    
    .btn {
        padding: 14px 20px;
        font-size: 0.9rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .nav-link,
    .btn,
    .checkbox-item {
        touch-action: manipulation;
    }
    
    .nav-link:active,
    .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    .checkbox-item:active {
        background-color: var(--bg-secondary);
        transition: background-color 0.1s ease;
    }
}