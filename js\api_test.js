/**
 * API测试工具
 * 用于验证前端向后端发送的API请求
 */

// 初始化日志记录器
const testLogger = window.BaziLogger ? window.BaziLogger.forModule('APITest') : {
    debug: console.log,
    info: console.log,
    warn: console.warn,
    error: console.error
};

// 测试删除报告API
function testDeleteReportAPI() {
    // 获取API基础URL
    const apiBaseUrl = typeof getApiBaseUrl === 'function' ? getApiBaseUrl() : 'https://api.example.com';
    
    // 生成测试ID和测试卡密
    const testReportId = 'test_' + Date.now();
    const testCardKey = 'test_card_' + Math.random().toString(36).substring(2, 10);
    
    testLogger.info('开始测试删除报告API');
    testLogger.info('API基础URL:', apiBaseUrl);
    testLogger.info('测试报告ID:', testReportId);
    testLogger.info('测试卡密:', testCardKey);
    
    // 创建一个拦截器来捕获网络请求
    const originalFetch = window.fetch;
    
    // 替换全局fetch函数以记录请求
    window.fetch = function(url, options) {
        testLogger.info('捕获到API请求:', { url, method: options?.method || 'GET' });
        
        // 显示详细信息
        const requestDetails = document.getElementById('apiTestResults');
        if (requestDetails) {
            requestDetails.innerHTML = `
                <div class="test-result success">
                    <h3>✅ API请求已发送</h3>
                    <p><strong>URL:</strong> ${url}</p>
                    <p><strong>方法:</strong> ${options?.method || 'GET'}</p>
                    <p><strong>参数:</strong> ${url.split('?')[1] || '无'}</p>
                    <p><strong>时间:</strong> ${new Date().toLocaleTimeString()}</p>
                </div>
            `;
        }
        
        // 不实际发送请求，返回模拟的成功响应
        return Promise.resolve({
            ok: true,
            status: 200,
            json: () => Promise.resolve({ success: true, message: '这是模拟的成功响应' })
        });
    };
    
    // 构建删除URL
    const deleteURL = `${apiBaseUrl}/api/reports/delete/${testReportId}?cardKey=${encodeURIComponent(testCardKey)}`;
    
    // 发送测试请求
    fetch(deleteURL, {
        method: 'DELETE'
    })
    .then(response => {
        testLogger.info('收到API响应:', response);
        
        // 测试完成后恢复原始fetch
        setTimeout(() => {
            window.fetch = originalFetch;
            testLogger.info('已恢复原始fetch函数');
        }, 2000);
        
        return response.json();
    })
    .then(data => {
        testLogger.info('响应数据:', data);
    })
    .catch(error => {
        testLogger.error('API请求失败:', error);
        
        // 显示错误信息
        const requestDetails = document.getElementById('apiTestResults');
        if (requestDetails) {
            requestDetails.innerHTML = `
                <div class="test-result error">
                    <h3>❌ API请求失败</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>时间:</strong> ${new Date().toLocaleTimeString()}</p>
                </div>
            `;
        }
        
        // 恢复原始fetch
        window.fetch = originalFetch;
    });
}

// 显示测试UI
function showAPITestInterface() {
    // 创建测试界面
    const testDiv = document.createElement('div');
    testDiv.className = 'api-test-container';
    testDiv.style.position = 'fixed';
    testDiv.style.bottom = '20px';
    testDiv.style.right = '20px';
    testDiv.style.width = '400px';
    testDiv.style.maxHeight = '80vh';
    testDiv.style.overflowY = 'auto';
    testDiv.style.backgroundColor = '#f8f9fa';
    testDiv.style.borderRadius = '8px';
    testDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    testDiv.style.padding = '16px';
    testDiv.style.zIndex = '9999';
    testDiv.style.border = '1px solid #e0e0e0';
    
    // 添加测试界面内容
    testDiv.innerHTML = `
        <div class="api-test-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333;">API测试工具</h3>
            <button id="closeTestBtn" style="background: none; border: none; cursor: pointer; font-size: 18px;">×</button>
        </div>
        <div class="api-test-description" style="margin-bottom: 15px; font-size: 14px; color: #666;">
            <p>此工具用于测试前端是否能够正确发送API请求到后端。点击下方按钮测试删除报告API。</p>
        </div>
        <div class="api-test-actions" style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button id="testDeleteAPIBtn" style="padding: 8px 16px; background-color: #4a90e2; color: white; border: none; border-radius: 4px; cursor: pointer;">测试删除报告API</button>
        </div>
        <div id="apiTestResults" style="background-color: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 14px; min-height: 100px;">
            <p style="color: #888; text-align: center;">点击按钮开始测试...</p>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(testDiv);
    
    // 添加事件监听
    document.getElementById('closeTestBtn').addEventListener('click', function() {
        document.body.removeChild(testDiv);
    });
    
    document.getElementById('testDeleteAPIBtn').addEventListener('click', function() {
        testDeleteReportAPI();
    });
}

// 在页面加载完成后添加测试按钮
document.addEventListener('DOMContentLoaded', function() {
    // 添加测试入口按钮
    const testEntryBtn = document.createElement('button');
    testEntryBtn.innerText = '测试API';
    testEntryBtn.style.position = 'fixed';
    testEntryBtn.style.bottom = '20px';
    testEntryBtn.style.right = '20px';
    testEntryBtn.style.zIndex = '9998';
    testEntryBtn.style.padding = '8px 16px';
    testEntryBtn.style.backgroundColor = '#4a90e2';
    testEntryBtn.style.color = 'white';
    testEntryBtn.style.border = 'none';
    testEntryBtn.style.borderRadius = '4px';
    testEntryBtn.style.cursor = 'pointer';
    testEntryBtn.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    
    testEntryBtn.addEventListener('click', function() {
        showAPITestInterface();
    });
    
    document.body.appendChild(testEntryBtn);
}); 