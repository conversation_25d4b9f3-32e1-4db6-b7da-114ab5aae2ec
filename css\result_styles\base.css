/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--hero-gradient, linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%));
    min-height: 100vh;
    color: var(--text-primary);
    line-height: var(--line-height-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* 键盘导航支持 */
:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(12px);
    box-shadow: 0 10px 40px rgba(15, 23, 42, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 背景装饰元素 */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: #f8fafc;
    }
} 