/* 文章详情页样式 */
.article-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    border-bottom: 1px solid var(--border-color);
}

.article-category {
    background: var(--primary-gradient);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 20px;
}

.article-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.3;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    color: var(--text-secondary);
    font-size: 0.95rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.article-content {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
    font-size: 1.05rem;
}

.article-content h2 {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 40px 0 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
}

.article-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 30px 0 15px;
}

.article-content p {
    margin-bottom: 20px;
    color: var(--text-primary);
}

.article-content blockquote {
    background: var(--bg-primary);
    border-left: 4px solid var(--primary-color);
    padding: 20px;
    margin: 30px 0;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: var(--text-secondary);
}

.article-content ul, .article-content ol {
    margin: 20px 0;
    padding-left: 30px;
}

.article-content li {
    margin-bottom: 8px;
    color: var(--text-primary);
}

.highlight-box {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: var(--border-radius);
    padding: 24px;
    margin: 30px 0;
}

.highlight-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.case-study {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px;
    margin: 30px 0;
    box-shadow: var(--shadow-sm);
}

.case-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bazi-chart {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 20px 0;
    max-width: 400px;
}

.bazi-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    text-align: center;
    font-weight: 500;
}

.bazi-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.bazi-value {
    font-size: 1.1rem;
    color: var(--text-primary);
}

.article-tags {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 40px 0;
    justify-content: center;
}

.tag {
    background: var(--bg-primary);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.article-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 40px;
    margin-top: 50px;
    text-align: center;
}