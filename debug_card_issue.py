import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from card_manager import CardManager
import json
from datetime import datetime

# 创建CardManager实例
card_manager = CardManager()

# 测试卡密
test_card = 'ZQJAfhgWPHeJbJ8s'

print(f"=== 卡密验证调试 ===")
print(f"测试卡密: {test_card}")
print(f"CardManager实例ID: {id(card_manager)}")
print(f"总卡密数量: {len(card_manager.cards_data)}")

# 检查卡密是否存在
if test_card in card_manager.cards_data:
    card_info = card_manager.cards_data[test_card]
    print(f"\n=== 卡密信息 ===")
    print(f"卡密存在: True")
    print(f"卡密详情: {json.dumps(card_info, indent=2, ensure_ascii=False)}")
    
    # 逐步验证
    print(f"\n=== 逐步验证 ===")
    print(f"1. 卡密有效性: {card_info.get('valid', False)}")
    
    # 检查过期时间
    expire_time_str = card_info['expire_time']
    expire_time = datetime.fromisoformat(expire_time_str)
    current_time = datetime.now()
    is_expired = current_time > expire_time
    print(f"2. 过期时间: {expire_time_str}")
    print(f"   当前时间: {current_time.isoformat()}")
    print(f"   是否过期: {is_expired}")
    
    # 检查使用次数
    usage_count = card_info.get('usage_count', 0)
    max_usage = card_info.get('max_usage', 0)
    usage_exceeded = usage_count >= max_usage
    print(f"3. 使用次数: {usage_count}/{max_usage}")
    print(f"   使用次数超限: {usage_exceeded}")
    
    # 调用validate_card方法
    validation_result = card_manager.validate_card(test_card)
    print(f"\n=== 验证结果 ===")
    print(f"validate_card()返回: {validation_result}")
    
    # 分析失败原因
    if not validation_result:
        print(f"\n=== 失败原因分析 ===")
        if not card_info.get('valid', False):
            print(f"- 卡密标记为无效")
        if is_expired:
            print(f"- 卡密已过期")
        if usage_exceeded:
            print(f"- 使用次数已达上限")
else:
    print(f"卡密不存在")

# 列出所有卡密
print(f"\n=== 所有卡密列表 ===")
for i, (card_key, card_info) in enumerate(card_manager.cards_data.items(), 1):
    print(f"{i}. {card_key}: valid={card_info.get('valid')}, expire={card_info.get('expire_time')}, usage={card_info.get('usage_count', 0)}/{card_info.get('max_usage', 0)}")