# Test Bazi Analysis API
$uri = "http://localhost:5000/webhook/bazi-analysis"
$headers = @{
    'Content-Type' = 'application/json'
}
$body = @{
    cardKey = "4QokDJieJvLUZORR"
    name = "Test User"
    gender = "0"
    year = "1990"
    month = "5"
    day = "15"
    hour = "10"
    minute = "30"
    isLunar = $false
} | ConvertTo-Json

Write-Host "Testing API: $uri"
Write-Host "Request data: $body"
Write-Host "=" * 50

try {
    $response = Invoke-WebRequest -Uri $uri -Method POST -Headers $headers -Body $body
    Write-Host "Status Code: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
    Write-Host "Test SUCCESS! Card validation passed."
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Response: $responseBody"
    }
}