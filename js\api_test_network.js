/**
 * API网络连接测试工具
 * 用于实际发送API请求并测试与后端的连接
 */

// 初始化日志记录器
const networkTestLogger = window.BaziLogger ? window.BaziLogger.forModule('NetworkTest') : {
    debug: console.log,
    info: console.log,
    warn: console.warn,
    error: console.error
};

// 测试网络连接
async function testNetworkConnection(url) {
    try {
        const startTime = Date.now();
        const response = await fetch(url, {
            method: 'GET',
            cache: 'no-cache',
            headers: {
                'Accept': 'application/json',
                'X-Test': 'network-test'
            }
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        networkTestLogger.info(`网络连接测试完成，响应状态: ${response.status}，耗时: ${duration}ms`);
        
        // 尝试解析JSON响应
        let responseData = null;
        try {
            responseData = await response.json();
        } catch (e) {
            // 如果不是JSON，获取文本内容
            responseData = await response.text();
        }
        
        return {
            success: response.ok,
            status: response.status,
            duration: duration,
            data: responseData
        };
    } catch (error) {
        networkTestLogger.error('网络连接测试失败:', error);
        
        return {
            success: false,
            error: error.message
        };
    }
}

// 测试向后端发送真实的API请求
async function testDeleteReportAPIWithRealRequest() {
    // 获取API基础URL
    const apiBaseUrl = typeof getApiBaseUrl === 'function' ? getApiBaseUrl() : 'https://api.example.com';
    
    // 生成测试ID和测试卡密
    const testReportId = 'test_' + Date.now();
    const testCardKey = 'test_card_' + Math.random().toString(36).substring(2, 10);
    
    // 构建删除URL
    const deleteURL = `${apiBaseUrl}/api/reports/delete/${testReportId}?cardKey=${encodeURIComponent(testCardKey)}`;
    
    networkTestLogger.info('开始真实API请求测试');
    networkTestLogger.info('API基础URL:', apiBaseUrl);
    networkTestLogger.info('测试请求URL:', deleteURL);
    
    // 显示测试中信息
    const resultsDiv = document.getElementById('networkTestResults');
    if (resultsDiv) {
        resultsDiv.innerHTML = `
            <div class="test-status testing">
                <h3>⏳ 正在测试真实网络请求...</h3>
                <p>请求URL: ${deleteURL}</p>
                <p>请稍候...</p>
            </div>
        `;
    }
    
    try {
        // 发送实际请求
        const startTime = Date.now();
        
        // 发送请求，但设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
        
        const response = await fetch(deleteURL, {
            method: 'DELETE',
            signal: controller.signal,
            headers: {
                'X-Test': 'network-test'
            }
        });
        
        clearTimeout(timeoutId);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // 尝试解析响应
        let responseData = null;
        try {
            responseData = await response.json();
        } catch (e) {
            try {
                responseData = await response.text();
            } catch (e2) {
                responseData = '无法解析响应内容';
            }
        }
        
        networkTestLogger.info(`API请求测试完成，响应状态: ${response.status}，耗时: ${duration}ms`);
        
        // 显示结果
        if (resultsDiv) {
            resultsDiv.innerHTML = `
                <div class="test-result ${response.ok ? 'success' : 'error'}">
                    <h3>${response.ok ? '✅ API请求成功' : '❌ API请求失败但连接正常'}</h3>
                    <p><strong>URL:</strong> ${deleteURL}</p>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>耗时:</strong> ${duration}ms</p>
                    <p><strong>响应:</strong></p>
                    <pre style="background: #f0f0f0; padding: 8px; overflow: auto; max-height: 100px; font-size: 12px;">${JSON.stringify(responseData, null, 2)}</pre>
                </div>
            `;
        }
        
        return {
            success: response.ok,
            status: response.status,
            duration: duration,
            data: responseData
        };
    } catch (error) {
        networkTestLogger.error('API请求测试失败:', error);
        
        // 显示错误
        if (resultsDiv) {
            resultsDiv.innerHTML = `
                <div class="test-result error">
                    <h3>❌ API请求失败</h3>
                    <p><strong>URL:</strong> ${deleteURL}</p>
                    <p><strong>错误:</strong> ${error.name === 'AbortError' ? '请求超时（10秒）' : error.message}</p>
                    <p><strong>建议:</strong> 请检查API基础URL是否正确，以及后端服务是否可访问</p>
                </div>
            `;
        }
        
        // 如果是超时错误，测试服务器连通性
        if (error.name === 'AbortError') {
            try {
                // 分析URL，只保留域名部分进行测试
                const urlObj = new URL(apiBaseUrl);
                const serverUrl = `${urlObj.protocol}//${urlObj.hostname}`;
                
                if (resultsDiv) {
                    resultsDiv.innerHTML += `
                        <div class="test-status testing">
                            <p>正在测试服务器连通性: ${serverUrl} ...</p>
                        </div>
                    `;
                }
                
                // 测试服务器连通性
                const connectionTest = await testNetworkConnection(serverUrl);
                
                if (resultsDiv) {
                    resultsDiv.innerHTML += `
                        <div class="test-result ${connectionTest.success ? 'success' : 'error'}">
                            <p><strong>服务器连通性:</strong> ${connectionTest.success ? '正常' : '失败'}</p>
                            ${connectionTest.success ? `<p><strong>响应时间:</strong> ${connectionTest.duration}ms</p>` : ''}
                            ${!connectionTest.success ? `<p><strong>错误:</strong> ${connectionTest.error}</p>` : ''}
                        </div>
                    `;
                }
            } catch (err) {
                networkTestLogger.error('服务器连通性测试失败:', err);
            }
        }
        
        return {
            success: false,
            error: error.message
        };
    }
}

// 显示网络测试UI
function showNetworkTestInterface() {
    // 创建测试界面
    const testDiv = document.createElement('div');
    testDiv.className = 'network-test-container';
    testDiv.style.position = 'fixed';
    testDiv.style.bottom = '20px';
    testDiv.style.left = '20px';
    testDiv.style.width = '400px';
    testDiv.style.maxHeight = '80vh';
    testDiv.style.overflowY = 'auto';
    testDiv.style.backgroundColor = '#f8f9fa';
    testDiv.style.borderRadius = '8px';
    testDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    testDiv.style.padding = '16px';
    testDiv.style.zIndex = '9999';
    testDiv.style.border = '1px solid #e0e0e0';
    
    // 获取API基础URL
    const apiBaseUrl = typeof getApiBaseUrl === 'function' ? getApiBaseUrl() : '未配置';
    
    // 添加测试界面内容
    testDiv.innerHTML = `
        <div class="network-test-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h3 style="margin: 0; color: #333;">网络连接测试</h3>
            <button id="closeNetworkTestBtn" style="background: none; border: none; cursor: pointer; font-size: 18px;">×</button>
        </div>
        <div class="network-test-description" style="margin-bottom: 15px; font-size: 14px; color: #666;">
            <p>此工具会发送真实的网络请求，测试与后端的连接是否正常。</p>
            <p><strong>当前API基础URL:</strong> ${apiBaseUrl}</p>
        </div>
        <div class="network-test-actions" style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button id="testRealNetworkBtn" style="padding: 8px 16px; background-color: #4a90e2; color: white; border: none; border-radius: 4px; cursor: pointer;">测试真实网络请求</button>
        </div>
        <div id="networkTestResults" style="background-color: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 14px; min-height: 100px;">
            <p style="color: #888; text-align: center;">点击按钮开始测试...</p>
        </div>
    `;
    
    // 添加到页面
    document.body.appendChild(testDiv);
    
    // 添加事件监听
    document.getElementById('closeNetworkTestBtn').addEventListener('click', function() {
        document.body.removeChild(testDiv);
    });
    
    document.getElementById('testRealNetworkBtn').addEventListener('click', function() {
        testDeleteReportAPIWithRealRequest();
    });
}

// 在页面加载完成后添加测试按钮
document.addEventListener('DOMContentLoaded', function() {
    // 添加测试入口按钮
    const testEntryBtn = document.createElement('button');
    testEntryBtn.innerText = '测试网络';
    testEntryBtn.style.position = 'fixed';
    testEntryBtn.style.bottom = '20px';
    testEntryBtn.style.left = '20px';
    testEntryBtn.style.zIndex = '9998';
    testEntryBtn.style.padding = '8px 16px';
    testEntryBtn.style.backgroundColor = '#4a90e2';
    testEntryBtn.style.color = 'white';
    testEntryBtn.style.border = 'none';
    testEntryBtn.style.borderRadius = '4px';
    testEntryBtn.style.cursor = 'pointer';
    testEntryBtn.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    
    testEntryBtn.addEventListener('click', function() {
        showNetworkTestInterface();
    });
    
    document.body.appendChild(testEntryBtn);
}); 