import requests
import json

# 测试分析API
def test_bazi_analysis():
    url = "http://127.0.0.1:5000/webhook/bazi-analysis"
    headers = {
        "Content-Type": "application/json"
    }
    data = {
        "year": 1990,
        "month": 5,
        "day": 15,
        "hour": 8,
        "minute": 30,
        "gender": "male",
        "cardKey": "NI45OgqRJEf7KZBonjMXLX7W"
    }
    
    print("发送请求到:", url)
    print("请求数据:", json.dumps(data, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=data)
    
    print("状态码:", response.status_code)
    print("响应内容:", response.text)

if __name__ == "__main__":
    test_bazi_analysis() 