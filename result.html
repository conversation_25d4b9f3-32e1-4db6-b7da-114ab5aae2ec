<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史分析报告</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/fonts.css">
    <!-- 结构化CSS引用 -->
    <link rel="stylesheet" href="css/result_styles/variables.css">
    <link rel="stylesheet" href="css/result_styles/base.css">
    <link rel="stylesheet" href="css/result_styles/main.css">
    <link rel="stylesheet" href="css/result_styles/navigation.css">
    <link rel="stylesheet" href="css/result_styles/buttons.css">
    <link rel="stylesheet" href="css/result_styles/cards.css">
    <link rel="stylesheet" href="css/result_styles/modal.css">
    <link rel="stylesheet" href="css/result_styles/loading.css">
    <link rel="stylesheet" href="css/result_styles/error.css">
    <link rel="stylesheet" href="css/result_styles/empty.css">
    <link rel="stylesheet" href="css/result_styles/animation/keyframes.css">
    <link rel="stylesheet" href="css/result_styles/responsive.css">
    <link rel="stylesheet" href="css/result_styles/dark-mode.css">
    <link rel="stylesheet" href="css/result_styles/emoji.css">
    <link rel="stylesheet" href="css/result_styles/skeleton.css">
    <link rel="stylesheet" href="css/result_styles/toast.css">
    <link rel="stylesheet" href="css/reports-summary.css">
    <style>
        /* 确保表情符号正确显示 */
        h1, .header h1 {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
        }

        /* 强制修复强制完成按钮的白底白字问题 */
        .action-btn.secondary,
        .action-btn.secondary.force-complete-btn {
            background: white !important;
            color: #6366f1 !important;
            border: 1px solid rgba(102, 126, 234, 0.3) !important;
            -webkit-text-fill-color: #6366f1 !important;
            /* text-fill-color: #6366f1 !important; */ /* 移除非标准属性以消除linter警告 */
        }

        .action-btn.secondary:hover,
        .action-btn.secondary.force-complete-btn:hover {
            background: rgba(102, 126, 234, 0.05) !important;
            color: #6366f1 !important;
            border-color: #6366f1 !important;
            -webkit-text-fill-color: #6366f1 !important;
            /* text-fill-color: #6366f1 !important; */ /* 移除非标准属性以消除linter警告 */
        }

        /* 确保深色模式CSS不会覆盖按钮文字 */
        .action-btn.secondary *,
        .action-btn.secondary.force-complete-btn * {
            color: inherit !important;
        }

        /* 超级强力修复 - 针对所有可能的选择器 */
        button.action-btn.secondary,
        button.action-btn.secondary.force-complete-btn,
        .action-btn.secondary,
        .action-btn.secondary.force-complete-btn,
        button[onclick*="forceCompleteAnalysis"],
        button:contains("强制完成") {
            background-color: white !important;
            background: white !important;
            color: #6366f1 !important;
            border: 1px solid rgba(102, 126, 234, 0.3) !important;
            -webkit-text-fill-color: #6366f1 !important;
            text-shadow: none !important;
        }

        button.action-btn.secondary:hover,
        button.action-btn.secondary.force-complete-btn:hover,
        .action-btn.secondary:hover,
        .action-btn.secondary.force-complete-btn:hover,
        button[onclick*="forceCompleteAnalysis"]:hover {
            background-color: rgba(102, 126, 234, 0.05) !important;
            background: rgba(102, 126, 234, 0.05) !important;
            color: #6366f1 !important;
            border-color: #6366f1 !important;
            -webkit-text-fill-color: #6366f1 !important;
        }

        /* 禁用分析操作按钮的悬停变色效果 */
        .analysis-actions .action-btn:hover,
        .analysis-actions .view-results-btn:hover,
        .analysis-actions .force-complete-btn:hover,
        .analysis-actions .debug-btn:hover,
        button[onclick*="viewCurrentResults"]:hover,
        button[onclick*="forceCompleteAnalysis"]:hover,
        button[onclick*="toggleDebugInfo"]:hover {
            /* 完全禁用悬停效果 */
            background: initial !important;
            background-color: initial !important;
            color: initial !important;
            border-color: initial !important;
            transform: none !important;
            box-shadow: none !important;
            -webkit-text-fill-color: initial !important;
            /* text-fill-color: initial !important; */ /* 移除非标准属性以消除linter警告 */
            /* 禁用过渡动画 */
            transition: none !important;
            /* 重置为按钮的原始状态 */
            filter: none !important;
            opacity: 1 !important;
        }

        /* 更强力的禁用规则 - 针对特定按钮类 */
        .analysis-actions .action-btn.primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .analysis-actions .action-btn.outline:hover {
            background: transparent !important;
            color: #667eea !important;
            border: 1px solid #667eea !important;
        }

        /* 特别处理强制完成按钮，确保保持白底蓝字 */
        .analysis-actions .force-complete-btn:hover,
        button[onclick*="forceCompleteAnalysis"]:hover {
            background: white !important;
            background-color: white !important;
            color: #6366f1 !important;
            border-color: rgba(102, 126, 234, 0.3) !important;
            -webkit-text-fill-color: #6366f1 !important;
            /* text-fill-color: #6366f1 !important; */ /* 移除非标准属性以消除linter警告 */
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="nav-link">
            <span>← 返回首页</span>
        </a>
        
        <div class="header">
            <h1>历史分析报告</h1>
            <p class="subtitle">查看您的八字分析历史记录</p>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="actions-section">
            <a href="up.html" class="action-btn primary">☯ 新建分析</a>
            <button onclick="refreshReports()" class="action-btn secondary">⚌ 刷新列表</button>
            <div class="card-filter">
                <input type="text" id="cardKeyFilter" placeholder="输入卡密查询相关报告" class="card-filter-input">
                <button onclick="filterReportsByCard()" class="action-btn secondary">⚍ 查询</button>
                <button onclick="clearCardFilter()" class="action-btn outline">⚏ 清除</button>
            </div>
        </div>
        
        <!-- 历史报告列表 -->
        <div id="reportsList" class="reports-container">
            <div class="loading-message">
                <div class="loading-icon">⚊</div>
                <h3>正在加载历史报告...</h3>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 八字分析系统 | 专业命理分析服务</p>
        </div>
    </div>

    <!-- 卡密验证模态框 -->
    <div id="cardKeyModal" class="card-key-modal">
        <div class="card-key-modal-content">
            <div class="card-key-modal-header">
                <h3 class="card-key-modal-title">请输入卡密</h3>
                <p class="card-key-modal-subtitle">查看历史分析报告需要验证卡密</p>
            </div>
            
            <div class="card-key-input-group">
                <input type="text" id="cardKeyInput" placeholder="请输入您的卡密" class="card-key-input">
                <div id="cardKeyStatus" class="card-key-status" style="display: none;"></div>
            </div>
            
            <div class="card-key-modal-actions">
                <button class="card-key-modal-btn primary" onclick="confirmCardKeyInput()" disabled>确认</button>
                <button class="card-key-modal-btn secondary" onclick="cancelCardKeyInput()">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 引入JavaScript文件 -->
    <script src="js/utils_logger.js"></script>
    <script src="js/console_cleanup.js"></script>
    <script src="js/api_card_service.js"></script>
    <script src="js/ui_custom_dialogs.js"></script>
    <script src="js/ui_modal_component.js"></script>
    <script src="js/api_analysis_service.js"></script>
    <script src="js/ui_reports_manager_core.js"></script>
    <script src="js/ui_reports_polling_functions.js"></script>
    <script src="js/ui_reports_manager_polling.js"></script>
    <script src="js/ui_reports_manager_data.js"></script>
    <script src="js/ui_reports_manager_extract_1.js"></script>
    <script src="js/ui_reports_manager_extract_2.js"></script>
    <script src="js/ui_reports_manager_extract_3.js"></script>
    <script src="js/ui_reports_manager_title.js"></script>
    <script src="js/ui_reports_components.js"></script>
    <script src="js/ui_reports_summary.js"></script>
    <script src="js/ui_reports_operations.js"></script>
    <script src="js/ui_reports_skeleton.js"></script>
    <script src="js/ui_reports_display.js"></script>
    <script src="js/ui_reports_init.js"></script>

    <script>
        // 备用的 viewCurrentResults 函数定义（防止函数未定义错误）
        if (typeof window.viewCurrentResults !== 'function') {
            window.viewCurrentResults = function() {
                console.log('🔍 使用备用的 viewCurrentResults 函数');

                let cardKey = null;

                // 尝试从URL参数获取卡密
                const urlParams = new URLSearchParams(window.location.search);
                cardKey = urlParams.get('cardKey');
                console.log('📋 从URL参数获取卡密:', cardKey);

                // 如果URL中没有卡密，尝试从全局变量获取
                if (!cardKey && window.currentCardKey) {
                    cardKey = window.currentCardKey;
                    console.log('🌐 从全局变量获取卡密:', cardKey);
                }

                if (!cardKey) {
                    console.error('❌ 无法获取卡密信息');
                    alert('无法获取卡密信息，请确保已输入有效的卡密');
                    return;
                }

                console.log('✅ 最终使用的卡密:', cardKey);

                // 在新窗口中打开详细结果页面，使用渐进式加载
                const detailedUrl = `detailed_result.html?cardKey=${encodeURIComponent(cardKey)}&progressive=true`;
                console.log('🚀 打开详细结果页面:', detailedUrl);

                window.open(detailedUrl, '_blank');
            };

            console.log('✅ 备用 viewCurrentResults 函数已定义');
        }

        // 强制修复强制完成按钮的白底白字问题
        function forceFixButtonStyles() {
            console.log('🔧 开始强制修复按钮样式');

            // 查找所有可能的强制完成按钮
            const buttons = document.querySelectorAll('button');
            let fixedCount = 0;

            buttons.forEach((button, index) => {
                if (button.textContent.includes('强制完成') ||
                    button.classList.contains('force-complete-btn') ||
                    (button.classList.contains('action-btn') && button.classList.contains('secondary'))) {

                    console.log(`🎯 找到需要修复的按钮 ${index + 1}:`, button.textContent.trim());

                    // 强制设置样式
                    button.style.setProperty('background', 'white', 'important');
                    button.style.setProperty('color', '#6366f1', 'important');
                    button.style.setProperty('border', '1px solid rgba(102, 126, 234, 0.3)', 'important');
                    button.style.setProperty('-webkit-text-fill-color', '#6366f1', 'important');

                    // 移除可能存在的旧事件监听器
                    button.removeEventListener('mouseenter', button._hoverEnter);
                    button.removeEventListener('mouseleave', button._hoverLeave);

                    // 添加新的悬停事件
                    button._hoverEnter = function() {
                        this.style.setProperty('background', 'rgba(102, 126, 234, 0.05)', 'important');
                        this.style.setProperty('color', '#6366f1', 'important');
                        this.style.setProperty('-webkit-text-fill-color', '#6366f1', 'important');
                    };

                    button._hoverLeave = function() {
                        this.style.setProperty('background', 'white', 'important');
                        this.style.setProperty('color', '#6366f1', 'important');
                        this.style.setProperty('-webkit-text-fill-color', '#6366f1', 'important');
                    };

                    button.addEventListener('mouseenter', button._hoverEnter);
                    button.addEventListener('mouseleave', button._hoverLeave);

                    fixedCount++;
                }
            });

            console.log(`✅ 已修复 ${fixedCount} 个按钮的样式`);
        }

        // 页面加载完成后立即修复
        document.addEventListener('DOMContentLoaded', forceFixButtonStyles);

        // 延迟修复，防止其他脚本覆盖
        setTimeout(forceFixButtonStyles, 100);
        setTimeout(forceFixButtonStyles, 500);
        setTimeout(forceFixButtonStyles, 1000);

        // 监听DOM变化，动态修复新添加的按钮
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
                let shouldFix = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldFix = true;
                    }
                });
                if (shouldFix) {
                    setTimeout(forceFixButtonStyles, 50);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    </script>

</body>
</html>