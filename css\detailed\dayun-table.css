/* 大运表格样式 */
.detailed-dayun-section {
    margin-top: 32px;
    overflow: hidden; /* 防止溢出 */
}

.detailed-dayun-section h4 {
    margin: 0 0 24px 0;
    color: #3b82f6;
    font-size: 22px;
    font-weight: 800;
    padding-bottom: 16px;
    position: relative;
    text-align: center;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8, #1e40af);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.detailed-dayun-section h4::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #1e40af);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 表格容器 */
.detailed-dayun-container {
    width: 100%;
    overflow-x: auto; /* 允许水平滚动 */
    position: relative;
    margin: 0;
    padding: 0;
}

/* 重构表格布局 - 确保所有行都有完全相同的尺寸 */
.detailed-dayun-table {
    width: 100%;
    table-layout: fixed; /* 固定表格布局，确保精确的列宽控制 */
    border-collapse: collapse; /* 合并边框 */
    border-spacing: 0;
    margin: 20px 0 0 0;
    padding: 0;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 表头样式 */
.detailed-dayun-table thead {
    background: linear-gradient(135deg, #4f46e5 0%, #4338ca 50%, #3730a3 100%);
}

.detailed-dayun-table thead tr {
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    background: transparent; /* 保持表头背景渐变可见 */
    height: 40px;
}

.detailed-dayun-table th {
    height: 40px;
    padding: 0 8px;
    color: #ffffff; /* 纯白色文字 */
    text-align: center;
    font-weight: 700;
    font-size: 14px; /* 稍微减小字体 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 增强文字阴影，提高可读性 */
    vertical-align: middle;
}

/* 表体行统一样式 */
.detailed-dayun-table tbody tr {
    height: 40px;
    max-height: 40px;
    min-height: 40px;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    background-color: rgba(255, 255, 255, 0.98);
}

/* 偶数行背景色 */
.detailed-dayun-table tbody tr:nth-child(even) {
    background-color: rgba(245, 243, 255, 0.7); /* 非常淡的紫色背景 */
}

/* 确保最后一行没有底部边框 */
.detailed-dayun-table tbody tr:last-child {
    border-bottom: none;
}

/* 统一所有单元格样式 */
.detailed-dayun-table td {
    height: 40px;
    max-height: 40px;
    min-height: 40px;
    padding: 0 8px;
    vertical-align: middle;
    text-align: center;
    font-size: 13px;
    color: #374151;
    font-weight: 500;
}

/* 列宽定义 */
.detailed-dayun-table th:nth-child(1),
.detailed-dayun-table td:nth-child(1) {
    width: 8%;
    min-width: 40px;
    font-weight: 700;
}

.detailed-dayun-table th:nth-child(2),
.detailed-dayun-table td:nth-child(2) {
    width: 12%;
    min-width: 60px;
    font-weight: 700;
}

.detailed-dayun-table th:nth-child(3),
.detailed-dayun-table td:nth-child(3) {
    width: 20%;
    min-width: 80px;
}

.detailed-dayun-table th:nth-child(4),
.detailed-dayun-table td:nth-child(4) {
    width: 60%;
    text-align: left;
    padding-left: 12px;
    font-size: 13px;
    line-height: 1.3;
}

/* 表体行悬停效果 */
.detailed-dayun-table tbody tr:hover {
    background-color: rgba(79, 70, 229, 0.05);
}

/* 当前大运行样式 - 完全相同的尺寸，只变更背景色 */
.detailed-dayun-table tr.current-dayun {
    background-color: rgba(79, 70, 229, 0.08);
    height: 40px;
    max-height: 40px;
    min-height: 40px;
    /* 不设置任何可能影响尺寸的属性 */
}

/* 为当前行添加左侧指示条，使用外部元素避免影响结构 */
.detailed-dayun-table tr.current-dayun td:first-child {
    position: relative;
}

.detailed-dayun-table tr.current-dayun td:first-child::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background-color: #4f46e5;
    z-index: 1;
    pointer-events: none;
}

/* 当前大运单元格文字样式 - 只修改颜色和字重，不影响尺寸 */
.detailed-dayun-table tr.current-dayun td {
    color: #4338ca;
    font-weight: 600;
}

/* 当前大运强调文本 */
.detailed-dayun-table tr.current-dayun strong {
    color: #3730a3;
    font-weight: 700;
}

/* 神煞列文字颜色 */
.detailed-dayun-table td:nth-child(4) {
    color: #4b5563;
}

/* 当前大运的神煞列文字颜色 */
.detailed-dayun-table tr.current-dayun td:nth-child(4) {
    color: #4b5563;
    font-weight: 500;
}

/* 强调文本样式 */
.detailed-dayun-table strong {
    color: #4f46e5;
    font-weight: 600;
}

/* 起运信息样式 */
.qiyun-info {
    margin: 16px 0;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.08), rgba(139, 92, 246, 0.08));
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-left: 4px solid var(--primary-color);
    border-radius: 12px;
    font-size: 15px;
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    position: relative;
    overflow: hidden;
}

.qiyun-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), rgba(139, 92, 246, 0.8));
}

/* 当前大运说明 */
.current-dayun-note {
    margin-top: 12px;
    padding: 8px 12px;
    font-size: 13px;
    color: var(--text-muted);
    text-align: center;
    background: rgba(99, 102, 241, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(99, 102, 241, 0.1);
}

/* 移动端适配 - 修复白底白字问题 */
@media (max-width: 768px) {
    /* 确保表头背景色和文字颜色有足够对比度 */
    .detailed-dayun-table thead {
        background: #4338ca !important; /* 使用纯色背景，避免渐变导致的问题 */
    }
    
    .detailed-dayun-table th {
        color: #ffffff !important; /* 强制白色文字 */
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* 增强文字阴影，提高可读性 */
        background-color: transparent !important; /* 确保背景透明，显示thead的背景色 */
        border: 1px solid rgba(255, 255, 255, 0.2) !important; /* 添加边框增强可见度 */
    }
    
    /* 确保表头在所有设备上可见 */
    .detailed-dayun-table thead th {
        background-color: #4338ca !important; /* 确保每个th单元格都有背景色 */
        position: relative !important;
        z-index: 5 !important;
    }
    
    /* 增强表头内容的可见性 */
    .detailed-dayun-table thead th::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2); /* 增加背景暗度 */
        z-index: -1;
        pointer-events: none;
    }
    
    /* 修复神煞列宽度不足问题 */
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 180px !important; /* 增加最小宽度 */
        width: auto !important; /* 允许自动调整宽度 */
        max-width: none !important; /* 移除最大宽度限制 */
        white-space: normal !important; /* 允许文本换行 */
        text-align: left !important;
        padding: 8px !important;
    }
    
    /* 调整神煞列文字样式 */
    .detailed-dayun-table td:nth-child(4) {
        font-size: 12px !important;
        line-height: 1.4 !important;
        word-break: break-word !important; /* 在单词间断行 */
        overflow-wrap: break-word !important; /* 确保长词能够换行 */
    }
}

/* 小屏幕额外优化 */
@media (max-width: 480px) {
    .detailed-dayun-table thead th {
        padding: 6px 4px !important;
        font-size: 12px !important;
        font-weight: 700 !important;
    }
    
    /* 确保表头文字在小屏设备上更清晰 */
    .detailed-dayun-table th {
        letter-spacing: 0.5px !important;
    }
    
    /* 针对小屏幕进一步优化神煞列 */
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 150px !important; /* 小屏设备上稍微减小最小宽度 */
        padding: 6px 8px !important;
        font-size: 11px !important; /* 减小字体大小 */
    }
}

/* 确保在暗模式下也有足够对比度 */
@media (prefers-color-scheme: dark) {
    .detailed-dayun-table thead {
        background: #4f46e5 !important;
    }
    
    .detailed-dayun-table th {
        color: #ffffff !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8) !important;
    }
}

/* 超小屏幕设备额外优化 */
@media (max-width: 360px) {
    /* 调整表格溢出处理 */
    .detailed-dayun-container {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        padding-bottom: 10px !important; /* 添加底部空间便于滚动 */
    }
    
    /* 进一步优化神煞列 */
    .detailed-dayun-table th:nth-child(4),
    .detailed-dayun-table td:nth-child(4) {
        min-width: 140px !important;
        font-size: 10px !important;
        line-height: 1.3 !important;
        padding: 5px 6px !important;
    }
} 