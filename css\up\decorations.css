/* 背景装饰效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(240, 147, 251, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(79, 172, 254, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 浮动装饰元素 */
.container::before {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    z-index: -1;
}

.container::after {
    content: '';
    position: absolute;
    bottom: 20%;
    left: 5%;
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, rgba(79, 172, 254, 0.1), rgba(102, 126, 234, 0.1));
    border-radius: 50%;
    animation: float 8s ease-in-out infinite reverse;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
} 