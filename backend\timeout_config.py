"""
超时配置模块
统一管理系统中所有超时设置
"""

# 超时配置（秒）
TIMEOUTS = {
    # HTTP请求相关超时
    'http_request': 30,          # HTTP请求超时（原来是10秒，增加到30秒）
    'bazi_api': 30,              # 八字API请求超时
    
    # LLM相关超时
    'llm_api': 120,              # LLM API调用超时（原来是60秒，增加到120秒）
    'llm_dimension': 300,        # 单个维度分析超时（5分钟）
    
    # 分析流程超时
    'analysis_total': 1800,      # 总分析超时（30分钟）
    'analysis_per_dimension': 300, # 每个维度最大分析时间（5分钟）
    
    # 系统维护超时
    'cleanup_interval': 3600,    # 状态清理间隔（1小时）
    'status_max_age': 86400,     # 状态最大保存时间（24小时）
}

# 重试配置
RETRY_CONFIG = {
    'max_retries': 3,            # 最大重试次数
    'retry_delay': 5,            # 重试延迟（秒）
    'backoff_factor': 2,         # 退避因子
}

def get_timeout(timeout_type):
    """
    获取指定类型的超时时间
    
    Args:
        timeout_type (str): 超时类型
        
    Returns:
        int: 超时时间（秒）
    """
    return TIMEOUTS.get(timeout_type, 30)  # 默认30秒

def get_retry_config():
    """
    获取重试配置
    
    Returns:
        dict: 重试配置
    """
    return RETRY_CONFIG.copy()

# 为了向后兼容，提供一些常用的超时常量
HTTP_TIMEOUT = TIMEOUTS['http_request']
LLM_TIMEOUT = TIMEOUTS['llm_api']
ANALYSIS_TIMEOUT = TIMEOUTS['analysis_total']
