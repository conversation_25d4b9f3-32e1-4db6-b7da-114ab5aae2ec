#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后提示词系统测试脚本
演示新系统的功能和使用方法
"""

import json
import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))

from prompt_manager import PromptManager
from prompt_evaluator import PromptEvaluator
from prompt_version_manager import PromptVersionManager, PromptABTester
from optimized_bazi_analyzer import OptimizedBaziAnalyzer

def test_prompt_manager():
    """测试提示词管理器"""
    print("=== 测试提示词管理器 ===")
    
    manager = PromptManager()
    
    # 测试获取可用维度
    dimensions = manager.get_available_dimensions()
    print(f"可用维度: {dimensions}")
    
    # 测试构建提示词
    bazi_info = """
    【八字基本信息】
    出生时间：1990年5月15日 14:30
    八字：庚午年 辛巳月 甲子日 辛未时
    
    【十神配置】
    天干十神：甲木(日主) 庚金(七杀) 辛金(正官) 辛金(正官)
    地支藏干十神：丁火(伤官) 戊土(偏财) 丙火(食神) 戊土(偏财) 癸水(正印) 己土(正财) 丁火(伤官) 乙木(比肩)
    """
    
    prompt = manager.build_prompt("性格", bazi_info)
    print(f"生成的提示词长度: {len(prompt)} 字符")
    print(f"提示词预览: {prompt[:200]}...")
    
    # 测试统计信息
    stats = manager.get_prompt_stats()
    print(f"系统统计: {stats}")
    
    print("✅ 提示词管理器测试完成\n")

def test_prompt_evaluator():
    """测试提示词评估器"""
    print("=== 测试提示词评估器 ===")
    
    evaluator = PromptEvaluator()
    
    # 模拟分析结果
    analysis_result = """
    ## 一、性格总论
    
    **核心特点：** 此人性格严谨务实，具有强烈的责任感和原则性
    
    根据天干正官和七杀的配置，此人外在表现严肃认真，做事有条理。
    
    ## 二、十神性格特质分析
    
    ### （一）外在性格表现（天干十神）
    
    **分析：** 天干中出现正官和七杀，表现为对外严谨守规，说话有条理，给人可靠稳重的感觉。同时具有威严感，不怒自威。
    
    ### （二）内在性格驱动（地支十神）
    
    **分析：** 地支藏干中的正印体现内在的学习欲望和精神追求，偏财显示对物质的关注，伤官表现为内心的表达欲望。
    
    ## 三、具体性格表现分析
    
    1. **人际交往：** 在人际交往中表现得比较严肃，有原则性
    2. **处事风格：** 做事按部就班，有条理性
    3. **情绪特点：** 情绪相对稳定，不轻易表露
    
    ## 四、性格发展建议
    
    ### （一）优势发挥
    1. **责任感强：** 可以在管理岗位发挥优势
    2. **原则性强：** 适合需要公正性的工作
    
    ### （二）短板改善
    1. **过于严肃：** 建议适当增加亲和力
    2. **缺乏灵活性：** 可以培养变通能力
    
    ## 五、性格总结
    
    综合来看，此人是典型的责任型性格，适合在需要严谨和原则性的环境中发展。
    """
    
    # 模拟八字信息
    bazi_info = {
        "天干十神": ["七杀", "正官", "正官"],
        "地支藏干十神": [["伤官", "偏财"], ["食神", "偏财"], ["正印"], ["正财", "伤官", "比肩"]]
    }
    
    # 进行评估
    evaluation = evaluator.evaluate_analysis_result(analysis_result, bazi_info, "性格")
    
    print(f"整体评分: {evaluation['overall_score']:.2f}")
    print(f"详细评分: {evaluation['detailed_scores']}")
    print(f"发现问题: {evaluation['issues']}")
    print(f"改进建议: {evaluation['suggestions']}")
    
    print("✅ 提示词评估器测试完成\n")

def test_version_manager():
    """测试版本管理器"""
    print("=== 测试版本管理器 ===")
    
    version_manager = PromptVersionManager()
    
    # 列出现有版本
    versions = version_manager.list_versions()
    print(f"现有版本数量: {len(versions)}")
    
    # 创建测试版本
    test_version_name = f"test_v{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    success = version_manager.create_version(
        test_version_name,
        "测试版本 - 自动创建",
        base_version=None
    )
    
    if success:
        print(f"✅ 成功创建测试版本: {test_version_name}")
        
        # 获取版本信息
        version_info = version_manager.get_version_info(test_version_name)
        print(f"版本信息: {version_info}")
        
        # 删除测试版本
        delete_success = version_manager.delete_version(test_version_name)
        if delete_success:
            print(f"✅ 成功删除测试版本: {test_version_name}")
    else:
        print("❌ 创建测试版本失败")
    
    print("✅ 版本管理器测试完成\n")

def test_ab_testing():
    """测试A/B测试功能"""
    print("=== 测试A/B测试功能 ===")
    
    version_manager = PromptVersionManager()
    ab_tester = PromptABTester(version_manager)
    
    # 创建测试实验（使用现有版本或默认版本）
    experiment_name = "personality_analysis_test"
    variants = ["current"]  # 使用当前版本作为测试
    
    success = ab_tester.create_experiment(
        experiment_name,
        variants,
        description="性格分析优化测试"
    )
    
    if success:
        print(f"✅ 成功创建A/B测试实验: {experiment_name}")
        
        # 测试用户分配
        test_users = ["user_001", "user_002", "user_003"]
        for user_id in test_users:
            variant = ab_tester.get_variant(user_id, experiment_name)
            print(f"用户 {user_id} 分配到变体: {variant}")
            
            # 记录测试结果
            ab_tester.record_result(user_id, experiment_name, "quality_score", 0.85)
        
        print("✅ A/B测试记录完成")
    else:
        print("❌ 创建A/B测试实验失败")
    
    print("✅ A/B测试功能测试完成\n")

def test_optimized_analyzer():
    """测试优化后的分析器"""
    print("=== 测试优化后的分析器 ===")
    
    # 模拟八字数据
    bazi_data = {
        "天干": ["庚", "辛", "甲", "辛"],
        "地支": ["午", "巳", "子", "未"],
        "天干十神": ["七杀", "正官", "日主", "正官"],
        "地支藏干十神": [
            ["伤官", "偏财"],
            ["食神", "偏财"], 
            ["正印"],
            ["正财", "伤官", "比肩"]
        ]
    }
    
    # 创建优化后的分析器
    analyzer = OptimizedBaziAnalyzer(
        personality_only=True,  # 只测试性格维度
        bazi_data=bazi_data,
        enable_evaluation=True,
        enable_ab_testing=False  # 暂时关闭A/B测试
    )
    
    print("🚀 开始分析...")
    
    # 分析单个维度
    result = analyzer.analyze_dimension("性格", user_id="test_user")
    
    print(f"分析维度: {result['dimension']}")
    print(f"使用版本: {result['version_used']}")
    print(f"分析内容长度: {len(result['analysis_content'])} 字符")
    
    if result.get('evaluation'):
        eval_result = result['evaluation']
        print(f"质量评分: {eval_result['overall_score']:.2f}")
        if eval_result.get('issues'):
            print(f"发现问题: {eval_result['issues'][:3]}")  # 只显示前3个问题
    
    if result.get('error'):
        print(f"❌ 分析出错: {result['error']}")
    else:
        print("✅ 分析完成")
    
    # 获取系统统计
    stats = analyzer.get_system_stats()
    print(f"系统统计: {stats}")
    
    print("✅ 优化后分析器测试完成\n")

def main():
    """主测试函数"""
    print("🧪 开始测试优化后的提示词系统")
    print("=" * 50)
    
    try:
        # 依次测试各个组件
        test_prompt_manager()
        test_prompt_evaluator()
        test_version_manager()
        test_ab_testing()
        test_optimized_analyzer()
        
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
