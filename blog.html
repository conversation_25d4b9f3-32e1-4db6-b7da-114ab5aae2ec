<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 咨询解答博客 - 八字分析系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/fonts.css">
    <!-- Google Fonts 已替换为本地字体文件 -->
    <link rel="stylesheet" href="css/blog.css">
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-to-home">
            ← 返回首页
        </a>
        
        <div class="blog-hero">
            <h1>💬 咨询解答博客</h1>
            <p>专业的八字命理解读，深入的案例分析，助您更好地理解命理奥秘</p>
        </div>
        
        <div class="blog-nav">
            <button class="nav-button active" data-category="all">全部文章</button>
            <button class="nav-button" data-category="analysis">案例分析</button>
            <button class="nav-button" data-category="theory">理论知识</button>
            <button class="nav-button" data-category="fortune">运势解读</button>
            <button class="nav-button" data-category="qa">问答精选</button>
        </div>
            <div class="blog-content">
            <div class="articles-container" id="articles-container">
                <!-- 文章内容将通过JavaScript动态加载 -->
            </div>
            
            <div class="sidebar">
                <div class="sidebar-card">
                    <h3 class="sidebar-title">🔥 热门文章</h3>
                    <div class="popular-articles" id="popular-articles">
                        <!-- 热门文章将通过JavaScript动态加载 -->
                    </div>
                </div>
                
                <div class="sidebar-card">
                    <h3 class="sidebar-title">📂 文章分类</h3>
                    <div class="categories-list" id="categories-list">
                        <!-- 文章分类将通过JavaScript动态加载 -->
                    </div>
                </div>
                
                <div class="sidebar-card expert-profile">
                    <h3 class="sidebar-title">👨‍🏫 专家简介</h3>
                    <div class="expert-info">
                        <div class="expert-avatar">
                            <div class="avatar-placeholder">天</div>
                        </div>
                        <div class="expert-details">
                            <h4 class="expert-name">天命之谓性</h4>
                            <p class="expert-title">资深八字命理师</p>
                        </div>
                    </div>
                    
                    <div class="expert-credentials">
                        <div class="credential-item">
                            <span class="credential-icon">🎓</span>
                            <span class="credential-text">省属师范大学本科毕业</span>
                        </div>
                        <div class="credential-item">
                            <span class="credential-icon">📚</span>
                            <span class="credential-text">服务客户超过3000人</span>
                        </div>
                        <div class="credential-item">
                            <span class="credential-icon">🏆</span>
                            <span class="credential-text">精通八字+紫微斗数</span>
                        </div>
                        <div class="credential-item">
                            <span class="credential-icon">📖</span>
                            <span class="credential-text">擅长感情问题</span>
                        </div>
                    </div>
                    
                    <div class="contact-info">
                        <h5 class="contact-title">📞 联系方式</h5>
                        <div class="contact-methods">
                            <div class="contact-item">
                                <span class="contact-icon">📱</span>
                                <span class="contact-text">微信：zwds120</span>
                                <button class="copy-btn" data-copy="zwds120">复制</button>
                            </div>
                
                        </div>
                    </div>
                    
                    <div class="consultation-cta">
                        <button class="consultation-btn">💬 立即咨询</button>
                    </div>
                </div>
                
                <div class="sidebar-card">
                    <h3 class="sidebar-title">💡 专家提醒</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6; font-size: 0.9rem;">
                        八字命理仅供参考，不应完全依赖。人生的发展需要结合个人努力、环境因素等多方面考虑。建议理性对待命理分析结果。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="pagination">
            <a href="#" class="page-button">← 上一页</a>
            <a href="#" class="page-button active">1</a>
            <a href="#" class="page-button">2</a>
            <a href="#" class="page-button">3</a>
            <a href="#" class="page-button">下一页 →</a>
        </div>
        
        <div class="footer">
            <p>© 2024 八字分析系统 | 专业命理分析服务</p>
        </div>
    </div>
    
    <script>
        // 全局变量存储文章数据
        let articlesData = [];
        let popularArticlesData = [];
        let categoriesData = [];
        let currentPage = 1;
        let articlesPerPage = 5; // 每页显示的文章数量
        let currentCategory = 'all';
        
        // 动态加载文章数据
        async function loadArticlesData() {
            try {
                // 并行加载所有数据
                const [articlesResponse, popularResponse, categoriesResponse] = await Promise.all([
                    fetch('./articles/articles.json'),
                    fetch('./articles/popular-articles.json'),
                    fetch('./articles/categories.json')
                ]);
                
                // 检查响应状态
                if (!articlesResponse.ok) throw new Error(`加载文章数据失败: ${articlesResponse.status}`);
                if (!popularResponse.ok) throw new Error(`加载热门文章失败: ${popularResponse.status}`);
                if (!categoriesResponse.ok) throw new Error(`加载分类数据失败: ${categoriesResponse.status}`);
                
                articlesData = await articlesResponse.json();
                popularArticlesData = await popularResponse.json();
                categoriesData = await categoriesResponse.json();
                
                // 渲染所有内容
                renderArticles('all');
                renderPopularArticles();
                renderCategories();
                
                // 重新设置事件监听器
                setupArticleEvents();
                setupSidebarEvents();
                
            } catch (error) {
                console.error('加载文章数据失败:', error);
                // 如果加载失败，显示错误信息
                document.getElementById('articles-container').innerHTML = '<p style="text-align: center; color: #666;">文章加载失败，请刷新页面重试</p>';
            }
        }
        
        // 渲染文章列表
        function renderArticles(filterCategory = 'all', page = 1) {
            currentCategory = filterCategory;
            currentPage = page;
            
            const container = document.getElementById('articles-container');
            const filteredArticles = filterCategory === 'all' 
                ? articlesData 
                : articlesData.filter(article => article.category === filterCategory);
            
            // 计算分页
            const totalArticles = filteredArticles.length;
            const totalPages = Math.ceil(totalArticles / articlesPerPage);
            const startIndex = (page - 1) * articlesPerPage;
            const endIndex = Math.min(startIndex + articlesPerPage, totalArticles);
            const articlesToShow = filteredArticles.slice(startIndex, endIndex);
            
            // 更新分页UI
            updatePagination(page, totalPages);
            
            // 渲染文章列表
            container.innerHTML = articlesToShow.map(article => {
                const categoryName = getCategoryName(article.category);
                return `
                    <article class="article-card" data-category="${article.category}" data-id="${article.id}">
                        <div class="article-meta">
                            <span class="article-category">${categoryName}</span>
                            <span class="article-date">
                                📅 ${article.date}
                            </span>
                        </div>
                        <h2 class="article-title">${article.title}</h2>
                        <p class="article-excerpt">
                            ${article.excerpt}
                        </p>
                        <div class="article-tags">
                            ${article.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                        <a href="#" class="read-more">阅读全文 →</a>
                    </article>
                `;
            }).join('');
            
            // 如果没有文章，显示提示信息
            if (articlesToShow.length === 0) {
                container.innerHTML = '<p style="text-align: center; padding: 40px; color: var(--text-secondary);">该分类下暂无文章</p>';
            }
        }
        
        // 渲染热门文章
        function renderPopularArticles() {
            const container = document.getElementById('popular-articles');
            container.innerHTML = popularArticlesData.map((article, index) => `
                <div class="popular-item" data-id="${article.id}">
                    <div class="popular-number">${index + 1}</div>
                    <div class="popular-content">
                        <div class="popular-title">${article.title}</div>
                        <div class="popular-date">${article.date}</div>
                    </div>
                </div>
            `).join('');
        }
        
        // 渲染分类列表
        function renderCategories() {
            const container = document.getElementById('categories-list');
            container.innerHTML = categoriesData.map(category => `
                <div class="category-item" data-category="${category.id}">
                    <span class="category-name">${category.name}</span>
                    <span class="category-count">${category.count}</span>
                </div>
            `).join('');
        }
        
        // 获取分类中文名称
        function getCategoryName(categoryId) {
            const categoryMap = {
                'analysis': '案例分析',
                'theory': '理论知识',
                'fortune': '运势解读',
                'qa': '问答精选',
                'guide': '开运指南'
            };
            return categoryMap[categoryId] || categoryId;
        }
        
        // 分类筛选功能
        // 设置导航按钮事件监听器
        function setupNavButtons() {
            const navButtons = document.querySelectorAll('.nav-button');
            
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的active类
                    navButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前按钮的active类
                    this.classList.add('active');
                    
                    const category = this.dataset.category;
                    
                    // 重新渲染文章列表（始终从第1页开始）
                    renderArticles(category, 1);
                    
                    // 重新设置文章卡片事件监听器
                    setupArticleEvents();
                });
            });
        }
        
        // 设置文章卡片点击事件
        function setupArticleEvents() {
            const articleCards = document.querySelectorAll('.article-card');
            articleCards.forEach(card => {
                card.addEventListener('click', function(event) {
                    // 阻止默认事件，防止点击"阅读全文"链接时重复触发
                    if (event.target.classList.contains('read-more')) {
                        event.preventDefault();
                    }
                    
                    const articleId = this.dataset.id;
                    if (articleId) {
                        window.location.href = `article.html?id=${articleId}`;
                    } else {
                        console.warn('文章ID缺失，无法跳转');
                    }
                });
            });
            
            // 为"阅读全文"链接单独绑定事件
            const readMoreLinks = document.querySelectorAll('.read-more');
            readMoreLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault(); // 阻止默认行为
                    const articleCard = this.closest('.article-card');
                    const articleId = articleCard.dataset.id;
                    if (articleId) {
                        window.location.href = `article.html?id=${articleId}`;
                    }
                });
            });
        }
        
        // 设置侧边栏交互事件
        function setupSidebarEvents() {
            // 热门文章点击事件
            const popularItems = document.querySelectorAll('.popular-item');
            popularItems.forEach(item => {
                item.addEventListener('click', function() {
                    const articleId = this.dataset.id;
                    if (articleId) {
                        console.log('点击热门文章，ID:', articleId);
                        window.location.href = `article.html?id=${articleId}`;
                    } else {
                        console.warn('热门文章ID缺失，无法跳转');
                    }
                });
            });
            
            // 分类列表点击事件
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', function() {
                    const categoryName = this.querySelector('.category-name').textContent;
                    console.log('点击分类:', categoryName);
                    
                    // 根据分类名称找到对应的导航按钮并触发点击
                    const categoryMap = {
                        '案例分析': 'analysis',
                        '理论知识': 'theory',
                        '运势解读': 'fortune',
                        '问答精选': 'qa',
                        '开运指南': 'guide'
                    };
                    
                    const targetCategory = categoryMap[categoryName];
                    if (targetCategory) {
                        const targetButton = document.querySelector(`[data-category="${targetCategory}"]`);
                        if (targetButton) {
                            targetButton.click();
                        }
                    }
                });
            });
        }
        
        // 更新分页UI
        function updatePagination(currentPage, totalPages) {
            const paginationContainer = document.querySelector('.pagination');
            
            // 不需要分页时隐藏分页容器
            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            } else {
                paginationContainer.style.display = 'flex';
            }
            
            let paginationHTML = '';
            
            // 上一页按钮
            if (currentPage > 1) {
                paginationHTML += `<a href="#" class="page-button prev-page">← 上一页</a>`;
            } else {
                paginationHTML += `<span class="page-button disabled">← 上一页</span>`;
            }
            
            // 页码按钮
            // 显示策略：当页数较多时，显示当前页附近的页码和首尾页码
            const maxPagesToShow = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
            let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
            
            // 调整startPage，确保显示正确数量的页码
            if (endPage - startPage + 1 < maxPagesToShow && startPage > 1) {
                startPage = Math.max(1, endPage - maxPagesToShow + 1);
            }
            
            // 显示首页
            if (startPage > 1) {
                paginationHTML += `<a href="#" class="page-button" data-page="1">1</a>`;
                if (startPage > 2) {
                    paginationHTML += `<span class="page-ellipsis">...</span>`;
                }
            }
            
            // 显示中间页码
            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    paginationHTML += `<a href="#" class="page-button active" data-page="${i}">${i}</a>`;
                } else {
                    paginationHTML += `<a href="#" class="page-button" data-page="${i}">${i}</a>`;
                }
            }
            
            // 显示尾页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHTML += `<span class="page-ellipsis">...</span>`;
                }
                paginationHTML += `<a href="#" class="page-button" data-page="${totalPages}">${totalPages}</a>`;
            }
            
            // 下一页按钮
            if (currentPage < totalPages) {
                paginationHTML += `<a href="#" class="page-button next-page">下一页 →</a>`;
            } else {
                paginationHTML += `<span class="page-button disabled">下一页 →</span>`;
            }
            
            paginationContainer.innerHTML = paginationHTML;
            
            // 添加页码按钮事件监听
            setupPaginationEvents();
        }
        
        // 设置分页按钮事件
        function setupPaginationEvents() {
            // 页码按钮点击事件
            const pageButtons = document.querySelectorAll('.pagination .page-button:not(.disabled)');
            pageButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    if (this.classList.contains('prev-page')) {
                        // 上一页
                        renderArticles(currentCategory, currentPage - 1);
                        // 滚动到页面顶部
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    } else if (this.classList.contains('next-page')) {
                        // 下一页
                        renderArticles(currentCategory, currentPage + 1);
                        // 滚动到页面顶部
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    } else {
                        // 指定页码
                        const page = parseInt(this.getAttribute('data-page'));
                        if (!isNaN(page)) {
                            renderArticles(currentCategory, page);
                            // 滚动到页面顶部
                            window.scrollTo({top: 0, behavior: 'smooth'});
                        }
                    }
                });
            });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 加载数据并设置导航按钮
            loadArticlesData().then(() => {
                setupNavButtons();
            }).catch(error => {
                console.error('初始化失败:', error);
            });
            
            // 专家简介卡片交互
            document.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const copyData = this.getAttribute('data-copy');
                    let textToCopy;
                    
                    if (copyData) {
                        textToCopy = copyData;
                    } else {
                        const contactText = this.parentElement.querySelector('.contact-text').textContent;
                        // 提取微信号（冒号后面的部分）
                        if (contactText.includes('微信：')) {
                            textToCopy = contactText.split('微信：')[1];
                        } else if (contactText.includes('邮箱：')) {
                            textToCopy = contactText.split('邮箱：')[1];
                        } else {
                            textToCopy = contactText;
                        }
                    }
                    
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        const originalText = this.textContent;
                        this.textContent = '已复制';
                        this.style.background = '#10b981';
                        setTimeout(() => {
                            this.textContent = originalText;
                            this.style.background = '';
                        }, 2000);
                    }).catch(() => {
                        alert('复制失败，请手动复制：' + textToCopy);
                    });
                });
            });
            
            document.querySelector('.consultation-btn')?.addEventListener('click', function() {
                // 创建模态框显示微信二维码
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                    cursor: pointer;
                `;
                
                const qrContainer = document.createElement('div');
                qrContainer.style.cssText = `
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    max-width: 90%;
                    max-height: 90%;
                    position: relative;
                `;
                
                const qrImage = document.createElement('img');
                qrImage.src = 'pic/我的微信号.jpg';
                qrImage.alt = '微信二维码';
                qrImage.style.cssText = `
                    max-width: 300px;
                    max-height: 300px;
                    width: 100%;
                    height: auto;
                    display: block;
                    margin: 0 auto;
                `;
                
                const title = document.createElement('h3');
                title.textContent = '扫码添加微信咨询';
                title.style.cssText = `
                    margin: 0 0 15px 0;
                    color: #333;
                    font-size: 18px;
                `;
                
                const subtitle = document.createElement('p');
                subtitle.textContent = '微信号：zwds120';
                subtitle.style.cssText = `
                    margin: 15px 0 0 0;
                    color: #666;
                    font-size: 14px;
                `;
                
                const closeBtn = document.createElement('button');
                closeBtn.textContent = '×';
                closeBtn.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;
                
                qrContainer.appendChild(closeBtn);
                qrContainer.appendChild(title);
                qrContainer.appendChild(qrImage);
                qrContainer.appendChild(subtitle);
                modal.appendChild(qrContainer);
                
                // 点击关闭按钮或背景关闭模态框
                const closeModal = () => {
                    document.body.removeChild(modal);
                };
                
                closeBtn.addEventListener('click', closeModal);
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeModal();
                    }
                });
                
                // 阻止容器点击事件冒泡
                qrContainer.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
                
                document.body.appendChild(modal);
            });
        });
        
        // 添加动画效果
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes fadeInDown {
                from {
                    opacity: 0;
                    transform: translateY(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>