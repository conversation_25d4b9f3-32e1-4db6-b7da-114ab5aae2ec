#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终解决方案
"""

import requests
import time

def test_final_solution():
    """测试最终解决方案"""
    print("🎯 测试最终解决方案")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 发送一个新请求
    print("\n1. 发送新的八字分析请求")
    print("-" * 40)
    
    payload = {
        "cardKey": "wdd",
        "year": "1990",
        "month": "3",
        "day": "25",
        "hour": "巳时",
        "gender": "0"
    }
    
    print(f"📤 发送请求: {payload}")
    
    response = requests.post('http://localhost:5000/webhook/bazi-analysis', json=payload)
    if response.status_code == 200:
        result = response.json()
        request_id = result.get('requestId')
        print(f"✅ 请求成功，ID: {request_id}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return False
    
    # 立即查询结果（应该返回处理中状态）
    print("\n2. 立即查询结果（应该显示处理中）")
    print("-" * 40)
    
    result_response = requests.get('http://localhost:5000/api/get_result/wdd')
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        
        print(f"📊 响应数据:")
        print(f"   完成状态: {result_data.get('completed', 'N/A')}")
        print(f"   处理中: {result_data.get('processing', 'N/A')}")
        print(f"   请求ID: {result_data.get('request_id', 'N/A')}")
        print(f"   状态: {result_data.get('status', 'N/A')}")
        
        if result_data.get('processing') == True:
            print("✅ 成功！API正确返回了处理中状态")
            
            # 等待处理完成
            print("\n3. 等待处理完成...")
            print("-" * 40)
            
            max_wait = 60  # 最多等待60秒
            wait_time = 0
            
            while wait_time < max_wait:
                time.sleep(5)
                wait_time += 5
                
                print(f"⏳ 等待中... ({wait_time}/{max_wait}秒)")
                
                # 查询结果
                check_response = requests.get('http://localhost:5000/api/get_result/wdd')
                if check_response.status_code == 200:
                    check_data = check_response.json()
                    
                    if check_data.get('completed'):
                        result_obj = check_data.get('result', {})
                        bz = result_obj.get('data', {}).get('bz', {})
                        
                        if bz:
                            birth_info = bz.get('8', '未知')
                            print(f"✅ 处理完成！")
                            print(f"   生辰: {birth_info}")
                            
                            # 检查是否是我们刚才提交的时间
                            if '1990' in birth_info and ('二月' in birth_info or '三月' in birth_info):
                                print("✅ 返回了正确的1990年数据")
                                return True
                            else:
                                print(f"❌ 返回了错误的数据: {birth_info}")
                                print("   应该是1990年二月或三月的数据")
                                return False
                        else:
                            print("❌ 没有八字数据")
                            return False
                    elif check_data.get('processing'):
                        print(f"   仍在处理中...")
                        continue
                    else:
                        print(f"   状态未知: {check_data}")
                        continue
                else:
                    print(f"❌ 查询失败: {check_response.status_code}")
                    return False
            
            print("⏰ 等待超时，但这可能是正常的（LLM分析需要时间）")
            return False
            
        elif result_data.get('completed'):
            # 如果直接返回了已完成的结果，检查是否是正确的数据
            result_obj = result_data.get('result', {})
            bz = result_obj.get('data', {}).get('bz', {})
            
            if bz:
                birth_info = bz.get('8', '未知')
                print(f"📅 返回的生辰: {birth_info}")
                
                if '1990' in birth_info and ('二月' in birth_info or '三月' in birth_info):
                    print("✅ 直接返回了正确的1990年数据")
                    return True
                else:
                    print(f"❌ 返回了错误的数据: {birth_info}")
                    print("   应该是1990年二月或三月的数据")
                    return False
            else:
                print("❌ 没有八字数据")
                return False
        else:
            print("❌ API没有返回处理中状态或完成状态")
            print(f"   实际返回: {result_data}")
            return False
    else:
        print(f"❌ 查询失败: {result_response.status_code}")
        return False

def main():
    """主函数"""
    print("🎯 最终解决方案测试")
    print("=" * 60)
    
    success = test_final_solution()
    
    if success:
        print("\n🎉 问题已完全解决！")
        print("💡 系统现在能够:")
        print("  - 正确识别正在处理中的最新请求")
        print("  - 返回处理中状态而不是旧数据")
        print("  - 处理完成后返回最新结果")
        print("  - 输入不同时间生成不同的八字分析")
        print("🔗 你现在可以在 http://localhost:5000/ 测试不同的日期时间")
        print("📝 每次输入不同的日期时间都会得到对应的正确结果")
    else:
        print("\n⚠️ 测试未完全通过")
        print("💡 可能的原因:")
        print("  - LLM分析需要更长时间")
        print("  - 网络连接问题")
        print("  - 需要进一步调试")
        print("🔗 请在浏览器中手动测试: http://localhost:5000/")

if __name__ == "__main__":
    main()
