/* 导航链接样式 */
.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 20px;
    padding: 12px 20px;
    border-radius: var(--radius-medium);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color-light);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.nav-link:hover {
    transform: translateX(-5px) translateY(-3px) scale(1.05);
    color: var(--primary-dark);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.2);
    border-color: var(--primary-color);
    backdrop-filter: blur(15px);
}

.nav-link::before {
    content: '🏠';
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

.nav-link:hover::before {
    transform: scale(1.2) rotate(5deg);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .nav-link {
        padding: 10px 16px;
        margin-bottom: 16px;
        font-size: 0.9rem;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .nav-link {
        padding: 8px 12px;
        font-size: 0.85rem;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .nav-link {
        background: var(--bg-glass);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .nav-link:hover {
        background: var(--bg-secondary);
        color: #94a3b8;
        border-color: #94a3b8;
    }
}