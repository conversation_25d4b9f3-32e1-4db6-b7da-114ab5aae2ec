/* 八字反推集成样式 */

/* 输入方式选择器 */
.input-mode-selector {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
    animation: fadeInUp 0.6s ease-out;
}

/* 深色模式下的输入方式选择器 */
@media (prefers-color-scheme: dark) {
    .input-mode-selector {
        background: linear-gradient(135deg, rgba(45, 55, 72, 0.8) 0%, rgba(26, 32, 44, 0.9) 100%);
        border: 1px solid rgba(74, 85, 104, 0.6);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    
    .radio-option {
        background: rgba(45, 55, 72, 0.6);
        border: 2px solid rgba(74, 85, 104, 0.4);
    }
    
    .radio-option:hover {
        background: rgba(74, 85, 104, 0.7);
        border-color: rgba(129, 140, 248, 0.6);
        box-shadow: 0 4px 16px rgba(129, 140, 248, 0.2);
    }
    
    .radio-label {
        color: #e2e8f0;
    }
    
    .radio-option input[type="radio"]:checked + .radio-label {
        color: #818cf8;
        text-shadow: 0 0 8px rgba(129, 140, 248, 0.4);
    }
    
    /* 八字输入区域深色模式 */
    .bazi-input-section {
        background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 0.95) 100%);
        border: 2px solid rgba(129, 140, 248, 0.6);
        backdrop-filter: blur(15px);
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    }
    
    .bazi-input-section .section-title {
        color: #e2e8f0;
        text-shadow: 0 2px 8px rgba(129, 140, 248, 0.3);
    }
    
    .bazi-input-section .input-group label {
        color: #cbd5e0;
        font-weight: 600;
    }
    
    .bazi-input-section .input-group input,
    .bazi-input-section .input-group select,
    .bazi-input-section .input-group textarea {
        background: #ffffff !important;
        color: #1a202c !important;
        border: 2px solid rgba(74, 85, 104, 0.6);
        backdrop-filter: blur(10px);
    }
    
    .bazi-input-section .input-group input::placeholder,
    .bazi-input-section .input-group textarea::placeholder {
        color: #6b7280 !important;
    }
    
    /* 四柱输入框占位符深色模式 */
    #yearPillar::placeholder,
    #monthPillar::placeholder,
    #dayPillar::placeholder,
    #timePillar::placeholder {
        color: #6b7280 !important;
        opacity: 0.8;
    }
    
    .bazi-input-section .input-group input:focus,
    .bazi-input-section .input-group select:focus,
    .bazi-input-section .input-group textarea:focus {
        border-color: rgba(129, 140, 248, 0.8);
        box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
        background: #ffffff !important;
        color: #1a202c !important;
    }
    
    /* 输入模式切换深色模式 */
    .input-mode-toggle {
        background: rgba(45, 55, 72, 0.7);
        border: 1px solid rgba(74, 85, 104, 0.5);
        backdrop-filter: blur(10px);
    }
    
    .toggle-option {
        color: #cbd5e0;
    }
    
    .toggle-option:hover {
        background-color: rgba(74, 85, 104, 0.6);
    }
    
    .toggle-option span {
        color: #cbd5e0;
    }
    
    .toggle-option input[type="radio"]:checked + span {
        color: #818cf8;
        font-weight: 600;
    }
    
    /* 完整八字输入框深色模式 */
    #baziString {
        background: rgba(45, 55, 72, 0.8);
        color: #e2e8f0;
        border: 2px solid rgba(74, 85, 104, 0.6);
        backdrop-filter: blur(10px);
    }
    
    #baziString:focus {
        border-color: rgba(129, 140, 248, 0.8);
        box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2);
        background: rgba(45, 55, 72, 0.9);
    }
    
    #baziString::placeholder {
        color: #a0aec0;
    }
}

/* 输入方式选择器的父容器动画 */
.input-mode-section {
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    transform: translateY(20px);
}

.input-mode-section[style*="opacity: 1"] {
    transform: translateY(0);
}

/* 淡入向上动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: white;
    border: 2px solid transparent;
}

.radio-option:hover {
    background: #f8f9fa;
    border-color: #6c757d;
}

.radio-option input[type="radio"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.radio-option input[type="radio"]:checked + .radio-label {
    color: #0d6efd;
    font-weight: 600;
}

.radio-label {
    font-size: 16px;
    color: #495057;
    transition: color 0.3s ease;
}

/* 导入统一配色系统 */
@import url('./unified-colors.css');

/* 八字输入区域 */
.bazi-input-section {
    background: var(--gradient-bazi);
    border: 2px solid var(--color-accent);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    box-shadow: var(--shadow-accent);
}

/* 输入模式切换样式 */
.input-mode-toggle {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    padding: 8px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.toggle-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.toggle-option:hover {
    background-color: #f8f9fa;
}

.toggle-option input[type="radio"] {
    margin: 0;
}

.toggle-option span {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

/* 八字输入模式容器 */
.bazi-input-mode {
    transition: all 0.3s ease;
}

/* 完整八字输入框样式 */
#baziString {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 16px;
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s;
}

#baziString:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

#baziString::placeholder {
    color: #6c757d;
    font-size: 14px;
}

.bazi-input-section .section-title {
    color: var(--accent-700);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    font-size: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.bazi-input-section .input-group {
    position: relative;
    margin-bottom: 15px;
}

.bazi-input-section .input-group label {
    color: var(--accent-800);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.bazi-input-section .input-group input,
.bazi-input-section .input-group select {
    background: #ffffff !important;
    color: #1a202c !important;
    border: 2px solid var(--accent-300);
    border-radius: var(--radius-md);
    padding: 12px 40px 12px 15px;
    font-size: 16px;
    width: 100%;
    transition: var(--transition-normal);
}

.bazi-input-section .input-group input:focus,
.bazi-input-section .input-group select:focus {
    border-color: var(--color-accent);
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    outline: none;
}

.bazi-input-section .input-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    pointer-events: none;
}

/* 反推按钮 - 现代美观设计 */
.reverse-btn {
    background: var(--gradient-bazi-button);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
        0 10px 30px rgba(255, 107, 107, 0.3),
        0 6px 20px rgba(238, 90, 36, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 20px auto;
    position: relative;
    overflow: hidden;
}

.reverse-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.8s ease;
}

.reverse-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    border-radius: 50%;
}

.reverse-btn:hover {
    background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 50%, #ff9ff3 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 
        0 15px 40px rgba(255, 107, 107, 0.5),
        0 10px 25px rgba(238, 90, 36, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.reverse-btn:hover::before {
    left: 100%;
}

.reverse-btn:hover::after {
    width: 300px;
    height: 300px;
}

.reverse-btn:active {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 8px 25px rgba(255, 107, 107, 0.4),
        0 4px 15px rgba(238, 90, 36, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.reverse-btn span {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 反推结果区域 */
.reverse-results {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    border: 2px solid #4caf50;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.reverse-results h4 {
    color: #2e7d32;
    margin-bottom: 15px;
    text-align: center;
    font-size: 18px;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.result-item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateX(5px);
}

.result-item.selected {
    background: #c8e6c9;
    border-color: #4caf50;
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.result-date {
    font-weight: 600;
    color: #1976d2;
    font-size: 16px;
}

.result-time {
    color: #f57c00;
    font-weight: 500;
}

.result-weekday {
    color: #6a1b9a;
    font-size: 14px;
    background: #f3e5f5;
    padding: 4px 8px;
    border-radius: 4px;
}

.no-results {
    text-align: center;
    color: #d32f2f;
    font-style: italic;
    padding: 20px;
    background: #ffebee;
    border-radius: 8px;
    border: 1px solid #ffcdd2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .input-mode-selector {
        flex-direction: column;
        gap: 10px;
    }
    
    .radio-option {
        justify-content: center;
    }
    
    .result-item {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }
    
    .results-list {
        max-height: 250px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bazi-input-section {
    animation: fadeIn 0.5s ease-out;
}

.reverse-results {
    animation: fadeIn 0.3s ease-out;
}

.result-item {
    animation: fadeIn 0.3s ease-out;
}

/* 八字反推结果样式 */
.reverse-result {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #fff9e6, #fff3d3);
    border: 2px solid #d4af37;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

/* 农历输入区域样式 */
.lunar-input-section {
    background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
    border: 2px solid #4682b4;
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 6px 20px rgba(70, 130, 180, 0.15);
    transition: all 0.3s ease;
}

.lunar-input-section:hover {
    box-shadow: 0 8px 25px rgba(70, 130, 180, 0.25);
    transform: translateY(-2px);
}

.lunar-input-section .section-title {
    color: #2c5aa0;
    font-size: 1.4em;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(44, 90, 160, 0.1);
}

/* 农历转换按钮样式 - 现代美观设计 */
.convert-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.1em;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
        0 10px 30px rgba(102, 126, 234, 0.3),
        0 6px 20px rgba(118, 75, 162, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.convert-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.8s ease;
}

.convert-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    border-radius: 50%;
}

.convert-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 50%, #f093fb 100%);
    transform: translateY(-4px) scale(1.05);
    box-shadow: 
        0 15px 40px rgba(102, 126, 234, 0.5),
        0 10px 25px rgba(118, 75, 162, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.convert-btn:hover::before {
    left: 100%;
}

.convert-btn:hover::after {
    width: 300px;
    height: 300px;
}

.convert-btn:active {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 8px 25px rgba(102, 126, 234, 0.4),
        0 4px 15px rgba(118, 75, 162, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.convert-btn span {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 农历转换结果样式 */
.convert-result {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f0f8ff, #e1f5fe);
    border: 2px solid #4682b4;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(70, 130, 180, 0.2);
}

.convert-result h4 {
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.convert-result-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    margin-bottom: 10px;
}

.result-date, .result-time, .result-weekday {
    padding: 8px 12px;
    background: rgba(70, 130, 180, 0.1);
    border-radius: 6px;
    font-weight: bold;
    color: #2c5aa0;
}

.apply-convert-btn {
    background: linear-gradient(135deg, #32cd32, #228b22);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: auto;
}

.apply-convert-btn:hover {
    background: linear-gradient(135deg, #228b22, #006400);
    transform: translateY(-1px);
}

/* 闰月复选框样式 */
.lunar-input-section input[type="checkbox"] {
    margin-right: 4px;
    transform: scale(1.2);
}

.lunar-input-section label {
    display: flex;
    align-items: center;
    color: #2c5aa0;
    font-weight: bold;
    gap: 4px;
    margin-bottom: 0;
    width: fit-content;
}

/* 闰月行布局调整 */
.lunar-input-section .form-row {
    align-items: center;
}

.lunar-input-section .input-group {
    margin-bottom: 0;
}

.lunar-input-section .form-actions {
    margin-top: 0;
    text-align: left;
}

/* 加载状态 */
.reverse-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.reverse-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}