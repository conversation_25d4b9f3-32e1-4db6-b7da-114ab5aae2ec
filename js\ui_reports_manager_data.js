/**
 * 报告管理器 - 数据处理
 * 提供报告数据获取和处理功能
 */

// 扩展HistoryReportsManager类的数据处理功能
HistoryReportsManager.prototype.fetchHistoryReports = async function(page = 1, pageSize = 10) {
    console.log(`=== fetchHistoryReports 开始获取报告 (页码: ${page}, 每页数量: ${pageSize}) ===`);
    
    // 使用缓存减少请求
    const cacheKey = `reports_cache_${page}_${pageSize}`;
    const cachedData = sessionStorage.getItem(cacheKey);
    
    // 如果有缓存且未过期，直接使用缓存（缓存时间5分钟）
    if (cachedData) {
        try {
            const cache = JSON.parse(cachedData);
            if (Date.now() - cache.timestamp < 5 * 60 * 1000) { 
                console.log('使用缓存的报告数据，缓存时间:', new Date(cache.timestamp).toLocaleTimeString());
                return cache.data;
            } else {
                console.log('缓存已过期，重新获取数据');
            }
        } catch (e) {
            console.error('解析缓存数据失败:', e);
        }
    }
    
    // 获取API基础URL
    const apiBaseUrl = getApiBaseUrl();
    console.log('使用API基础URL:', apiBaseUrl);
    
    // 创建并发任务数组
    const tasks = [];
    
    // 任务1: 获取本地存储的报告
    const getLocalReportsTask = async () => {
        console.log('开始获取本地报告');
        try {
            const allLocalReports = JSON.parse(localStorage.getItem('baziReports') || '[]');
            console.log('本地报告总数量:', allLocalReports.length);
            
            // 添加来源标记
            const localReports = allLocalReports.map(report => ({
                ...report,
                source: 'local'
            }));
            
            // 确保本地报告包含必要的信息
            localReports.forEach((report, index) => {
                if (index < 5) { // 只打印前5条日志，避免日志过多
                    console.log(`处理本地报告 ${index + 1}:`, report.id);
                }
                this.ensureReportData(report);
            });
            
            return localReports;
        } catch (error) {
            console.error('获取本地报告失败:', error);
            return [];
        }
    };
    tasks.push(getLocalReportsTask());
    
    // 任务2: 获取服务器报告（为每个卡密创建单独的并发任务）
    const validatedCards = getValidatedCards();
    const serverReportsTasks = validatedCards.map(async (cardInfo) => {
        try {
            // 使用原始卡密，去除可能的时间戳后缀
            let originalCardKey = cardInfo.cardKey;
            
            // 如果卡密包含时间戳后缀（格式：原始卡密_时间戳_随机字符串），则提取原始卡密
            const timestampPattern = /_\d+_[a-f0-9]+$/;
            if (timestampPattern.test(originalCardKey)) {
                originalCardKey = originalCardKey.replace(timestampPattern, '');
                console.log(`检测到带时间戳的卡密，提取原始卡密: ${cardInfo.cardKey} -> ${originalCardKey}`);
            }
            
            // 添加分页和简洁模式参数
            console.log(`开始获取卡密 ${originalCardKey} 的服务器报告`);
            const requestUrl = `${apiBaseUrl}/api/reports/list?cardKey=${encodeURIComponent(originalCardKey)}&page=${page}&pageSize=${pageSize}&brief=true`;
            
            const response = await fetch(requestUrl);
            if (!response.ok) {
                console.error(`获取卡密 ${originalCardKey} 的报告失败: HTTP ${response.status}`);
                
                // 如果是401未授权错误，自动删除失效的卡密
                if (response.status === 401) {
                    console.warn(`卡密 ${originalCardKey} 验证失效，自动从缓存中删除`);
                    removeValidatedCard(cardInfo.cardKey); // 删除原始卡密（可能包含时间戳）
                    if (originalCardKey !== cardInfo.cardKey) {
                        removeValidatedCard(originalCardKey); // 也删除提取的原始卡密
                    }
                }
                
                return [];
            }
            
            const data = await response.json();
            console.log(`卡密 ${originalCardKey} 的服务器响应数据:`, data);
            
            if (data.success && data.data) {
                // 添加来源标记
                const serverReports = data.data.map(report => ({
                    ...report,
                    source: 'server'
                }));
                
                // 记录分页信息
                const paginationInfo = data.pagination || {};
                
                return {
                    reports: serverReports,
                    pagination: paginationInfo,
                    cardKey: originalCardKey
                };
            }
            return { reports: [], pagination: {}, cardKey: originalCardKey };
        } catch (error) {
            console.error(`获取卡密 ${cardInfo.cardKey} 的报告失败:`, error);
            return { reports: [], pagination: {}, cardKey: cardInfo.cardKey };
        }
    });
    
    // 添加所有服务器任务
    tasks.push(...serverReportsTasks);
    
    try {
        // 并发执行所有任务
        console.log(`开始并发执行 ${tasks.length} 个任务`);
        const startTime = Date.now();
        const results = await Promise.all(tasks);
        const endTime = Date.now();
        console.log(`所有任务完成，耗时: ${endTime - startTime}ms`);
        
        // 第一个结果是本地报告
        const localReports = results[0];
        console.log('获取到本地报告数量:', localReports.length);
        
        // 其余结果是服务器报告
        const serverResults = results.slice(1);
        
        // 合并所有服务器报告
        let allServerReports = [];
        let hasMoreServerData = false;
        
        serverResults.forEach(result => {
            if (result && result.reports) {
                allServerReports = [...allServerReports, ...result.reports];
                
                // 检查是否有更多数据
                if (result.pagination && result.reports.length >= pageSize) {
                    hasMoreServerData = true;
                }
            }
        });
        
        console.log('所有服务器报告数量:', allServerReports.length);
        
        // 合并本地和服务器报告
        let allReports = [];
        
        if (page === 1) {
            // 第一页：合并本地和服务器报告
            allReports = [...localReports, ...allServerReports];
            
            // 去重：如果本地和服务器都有相同ID的报告，优先保留本地报告
            const reportMap = new Map();
            allReports.forEach(report => {
                const existingReport = reportMap.get(report.id);
                if (!existingReport || report.source === 'local') {
                    reportMap.set(report.id, report);
                }
            });
            allReports = Array.from(reportMap.values());
        } else {
            // 其他页：只返回服务器报告（本地报告已在第一页全部返回）
            allReports = [...allServerReports];
        }
        
        // 按时间戳降序排序
        allReports.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        console.log('合并后的总报告数量:', allReports.length);
        console.log('=== fetchHistoryReports 完成 ===');
        
        // 构建结果对象
        const result = {
            reports: allReports,
            currentPage: page,
            pageSize: pageSize,
            hasMore: hasMoreServerData || (page === 1 && localReports.length > pageSize)
        };
        
        // 缓存结果
        const cacheData = {
            data: result,
            timestamp: Date.now()
        };
        sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
        
        return result;
    } catch (error) {
        console.error('获取报告失败:', error);
        
        // 获取本地报告作为备用
        try {
            const localReports = JSON.parse(localStorage.getItem('baziReports') || '[]')
                .map(report => ({
                    ...report,
                    source: 'local'
                }));
            
            // 对本地报告进行分页
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pagedLocalReports = localReports.slice(startIndex, endIndex);
            
            return {
                reports: pagedLocalReports,
                currentPage: page,
                pageSize: pageSize,
                hasMore: endIndex < localReports.length,
                isOfflineMode: true // 标记为离线模式
            };
        } catch (localError) {
            console.error('获取本地备用报告也失败:', localError);
            return {
                reports: [],
                currentPage: page,
                pageSize: pageSize,
                hasMore: false,
                error: error.message
            };
        }
    }
};

// 获取卡密筛选报告
HistoryReportsManager.prototype.fetchReportsByCardKey = async function(cardKey, page = 1, pageSize = 10) {
    // 使用缓存减少请求
    const cacheKey = `reports_card_${cardKey}_${page}_${pageSize}`;
    const cachedData = sessionStorage.getItem(cacheKey);
    
    // 如果有缓存且未过期，直接使用缓存
    if (cachedData) {
        try {
            const cache = JSON.parse(cachedData);
            if (Date.now() - cache.timestamp < 60 * 1000) { // 1分钟缓存
                console.log('使用缓存的卡密报告数据');
                return cache.data;
            }
        } catch (e) {
            console.error('解析缓存数据失败:', e);
        }
    }
    
    // 创建并发任务
    const tasks = [];
    
    // 任务1: 获取本地存储的报告并筛选
    const getLocalReportsTask = async () => {
        try {
            const localReports = this.getLocalReports().filter(
                report => report.cardKey === cardKey
            );
            return localReports.map(report => ({
                ...report,
                source: 'local'
            }));
        } catch (error) {
            console.error('获取本地报告失败:', error);
            return [];
        }
    };
    tasks.push(getLocalReportsTask());
    
    // 任务2: 获取服务器端的报告
    const getServerReportsTask = async () => {
        try {
            const apiBaseUrl = getApiBaseUrl();
            const response = await fetch(`${apiBaseUrl}/api/reports/card/${cardKey}?page=${page}&pageSize=${pageSize}`);
            if (!response.ok) {
                return { reports: [], pagination: {} };
            }
            
            const data = await response.json();
            if (data.success && data.data) {
                return {
                    reports: data.data.map(report => ({
                        ...report,
                        source: 'server'
                    })),
                    pagination: data.pagination || {}
                };
            }
            return { reports: [], pagination: {} };
        } catch (error) {
            console.warn(`获取卡密(${cardKey})报告失败:`, error);
            return { reports: [], pagination: {} };
        }
    };
    tasks.push(getServerReportsTask());
    
    try {
        // 并发执行所有任务
        console.log('开始并发获取卡密报告');
        const startTime = Date.now();
        const [localReports, serverResult] = await Promise.all(tasks);
        const endTime = Date.now();
        console.log(`卡密报告获取完成，耗时: ${endTime - startTime}ms`);
        
        // 解构服务器结果
        const { reports: serverReports, pagination } = serverResult;
        
        // 合并报告并按时间排序
        let allReports = [];
        
        if (page === 1) {
            // 第一页：合并本地和服务器报告
            allReports = [...localReports, ...serverReports];
        } else {
            // 其他页：只包含服务器报告
            allReports = [...serverReports];
        }
        
        // 排序
        allReports.sort((a, b) => {
            const dateA = new Date(a.timestamp);
            const dateB = new Date(b.timestamp);
            return dateB - dateA;
        });
        
        // 构建结果
        const result = {
            reports: allReports,
            currentPage: page,
            pageSize: pageSize,
            hasMore: serverReports.length >= pageSize
        };
        
        // 缓存结果
        const cacheData = {
            data: result,
            timestamp: Date.now()
        };
        sessionStorage.setItem(cacheKey, JSON.stringify(cacheData));
        
        return result;
    } catch (error) {
        console.error('获取卡密报告失败:', error);
        
        // 获取本地报告作为备用
        try {
            const localReports = this.getLocalReports().filter(
                report => report.cardKey === cardKey
            );
            
            // 对本地报告进行分页
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pagedLocalReports = localReports.slice(startIndex, endIndex);
            
            return {
                reports: pagedLocalReports,
                currentPage: page,
                pageSize: pageSize,
                hasMore: endIndex < localReports.length,
                isOfflineMode: true
            };
        } catch (localError) {
            console.error('获取本地备用报告也失败:', localError);
            return {
                reports: [],
                currentPage: page,
                pageSize: pageSize,
                hasMore: false,
                error: error.message
            };
        }
    }
};

// 获取本地报告
HistoryReportsManager.prototype.getLocalReports = function() {
    try {
        const reportsJson = localStorage.getItem('baziReports');
        if (reportsJson) {
            const reports = JSON.parse(reportsJson);
            return reports.map(report => ({
                ...report,
                source: 'local'
            }));
        }
    } catch (error) {
        console.error('解析本地报告失败:', error);
    }
    return [];
};

// 调试localStorage中的报告数据
HistoryReportsManager.prototype.debugLocalStorageReports = function() {
    console.log('=== 调试localStorage中的报告数据 ===');
    
    try {
        const baziReports = localStorage.getItem('baziReports');
        if (!baziReports) {
            console.log('localStorage中没有找到baziReports数据');
            return;
        }
        
        const reports = JSON.parse(baziReports);
        console.log('报告总数:', reports.length);
        
        reports.forEach((report, index) => {
            console.log(`报告 ${index + 1}:`);
            console.log('  ID:', report.id);
            console.log('  birthDate:', report.birthDate);
            console.log('  gender:', report.gender);
            console.log('  analysisData:', report.analysisData ? '存在' : '不存在');
            console.log('  resultData:', report.resultData ? '存在' : '不存在');
            console.log('  data:', report.data ? '存在' : '不存在');
            console.log('  报告对象的所有键:', Object.keys(report));
            
            if (report.analysisData) {
                console.log('  analysisData.birthDate:', report.analysisData.birthDate);
                console.log('  analysisData.gender:', report.analysisData.gender);
            }
            
            if (report.resultData && report.resultData.basicInfo) {
                console.log('  resultData.basicInfo.birthDate:', report.resultData.basicInfo.birthDate);
                console.log('  resultData.basicInfo.gender:', report.resultData.basicInfo.gender);
            }
            
            if (report.data) {
                console.log('  data对象的所有键:', Object.keys(report.data));
                console.log('  data.birthDate:', report.data.birthDate);
                console.log('  data.birth_date:', report.data.birth_date);
                console.log('  data.gender:', report.data.gender);
                
                if (report.data.basicInfo) {
                    console.log('  data.basicInfo:', report.data.basicInfo);
                    console.log('  data.basicInfo对象的所有键:', Object.keys(report.data.basicInfo));
                }
                
                if (report.data.requestData) {
                    console.log('  data.requestData:', report.data.requestData);
                    console.log('  data.requestData对象的所有键:', Object.keys(report.data.requestData));
                }
            }
            
            console.log('  完整报告对象:', report);
            console.log('---');
        });
    } catch (error) {
        console.error('调试localStorage报告数据失败:', error);
    }
};