# 八字分析结果查看器 - 模块化结构

本目录包含了八字分析结果查看器的模块化实现，将原本单一的大文件拆分为多个功能模块，便于维护和扩展。

## 文件结构

- **detailed_result_core.js**: 核心类和初始化逻辑
  - 负责初始化视图
  - 定义主类 `DetailedResultViewer`
  - 委托给各个模块的方法调用

- **detailed_result_loader.js**: 数据加载模块
  - 处理从API、文件、本地存储加载分析结果
  - 包含 `loadResultFromAPI`、`loadResultFromFile`、`loadResultFromLocal` 方法

- **detailed_result_display.js**: 显示模块
  - 负责格式化和显示分析结果
  - 包含 `displayResult`、`showError`、`showEmptyState` 等方法
  - 处理Markdown格式的内容转换

- **detailed_result_bazi_info.js**: 八字信息显示模块
  - 专门处理八字信息的生成和显示
  - 包含 `generateBaziInfoSection` 方法
  - 包含五行颜色处理辅助函数

- **detailed_result_utils.js**: 工具函数模块
  - 包含通用工具函数
  - 实现展开/收起功能
  - 处理标题编辑和保存
  - 实现报告下载功能

- **detailed_result_events.js**: 事件处理模块
  - 处理用户交互事件
  - 设置DOM事件监听
  - 绑定全局事件处理函数

## 使用方法

主入口文件 `../detailed_result.js` 通过ES模块导入各个功能模块，并在页面加载时初始化视图。

```javascript
// 导入主类
import { DetailedResultViewer } from './detailed_result/detailed_result_core.js';

// 页面加载完成后初始化
window.addEventListener('DOMContentLoaded', function() {
    window.detailedResultViewer = new DetailedResultViewer();
});
```

## 模块依赖关系

```
detailed_result.js
  └── detailed_result_core.js
      ├── detailed_result_loader.js
      ├── detailed_result_display.js
      │   └── detailed_result_bazi_info.js
      ├── detailed_result_events.js
      └── detailed_result_utils.js
```

## 优势

1. **可维护性**: 每个模块专注于特定功能，便于理解和修改
2. **可扩展性**: 易于添加新功能或修改现有功能
3. **代码复用**: 功能模块化后可在其他地方复用
4. **性能优化**: 支持按需加载，减少初始加载体积
5. **协作开发**: 多人可同时在不同模块工作，减少冲突 