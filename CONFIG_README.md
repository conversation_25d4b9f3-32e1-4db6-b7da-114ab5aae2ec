# 配置管理系统使用说明

## 概述

本项目现在支持通过配置文件和Web界面来管理各种设置，方便用户调整模式和参数。

## 配置文件

### 配置文件位置
- 主配置文件：`backend/config.json`
- 配置管理模块：`backend/config_manager.py`
- 配置API接口：`backend/config_api.py`

### 配置文件结构

```json
{
  "api": {
    "url": "https://openrouter.ai/api/v1/chat/completions",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer your-api-key"
    },
    "model_name": "deepseek/deepseek-r1:free",
    "timeout": 60,
    "max_retries": 3,
    "retry_delay": 10
  },
  "analysis": {
    "personality_only": true,
    "available_dimensions": [
      "性格特征", "事业发展", "财运分析", "感情婚姻",
      "健康状况", "人际关系", "学业教育", "家庭关系"
    ],
    "enabled_dimensions": ["性格特征"]
  },
  "paths": {
    "prompts_dir": "prompts",
    "output_dir": "summaries",
    "logs_dir": "llm_logs"
  },
  "server": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_enabled": true,
    "console_enabled": true
  },
  "card_validation": {
    "enabled": true,
    "valid_cards": ["test123", "demo456"]
  },
  "webhook": {
    "secret_key": "a7712e3b-7f96-4b2e-9573-a6f87d9fd848",
    "enabled": true
  }
}
```

## 使用方法

### 1. Web界面管理（推荐）

访问配置管理页面：`http://localhost:5000/config`

功能包括：
- 📊 **分析配置**：切换性格分析模式，选择启用的分析维度
- 🔗 **API配置**：设置API地址、模型、密钥、重试参数
- 🎫 **卡密管理**：添加/删除有效卡密，启用/禁用卡密验证
- 🖥️ **服务器配置**：设置服务器地址、端口、调试模式
- 💾 **配置操作**：保存配置、重新加载配置
- 📋 **当前配置**：查看完整的配置信息

### 2. 直接编辑配置文件

1. 编辑 `backend/config.json` 文件
2. 重启服务器或调用重新加载API

### 3. 程序化调用

```python
from config_manager import get_config

# 获取配置实例
config = get_config()

# 读取配置
api_url = config.get_api_url()
model_name = config.get_model_name()
is_personality_only = config.is_personality_only()

# 修改配置
config.set('api.model_name', 'new-model')
config.toggle_personality_only()
config.add_valid_card('new-card-123')

# 保存配置
config.save()
```

## 配置项说明

### API配置 (`api`)
- `url`: LLM API接口地址
- `headers`: API请求头，包含认证信息
- `model_name`: 使用的模型名称
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数
- `retry_delay`: 重试间隔（秒）

### 分析配置 (`analysis`)
- `personality_only`: 是否只分析性格维度（节省API额度）
- `available_dimensions`: 所有可用的分析维度
- `enabled_dimensions`: 当前启用的分析维度

### 路径配置 (`paths`)
- `prompts_dir`: 提示词文件夹
- `output_dir`: 输出文件夹
- `logs_dir`: 日志文件夹

### 服务器配置 (`server`)
- `host`: 服务器监听地址
- `port`: 服务器端口
- `debug`: 是否启用调试模式

### 日志配置 (`logging`)
- `level`: 日志级别（DEBUG, INFO, WARNING, ERROR）
- `format`: 日志格式
- `file_enabled`: 是否启用文件日志
- `console_enabled`: 是否启用控制台日志

### 卡密验证 (`card_validation`)
- `enabled`: 是否启用卡密验证
- `valid_cards`: 有效卡密列表

### Webhook配置 (`webhook`)
- `secret_key`: Webhook密钥
- `enabled`: 是否启用Webhook

## API接口

### 获取配置
```
GET /api/config/get
```

### 设置配置项
```
POST /api/config/set
Content-Type: application/json

{
  "key": "api.model_name",
  "value": "new-model"
}
```

### 保存配置
```
POST /api/config/save
```

### 重新加载配置
```
POST /api/config/reload
```

### 切换性格分析模式
```
POST /api/config/toggle-personality-only
```

### 获取/设置分析维度
```
GET /api/config/dimensions
POST /api/config/dimensions
Content-Type: application/json

{
  "dimensions": ["性格特征", "事业发展"]
}
```

### 卡密管理
```
GET /api/config/cards
POST /api/config/cards/add
POST /api/config/cards/remove
```

## 常见使用场景

### 1. 节省API额度
- 启用"只分析性格维度"模式
- 或者只选择需要的分析维度

### 2. 更换API服务商
- 修改API地址和密钥
- 调整模型名称
- 根据新服务商调整重试参数

### 3. 调试模式
- 启用调试模式查看详细日志
- 启用文件日志保存调试信息

### 4. 生产环境部署
- 关闭调试模式
- 设置合适的服务器地址和端口
- 配置日志级别为WARNING或ERROR

## 注意事项

1. **配置修改后**：某些配置（如服务器配置）需要重启服务器才能生效
2. **API密钥安全**：请妥善保管API密钥，不要提交到版本控制系统
3. **配置备份**：建议定期备份配置文件
4. **权限控制**：配置管理页面建议在生产环境中添加访问控制

## 故障排除

### 配置文件损坏
- 删除 `config.json` 文件，系统会自动创建默认配置

### API配置错误
- 检查API地址、密钥、模型名称是否正确
- 查看日志文件获取详细错误信息

### 权限问题
- 确保应用有读写配置文件的权限
- 检查日志目录的写入权限

## 更新日志

- **v1.0**: 初始版本，支持基本配置管理
- 添加了Web界面配置管理
- 支持动态配置加载和保存
- 集成到现有的八字分析系统