/**
 * @file 八字分析结果工具函数模块
 * @description 包含通用工具函数和辅助方法
 */

/**
 * 初始化展开/收起功能
 */
function initializeToggleFeatures() {
    // 为所有维度标题添加点击事件
    const headers = document.querySelectorAll('.detailed-dimension-header');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', () => {
            toggleSection(header);
        });
        
        // 设置初始状态
        const content = header.nextElementSibling;
        if (content && content.classList.contains('detailed-dimension-content')) {
            if (header.classList.contains('active')) {
                // 展开状态 - 获取实际内容高度
                content.style.maxHeight = 'none';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
                const actualHeight = content.scrollHeight;
                content.style.maxHeight = actualHeight + 'px';
                
                // 延迟移除固定高度，允许内容自适应
                setTimeout(() => {
                    if (content.classList.contains('active')) {
                        content.style.maxHeight = 'none';
                    }
                }, 50);
            } else {
                // 收起状态
                content.style.maxHeight = '0';
                content.style.opacity = '0';
                content.style.transform = 'translateY(-10px)';
            }
        }
    });
}

/**
 * 切换部分的展开/收起状态
 * @param {HTMLElement} header - 标题元素
 */
function toggleSection(header) {
    const content = header.nextElementSibling;
    const toggleIcon = header.querySelector('.detailed-toggle-icon');
    const isActive = header.classList.contains('active');
    
    if (isActive) {
        // 收起
        header.classList.remove('active');
        content.classList.remove('active');
        content.style.maxHeight = '0';
        content.style.opacity = '0';
        content.style.transform = 'translateY(-10px)';
        if (toggleIcon) {
            toggleIcon.textContent = '▶';
        }
    } else {
        // 展开
        header.classList.add('active');
        content.classList.add('active');
        
        // 临时显示内容以获取真实高度
        const originalMaxHeight = content.style.maxHeight;
        const originalOpacity = content.style.opacity;
        const originalTransform = content.style.transform;
        
        content.style.maxHeight = 'none';
        content.style.opacity = '1';
        content.style.transform = 'translateY(0)';
        
        // 获取实际内容高度
        const actualHeight = content.scrollHeight;
        
        // 重置为收起状态
        content.style.maxHeight = '0';
        content.style.opacity = '0';
        content.style.transform = 'translateY(-10px)';
        
        // 强制重绘
        content.offsetHeight;
        
        // 设置目标高度并触发动画
        requestAnimationFrame(() => {
            content.style.maxHeight = actualHeight + 'px';
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        });
        
        if (toggleIcon) {
            toggleIcon.textContent = '▼';
        }
        
        // 动画完成后移除固定高度，允许内容自适应
        setTimeout(() => {
            if (content.classList.contains('active')) {
                content.style.maxHeight = 'none';
            }
        }, 400); // 与CSS动画时间一致
    }
}

/**
 * 编辑标题
 */
function editTitle() {
    const titleElement = document.getElementById('resultTitle');
    titleElement.contentEditable = true;
    titleElement.focus();
    
    // 选中所有文本
    const range = document.createRange();
    range.selectNodeContents(titleElement);
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
}

/**
 * 下载报告
 */
function downloadReport() {
    console.log('downloadReport函数被调用');
    try {
        // 显示下载提示
        const downloadBtn = document.querySelector('.detailed-download-btn');
        const originalText = downloadBtn.innerHTML;
        downloadBtn.innerHTML = '<span class="download-icon">⏳</span><span class="btn-text">准备中...</span>';
        downloadBtn.disabled = true;
        
        const title = document.getElementById('resultTitle').textContent || '八字分析报告';
        const time = document.getElementById('generateTime').textContent || '未知时间';
        const dateStr = new Date().toISOString().slice(0, 10);
        
        // 确保所有流年部分都被展开
        console.log('准备下载报告：确保流年部分被展开');
        const fortuneYearSections = document.querySelectorAll('.detailed-dimension-section');
        let flowYearFound = false;
        
        fortuneYearSections.forEach(section => {
            const header = section.querySelector('.detailed-dimension-header');
            const content = section.querySelector('.detailed-dimension-content');
            const title = section.querySelector('.detailed-dimension-title');
            
            // 检查是否为流年部分（通过标题文本判断）
            if (title && title.textContent && 
                (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                flowYearFound = true;
                console.log('找到流年部分，强制展开:', title.textContent);
                // 强制添加active类
                if (header && !header.classList.contains('active')) {
                    header.classList.add('active');
                }
                if (content && !content.classList.contains('active')) {
                    content.classList.add('active');
                }
                // 更新展开/收起图标
                const toggleIcon = section.querySelector('.detailed-toggle-icon');
                if (toggleIcon) toggleIcon.textContent = '▼';
            }
        });
        
        if (!flowYearFound) {
            console.log('没有找到流年部分，请检查流年内容是否存在');
        }
        
        // 获取八字信息
        const baziInfoElement = document.querySelector('.bazi-info-section');
        let baziInfoHtml = '';
        if (baziInfoElement) {
            baziInfoHtml = baziInfoElement.outerHTML;
        }
        
        // 获取分析内容
        let analysisHtml = '';
        const sections = document.querySelectorAll('.detailed-dimension-section');
        sections.forEach(section => {
            // 这里复制前确保流年相关部分的内容标记为展开
            const title = section.querySelector('.detailed-dimension-title');
            if (title && title.textContent && 
                (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                // 复制前先确保所有流年内容都是展开状态
                const content = section.querySelector('.detailed-dimension-content');
                if (content) {
                    content.classList.add('active');
                    content.style.display = 'block';
                }
            }
            analysisHtml += section.outerHTML;
        });
        
        // 如果没有内容，显示提示
        if (!baziInfoHtml && !analysisHtml) {
            alert('暂无可下载的分析内容，请确保页面已完全加载。');
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
            return;
        }
        
        // 生成完整的HTML内容（内联CSS和JS）
        const htmlContent = generateCompleteReport(title, time, baziInfoHtml, analysisHtml);
        
        // 创建下载
        const fileName = `八字分析报告_${title.replace(/[^\w\s-]/g, '')}_${dateStr}.html`;
        downloadFile(htmlContent, fileName, 'text/html');
        
        // 恢复按钮状态
        setTimeout(() => {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        }, 1000);
        
    } catch (error) {
        console.error('下载报告失败:', error);
        alert('下载失败，请稍后重试。');
        
        // 恢复按钮状态
        const downloadBtn = document.querySelector('.detailed-download-btn');
        if (downloadBtn) {
            downloadBtn.innerHTML = '<span class="download-icon">📥</span><span class="btn-text">下载报告</span>';
            downloadBtn.disabled = false;
        }
    }
}

/**
 * 生成完整的报告HTML内容
 * @param {string} title - 报告标题
 * @param {string} time - 生成时间
 * @param {string} baziInfoHtml - 八字信息HTML
 * @param {string} analysisHtml - 分析内容HTML
 * @returns {string} 完整的HTML内容
 */
function generateCompleteReport(title, time, baziInfoHtml, analysisHtml) {
    const cssContent = getReportStyles();
    const jsContent = getReportScript();
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
${cssContent}
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <h1>${title}</h1>
            <p class="generate-time">${time}</p>
            <div class="report-actions">
                <button onclick="window.print()" class="print-btn">🖨️ 打印</button>
                <button onclick="toggleAllSections()" class="toggle-btn">📋 展开/收起全部</button>
            </div>
        </header>
        
        <main class="report-content">
            ${baziInfoHtml}
            <div class="analysis-sections">
                ${analysisHtml}
            </div>
        </main>
        
        <footer class="report-footer">
            <p>生成时间：${time}</p>
            <p>八字分析系统 - 专业命理分析报告</p>
        </footer>
    </div>
    <script>
${jsContent}
    </script>
</body>
</html>`;
}

/**
 * 获取报告样式
 * @returns {string} CSS样式内容
 */
function getReportStyles() {
    return `/* 八字分析报告样式 - 完整版本 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #5d59d1;
    --primary-dark: #4844b8;
    --primary-light: #a8b4f5;
    --primary-gradient: linear-gradient(135deg, #5d59d1 0%, #8362e6 100%);
    
    /* 背景色 */
    --background-light: #f8f9fd;
    --bg-card: #ffffff;
    --bg-primary: #f8f9fd;
    --bg-secondary: #f7fafc;
    --bg-overlay: rgba(255, 255, 255, 0.85);
    
    /* 文本颜色 */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #718096;
    --text-muted: #a0aec0;
    
    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-color-light: #edf2f7;
    
    /* 阴影效果 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.07), 0 4px 6px rgba(0, 0, 0, 0.05);
    
    /* 圆角半径 */
    --radius-small: 6px;
    --radius-medium: 8px;
    --radius-large: 12px;
    --border-radius-lg: 12px;
    
    /* 动画过渡 */
    --transition-normal: all 0.3s ease;
    
    /* 字体设置 */
    --font-family-primary: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --line-height-base: 1.6;
    
    /* 五行颜色 */
    --wuxing-jin-color: #d4af37;
    --wuxing-mu-color: #38a169;
    --wuxing-shui-color: #3182ce;
    --wuxing-huo-color: #e53e3e;
    --wuxing-tu-color: #b7791f;
    --wuxing-kong-color: #a0aec0;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-family: var(--font-family-primary);
    font-size: 16px;
    color: var(--text-primary);
    background-color: var(--background-light);
    line-height: var(--line-height-base);
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    line-height: var(--line-height-base);
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    margin: 0;
}

.report-container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.report-header {
    background: var(--primary-gradient);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.report-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
    letter-spacing: -0.03em;
}

.generate-time {
    font-size: 1.1em;
    opacity: 0.9;
    margin-bottom: 15px;
}

.report-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.print-btn, .toggle-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.print-btn:hover, .toggle-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.report-content {
    padding: 30px;
}

/* 八字信息部分样式 */
.bazi-info-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-large);
    margin-bottom: 30px;
    border-left: 4px solid var(--primary-color);
    padding: 24px;
    box-shadow: var(--shadow-sm);
}

.bazi-info-section h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 700;
}

.detailed-bazi-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.detailed-bazi-info-item {
    background: var(--bg-card);
    padding: 16px;
    border-radius: var(--radius-medium);
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.detailed-bazi-info-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.detailed-bazi-info-item strong {
    color: var(--text-primary);
    font-weight: 600;
    margin-right: 8px;
}

/* 八字表格样式 */
.bazi-table-container {
    margin: 16px 0;
    overflow-x: auto;
}

.detailed-bazi-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 8px;
    margin: 0 auto;
}

.detailed-bazi-table th {
    background: var(--primary-gradient);
    color: white;
    padding: 12px;
    border-radius: var(--radius-medium);
    font-size: 16px;
    text-align: center;
    font-weight: 600;
}

.detailed-pillar-cell {
    padding: 14px 8px;
    vertical-align: top;
    border-radius: var(--radius-medium);
    border: 1px solid var(--primary-light);
    background-color: var(--bg-card);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-align: center;
    min-width: 80px;
    box-shadow: var(--shadow-sm);
}

.detailed-pillar-cell:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.detailed-bazi-cell {
    position: relative;
    padding: 10px 6px;
    margin-bottom: 5px;
    background-color: rgba(99, 102, 241, 0.1);
    border-radius: 8px 8px 0 0;
    box-shadow: var(--shadow-sm);
}

.detailed-shishen {
    display: inline-block;
    font-size: 0.8em;
    padding: 2px 6px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    margin-bottom: 4px;
    font-weight: 500;
}

.detailed-tiangan {
    font-size: 1.5em;
    font-weight: 600;
    margin: 6px 0;
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
}

.detailed-dizhi {
    font-size: 1.5em;
    font-weight: 600;
    margin: 12px 0;
    padding: 12px 0;
    border-radius: 6px;
    box-shadow: var(--shadow-sm);
    text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
}

/* 五行颜色应用 */
.wuxing-jin { color: var(--wuxing-jin-color) !important; }
.wuxing-mu { color: var(--wuxing-mu-color) !important; }
.wuxing-shui { color: var(--wuxing-shui-color) !important; }
.wuxing-huo { color: var(--wuxing-huo-color) !important; }
.wuxing-tu { color: var(--wuxing-tu-color) !important; }
.wuxing-kong { color: var(--wuxing-kong-color) !important; }

/* 分析维度部分样式 */
.detailed-dimension-section {
    margin-bottom: 25px;
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color-light);
}

.detailed-dimension-header {
    background: var(--primary-gradient);
    color: white;
    padding: 20px 24px;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detailed-dimension-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.detailed-dimension-title {
    font-size: 1.3em;
    font-weight: 600;
    margin: 0;
}

.detailed-toggle-icon {
    font-size: 1.2em;
    transition: transform 0.3s ease;
}

.detailed-dimension-header.active .detailed-toggle-icon {
    transform: rotate(90deg);
}

.detailed-dimension-content {
    background: var(--bg-card);
    border-top: 1px solid var(--border-color-light);
    display: none;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.detailed-dimension-content.active {
    display: block;
    max-height: none;
    overflow: visible;
}

.detailed-dimension-text {
    padding: 24px;
    line-height: 1.8;
    color: var(--text-secondary);
}

.detailed-dimension-text h1,
.detailed-dimension-text h2,
.detailed-dimension-text h3,
.detailed-dimension-text h4,
.detailed-dimension-text h5,
.detailed-dimension-text h6 {
    color: var(--text-primary);
    margin: 20px 0 12px 0;
    font-weight: 700;
}

.detailed-dimension-text h4 {
    color: var(--primary-color);
    font-size: 1.2em;
}

.detailed-dimension-text ul,
.detailed-dimension-text ol {
    margin: 12px 0;
    padding-left: 24px;
}

.detailed-dimension-text li {
    margin: 8px 0;
    line-height: 1.6;
}

.detailed-dimension-text strong {
    color: var(--text-primary);
    font-weight: 600;
}

.detailed-dimension-text em {
    color: var(--primary-color);
    font-style: normal;
    font-weight: 500;
}

.detailed-dimension-text p {
    margin: 12px 0;
    line-height: 1.8;
}

.detailed-dimension-text blockquote {
    border-left: 4px solid var(--primary-color);
    background-color: var(--bg-secondary);
    padding: 16px 20px;
    margin: 16px 0;
    border-radius: 0 var(--radius-medium) var(--radius-medium) 0;
    color: var(--text-secondary);
}

.detailed-dimension-text code {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.detailed-dimension-text pre {
    background-color: var(--bg-secondary);
    padding: 16px;
    border-radius: var(--radius-medium);
    overflow-x: auto;
    margin: 16px 0;
    border-left: 4px solid var(--primary-color);
    font-family: inherit;
    line-height: 1.6;
}

.detailed-dimension-text pre code {
    background-color: transparent;
    padding: 0;
    color: var(--text-primary);
    font-family: inherit;
    font-size: inherit;
}

/* 处理markdown格式的粗体标记 */
.detailed-dimension-text pre code,
.detailed-dimension-text {
    /* 使用正则表达式替换 **text** 为粗体 */
}

/* 通过JavaScript处理的粗体样式 */
.detailed-dimension-text .markdown-bold {
    font-weight: bold;
    color: var(--primary-color);
}

/* 隐藏markdown标记符号 */
.detailed-dimension-text .markdown-marker {
    display: none;
}

/* 处理转换后的strong标签样式 */
.detailed-dimension-text strong {
    font-weight: bold;
    color: var(--primary-color);
    background-color: rgba(93, 89, 209, 0.1);
    padding: 1px 3px;
    border-radius: 3px;
}

.detailed-dimension-text pre code strong {
    background-color: rgba(93, 89, 209, 0.15);
    color: var(--primary-dark);
}

/* 缩进列表项样式 */
.detailed-dimension-text .indent-list-item {
    line-height: 1.6;
    color: inherit;
    padding: 2px 0;
    display: block;
}

.detailed-dimension-text .indent-list-item strong {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(93, 89, 209, 0.1);
    padding: 1px 3px;
    border-radius: 3px;
}

.detailed-dimension-text table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.detailed-dimension-text th,
.detailed-dimension-text td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color-light);
}

.detailed-dimension-text th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
}

.detailed-dimension-text tr:nth-child(even) {
    background-color: var(--bg-secondary);
}

.detailed-dimension-text hr {
    border: none;
    border-top: 2px solid var(--border-color);
    margin: 24px 0;
}

/* 兼容旧版样式 */
.dimension-section {
    margin-bottom: 25px;
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.dimension-header {
    background: var(--primary-gradient);
    color: white;
    padding: 20px 24px;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dimension-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.dimension-header.active::after {
    content: '▼';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.dimension-header::after {
    content: '▶';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.dimension-title {
    font-size: 1.3em;
    font-weight: 600;
    padding-right: 30px;
    margin: 0;
}

.dimension-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color-light);
    border-top: none;
    display: none;
}

.dimension-content.active {
    display: block;
}

.dimension-text {
    padding: 24px;
    line-height: 1.8;
    color: var(--text-secondary);
}

.dimension-text h4 {
    color: var(--primary-color);
    margin: 15px 0 10px 0;
    font-size: 1.2em;
    font-weight: 600;
}

.dimension-text ul, .dimension-text ol {
    margin: 12px 0;
    padding-left: 24px;
}

.dimension-text li {
    margin: 8px 0;
    line-height: 1.6;
}

.dimension-text strong {
    color: var(--text-primary);
    font-weight: 600;
}

.dimension-text p {
    margin: 12px 0;
    line-height: 1.8;
}

.report-footer {
    background: var(--bg-secondary);
    padding: 24px;
    text-align: center;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color-light);
}

.report-footer p {
    margin: 5px 0;
    font-size: 0.9em;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #f1f5f9;
        --text-light: #e2e8f0;
        --text-muted: #94a3b8;
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-card: #1e293b;
        --bg-overlay: rgba(15, 23, 42, 0.85);
        --border-color: #334155;
        --border-color-light: #1e293b;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 8px -1px rgba(0, 0, 0, 0.5), 0 2px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 12px 20px -3px rgba(0, 0, 0, 0.5), 0 6px 10px -2px rgba(0, 0, 0, 0.4);
        --background-light: #0f172a;
    }
    
    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: var(--text-primary);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .report-container {
        border-radius: var(--radius-medium);
    }
    
    .report-header {
        padding: 20px;
    }
    
    .report-header h1 {
        font-size: 2em;
    }
    
    .report-content {
        padding: 20px;
    }
    
    .detailed-bazi-basic-info {
        grid-template-columns: 1fr;
    }
    
    .report-actions {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }
    
    .print-btn, .toggle-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .detailed-dimension-header {
        padding: 16px 20px;
    }
    
    .detailed-dimension-title {
        font-size: 1.1em;
    }
    
    .detailed-dimension-text {
        padding: 20px;
    }
    
    .bazi-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .detailed-bazi-table {
        min-width: 400px;
    }
}

@media (max-width: 480px) {
    .report-header h1 {
        font-size: 1.8em;
    }
    
    .detailed-dimension-header {
        padding: 12px 16px;
    }
    
    .detailed-dimension-text {
        padding: 16px;
    }
    
    .detailed-bazi-info-item {
        padding: 12px;
    }
}

/* 打印样式优化 */
@media print {
    body {
        background: white !important;
        padding: 0;
        color: black !important;
    }
    
    .report-container {
        box-shadow: none;
        border-radius: 0;
        max-width: none;
    }
    
    .report-actions {
        display: none !important;
    }
    
    .print-btn, .toggle-btn {
        display: none !important;
    }
    
    /* 确保所有内容都显示，不管是否折叠 */
    .dimension-content,
    .detailed-dimension-content {
        display: block !important;
        height: auto !important;
        opacity: 1 !important;
        overflow: visible !important;
        visibility: visible !important;
        max-height: none !important;
    }
    
    /* 隐藏展开/收起指示器 */
    .dimension-header::after,
    .detailed-dimension-header::after,
    .detailed-toggle-icon {
        display: none !important;
    }
    
    /* 确保流年部分在打印时显示 */
    [class*="运势"],
    [class*="流年"],
    .fortune-year-section,
    .detailed-dimension-section:has(.detailed-dimension-title:contains("运势")),
    .detailed-dimension-section:has(.detailed-dimension-title:contains("流年")) {
        display: block !important;
        visibility: visible !important;
    }
    
    .dimension-header,
    .detailed-dimension-header {
        background: var(--primary-gradient) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        color: white !important;
    }
    
    .report-header {
        background: var(--primary-gradient) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        color: white !important;
    }
    
    /* 分页控制 */
    .detailed-dimension-section {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    .bazi-info-section {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    /* 表格打印优化 */
    .detailed-bazi-table {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    .detailed-bazi-table th {
        background: var(--primary-gradient) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        color: white !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --primary-dark: #000060;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #000000;
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .detailed-toggle-icon {
        transition: none;
    }
    
    .detailed-dimension-content {
        transition: none;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* 选择文本样式 */
::selection {
    background: var(--primary-light);
    color: white;
}

::-moz-selection {
    background: var(--primary-light);
    color: white;
}
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.dimension-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.dimension-header.active::after {
    content: '▼';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.dimension-header::after {
    content: '▶';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
}

.dimension-title {
    font-size: 1.2em;
    font-weight: 500;
    padding-right: 30px;
}

.dimension-content {
    background: white;
    border: 1px solid #e9ecef;
    border-top: none;
    display: none;
}

.dimension-content.active {
    display: block;
}

.dimension-text {
    padding: 20px;
    line-height: 1.8;
    color: #495057;
}

.dimension-text h4 {
    color: #667eea;
    margin: 15px 0 10px 0;
    font-size: 1.1em;
}

.dimension-text ul, .dimension-text ol {
    margin: 10px 0;
    padding-left: 20px;
}

.dimension-text li {
    margin: 5px 0;
}

.report-footer {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    color: #6c757d;
    border-top: 1px solid #e9ecef;
}

.report-footer p {
    margin: 5px 0;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .report-container {
        border-radius: 10px;
    }
    
    .report-header {
        padding: 20px;
    }
    
    .report-header h1 {
        font-size: 2em;
    }
    
    .report-content {
        padding: 20px;
    }
    
    .bazi-info-grid {
        grid-template-columns: 1fr;
    }
    
    .report-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media print {
    body {
        background: white;
        padding: 0;
    }
    
    .report-container {
        box-shadow: none;
        border-radius: 0;
    }
    
    .report-actions {
        display: none;
    }
    
    /* 关键修改：确保所有内容都显示，不管是否折叠 */
    .dimension-content,
    .detailed-dimension-content {
        display: block !important;
        height: auto !important;
        opacity: 1 !important;
        overflow: visible !important;
        visibility: visible !important;
        max-height: none !important;
    }
    
    /* 隐藏展开/收起指示器 */
    .dimension-header::after,
    .detailed-dimension-header::after,
    .detailed-toggle-icon {
        display: none !important;
    }
    
    /* 确保流年部分在打印时显示 */
    [class*="运势"],
    [class*="流年"],
    .fortune-year-section,
    .detailed-dimension-section:has(.detailed-dimension-title:contains("运势")),
    .detailed-dimension-section:has(.detailed-dimension-title:contains("流年")) {
        display: block !important;
        visibility: visible !important;
    }
    
    .dimension-header,
    .detailed-dimension-header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
`;
}

/**
 * 获取报告脚本
 * @returns {string} JavaScript脚本内容
 */
function getReportScript() {
    return `// 八字分析报告交互脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加展开/收起功能
    const headers = document.querySelectorAll('.dimension-header');
    
    headers.forEach(header => {
        header.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const isActive = this.classList.contains('active');
            
            if (isActive) {
                this.classList.remove('active');
                content.classList.remove('active');
            } else {
                this.classList.add('active');
                content.classList.add('active');
            }
        });
    });
    
    // 默认展开所有内容
    const contents = document.querySelectorAll('.dimension-content');
    const headerElements = document.querySelectorAll('.dimension-header');
    contents.forEach((content, index) => {
        content.classList.add('active');
        if (headerElements[index]) {
            headerElements[index].classList.add('active');
        }
    });
    
    // 特别确保流年部分也被展开（查找所有包含"运势"或"流年"的标题部分）
    const allSections = document.querySelectorAll('.dimension-section, .detailed-dimension-section');
    allSections.forEach(section => {
        const title = section.querySelector('.dimension-title, .detailed-dimension-title');
        if (title && title.textContent && 
            (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
            console.log('报告中找到流年部分，确保展开:', title.textContent);
            // 展开相关内容
            const header = section.querySelector('.dimension-header, .detailed-dimension-header');
            const content = section.querySelector('.dimension-content, .detailed-dimension-content');
            if (header) header.classList.add('active');
            if (content) {
                content.classList.add('active');
                content.style.display = 'block';
                content.style.maxHeight = 'none';
                content.style.opacity = '1';
                content.style.visibility = 'visible';
            }
        }
    });
    
    // 处理markdown格式的粗体标记和列表
    function processMarkdownBold() {
        const textElements = document.querySelectorAll('.detailed-dimension-text pre code, .detailed-dimension-text');
        textElements.forEach(element => {
            if (element.innerHTML && element.innerHTML.includes('**')) {
                // 替换 **text** 为 <strong>text</strong>
                element.innerHTML = element.innerHTML.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
            }
        });
    }
    
    // 处理缩进列表格式
    function processIndentedLists() {
        const textElements = document.querySelectorAll('.detailed-dimension-text pre code, .detailed-dimension-text pre');
        textElements.forEach(element => {
            if (element.innerHTML) {
                let content = element.innerHTML;
                
                // 处理多级缩进列表：匹配带粗体标题的列表项
                content = content.replace(/(\s{2,})- \*\*([^*]+)\*\*：([^\n]+)/g, 
                    function(match, spaces, title, text) {
                        const indentLevel = Math.floor(spaces.length / 2);
                        const marginLeft = indentLevel * 16;
                        return '<div class="indent-list-item" style="margin-left: ' + marginLeft + 'px; margin-bottom: 8px;"><strong>' + title + '</strong>：' + text + '</div>';
                    });
                
                // 处理简单缩进列表项
                content = content.replace(/(\s{2,})- ([^\n]+)/g, 
                    function(match, spaces, text) {
                        const indentLevel = Math.floor(spaces.length / 2);
                        const marginLeft = indentLevel * 16;
                        return '<div class="indent-list-item" style="margin-left: ' + marginLeft + 'px; margin-bottom: 6px;">• ' + text + '</div>';
                    });
                
                element.innerHTML = content;
            }
        });
    }
    
    // 执行markdown处理
    processMarkdownBold();
    processIndentedLists();
    
    // 添加打印功能
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            window.print();
        }
    });
    
    // 添加平滑滚动
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// 全局函数：切换所有章节
function toggleAllSections() {
    const headers = document.querySelectorAll('.dimension-header');
    const contents = document.querySelectorAll('.dimension-content');
    
    // 检查是否所有章节都已展开
    const allExpanded = Array.from(contents).every(content => content.classList.contains('active'));
    
    if (allExpanded) {
        // 收起所有章节
        headers.forEach(header => header.classList.remove('active'));
        contents.forEach(content => content.classList.remove('active'));
    } else {
        // 展开所有章节
        headers.forEach(header => header.classList.add('active'));
        contents.forEach(content => content.classList.add('active'));
    }
}`;
}

/**
 * 下载文件
 * @param {string} content - 文件内容
 * @param {string} fileName - 文件名
 * @param {string} mimeType - MIME类型
 */
function downloadFile(content, fileName, mimeType) {
    try {
        const blob = new Blob([content], { type: `${mimeType};charset=utf-8` });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

/**
 * 保存自定义标题
 * @param {string} title - 标题文本
 */
function saveTitle(title) {
    try {
        // 获取当前报告的唯一标识
        const reportKey = this.getCurrentReportKey();
        if (reportKey) {
            // 为每个报告保存独立的标题
            localStorage.setItem(`customReportTitle_${reportKey}`, title);
            console.log('标题已保存:', title, '报告标识:', reportKey);
        } else {
            // 如果无法获取报告标识，使用通用键（向后兼容）
            localStorage.setItem('customReportTitle', title);
            console.log('标题已保存（通用）:', title);
        }
    } catch (error) {
        console.error('保存标题失败:', error);
    }
}

/**
 * 加载自定义标题
 */
function loadCustomTitle() {
    // 获取当前报告的唯一标识
    const reportKey = this.getCurrentReportKey();
    let customTitle = null;
    
    if (reportKey) {
        // 尝试加载该报告的专属标题
        customTitle = localStorage.getItem(`customReportTitle_${reportKey}`);
    }
    
    // 如果没有专属标题，尝试加载通用标题（向后兼容）
    if (!customTitle) {
        customTitle = localStorage.getItem('customReportTitle');
    }
    
    // 如果有自定义标题，设置到页面上
    if (customTitle && customTitle.trim() !== '') {
        const titleElement = document.getElementById('resultTitle');
        if (titleElement) {
            titleElement.textContent = customTitle.trim();
            console.log('已加载自定义标题:', customTitle.trim(), '报告标识:', reportKey);
        }
    }
}

/**
 * 获取当前报告的唯一标识
 * @returns {string|null} 报告标识
 */
function getCurrentReportKey() {
    // 获取当前报告的唯一标识，优先级：cardKey > reportId > URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const cardKey = urlParams.get('cardKey');
    const reportId = urlParams.get('id');
    const filePath = urlParams.get('file');
    
    if (cardKey) {
        return cardKey;
    } else if (reportId) {
        return reportId;
    } else if (filePath) {
        return filePath.replace(/[^a-zA-Z0-9]/g, '_'); // 将文件路径转换为安全的键名
    }
    
    return null;
}

/**
 * 导出为分离的HTML、CSS和JS文件
 */
function exportSeparateFiles() {
    console.log('exportSeparateFiles函数被调用');
    try {
        // 显示下载提示
        const exportBtn = document.querySelector('.detailed-export-btn');
        if (exportBtn) {
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<span class="export-icon">⏳</span><span class="btn-text">准备中...</span>';
            exportBtn.disabled = true;
        }
        
        const title = document.getElementById('resultTitle').textContent || '八字分析报告';
        const time = document.getElementById('generateTime').textContent || '未知时间';
        const dateStr = new Date().toISOString().slice(0, 10);
        
        // 确保所有流年部分都被展开（复用downloadReport中的代码）
        console.log('准备导出文件：确保流年部分被展开');
        const fortuneYearSections = document.querySelectorAll('.detailed-dimension-section');
        fortuneYearSections.forEach(section => {
            const header = section.querySelector('.detailed-dimension-header');
            const content = section.querySelector('.detailed-dimension-content');
            const title = section.querySelector('.detailed-dimension-title');
            
            if (title && title.textContent && 
                (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                console.log('找到流年部分，强制展开:', title.textContent);
                if (header && !header.classList.contains('active')) {
                    header.classList.add('active');
                }
                if (content && !content.classList.contains('active')) {
                    content.classList.add('active');
                }
                
                const toggleIcon = section.querySelector('.detailed-toggle-icon');
                if (toggleIcon) toggleIcon.textContent = '▼';
            }
        });
        
        // 获取八字信息
        const baziInfoElement = document.querySelector('.bazi-info-section');
        let baziInfoHtml = '';
        if (baziInfoElement) {
            baziInfoHtml = baziInfoElement.outerHTML;
        }
        
        // 获取分析内容
        let analysisHtml = '';
        const sections = document.querySelectorAll('.detailed-dimension-section');
        sections.forEach(section => {
            const title = section.querySelector('.detailed-dimension-title');
            if (title && title.textContent && 
                (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                const content = section.querySelector('.detailed-dimension-content');
                if (content) {
                    content.classList.add('active');
                    content.style.display = 'block';
                }
            }
            analysisHtml += section.outerHTML;
        });
        
        // 如果没有内容，显示提示
        if (!baziInfoHtml && !analysisHtml) {
            alert('暂无可导出的分析内容，请确保页面已完全加载。');
            if (exportBtn) {
                exportBtn.innerHTML = '<span class="export-icon">📤</span><span class="btn-text">导出源文件</span>';
                exportBtn.disabled = false;
            }
            return;
        }
        
        // 获取CSS和JS内容
        const cssContent = getReportStyles();
        const jsContent = getReportScript();
        
        // 创建HTML内容（引用外部CSS和JS）
        const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <h1>${title}</h1>
            <p class="generate-time">${time}</p>
            <div class="report-actions">
                <button onclick="window.print()" class="print-btn">🖨️ 打印</button>
                <button onclick="toggleAllSections()" class="toggle-btn">📋 展开/收起全部</button>
            </div>
        </header>
        
        <main class="report-content">
            ${baziInfoHtml}
            <div class="analysis-sections">
                ${analysisHtml}
            </div>
        </main>
        
        <footer class="report-footer">
            <p>生成时间：${time}</p>
            <p>八字分析系统 - 专业命理分析报告</p>
        </footer>
    </div>
    <script src="script.js"></script>
</body>
</html>`;
        
        // 使用JSZip创建zip文件（如果浏览器支持）
        if (typeof JSZip === 'undefined') {
            // 如果没有加载JSZip库，则单独下载文件
            console.log('JSZip库未加载，使用单独下载方式');
            
            const baseFileName = `八字分析报告_${title.replace(/[^\w\s-]/g, '')}_${dateStr}`;
            
            // 下载HTML文件
            downloadFile(htmlContent, `${baseFileName}.html`, 'text/html');
            
            // 下载CSS文件
            setTimeout(() => {
                downloadFile(cssContent, 'styles.css', 'text/css');
            }, 500);
            
            // 下载JS文件
            setTimeout(() => {
                downloadFile(jsContent, 'script.js', 'text/javascript');
            }, 1000);
            
            // 显示成功提示
            setTimeout(() => {
                alert('已下载HTML、CSS和JS文件，请将它们保存在同一目录下使用。');
            }, 1500);
        } else {
            // 使用JSZip创建zip文件
            const zip = new JSZip();
            
            // 添加文件到zip
            zip.file(`${title}.html`, htmlContent);
            zip.file('styles.css', cssContent);
            zip.file('script.js', jsContent);
            
            // 生成zip文件并下载
            zip.generateAsync({type: 'blob'})
                .then(function(content) {
                    const url = URL.createObjectURL(content);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `八字分析报告_${title.replace(/[^\w\s-]/g, '')}_${dateStr}.zip`;
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    console.log('ZIP文件生成完成');
                });
        }
        
        // 恢复按钮状态
        if (exportBtn) {
            setTimeout(() => {
                exportBtn.innerHTML = '<span class="export-icon">📤</span><span class="btn-text">导出源文件</span>';
                exportBtn.disabled = false;
            }, 1000);
        }
        
    } catch (error) {
        console.error('导出文件失败:', error);
        alert('导出失败，请稍后重试。');
        
        // 恢复按钮状态
        const exportBtn = document.querySelector('.detailed-export-btn');
        if (exportBtn) {
            exportBtn.innerHTML = '<span class="export-icon">📤</span><span class="btn-text">导出源文件</span>';
            exportBtn.disabled = false;
        }
    }
}