/* 深色模式样式 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: #f8fafc;
    }
    
    /* 深色模式下的卡片 */
    .report-card {
        background: var(--bg-card);
        border-color: var(--border-color);
        color: var(--text-primary);
        box-shadow: var(--shadow-md);
    }
    
    .report-card:hover {
        background: var(--bg-tertiary);
        border-color: var(--border-color-dark);
        box-shadow: var(--card-hover-shadow);
    }
    
    .report-header {
        background: linear-gradient(to right, rgba(99, 102, 241, 0.15), rgba(79, 70, 229, 0.15));
        border-bottom-color: var(--border-color);
    }
    
    .report-title {
        color: var(--text-primary) !important;
        font-weight: 600;
    }
    
    .report-source {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }
    
    .report-meta {
        color: var(--text-secondary);
    }
    
    .report-date {
        color: var(--text-light);
    }
    
    .report-summary {
        color: var(--text-secondary);
    }
    
    .report-actions .btn {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }
    
    .report-actions .view-btn:hover {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border-color: #6366f1;
    }
    
    .report-actions .delete-btn:hover {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border-color: #ef4444;
    }
    
    /* 操作按钮深色模式 */
    .action-buttons {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .action-buttons::before {
        background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1));
    }
    
    .action-btn {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border-color: var(--border-color);
    }
    
    .action-btn:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
        border-color: var(--border-color-dark);
    }
    
    /* 主操作按钮特殊样式 */
    .action-btn.primary {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border-color: #6366f1;
    }
    
    .action-btn.primary:hover {
        background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(99, 102, 241, 0.2);
    }
    
    /* 次要操作按钮样式 - 修复白底白字问题 */
    .action-btn.secondary {
        background: white !important;
        color: var(--primary-color) !important;
        border: 1px solid rgba(102, 126, 234, 0.3) !important;
        /* 防止其他CSS覆盖 */
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }

    .action-btn.secondary:hover {
        background: rgba(102, 126, 234, 0.05) !important;
        color: var(--primary-color) !important;
        border-color: #6366f1 !important;
        /* 防止悬停时文字变白 */
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }
    
    /* 卡密模态框深色模式 */
    .card-key-modal-content {
        background: var(--bg-card);
        border-color: var(--border-color);
    }
    
    .card-key-modal-title {
        color: var(--text-primary);
    }
    
    .card-key-modal-subtitle {
        color: var(--text-secondary);
    }
    
    .card-key-input {
        background: var(--bg-input);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .card-key-input:focus {
        background: var(--bg-tertiary);
        border-color: #6366f1;
    }
    
    .card-key-input::placeholder {
        color: var(--text-muted);
    }
    
    .card-key-modal-btn.secondary {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }
    
    .card-key-modal-btn.secondary:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
    }
    
    /* 导航链接优化 */
    .nav-link {
        color: #a5b4fc;
    }
    
    .nav-link:hover {
        color: #c7d2fe;
        text-decoration: underline;
    }
    
    /* 筛选框样式 */
    .card-filter-input {
        background: #0f172a;
        border-color: #334155;
        color: #f8fafc;
    }
    
    .card-filter-input:focus {
        border-color: #6366f1;
        box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    }
    
    .card-filter-input::placeholder {
        color: #94a3b8;
    }
}

/* 手机端深色模式特殊优化 */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    .report-card {
        background: var(--bg-card);
        border: 2px solid var(--border-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }
    
    .report-card:hover,
    .report-card:active {
        background: var(--bg-tertiary);
        border-color: var(--border-color-dark);
        transform: none; /* 移动端不使用transform效果 */
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
    }
    
    .report-title {
        color: var(--text-primary) !important;
        font-weight: 700;
    }
    
    .report-source {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-weight: 600;
    }
    
    .report-date {
        color: var(--text-secondary);
        font-weight: 600;
    }
    
    .report-actions .btn {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 2px solid var(--border-color);
        font-weight: 600;
    }
    
    .report-actions .view-btn {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border-color: #6366f1;
    }
    
    .report-actions .delete-btn {
        background: var(--bg-tertiary);
        color: #ff6b6b;
        border-color: #ff6b6b;
    }
    
    .report-actions .delete-btn:hover,
    .report-actions .delete-btn:active {
        background: #ef4444;
        color: white;
    }
} 