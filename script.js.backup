// 全局变量存储轮询状态
let pollingInterval = null;
let currentCardKey = null;

// 获取API基础URL的函数
function getApiBaseUrl() {
    const hostname = window.location.hostname;
    const port = window.location.port;
    
    // 开发环境使用完整URL（包含端口）
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return `http://${hostname}:${port || 5000}`;
    }
    
    // 生产环境仅使用相对路径
    return '';
}

// 保存分析结果到本地存储
function saveAnalysisResult(cardKey, resultData, statusData) {
    try {
        // 获取现有的报告列表
        const existingReports = JSON.parse(localStorage.getItem('baziReports') || '[]');
        
        // 创建新的报告对象
        const newReport = {
            id: `${cardKey}_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`,
            cardKey: cardKey,
            name: getFormValue('nameInput') || '未知',
            gender: getFormValue('genderInput') === '1' ? '男' : '女',
            birthDate: `${getFormValue('yearInput')}年${getFormValue('monthInput')}月${getFormValue('dayInput')}日`,
            birthTime: getFormValue('hourInput'),
            location: '未知',
            completedTime: new Date().toLocaleString(),
            timestamp: Date.now(),
            resultData: resultData,
            statusData: statusData
        };
        
        // 添加到报告列表开头（最新的在前面）
        existingReports.unshift(newReport);
        
        // 保存到localStorage
        localStorage.setItem('baziReports', JSON.stringify(existingReports));
        
        // 同时保存为最新结果（用于向后兼容）
        localStorage.setItem('latestBaziResult', JSON.stringify(newReport));
        
        console.log('分析结果已保存到localStorage:', newReport.id);
        
    } catch (error) {
        console.error('保存分析结果失败:', error);
    }
}

// 获取表单值的辅助函数
function getFormValue(elementId) {
    const element = document.getElementById(elementId);
    return element ? element.value : '';
}

// 表单提交处理
class BaziAnalyzer {
    constructor() {
        this.form = document.getElementById('textForm');
        this.submitBtn = document.querySelector('.submit-btn');
        this.resultContainer = document.getElementById('result');
        this.progressSection = document.querySelector('.progress-section');
        this.resultContent = document.querySelector('.result-content');
        this.pollingInterval = null;
        this.isSubmitting = false;
        
        this.init();
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    init() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element : document.getElementById(elementId);
        return element ? element.value : '';
    }); // 修复了缺少分号和括号闭合的问题
        // 添加输入验证
        this.addInputValidation();
        
        // 默认值填充功能已移除
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    

    

    
    addInputValidation() {
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        
        // 添加卡密实时验证
        const cardKeyInput = document.getElementById('cardKeyInput');
        if (cardKeyInput) {
            cardKeyInput.addEventListener('blur', () => this.validateCardKey());
            cardKeyInput.addEventListener('input', () => this.clearCardKeyStatus());
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';
        
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (field.type === 'number') {
            const num = parseInt(value);
            const min = parseInt(field.min);
            const max = parseInt(field.max);
            
            if (value && (isNaN(num) || num < min || num > max)) {
                isValid = false;
                errorMessage = `请输入${min}-${max}之间的数字`;
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        this.setFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    setFieldValidation(field, isValid, message) {
        const inputGroup = field.closest('.input-group');
        const existingError = inputGroup.querySelector('.field-error');
        
        if (existingError) {
            existingError.remove();
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        if (!isValid && message) {
            field.style.borderColor = 'var(--error-color)';
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.style.cssText = 'color: var(--error-color); font-size: 0.85rem; margin-top: 4px;';
            errorDiv.textContent = message;
            inputGroup.appendChild(errorDiv);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
            field.style.borderColor = '';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    clearFieldError(field) {
        const inputGroup = field.closest('.input-group');
        const existingError = inputGroup.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
            field.style.borderColor = '';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    async validateCardKey() {
        const cardKeyInput = document.getElementById('cardKeyInput');
        const cardKey = cardKeyInput.value.trim();
        
        if (!cardKey) {
            this.clearCardKeyStatus();
            return;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 显示验证中状态
        this.showCardKeyStatus('validating', '🔄 验证中...');
        
        try {
            // 根据当前环境确定API基础URL
            const apiBaseUrl = getApiBaseUrl();
                
            console.log('使用API基础URL验证卡密:', apiBaseUrl);
            
            const response = await fetch(`${apiBaseUrl}/api/validate-card`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    },
                body: JSON.stringify({ cardKey: cardKey })
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                const remainingUsage = result.data.remaining_usage;
                const expireTime = new Date(result.data.expire_time).toLocaleDateString('zh-CN');
                this.showCardKeyStatus('valid', `✅ 卡密有效 (剩余${remainingUsage}次，有效期至${expireTime})`);
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                this.showCardKeyStatus('invalid', `❌ ${result.error || '卡密无效'}`);
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } catch (error) {
            console.error('卡密验证失败:', error);
            this.showCardKeyStatus('error', '❌ 验证失败，请检查网络连接');
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    clearCardKeyStatus() {
        const cardKeyInput = document.getElementById('cardKeyInput');
        const inputGroup = cardKeyInput.closest('.input-group');
        const existingStatus = inputGroup.querySelector('.card-status');
        
        if (existingStatus) {
            existingStatus.remove();
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        cardKeyInput.style.borderColor = '';
        cardKeyInput.style.backgroundColor = '';
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    showCardKeyStatus(type, message) {
        const cardKeyInput = document.getElementById('cardKeyInput');
        const inputGroup = cardKeyInput.closest('.input-group');
        
        // 清除现有状态
        this.clearCardKeyStatus();
        
        // 创建状态显示元素
        const statusDiv = document.createElement('div');
        statusDiv.className = 'card-status';
        statusDiv.style.cssText = `
            font-size: 0.85rem;
            margin-top: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
        `;
        
        // 根据状态类型设置样式
        switch (type) {
            case 'validating':
                statusDiv.style.color = '#666';
                statusDiv.style.backgroundColor = '#f0f0f0';
                cardKeyInput.style.borderColor = '#ccc';
                break;
            case 'valid':
                statusDiv.style.color = '#4CAF50';
                statusDiv.style.backgroundColor = '#e8f5e8';
                cardKeyInput.style.borderColor = '#4CAF50';
                cardKeyInput.style.backgroundColor = '#f8fff8';
                break;
            case 'invalid':
            case 'error':
                statusDiv.style.color = '#f44336';
                statusDiv.style.backgroundColor = '#ffebee';
                cardKeyInput.style.borderColor = '#f44336';
                cardKeyInput.style.backgroundColor = '#fff8f8';
                break;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        statusDiv.textContent = message;
        inputGroup.appendChild(statusDiv);
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    async handleSubmit() {
        if (this.isSubmitting) return;
        
        // 验证所有字段
        const inputs = this.form.querySelectorAll('input[required], select[required]');
        let isFormValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        
        if (!isFormValid) {
            this.showError('请检查并修正表单中的错误');
            return;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 获取表单数据
        const formData = this.getFormData();
        console.log('提交数据:', formData);
        
        // 开始提交
        this.setSubmitting(true);
        
        try {
            await this.sendBaziRequest(formData);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } catch (error) {
            console.error('提交失败:', error);
            this.showError('提交失败: ' + error.message);
            this.setSubmitting(false);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    getFormData() {
        return {
            year: document.getElementById('yearInput').value,
            month: document.getElementById('monthInput').value,
            day: document.getElementById('dayInput').value,
            time: document.getElementById('hourInput').value,
            gender: document.getElementById('genderInput').value,
            cardKey: document.getElementById('cardKeyInput').value
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    setSubmitting(isSubmitting) {
        this.isSubmitting = isSubmitting;
        const btnIcon = this.submitBtn.querySelector('.btn-icon');
        const btnText = this.submitBtn.querySelector('.btn-text');
        const btnLoading = this.submitBtn.querySelector('.btn-loading');
        
        if (isSubmitting) {
            this.submitBtn.disabled = true;
            btnIcon.style.display = 'none';
            btnText.style.display = 'none';
            btnLoading.style.display = 'block';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
            this.submitBtn.disabled = false;
            btnIcon.style.display = 'inline';
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }

    async sendBaziRequest(formData) {
        const { year, month, day, time, gender, cardKey } = formData;
        
        // 存储当前cardKey
        currentCardKey = cardKey;
        
        // 根据当前环境确定API基础URL
        const apiBaseUrl = getApiBaseUrl();
            
        console.log('使用API基础URL发送请求:', apiBaseUrl);
        
        // 构建请求URL
        const webhookPath = '/webhook/a7712e3b-7f96-4b2e-9573-a6f87d9fd848';
        const params = new URLSearchParams({
            year,
            month,
            day,
            time,
            gender,
            cardKey
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        
        const requestUrl = `${apiBaseUrl}${webhookPath}?${params.toString()}`;
        console.log('发送请求到:', requestUrl);
        
        // 显示初始状态
        this.showProgress('正在提交请求...', 0);
        
        try {
            const response = await fetch(requestUrl);
            
            if (!response.ok) {
                // 针对不同状态码显示不同错误信息
                if (response.status === 401) {
                    throw new Error('🔑 卡密验证失败，请检查您的卡密是否正确');
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (response.status === 400) {
                    throw new Error('📝 请求参数有误，请检查输入信息');
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (response.status === 500) {
                    throw new Error('🔧 服务器内部错误，请稍后重试');
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                    throw new Error(`❌ 请求失败 (状态码: ${response.status})`);
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            
            const data = await response.json();
            console.log('请求提交成功:', data);
            
            // 获取请求ID，优先使用requestId，向后兼容cardKey
            const requestId = data.requestId || data.request_id;
            
            // 在localStorage中保存当前进行中的分析信息
            const ongoingAnalysis = {
                id: requestId || cardKey,
                type: requestId ? 'request' : 'card',
                startTime: Date.now(),
                cardKey: cardKey,
                requestId: requestId
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
            
            localStorage.setItem('ongoingAnalysis', JSON.stringify(ongoingAnalysis));
            console.log('已保存进行中的分析信息:', ongoingAnalysis);
            
            if (requestId) {
                console.log('获取到请求ID:', requestId);
                // 跳转到结果页面，同时传递requestId和cardKey
                window.location.href = `result.html?requestId=${encodeURIComponent(requestId)}&cardKey=${encodeURIComponent(cardKey)}`;
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                // 向后兼容，仅使用cardKey
                window.location.href = `result.html?cardKey=${encodeURIComponent(cardKey)}`;
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } catch (error) {
            console.error('请求提交失败:', error);
            throw error;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    showProgress(message, progress = 0, elapsedTime = null) {
        if (!this.progressSection) {
            return;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        this.progressSection.style.display = 'block';
        this.resultContent.innerHTML = '';
        
        const progressStatus = this.progressSection.querySelector('.progress-status');
        const progressFill = this.progressSection.querySelector('.progress-fill');
        const elapsedTimeElement = this.progressSection.querySelector('.elapsed-time');
        
        if (!progressStatus || !progressFill) {
            return;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 添加动画效果
        progressStatus.style.opacity = '0';
        setTimeout(() => {
            progressStatus.textContent = message;
            progressStatus.style.opacity = '1';
            progressStatus.style.transition = 'opacity 0.3s ease';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 100);
        
        // 平滑的进度条动画
        progressFill.style.transition = 'width 0.5s ease-out';
        progressFill.style.width = `${progress}%`;
        
        // 根据进度改变进度条颜色
        if (progress < 30) {
            progressFill.style.background = 'linear-gradient(90deg, #ff7675, #fd79a8)';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (progress < 70) {
            progressFill.style.background = 'linear-gradient(90deg, #fdcb6e, #e17055)';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
            progressFill.style.background = 'linear-gradient(90deg, #00b894, #00cec9)';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        if (elapsedTime && elapsedTimeElement) {
            elapsedTimeElement.textContent = elapsedTime;
            elapsedTimeElement.style.color = '#6c5ce7';
            elapsedTimeElement.style.fontWeight = '600';
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 添加脉冲动画效果
        if (progress < 100) {
            this.addPulseAnimation();
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 强制重绘
        this.progressSection.offsetHeight;
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    addPulseAnimation() {
        if (!document.getElementById('pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'pulse-animation';
            style.textContent = `
                @keyframes pulse {
                    0% { box-shadow: 0 0 0 0 rgba(108, 92, 231, 0.7); }
                    70% { box-shadow: 0 0 0 10px rgba(108, 92, 231, 0); }
                    100% { box-shadow: 0 0 0 0 rgba(108, 92, 231, 0); }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                .progress-section {
                    animation: pulse 2s infinite;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                .dimension-tag {
                    display: inline-block;
                    padding: 4px 8px;
                    margin: 2px;
                    border-radius: 12px;
                    font-size: 0.8rem;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                .dimension-tag.completed {
                    background: linear-gradient(135deg, #00b894, #00cec9);
                    color: white;
                    transform: scale(1.05);
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                .dimension-tag.current {
                    background: linear-gradient(135deg, #fdcb6e, #e17055);
                    color: white;
                    animation: currentDimension 1.5s infinite;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                .dimension-tag.pending {
                    background: #ddd;
                    color: #666;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                @keyframes currentDimension {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            `;
            document.head.appendChild(style);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    showError(message) {
        this.progressSection.style.display = 'none';
        this.resultContent.innerHTML = `
            <div class="error-message" style="
                background: linear-gradient(135deg, rgba(255, 82, 82, 0.1) 0%, rgba(255, 82, 82, 0.05) 100%);
                border: 1px solid var(--error-color);
                color: var(--error-color);
                padding: 20px;
                border-radius: var(--border-radius);
                text-align: center;
                margin-bottom: 16px;
            ">
                <div style="font-size: 2rem; margin-bottom: 8px;">❌</div>
                <div style="font-weight: 600; margin-bottom: 8px;">操作失败</div>
                <div>${message}</div>
            </div>
            <button onclick="location.reload()" style="
                background: var(--error-color);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: var(--border-radius);
                cursor: pointer;
                font-weight: 600;
                transition: var(--transition);
            ">重新开始</button>
        `;
        this.resultContent.classList.add('error');
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    showSuccess(cardKey) {
        this.progressSection.style.display = 'none';
        
        // 获取当前时间
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        
        this.resultContent.innerHTML = `
            <div style="
                background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 212, 170, 0.05) 100%);
                border: 1px solid var(--success-color);
                color: var(--success-color);
                padding: 24px;
                border-radius: var(--border-radius);
                text-align: center;
                margin-bottom: 20px;
                animation: successFadeIn 0.6s ease-out;
            ">
                <div style="font-size: 3rem; margin-bottom: 12px; animation: successBounce 0.8s ease-out;">🎉</div>
                <div style="font-size: 1.3rem; font-weight: 700; margin-bottom: 8px;">八字分析完成！</div>
                <div style="font-size: 1rem; margin-bottom: 12px;">您的专属命理分析报告已生成</div>
                <div style="font-size: 0.9rem; color: var(--text-secondary);">完成时间：${timeString}</div>
            </div>
            
            <div style="text-align: center; margin-bottom: 20px;">
                <a href="detailed_result.html?cardKey=${encodeURIComponent(cardKey)}" 
                   target="_blank" 
                   class="result-button" 
                   style="
                       display: inline-block;
                       background: linear-gradient(135deg, var(--success-color) 0%, #00b894 100%);
                       color: white;
                       text-decoration: none;
                       padding: 16px 32px;
                       border-radius: var(--border-radius);
                       font-weight: 600;
                       font-size: 1.1rem;
                       transition: var(--transition);
                       box-shadow: var(--shadow-lg);
                       transform: translateY(0);
                   "
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 30px rgba(0, 212, 170, 0.3)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow-lg)'"
                >🔮 查看完整分析报告</a>
            </div>
            
            <div style="
                background: var(--bg-secondary);
                border: 1px solid var(--border-color);
                border-radius: var(--border-radius);
                padding: 16px;
                text-align: center;
                font-size: 0.9rem;
                color: var(--text-secondary);
            ">
                <div style="margin-bottom: 8px;">💡 <strong>温馨提示</strong></div>
                <div>• 分析报告包含多个维度的详细解读</div>
                <div>• 建议收藏页面链接以便随时查看</div>
                <div>• 如有疑问，欢迎联系客服咨询</div>
            </div>
        `;
        
        this.resultContent.classList.add('success');
        
        // 添加成功动画的CSS
        if (!document.getElementById('success-animations')) {
            const style = document.createElement('style');
            style.id = 'success-animations';
            style.textContent = `
                @keyframes successFadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                @keyframes successBounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-10px); }
                    60% { transform: translateY(-5px); }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            `;
            document.head.appendChild(style);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }

    startPollingForCompletion(cardKey) {
        let fallbackProgress = 10;
        let pollCount = 0;
        let lastStatus = null;
        
        this.showProgress('🚀 请求已提交，正在初始化处理...', fallbackProgress);
        
        // 开始轮询
        this.pollingInterval = setInterval(() => {
            pollCount++;
            this.checkCompletionStatus(cardKey, (isCompleted, resultData, statusData) => {
                if (isCompleted) {
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                    this.setSubmitting(false);
                    this.showProgress('✅ 分析完成，正在保存结果...', 100);
                    
                    // 保存分析结果到本地存储
                    if (resultData && statusData) {
                        this.saveAnalysisResult(cardKey, resultData, statusData);
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                    
                    setTimeout(() => {
                        this.showSuccess(cardKey);
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 1000);
                    
                    if (resultData) {
                        console.log('处理结果:', resultData);
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                    // 优先使用后端返回的实际进度
                    if (statusData && statusData.llm_progress) {
                        this.displayLLMProgress(statusData.llm_progress, statusData.elapsed_time_formatted);
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (statusData && statusData.status) {
                        // 智能进度计算
                        const statusProgress = this.calculateProgressByStatus(statusData.status, pollCount);
                        fallbackProgress = Math.max(fallbackProgress, statusProgress);
                        
                        const statusMessage = this.getStatusMessage(statusData.status);
                        this.showProgress(statusMessage, fallbackProgress, statusData.elapsed_time_formatted);
                        
                        // 状态变化时给用户反馈
                        if (lastStatus !== statusData.status) {
                            this.showStatusChange(lastStatus, statusData.status);
                            lastStatus = statusData.status;
                        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                        // 基于轮询次数的智能进度
                        fallbackProgress = Math.min(10 + (pollCount * 2), 85);
                        const messages = [
                            '🔄 正在处理中...',
                            '📊 正在分析八字信息...',
                            '🤖 正在调用AI分析引擎...',
                            '📝 正在生成详细报告...'
                        ];
                        const messageIndex = Math.floor(pollCount / 3) % messages.length;
                        this.showProgress(messages[messageIndex], fallbackProgress);
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 2000); // 每2秒检查一次
        
        // 设置超时时间（10分钟，给LLM更多处理时间）
        setTimeout(() => {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
                this.setSubmitting(false);
                this.showError('⏰ 等待超时（10分钟），请稍后重试或联系客服。如果问题持续，可能是LLM服务繁忙。');
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 600000); // 10分钟超时
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    calculateProgressByStatus(status, pollCount) {
        // 根据状态计算智能进度
        const statusProgressMap = {
            'pending': 15,
            'processing': 25,
            'card_verified': 35,
            'bazi_calculated': 45,
            'llm_analyzing': 60,
            'generating_summary': 85,
            'completing': 95,
            'completed': 100
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
        
        const baseProgress = statusProgressMap[status] || 20;
        // 添加基于时间的微调
        const timeBonus = Math.min(pollCount * 1.5, 15);
        return Math.min(baseProgress + timeBonus, 98);
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    showStatusChange(oldStatus, newStatus) {
        if (!oldStatus) return;
        
        // 创建状态变化提示
        const statusChangeDiv = document.createElement('div');
        statusChangeDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 20px rgba(108, 92, 231, 0.3);
            z-index: 1000;
            animation: slideInRight 0.5s ease-out;
        `;
        
        const statusNames = {
            'pending': '等待处理',
            'processing': '开始处理',
            'card_verified': '卡密验证完成',
            'bazi_calculated': '八字计算完成',
            'llm_analyzing': 'AI分析中',
            'generating_summary': '生成摘要',
            'completing': '即将完成',
            'completed': '处理完成'
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
        
        statusChangeDiv.innerHTML = `
            🔄 ${statusNames[newStatus] || newStatus}
        `;
        
        document.body.appendChild(statusChangeDiv);
        
        // 添加动画样式
        if (!document.getElementById('status-change-animation')) {
            const style = document.createElement('style');
            style.id = 'status-change-animation';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            `;
            document.head.appendChild(style);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 3秒后移除提示
        setTimeout(() => {
            statusChangeDiv.style.animation = 'slideOutRight 0.5s ease-out';
            setTimeout(() => {
                if (statusChangeDiv.parentNode) {
                    statusChangeDiv.parentNode.removeChild(statusChangeDiv);
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 500);
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }, 3000);
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    displayLLMProgress(llmProgress, elapsedTime, progress) {
        const llmProgressElement = this.progressSection.querySelector('.llm-progress');
        const llmDimensionsElement = this.progressSection.querySelector('.llm-dimensions');
        
        llmProgressElement.style.display = 'block';
        
        // 清空现有维度标签
        llmDimensionsElement.innerHTML = '';
        
        // 使用后端返回的实际进度
        const actualProgress = llmProgress.progress || progress;
        
        // 添加维度标签，区分已完成和当前处理的维度
        if (llmProgress.dimensions && Array.isArray(llmProgress.dimensions)) {
            llmProgress.dimensions.forEach(dimension => {
                const tag = document.createElement('span');
                tag.className = 'dimension-tag';
                
                // 标记已完成的维度
                if (llmProgress.completed_dimensions && llmProgress.completed_dimensions.includes(dimension)) {
                    tag.classList.add('completed');
                    tag.innerHTML = `✅ ${dimension}`;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (llmProgress.current_dimension === dimension) {
                    tag.classList.add('current');
                    tag.innerHTML = `🔄 ${dimension}`;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                    tag.classList.add('pending');
                    tag.innerHTML = `⏳ ${dimension}`;
                }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                
                tag.textContent = tag.innerHTML;
                llmDimensionsElement.appendChild(tag);
            }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    });
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        // 构建详细的状态消息
        let statusMessage = '🤖 LLM深度分析中';
        if (llmProgress.current_dimension) {
            statusMessage += ` - 正在分析：${llmProgress.current_dimension}`;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        if (llmProgress.completed_dimensions && llmProgress.dimensions) {
            const completed = llmProgress.completed_dimensions.length;
            const total = llmProgress.dimensions.length;
            statusMessage += ` (${completed}/${total})`;
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
        
        this.showProgress(statusMessage, actualProgress, elapsedTime);
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
    
    getStatusMessage(status) {
        const statusMessages = {
            'pending': '⏳ 等待处理',
            'processing': '🔄 正在处理',
            'generating_summary': '📝 生成摘要中',
            'llm_analyzing': '🤖 LLM深度分析中',
            'completing': '✅ 即将完成',
            'completed': '✅ 处理完成',
            'failed': '❌ 处理失败'
        }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
        
        return statusMessages[status] || `🔄 ${status}`;
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }

    checkCompletionStatus(identifier, callback, isRequestId = false) {
         const xhr = new XMLHttpRequest();
         
         // 根据当前环境确定API基础URL
         const apiBaseUrl = getApiBaseUrl();
         
         // 构建查询参数，优先使用requestId
         const queryParam = isRequestId ? `requestId=${encodeURIComponent(identifier)}` : `cardKey=${encodeURIComponent(identifier)}`;
         xhr.open('GET', `${apiBaseUrl}/webhook/check-status?${queryParam}`, true);
         xhr.onreadystatechange = () => {
             if (xhr.readyState === 4) {
                 if (xhr.status === 200) {
                     try {
                         const response = JSON.parse(xhr.responseText);
                         console.log('状态检查响应:', response);
                         const isCompleted = response.completed === true || response.status === 'completed';
                         console.log('是否完成:', isCompleted);
                         callback(isCompleted, response.result, response);
                     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } catch (e) {
                         console.error('解析响应失败:', e);
                         callback(false, null, null);
                     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                 }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else if (xhr.status === 401) {
                     console.error('卡密无效:', xhr.responseText);
                     try {
                         const errorResponse = JSON.parse(xhr.responseText);
                         this.showError(`🔑 ${errorResponse.error || '卡密验证失败，请检查卡密是否正确'}`);
                         callback(false, null, { error: errorResponse.error || '卡密无效', status: 'failed' });
                     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } catch (e) {
                         this.showError('🔑 卡密验证失败，请检查卡密是否正确');
                         callback(false, null, { error: '卡密无效', status: 'failed' });
                     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                     // 清除轮询
                     if (this.pollingInterval) {
                         clearInterval(this.pollingInterval);
                         this.pollingInterval = null;
                     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
                     this.setSubmitting(false);
                 }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    } else {
                     console.error('检查状态失败:', xhr.status);
                     callback(false, null, null);
                 }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
             }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
         }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    };
         xhr.send();
     }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
}

// 初始化分析器
const baziAnalyzer = new BaziAnalyzer();

// 页面加载完成后的初始化
window.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成');
    document.getElementById('result').innerHTML = '';
});

// 页面卸载时清理轮询
window.addEventListener('beforeunload', function() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }

    // 获取表单值的辅助函数
    getFormValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }
});