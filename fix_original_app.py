#!/usr/bin/env python3
"""
修复原始app.py的脚本
"""

import os
import shutil
from datetime import datetime

def backup_original_app():
    """备份原始app.py"""
    backend_dir = 'backend'
    original_app = os.path.join(backend_dir, 'app.py')
    backup_app = os.path.join(backend_dir, f'app_backup_{int(datetime.now().timestamp())}.py')
    
    if os.path.exists(original_app):
        shutil.copy2(original_app, backup_app)
        print(f"✅ 已备份原始app.py到: {backup_app}")
        return True
    return False

def create_fixed_app():
    """创建修复后的app.py"""
    backend_dir = 'backend'
    app_path = os.path.join(backend_dir, 'app.py')
    
    # 读取原始文件的前面部分（导入和配置）
    try:
        with open(app_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到路由定义的开始位置
        route_start = content.find('@app.route(\'/\')')
        if route_start == -1:
            print("❌ 找不到根路径路由定义")
            return False
        
        # 保留路由定义之前的所有内容
        before_routes = content[:route_start]
        
        # 创建新的路由定义
        new_routes = '''@app.route('/')
def index():
    """主页 - 返回前端主页HTML文件"""
    try:
        logger.info("访问根路径")
        
        # 简化的文件查找逻辑
        file_candidates = ['index.html', 'up.html']
        path_candidates = [project_dir, STATIC_FOLDER, os.getcwd()]
        
        for filename in file_candidates:
            for base_path in path_candidates:
                file_path = os.path.join(base_path, filename)
                if os.path.exists(file_path):
                    logger.info(f"找到文件: {file_path}")
                    return send_file(file_path)
        
        # 如果找不到文件，返回简单的HTML页面
        logger.warning("找不到HTML文件，返回默认页面")
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>八字分析系统</title>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .container {{ max-width: 800px; margin: 0 auto; }}
                .status {{ background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .links {{ margin: 20px 0; }}
                .links a {{ display: inline-block; margin: 10px; padding: 10px 20px; 
                           background: #007cba; color: white; text-decoration: none; border-radius: 3px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎉 八字分析系统</h1>
                <div class="status">
                    <h3>✅ Flask服务器运行正常</h3>
                    <p>当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>项目目录: {project_dir}</p>
                    <p>工作目录: {os.getcwd()}</p>
                </div>
                <div class="links">
                    <a href="/test">测试页面</a>
                    <a href="/api/health">健康检查</a>
                    <a href="/static/up.html">分析页面</a>
                    <a href="/static/index.html">主页</a>
                </div>
            </div>
        </body>
        </html>
        """
    except Exception as e:
        logger.error(f"访问主页时出错: {str(e)}")
        return f"<h1>欢迎使用八字服务</h1><p>错误: {str(e)}</p>", 500

'''
        
        # 找到其他路由的开始位置
        test_route_start = content.find('@app.route(\'/test\')')
        if test_route_start != -1:
            # 保留其他路由
            after_routes = content[test_route_start:]
        else:
            # 如果找不到其他路由，添加基本路由
            after_routes = '''
@app.route('/test')
def test_page():
    """简单的测试页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head><title>测试页面</title></head>
    <body>
        <h1>🎉 Flask服务器运行正常！</h1>
        <p>这是一个简单的测试页面。</p>
        <ul>
            <li><a href="/">返回主页</a></li>
            <li><a href="/api/health">健康检查</a></li>
            <li><a href="/up.html">分析页面</a></li>
        </ul>
    </body>
    </html>
    """

''' + content[content.find('if __name__ == \'__main__\''):]
        
        # 组合新的文件内容
        new_content = before_routes + new_routes + after_routes
        
        # 写入新文件
        with open(app_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 已修复app.py")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复原始app.py")
    print("=" * 50)
    
    # 备份原始文件
    if backup_original_app():
        print("✅ 备份完成")
    else:
        print("❌ 备份失败")
        return
    
    # 修复文件
    if create_fixed_app():
        print("✅ 修复完成")
        print("\n💡 现在可以重新启动Flask服务器:")
        print("python backend/app.py")
    else:
        print("❌ 修复失败")

if __name__ == "__main__":
    main()
