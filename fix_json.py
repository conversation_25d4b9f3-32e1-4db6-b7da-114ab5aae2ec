import json
import re

# 读取原文件
with open(r'c:\Users\<USER>\Desktop\project\articles\articles.json','r',encoding='utf-8') as f:
    content = f.read()

print('原文件大小:', len(content))

# 查找并修复引号问题
# 在JSON字符串中，双引号需要转义为 \"
fixed_content = content

# 修复content字段中未转义的双引号
# 查找 "火" 这样的模式并替换为 \"火\"
pattern = r'("[^"]*?)"([^"]*?)"([^"]*?"[^"]*?[,}])'
fixed_content = re.sub(pattern, r'\1\"\2\"\3', fixed_content)

# 更精确的修复：在content字段内的引号
pattern2 = r'("content":\s*"[^"]*?)"([^"]*?)"([^"]*?"[^"]*?[,}])'
while re.search(pattern2, fixed_content):
    fixed_content = re.sub(pattern2, r'\1\"\2\"\3', fixed_content)

print('修复后文件大小:', len(fixed_content))

# 验证JSON格式
try:
    data = json.loads(fixed_content)
    print('JSON格式修复成功！包含', len(data), '个文章')
    
    # 保存修复后的文件
    with open(r'c:\Users\<USER>\Desktop\project\articles\articles_fixed.json','w',encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print('已保存修复后的文件为 articles_fixed.json')
    
except json.JSONDecodeError as e:
    print(f'仍有JSON错误: {e}')
    # 显示错误位置附近的内容
    lines = fixed_content.split('\n')
    char_count = 0
    for i, line in enumerate(lines):
        if char_count + len(line) + 1 >= e.pos:
            pos_in_line = e.pos - char_count
            print(f'错误在第{i+1}行，第{pos_in_line}个字符')
            print(f'错误行: {line}')
            if pos_in_line > 10:
                print(f'错误附近: {line[max(0,pos_in_line-20):pos_in_line+20]}')
            break
        char_count += len(line) + 1