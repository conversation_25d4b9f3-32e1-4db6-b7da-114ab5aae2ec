/**
 * 卡密管理和验证服务
 * 提供卡密验证、存储和管理功能
 */

// 获取API基础URL的统一函数
function getApiBaseUrl() {
    return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' 
        ? 'http://localhost:5000' 
        : `${window.location.protocol}//${window.location.hostname}:5000`;
}

// 多卡密管理函数
function getValidatedCards() {
    try {
        const cardsJson = localStorage.getItem('validatedCards');
        if (cardsJson) {
            const cards = JSON.parse(cardsJson);
            // 按时间戳降序排序，最新的在前面
            return cards.sort((a, b) => b.timestamp - a.timestamp);
        }
    } catch (error) {
        console.error('解析已验证卡密失败:', error);
        localStorage.removeItem('validatedCards');
    }
    return [];
}

function addValidatedCard(cardKey) {
    const validatedCards = getValidatedCards();
    const existingIndex = validatedCards.findIndex(card => card.cardKey === cardKey);
    
    if (existingIndex >= 0) {
        // 更新现有卡密的时间戳
        validatedCards[existingIndex].timestamp = Date.now();
    } else {
        // 添加新卡密
        validatedCards.unshift({
            cardKey: cardKey,
            timestamp: Date.now()
        });
    }
    
    // 只保留最近的10个卡密
    const limitedCards = validatedCards.slice(0, 10);
    localStorage.setItem('validatedCards', JSON.stringify(limitedCards));
    console.log('已保存卡密到缓存:', cardKey);
}

function removeValidatedCard(cardKey) {
    const validatedCards = getValidatedCards();
    const filteredCards = validatedCards.filter(card => card.cardKey !== cardKey);
    localStorage.setItem('validatedCards', JSON.stringify(filteredCards));
    console.log('已从缓存中移除卡密:', cardKey);
}

function getAllValidatedCardKeys() {
    return getValidatedCards().map(card => card.cardKey);
}

/**
 * 验证卡密格式和有效性
 * @param {string} cardKey - 卡密
 * @returns {Promise<boolean>} 是否有效
 */
async function validateCardKey(cardKey) {
    if (!cardKey || cardKey.trim() === '') {
        return false;
    }
    
    // 基本长度检查（可根据实际需求调整）
    if (cardKey.length < 6) {
        return false;
    }
    
    try {
        // 临时处理：在本地验证阶段，直接返回成功
        // 实际部署时应该替换为服务器验证
        console.log('卡密验证成功:', cardKey);
        return true;
        
        // 服务器验证代码（需要部署后启用）
        /*
        const apiBaseUrl = getApiBaseUrl();
        const response = await fetch(`${apiBaseUrl}/api/validate-card`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ cardKey: cardKey })
        });
        
        const data = await response.json();
        return data.success === true;
        */
    } catch (error) {
        console.error('验证卡密时出错:', error);
        return false;
    }
} 