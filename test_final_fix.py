#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果
"""

import requests
import time

def test_different_times():
    """测试不同时间是否生成不同结果"""
    print("🎯 测试不同时间生成不同结果")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 测试用例
    test_cases = [
        {
            "name": "测试1：1995年6月20日午时",
            "cardKey": "wdd",
            "data": {
                "year": "1995",
                "month": "6", 
                "day": "20",
                "hour": "午时",
                "gender": "1"
            }
        },
        {
            "name": "测试2：1988年2月14日卯时",
            "cardKey": "wdd",
            "data": {
                "year": "1988",
                "month": "2",
                "day": "14", 
                "hour": "卯时",
                "gender": "1"
            }
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送分析请求
            payload = {
                "cardKey": test_case["cardKey"],
                **test_case["data"]
            }
            
            print(f"📤 发送请求: {payload}")
            
            response = requests.post(
                'http://localhost:5000/webhook/bazi-analysis',
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                request_id = result.get('requestId')
                print(f"✅ 请求成功，ID: {request_id}")
                
                # 等待处理完成
                print("⏳ 等待处理完成...")
                time.sleep(15)
                
                # 查询结果 - 使用卡密查询
                print(f"🔍 查询卡密 {test_case['cardKey']} 的结果...")
                result_response = requests.get(f'http://localhost:5000/api/get_result/{test_case["cardKey"]}')
                
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    
                    if result_data.get('completed'):
                        result_obj = result_data.get('result', {})
                        bz = result_obj.get('data', {}).get('bz', {})
                        
                        if bz:
                            birth_info = bz.get('8', '未知')
                            bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                            
                            print(f"✅ 处理完成")
                            print(f"   生辰: {birth_info}")
                            print(f"   八字: {bazi_str}")
                            
                            results.append({
                                'name': test_case['name'],
                                'birth_info': birth_info,
                                'bazi': bazi_str,
                                'success': True
                            })
                        else:
                            print(f"❌ 没有八字数据")
                            results.append({'name': test_case['name'], 'success': False})
                    else:
                        print(f"⏳ 仍在处理中...")
                        results.append({'name': test_case['name'], 'success': False})
                else:
                    print(f"❌ 查询失败: {result_response.status_code}")
                    if result_response.status_code == 500:
                        print("   可能是时间比较错误，需要进一步修复")
                    results.append({'name': test_case['name'], 'success': False})
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                results.append({'name': test_case['name'], 'success': False})
                
        except Exception as e:
            print(f"💥 测试失败: {str(e)}")
            results.append({'name': test_case['name'], 'success': False})
    
    # 分析结果
    print(f"\n📊 结果分析")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get('success')]
    
    if len(successful_results) >= 2:
        # 检查是否生成了不同的结果
        birth_infos = [r['birth_info'] for r in successful_results]
        bazi_strings = [r['bazi'] for r in successful_results]
        
        print("📋 获得的结果:")
        for r in successful_results:
            print(f"   - {r['name']}: {r['birth_info']}")
        
        if len(set(birth_infos)) > 1 and len(set(bazi_strings)) > 1:
            print("\n✅ 成功！不同时间生成了不同的结果")
            return True
        else:
            print("\n❌ 失败：不同时间生成了相同的结果")
            return False
    else:
        print("❌ 测试失败，无法获得足够的结果进行比较")
        return False

def main():
    """主函数"""
    print("🎯 最终修复效果测试")
    print("=" * 60)
    
    success = test_different_times()
    
    if success:
        print("\n🎉 修复成功！")
        print("💡 现在系统能够正确处理不同时间的八字分析")
        print("🔗 你可以在 http://localhost:5000/ 测试不同的日期时间")
    else:
        print("\n⚠️ 还需要进一步修复")
        print("💡 请检查服务器日志以了解具体问题")

if __name__ == "__main__":
    main()
