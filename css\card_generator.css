/* 卡密生成器页面样式 */
:root {
    --primary-color: #4a6cf7;
    --accent-color: #6c5ce7;
    --success-color: #38b2ac;
    --warning-color: #ecc94b;
    --danger-color: #e53e3e;
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --border-color: #e2e8f0;
    --border-color-light: #edf2f7;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --transition: all 0.3s ease;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #38b2ac 0%, #2f855a 100%);
    --warning-gradient: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
    --danger-gradient: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    --hero-gradient: linear-gradient(135deg, #4a6cf7 0%, #6c5ce7 100%);
    --card-gradient: linear-gradient(to bottom, #ffffff 0%, #f8fafc 100%);
    --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: var(--hero-gradient);
    color: white;
    padding: 30px;
    text-align: center;
    border-radius: var(--border-radius-lg);
    margin-bottom: 30px;
    box-shadow: var(--shadow-lg);
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.nav-links {
    margin-top: 15px;
}

.nav-link {
    display: inline-block;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.content {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.section {
    margin-bottom: 30px;
    padding: 25px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.section h2 {
    color: var(--text-primary);
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

.btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn:hover {
    background: #3a5bd9;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: var(--primary-color);
}

.btn-primary:hover {
    background: #3a5bd9;
}

.btn-secondary {
    background: var(--accent-color);
}

.btn-secondary:hover {
    background: #5a4ad9;
}

.btn-success {
    background: var(--success-color);
}

.btn-success:hover {
    background: #2c9a8c;
}

.btn-danger {
    background: var(--danger-color);
}

.btn-danger:hover {
    background: #c53030;
}

.status {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: none;
    font-weight: 500;
}

.status.success {
    background: rgba(56, 178, 172, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(56, 178, 172, 0.3);
}

.status.error {
    background: rgba(229, 62, 62, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(229, 62, 62, 0.3);
}

.loading {
    text-align: center;
    padding: 30px;
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(74, 108, 247, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    margin: 0 auto 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.stat-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow-sm);
    flex: 1;
    margin: 0 10px 10px 0;
    text-align: center;
    min-width: 120px;
}

.stat-label {
    display: block;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.card-list-container,
.system-card-list-container {
    margin-top: 20px;
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.card-table {
    width: 100%;
    border-collapse: collapse;
}

.card-table th,
.card-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color-light);
}

.card-table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 0 var(--border-color);
}

.card-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.card-table tr:hover {
    background-color: rgba(74, 108, 247, 0.05);
}

.saved-badge {
    display: inline-block;
    padding: 4px 8px;
    background-color: rgba(56, 178, 172, 0.1);
    color: var(--success-color);
    border-radius: 4px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.unsaved-badge {
    display: inline-block;
    padding: 4px 8px;
    background-color: rgba(237, 137, 54, 0.1);
    color: #ed8936;
    border-radius: 4px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.delete-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.delete-btn:hover {
    background: #c53030;
    transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .content {
        padding: 20px;
    }
    
    .section {
        padding: 15px;
    }
    
    .card-stats {
        flex-direction: column;
    }
    
    .stat-item {
        margin-right: 0;
    }
    
    .btn {
        width: 100%;
        margin-right: 0;
    }
}