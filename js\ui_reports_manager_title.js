/**
 * 报告标题生成器
 * 为历史报告生成个性化标题
 */

// 扩展HistoryReportsManager类，添加标题生成方法
HistoryReportsManager.prototype.generateReportTitle = function(report) {
    if (!report) {
        return '未知报告';
    }
    
    try {
        console.log('生成报告标题，报告ID:', report.id);
        
        // 尝试从报告中提取姓名
        let name = '';
        let birthDate = '';
        let gender = '';
        
        // 记录数据调试信息
        console.log('报告数据结构:', {
            hasData: !!report.data,
            dataKeys: report.data ? Object.keys(report.data) : [],
            dataType: report.data ? typeof report.data : 'undefined'
        });
        
        // 优先从原始请求数据中获取出生年月日和性别 - 多个可能的路径
        let originalRequestData = null;
        
        // 路径1: report.data.requestData (最常见的保存位置)
        if (report.data && report.data.requestData) {
            originalRequestData = report.data.requestData;
            console.log('从report.data.requestData获取原始请求数据:', originalRequestData);
        }
        // 路径2: report.analysisData.requestData
        else if (report.analysisData && report.analysisData.requestData) {
            originalRequestData = report.analysisData.requestData;
            console.log('从report.analysisData.requestData获取原始请求数据:', originalRequestData);
        }
        // 路径3: report.data.resultData.requestData
        else if (report.data && report.data.resultData && report.data.resultData.requestData) {
            originalRequestData = report.data.resultData.requestData;
            console.log('从report.data.resultData.requestData获取原始请求数据');
        }
        // 路径4: report.resultData.requestData
        else if (report.resultData && report.resultData.requestData) {
            originalRequestData = report.resultData.requestData;
            console.log('从report.resultData.requestData获取原始请求数据');
        }
        // 路径5: report.originalRequestData (直接保存的情况)
        else if (report.originalRequestData) {
            originalRequestData = report.originalRequestData;
            console.log('从report.originalRequestData获取原始请求数据');
        }
        
        // 如果找到原始请求数据，直接从中提取信息
        if (originalRequestData) {
            console.log('找到原始请求数据，开始提取信息，数据内容:', originalRequestData);
            console.log('原始请求数据的字段:', Object.keys(originalRequestData));
            
            // 提取年月日 - 检查多种可能的字段名
            const year = originalRequestData.year || originalRequestData.birthYear;
            const month = originalRequestData.month || originalRequestData.birthMonth;
            const day = originalRequestData.day || originalRequestData.birthDay;
            
            console.log('提取的年月日:', { year, month, day });
            console.log('年月日类型检查:', { 
                yearType: typeof year, 
                monthType: typeof month, 
                dayType: typeof day,
                yearEmpty: year === '',
                monthEmpty: month === '',
                dayEmpty: day === ''
            });
            
            // 检查年月日是否为有效值（不为空字符串、null、undefined）
            if (year && year !== '' && month && month !== '' && day && day !== '') {
                // 构建标准日期格式
                birthDate = `${year}年${month}月${day}日`;
                console.log('从原始请求数据构建的出生日期:', birthDate);
            } else {
                console.log('原始请求数据中缺少完整的年月日信息，详细检查:', {
                    year: year,
                    month: month,
                    day: day,
                    yearValid: year && year !== '',
                    monthValid: month && month !== '',
                    dayValid: day && day !== ''
                });
            }
            
            // 提取性别 - 检查多种可能的字段名
            const genderValue = originalRequestData.gender || originalRequestData.sex;
            if (genderValue && genderValue !== '') {
                gender = genderValue;
                console.log('从原始请求数据获取的性别:', gender);
            } else {
                console.log('原始请求数据中没有性别信息，性别值:', genderValue);
            }
        }
        
        // 如果没有从原始请求数据获取到信息，尝试从八字数据中提取
        if (!birthDate || !gender) {
            console.log('未从原始请求数据获取到完整信息，尝试从八字数据和其他渠道提取');
            
            // 尝试从八字数据中提取出生时间信息
            let baziData = null;
            
            // 查找八字数据的多个可能路径
            if (report.data && (report.data.sex !== undefined || report.data.bz)) {
                // 直接使用report.data作为八字数据（新格式）
                baziData = report.data;
                console.log('从report.data找到八字数据（直接格式）');
            } else if (report.data && report.data.bazi_data) {
                baziData = report.data.bazi_data;
                console.log('从report.data.bazi_data找到八字数据');
            } else if (report.data && report.data.resultData && report.data.resultData.bazi_data) {
                baziData = report.data.resultData.bazi_data;
                console.log('从report.data.resultData.bazi_data找到八字数据');
            } else if (report.resultData && report.resultData.bazi_data) {
                baziData = report.resultData.bazi_data;
                console.log('从report.resultData.bazi_data找到八字数据');
            } else if (report.analysisData && report.analysisData.bazi_data) {
                baziData = report.analysisData.bazi_data;
                console.log('从report.analysisData.bazi_data找到八字数据');
            } else if (report.bazi_data) {
                baziData = report.bazi_data;
                console.log('从report.bazi_data找到八字数据');
            }
            
            // 如果找到八字数据，尝试提取出生时间和性别
            if (baziData) {
                console.log('八字数据结构:', baziData);
                
                // 从八字数据中提取性别
                if (!gender && baziData.sex !== undefined) {
                    if (baziData.sex === 1 || baziData.sex === '1') {
                        gender = '男';
                    } else if (baziData.sex === 2 || baziData.sex === '2') {
                        gender = '女';
                    } else if (typeof baziData.sex === 'string') {
                        gender = baziData.sex;
                    }
                    console.log('从八字数据提取的性别:', gender);
                }
                
                // 从八字数据的bz字段提取出生时间（通常在bz["8"]中）
                if (!birthDate && baziData.bz && typeof baziData.bz === 'object') {
                    // 检查bz["8"]字段，这里通常包含完整的出生时间字符串
                    const birthTimeString = baziData.bz["8"] || baziData.bz[8];
                    if (birthTimeString && typeof birthTimeString === 'string') {
                        console.log('从八字数据bz["8"]提取的出生时间字符串:', birthTimeString);
                        
                        // 解析出生时间字符串，例如："1899年腊月初一 子时"
                        // 尝试提取年份和日期部分
                        const timeMatch = birthTimeString.match(/(\d{4})年(.+?)\s/);
                        if (timeMatch) {
                            const year = timeMatch[1];
                            const dateStr = timeMatch[2];
                            
                            // 构建出生日期
                            birthDate = `${year}年${dateStr}`;
                            console.log('从八字数据解析的出生日期:', birthDate);
                        } else {
                            // 如果无法解析，直接使用原字符串（去掉时辰部分）
                            birthDate = birthTimeString.replace(/\s[子丑寅卯辰巳午未申酉戌亥]时$/, '');
                            console.log('使用八字数据原始出生时间（去掉时辰）:', birthDate);
                        }
                    }
                }
            }
            
            // 处理不同的数据结构
            const data = report.data || {};
            const resultData = report.resultData || {};
            const analysisData = report.analysisData || {};
            
            // 先直接从报告顶层属性中提取信息(优先级最高)
            if (report.birthDate || report.birth_date || report.gender || report.sex) {
                console.log('从报告顶层属性提取信息');
                
                // 提取出生日期
                if (!birthDate) {
                    birthDate = report.birthDate || report.birth_date || '';
                }
                
                // 提取性别
                if (!gender) {
                    gender = report.gender || report.sex || '';
                }
                
                console.log('从顶层属性提取的信息:', { birthDate, gender });
            }
            // 如果顶层没有，再尝试其他结构
            else if (data.basicInfo && typeof data.basicInfo === 'object') {
                console.log('从data.basicInfo提取基本信息');
                const basicInfo = data.basicInfo;
                
                // 提取姓名
                name = basicInfo.name || '';
                
                // 提取出生日期
                if (!birthDate) {
                    birthDate = basicInfo.birthDate || basicInfo.birth_date || '';
                }
                
                // 提取性别
                if (!gender) {
                    gender = basicInfo.gender || basicInfo.sex || '';
                }
                
                console.log('从basicInfo提取的信息:', { name, birthDate, gender });
            } else {
                // 标准模式，从各个位置尝试提取
                console.log('尝试从标准数据结构提取信息');
                
                // 优先从基本信息中提取
                const basicInfo = {};
                
                // 如果是嵌套结构，尝试提取深层信息
                if (typeof data === 'object' && data !== null) {
                    if (data.basicInfo) {
                        Object.assign(basicInfo, data.basicInfo);
                    } else if (data.resultData && data.resultData.basicInfo) {
                        Object.assign(basicInfo, data.resultData.basicInfo);
                    }
                }
                
                // 提取姓名
                name = basicInfo.name || data.name || resultData.name || analysisData.name || '';
                
                // 提取出生日期
                if (!birthDate) {
                    birthDate = basicInfo.birthDate || basicInfo.birth_date || 
                                data.birthDate || data.birth_date || 
                                resultData.birthDate || resultData.birth_date || 
                                analysisData.birthDate || analysisData.birth_date || 
                                report.birthDate || report.birth_date || '';
                }
                
                // 提取性别
                if (!gender) {
                    gender = basicInfo.gender || basicInfo.sex || 
                            data.gender || data.sex || 
                            resultData.gender || resultData.sex || 
                            analysisData.gender || analysisData.sex || 
                            report.gender || report.sex || '';
                }
                        
                console.log('从标准数据结构提取的信息:', { name, birthDate, gender });
            }
        }
        
        // 从report直接提取的备用信息
        const fallbackBirthDate = report.birthDate || report.birth_date || '';
        const fallbackGender = report.gender || report.sex || '';
        
        // 使用备用信息（如果主要提取失败）
        if (!birthDate && fallbackBirthDate) {
            birthDate = fallbackBirthDate;
            console.log('使用备用出生日期:', birthDate);
        }
        
        if (!gender && fallbackGender) {
            gender = fallbackGender;
            console.log('使用备用性别:', gender);
        }
        
        // 格式化性别
        let genderText = '';
        if (typeof gender === 'string') {
            if (gender.toLowerCase() === 'male' || gender === '男' || gender === '1') {
                genderText = '男';
            } else if (gender.toLowerCase() === 'female' || gender === '女' || gender === '2') {
                genderText = '女';
            } else {
                genderText = gender;
            }
        } else if (gender === 1) {
            genderText = '男';
        } else if (gender === 2) {
            genderText = '女';
        }
        
        // 格式化出生日期
        if (birthDate) {
            // 如果是ISO格式的日期，转换成更友好的格式
            if (birthDate.includes('T') || birthDate.includes('-')) {
                try {
                    const date = new Date(birthDate);
                    if (!isNaN(date.getTime())) {
                        birthDate = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
                    }
                } catch (e) {
                    console.error('日期格式转换失败:', e);
                }
            }
        }
        
        // 我们不再从ID中提取时间戳作为出生日期，因为ID时间戳是报告创建时间，不是命主出生时间
        
        console.log('最终使用的信息:', { name, birthDate, genderText });
        
        // 生成报告标题 - 优先使用原始请求数据构建出生时间
        let title = '';
        
        // 如果有原始请求数据，优先使用年月日构建标题
        if (originalRequestData && originalRequestData.year && originalRequestData.month && originalRequestData.day) {
            const year = originalRequestData.year;
            const month = originalRequestData.month;
            const day = originalRequestData.day;
            const birthTimeFromRequest = `${year}年${month}月${day}日`;
            
            if (genderText) {
                title = `${birthTimeFromRequest} ${genderText}性八字分析`;
            } else {
                title = `${birthTimeFromRequest} 八字分析`;
            }
            
            console.log('使用原始请求数据构建标题:', title);
        }
        // 如果没有原始请求数据，使用提取的出生日期
        else if (birthDate && genderText) {
            title = `${birthDate} ${genderText}性八字分析`;
        } else if (birthDate) {
            title = `${birthDate} 八字分析`;
        } else if (genderText) {
            title = `${genderText}性八字分析`;
        } else {
            title = '八字分析报告';
        }
        
        console.log('生成的标题:', title, '使用参数:', {
            birthDate: birthDate,
            gender: gender,
            genderText: genderText,
            reportId: report.id,
            hasTopLevelBirthDate: !!report.birthDate || !!report.birth_date,
            hasTopLevelGender: !!report.gender || !!report.sex,
            hasDataBasicInfo: !!(report.data && report.data.basicInfo),
            dataSource: report._source || 'unknown'
        });
        
        return title;
    } catch (error) {
        console.error('生成报告标题时出错:', error);
        return '八字分析报告';
    }
};