<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词管理系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .prompt-manager {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .tab:hover:not(.active) {
            background: rgba(255,255,255,0.5);
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            min-height: 200px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .prompt-list {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .prompt-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .prompt-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }
        
        .prompt-item h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 18px;
        }
        
        .prompt-item .meta {
            color: #666;
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .prompt-item .actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .version-list {
            display: grid;
            gap: 15px;
            margin-top: 20px;
        }
        
        .version-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .version-item.current {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .version-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .version-info .meta {
            color: #666;
            font-size: 12px;
        }
        
        .evaluation-result {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .score-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .score-excellent { background: #28a745; }
        .score-good { background: #17a2b8; }
        .score-fair { background: #ffc107; color: #333; }
        .score-poor { background: #dc3545; }
        
        .issues-list {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .issues-list h4 {
            margin: 0 0 10px 0;
            color: #dc3545;
        }
        
        .issues-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .issues-list li {
            margin-bottom: 5px;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        @media (max-width: 768px) {
            .prompt-manager {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                margin-bottom: 5px;
            }
            
            .tab-content {
                padding: 20px;
            }
            
            .version-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="prompt-manager">
        <div class="header">
            <h1>🧠 提示词管理系统</h1>
            <p>八字分析提示词的统一管理、版本控制和质量评估平台</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('prompts')">📝 提示词管理</button>
            <button class="tab" onclick="switchTab('versions')">🔄 版本控制</button>
            <button class="tab" onclick="switchTab('evaluation')">📊 质量评估</button>
            <button class="tab" onclick="switchTab('testing')">🧪 A/B测试</button>
        </div>
        
        <!-- 提示词管理 -->
        <div id="prompts-tab" class="tab-content active">
            <h2>📝 提示词管理</h2>
            
            <div class="form-group">
                <label for="dimension-select">选择分析维度：</label>
                <select id="dimension-select" onchange="loadPrompt()">
                    <option value="">请选择维度</option>
                    <option value="性格">性格分析</option>
                    <option value="感情">感情分析</option>
                    <option value="职业">职业分析</option>
                    <option value="健康">健康分析</option>
                    <option value="日主强弱">日主强弱</option>
                    <option value="学业">学业分析</option>
                    <option value="2025运势">2025运势</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="prompt-content">提示词内容：</label>
                <textarea id="prompt-content" placeholder="在此编辑提示词内容..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="prompt-description">版本描述：</label>
                <input type="text" id="prompt-description" placeholder="描述此次修改的内容...">
            </div>
            
            <div>
                <button class="btn btn-primary" onclick="savePrompt()">💾 保存提示词</button>
                <button class="btn btn-secondary" onclick="previewPrompt()">👁️ 预览效果</button>
                <button class="btn btn-success" onclick="testPrompt()">🧪 测试分析</button>
            </div>
            
            <div id="prompt-preview" style="display: none; margin-top: 20px;">
                <h3>预览效果</h3>
                <div id="preview-content" class="evaluation-result"></div>
            </div>
        </div>
        
        <!-- 版本控制 -->
        <div id="versions-tab" class="tab-content">
            <h2>🔄 版本控制</h2>
            
            <div style="margin-bottom: 20px;">
                <button class="btn btn-primary" onclick="createVersion()">➕ 创建新版本</button>
                <button class="btn btn-secondary" onclick="loadVersions()">🔄 刷新列表</button>
            </div>
            
            <div id="versions-list" class="version-list">
                <div class="loading">正在加载版本列表...</div>
            </div>
        </div>
        
        <!-- 质量评估 -->
        <div id="evaluation-tab" class="tab-content">
            <h2>📊 质量评估</h2>
            
            <div class="form-group">
                <label for="eval-dimension">选择评估维度：</label>
                <select id="eval-dimension">
                    <option value="性格">性格分析</option>
                    <option value="感情">感情分析</option>
                    <option value="职业">职业分析</option>
                    <option value="健康">健康分析</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="test-bazi">测试八字数据：</label>
                <textarea id="test-bazi" placeholder="输入测试用的八字数据...">
【八字基本信息】
出生时间：1990年5月15日 14:30
八字：庚午年 辛巳月 甲子日 辛未时

【十神配置】
天干十神：甲木(日主) 庚金(七杀) 辛金(正官) 辛金(正官)
地支藏干十神：丁火(伤官) 戊土(偏财) 丙火(食神) 戊土(偏财) 癸水(正印) 己土(正财) 丁火(伤官) 乙木(比肩)
                </textarea>
            </div>
            
            <div>
                <button class="btn btn-primary" onclick="runEvaluation()">🔍 开始评估</button>
                <button class="btn btn-secondary" onclick="batchEvaluation()">📊 批量评估</button>
            </div>
            
            <div id="evaluation-results" style="display: none;">
                <!-- 评估结果将在这里显示 -->
            </div>
        </div>
        
        <!-- A/B测试 -->
        <div id="testing-tab" class="tab-content">
            <h2>🧪 A/B测试</h2>
            
            <div class="form-group">
                <label for="experiment-name">实验名称：</label>
                <input type="text" id="experiment-name" placeholder="输入实验名称...">
            </div>
            
            <div class="form-group">
                <label for="variant-versions">测试版本（用逗号分隔）：</label>
                <input type="text" id="variant-versions" placeholder="例如：v1.0,v1.1,v1.2">
            </div>
            
            <div class="form-group">
                <label for="traffic-split">流量分配（用逗号分隔，总和为1）：</label>
                <input type="text" id="traffic-split" placeholder="例如：0.33,0.33,0.34">
            </div>
            
            <div>
                <button class="btn btn-primary" onclick="createExperiment()">🚀 创建实验</button>
                <button class="btn btn-secondary" onclick="loadExperiments()">📋 查看实验</button>
            </div>
            
            <div id="experiments-list" style="margin-top: 20px;">
                <!-- 实验列表将在这里显示 -->
            </div>
        </div>
    </div>
    
    <script src="js/prompt_manager.js"></script>
</body>
</html>
