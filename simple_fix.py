import json

# 读取原文件
with open(r'c:\Users\<USER>\Desktop\project\articles\articles.json','r',encoding='utf-8') as f:
    content = f.read()

print('开始修复JSON...')

# 简单的修复方法：在content字段内的双引号前加反斜杠
# 找到所有content字段，然后在其内容中转义双引号
lines = content.split('\n')
fixed_lines = []

in_content = False
content_start = False

for line in lines:
    if '"content":' in line:
        in_content = True
        content_start = True
    
    if in_content:
        if content_start:
            # 这是content字段的开始行
            # 找到第一个引号后的内容
            parts = line.split('"content":', 1)
            if len(parts) == 2:
                prefix = parts[0] + '"content":'
                content_part = parts[1]
                # 在content内容中转义双引号（除了最开始和最结尾的）
                if content_part.strip().startswith('"') and content_part.strip().endswith('"'):
                    # 移除开头和结尾的引号
                    inner_content = content_part.strip()[1:-1]
                    # 转义内部的双引号
                    inner_content = inner_content.replace('"', '\\"')
                    # 重新组装
                    line = prefix + ' "' + inner_content + '"'
                fixed_lines.append(line)
            else:
                fixed_lines.append(line)
            content_start = False
        else:
            # content字段的后续行
            if line.strip().endswith('"}') or line.strip().endswith('"},'):
                # content字段结束
                in_content = False
            # 转义这行中的双引号
            line = line.replace('"', '\\"')
            fixed_lines.append(line)
    else:
        fixed_lines.append(line)

fixed_content = '\n'.join(fixed_lines)

print('尝试验证修复后的JSON...')
try:
    data = json.loads(fixed_content)
    print('JSON格式修复成功！包含', len(data), '个文章')
    
    # 保存修复后的文件
    with open(r'c:\Users\<USER>\Desktop\project\articles\articles_fixed.json','w',encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print('已保存修复后的文件为 articles_fixed.json')
    
except json.JSONDecodeError as e:
    print(f'仍有JSON错误: {e}')
    print('尝试手动修复特定问题...')
    
    # 手动修复已知的问题
    manual_fixed = content
    # 修复"火"这样的引号
    manual_fixed = manual_fixed.replace('"火"', '\\"火\\"')
    manual_fixed = manual_fixed.replace('"quoted"', '\\"quoted\\"')
    
    try:
        data = json.loads(manual_fixed)
        print('手动修复成功！')
        with open(r'c:\Users\<USER>\Desktop\project\articles\articles_fixed.json','w',encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print('已保存修复后的文件')
    except json.JSONDecodeError as e2:
        print(f'手动修复也失败: {e2}')