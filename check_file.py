#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 bazi_results.json 文件
"""

import json

def check_file():
    """检查文件内容"""
    try:
        with open('bazi_results.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"文件中总记录数: {len(data)}")
        
        # 查找 Xs1MR9iVx9RNZOk6 相关记录
        xs1mr_keys = [k for k in data.keys() if 'Xs1MR9iVx9RNZOk6' in k]
        print(f"包含 Xs1MR9iVx9RNZOk6 的记录: {len(xs1mr_keys)}")
        
        for k in xs1mr_keys:
            print(f"  请求ID: {k}")
            result = data[k]
            
            # 基本信息
            card_key = result.get('card_key', '未知')
            print(f"    卡密: {card_key}")
            
            # 八字数据
            data_obj = result.get('data', {})
            bz = data_obj.get('bz', {})
            
            if bz:
                birth_info = bz.get('8', '未知生辰')
                print(f"    生辰: {birth_info}")
                
                # 显示八字四柱
                if len(bz) >= 8:
                    bazi_str = f"{bz.get('0', '?')}{bz.get('1', '?')} {bz.get('2', '?')}{bz.get('3', '?')} {bz.get('4', '?')}{bz.get('5', '?')} {bz.get('6', '?')}{bz.get('7', '?')}"
                    print(f"    八字: {bazi_str}")
            else:
                print("    八字: 无数据")
            
            # 处理时间
            processed_time = result.get('processed_time', '未知')
            print(f"    处理时间: {processed_time}")
            
            # 成功状态
            success = result.get('success', False)
            print(f"    成功状态: {success}")
            print()
        
        # 显示前5个记录的键名
        print("\n前5个记录的键名:")
        for i, key in enumerate(list(data.keys())[:5]):
            print(f"  {i+1}. {key}")
        
        # 显示最后5个记录的键名
        print("\n最后5个记录的键名:")
        keys = list(data.keys())
        for i, key in enumerate(keys[-5:]):
            print(f"  {len(keys)-4+i}. {key}")
            
    except Exception as e:
        print(f"检查文件失败: {str(e)}")

if __name__ == "__main__":
    check_file()
