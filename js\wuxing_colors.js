// 天干地支五行属性映射
const TIANGAN_WUXING = {
    '甲': '木',
    '乙': '木',
    '丙': '火',
    '丁': '火',
    '戊': '土',
    '己': '土',
    '庚': '金',
    '辛': '金',
    '壬': '水',
    '癸': '水'
};

const DIZHI_WUXING = {
    '子': '水',
    '丑': '土',
    '寅': '木',
    '卯': '木',
    '辰': '土',
    '巳': '火',
    '午': '火',
    '未': '土',
    '申': '金',
    '酉': '金',
    '戌': '土',
    '亥': '水'
};

// 根据五行返回颜色值
function getWuxingColor(wuxing) {
    switch (wuxing) {
        case '火':
            return 'rgb(211, 6, 5)';
        case '土':
            return 'rgb(135, 109, 2)';
        case '金':
            return 'rgb(231, 144, 15)';
        case '水':
            return 'rgb(49, 131, 239)';
        case '木':
            return 'rgb(13, 230, 51)';
        case '空':
            return 'rgb(255, 255, 255)';
        default:
            return 'rgb(0, 0, 0)'; // 默认黑色
    }
}

// 根据天干获取颜色
function getTianganColor(tiangan) {
    const wuxing = TIANGAN_WUXING[tiangan];
    return getWuxingColor(wuxing);
}

// 根据地支获取颜色
function getDizhiColor(dizhi) {
    const wuxing = DIZHI_WUXING[dizhi];
    return getWuxingColor(wuxing);
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getTianganColor,
        getDizhiColor,
        getWuxingColor,
        TIANGAN_WUXING,
        DIZHI_WUXING
    };
}