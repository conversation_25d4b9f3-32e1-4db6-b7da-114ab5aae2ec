/**
 * @file 八字分析结果显示模块
 * @description 负责格式化和显示分析结果
 */

// 注意：这些函数在其他文件中定义，通过全局作用域访问
// generateBaziInfoSection 在 detailed_result_bazi_info.js 中定义
// initializeToggleFeatures 在 detailed_result_utils.js 中定义

/**
 * 过滤掉分析内容中的提示词和格式指令
 * @param {string} content - 原始分析内容
 * @param {string} key - 分析维度的键名
 * @returns {string} - 过滤后的分析内容
 */
function filterPromptWords(content, key = '') {
    if (!content || typeof content !== 'string') {
        return content;
    }
    
    // 启用调试日志，可以在需要时打开
    const enableLogging = false;
    const logPrefix = `[过滤:${key}]`;
    
    if (enableLogging) {
        console.log(`${logPrefix} 开始过滤，原始长度: ${content.length}`);
    }
    
    // 处理花括号包裹的情况
    let filtered = content.trim();
    // 如果内容被花括号完整包裹，且内部没有其他花括号，直接去除外层花括号
    if (filtered.startsWith('{') && filtered.endsWith('}')) {
        const inner = filtered.slice(1, -1).trim();
        if (!inner.includes('{') && !inner.includes('}')) {
            filtered = inner;
            if (enableLogging) {
                console.log(`${logPrefix} 移除了外层花括号`);
            }
        }
    }
    
    // 先尝试解析内容中可能的JSON格式
    let jsonParsed = false;
    
    // 检查是否是JSON字符串
    if (filtered.trim().startsWith('{') && filtered.trim().endsWith('}')) {
        try {
            const jsonObj = JSON.parse(filtered);
            
            // 从JSON中提取实际内容
            if (jsonObj.结果输出) {
                filtered = jsonObj.结果输出;
                jsonParsed = true;
                if (enableLogging) {
                    console.log(`${logPrefix} 从JSON中提取了"结果输出"字段，长度: ${filtered.length}`);
                }
            } else if (jsonObj.content) {
                filtered = jsonObj.content;
                jsonParsed = true;
                if (enableLogging) {
                    console.log(`${logPrefix} 从JSON中提取了"content"字段，长度: ${filtered.length}`);
                }
            } else if (jsonObj.analysis) {
                filtered = jsonObj.analysis;
                jsonParsed = true;
                if (enableLogging) {
                    console.log(`${logPrefix} 从JSON中提取了"analysis"字段，长度: ${filtered.length}`);
                }
            } else {
                // 查找最长的字符串字段作为内容
                let longestContent = '';
                for (const field in jsonObj) {
                    if (typeof jsonObj[field] === 'string' && 
                        jsonObj[field].length > longestContent.length &&
                        field !== 'format_template') { // 排除format_template字段
                        longestContent = jsonObj[field];
                    }
                }
                
                if (longestContent.length > 50) { // 确保内容足够长
                    filtered = longestContent;
                    jsonParsed = true;
                    if (enableLogging) {
                        console.log(`${logPrefix} 从JSON中提取了最长字符串字段，长度: ${filtered.length}`);
                    }
                }
            }
        } catch (e) {
            // JSON解析失败，继续使用原始内容
            if (enableLogging) {
                console.log(`${logPrefix} JSON解析尝试失败: ${e.message}`);
            }
        }
    }
    
    // 处理JSON字符串中的转义字符
    if (!jsonParsed && filtered.includes('\\"') && filtered.includes('\\n')) {
        try {
            // 尝试对可能的JSON字符串进行清理和解析
            let cleaned = filtered;
            // 如果内容被额外的引号包围，移除它们
            if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
                cleaned = cleaned.substring(1, cleaned.length - 1);
            }
            // 将转义的JSON字符串转换为对象
            const jsonObj = JSON.parse(cleaned);
            
            // 从JSON中提取实际内容
            if (jsonObj.结果输出) {
                filtered = jsonObj.结果输出;
                jsonParsed = true;
            } else if (jsonObj.content) {
                filtered = jsonObj.content;
                jsonParsed = true;
            } else if (jsonObj.analysis) {
                filtered = jsonObj.analysis;
                jsonParsed = true;
            }
        } catch (e) {
            // 第二次尝试解析JSON失败，继续使用原始内容
            if (enableLogging) {
                console.log(`${logPrefix} 第二次JSON解析尝试失败: ${e.message}`);
            }
        }
    }
    
    // 移除提示词规范部分（使用正则表达式匹配各种可能的格式）
    filtered = filtered.replace(/【重要提醒】.*?(\n\n|\n(?=\S))/gs, '');
    filtered = filtered.replace(/【格式规范】.*?(\n\n|\n(?=\S))/gs, '');
    filtered = filtered.replace(/^\s*【.*?】.*?(\n\n|\n(?=\S))/gms, '');
    
    // 移除包含"严禁"、"ONLY"等关键词的指令性语句
    filtered = filtered.replace(/.*严禁.*(\n|$)/g, '');
    filtered = filtered.replace(/.*ONLY.*(\n|$)/g, '');
    filtered = filtered.replace(/.*必须使用.*(\n|$)/g, '');
    filtered = filtered.replace(/.*\bmarkdown\b.*(\n|$)/gi, '');
    filtered = filtered.replace(/.*代码格式.*(\n|$)/g, '');
    
    // 移除格式模板相关指令
    filtered = filtered.replace(/^请(?:根据|按照|严格按照)(?:以下|下列)(?:要求|格式|模板).*?(?:进行|输出|分析).*?(\n\n|\n(?=\S))/gs, '');
    filtered = filtered.replace(/^根据.*?(?:命盘信息|八字|五行).*?(?:严格|格式).*?(?:分析|输出).*?[：:]/gs, '');
    filtered = filtered.replace(/^以下是(?:对「.*?」维度的|针对「.*?」的|关于)(?:专业|详细)?分析.*?[：:]/gs, '');
    filtered = filtered.replace(/^以下是.*?维度的分析结果.*?[：:]/gs, '');
    filtered = filtered.replace(/^严格按照(?:要求|格式).*?分析.*?[：:]/gs, '');
    filtered = filtered.replace(/^根据(?:严格)?要求.*?[：:]/gs, '');
    filtered = filtered.replace(/^按照(?:格式)?模板.*?[：:]/gs, '');
    
    // 移除内容开头的引导语
    filtered = filtered.replace(/^本次分析(?:仅|只)?针对.*?[。.]?(\n|$)/g, '');
    filtered = filtered.replace(/^分析结果如下.*?[：:。.]?(\n|$)/g, '');
    filtered = filtered.replace(/^(?:以下|下面)(?:是|为).*?(?:分析|结果).*?[：:。.]?(\n|$)/g, '');
    filtered = filtered.replace(/^(?:请|根据).*?(?:格式|要求|模板).*?[：:。.]?(\n|$)/g, '');
    
    // 处理标准节格式下的分析指导
    filtered = filtered.replace(/(?:【|「)分析指导(?:】|」)[\s\S]*?(?=(?:【|「)|\n\n)/g, '');
    
    // 移除"纯文本输出"等提示
    filtered = filtered.replace(/（纯文本输出）/g, '');
    filtered = filtered.replace(/\(纯文本输出\)/g, '');
    filtered = filtered.replace(/（仅限.*?维度）/g, '');
    filtered = filtered.replace(/\(仅限.*?维度\)/g, '');
    
    // 处理格式模板中常见的指令性短语
    filtered = filtered.replace(/【维度限制】.*?(\n|$)/g, '');
    filtered = filtered.replace(/【专项限制】.*?(\n|$)/g, '');
    filtered = filtered.replace(/【格式规范】.*?(\n|$)/g, '');
    filtered = filtered.replace(/【标题要求】.*?(\n|$)/g, '');
    filtered = filtered.replace(/【内容要求】.*?(\n|$)/g, '');
    filtered = filtered.replace(/【人称要求】.*?(\n|$)/g, '');
    
    // 确保目录格式正确（如"（一）"前面换行）
    filtered = filtered.replace(/([^\n])\s*(\（[一二三四五六七八九十]+\))/g, '$1\n\n$2');
    filtered = filtered.replace(/([^\n])\s*(\（[一二三四五六七八九十]+\）)/g, '$1\n\n$2');
    
    // 专门处理日主强弱判断等提示词内容
    filtered = filtered.replace(/(日主强弱判断|用神忌神确定|格局高低评估|十神组合分析|大运流年影响).*?(\n|$)/g, '');
    
    // 特殊处理感情维度，修复可能的格式问题
    if (key === 'relationship' || key === '感情' || key === '感情婚姻') {
        if (enableLogging) {
            console.log(`${logPrefix} 处理感情维度特定过滤`);
        }
        
        // 修正可能断开的标题
        filtered = filtered.replace(/([一二三四五六七八九十]+、)感情/g, '$1感情总论');
        filtered = filtered.replace(/([一二三四五六七八九十]+、)恋爱/g, '$1恋爱表现');
        filtered = filtered.replace(/([一二三四五六七八九十]+、)婚姻/g, '$1婚姻状况');
        
        // 修复可能出现的格式问题
        filtered = filtered.replace(/(\n)恋爱表现.*?[:：]/g, '\n（一）恋爱表现——');
        filtered = filtered.replace(/(\n)婚姻状况.*?[:：]/g, '\n（二）婚姻状况——');
        filtered = filtered.replace(/(\n)伴侣匹配.*?[:：]/g, '\n（三）伴侣匹配——');
        
        // 修复分点内容
        filtered = filtered.replace(/(\n\s*)([1-9]\.)([^：]+)[:：]/g, '$1$2 $3：');
        filtered = filtered.replace(/(\n\s*)([a-z]\.)([^：]+)[:：]/g, '$1$2 $3：');
    }
    
    // 特别处理年度运势分析（如2025年运势）
    if (key && key.includes('运势')) {
        if (enableLogging) {
            console.log(`${logPrefix} 进行年度运势特殊过滤`);
        }
        
        // 检查是否包含特定年份（如2025年运势）
        const yearMatch = key.match(/(\d{4})年运势/);
        const targetYear = yearMatch ? yearMatch[1] : null;
        
        // 移除开头的提示词段落（不管内容是什么）
        filtered = filtered.replace(/^[【\[].*?[】\]].*?\n+/g, '');
        
        // 移除格式要求相关的句子
        filtered = filtered.replace(/.*纯文本.*(\n|$)/g, '');
    }
    
    // 移除开头的空白行
    filtered = filtered.replace(/^\s*\n+/, '');
    
    // 再次检查并移除最外层的花括号（如果仍存在）
    filtered = filtered.trim();
    if (filtered.startsWith('{') && filtered.endsWith('}') && 
        !filtered.slice(1, -1).trim().startsWith('{') && 
        !filtered.slice(1, -1).trim().endsWith('}')) {
        filtered = filtered.slice(1, -1).trim();
        if (enableLogging) {
            console.log(`${logPrefix} 最终移除了外层花括号`);
        }
    }
    
    if (enableLogging) {
        console.log(`${logPrefix} 过滤完成，新长度: ${filtered.length}`);
        if (content.length - filtered.length > 50) {
            console.log(`${logPrefix} 移除了 ${content.length - filtered.length} 个字符`);
        }
    }
    
    return filtered;
}

/**
 * 深度解析嵌套的JSON结构，提取可读文本
 * @param {Object|String} data - 要解析的数据
 * @param {String} key - 维度键名，用于特定处理
 * @returns {String} 解析后的文本内容
 */
function extractNestedContent(data, key = '') {
    if (!data) return '';
    
    // 如果是字符串，先检查是否被花括号包裹，如果是则尝试解析JSON
    if (typeof data === 'string') {
        // 移除字符串开头和结尾的花括号
        let processedData = data.trim();
        if (processedData.startsWith('{') && processedData.endsWith('}')) {
            try {
                // 尝试解析JSON
                const parsed = JSON.parse(processedData);
                return extractNestedContent(parsed, key);
            } catch (e) {
                // 不是有效的JSON，可能只是文本包含花括号
                // 如果整个内容就是被花括号包裹的文本，去除花括号
                if (processedData.startsWith('{') && processedData.endsWith('}') && 
                    !processedData.slice(1, -1).includes('{') && !processedData.slice(1, -1).includes('}')) {
                    return processedData.slice(1, -1).trim();
                }
                // 否则保持原样返回
                return processedData;
            }
        }
        return processedData;
    }
    
    // 如果是数组，合并所有项的内容
    if (Array.isArray(data)) {
        return data.map(item => extractNestedContent(item, key)).join('\n\n');
    }
    
    // 对象特殊处理
    if (typeof data === 'object') {
        // 检查特定字段
        if (data.结果输出) return data.结果输出;
        if (data.content) return data.content;
        
        // 感情维度特殊处理
        if (key === 'relationship' || key === '感情') {
            // 提取关键部分组成文本
            let result = '';
            
            // 处理感情总论
            if (data.感情总论) {
                result += `一、感情总论\n${data.感情总论}\n\n`;
            }
            
            // 处理关键要点分析
            if (data.感情关键要点分析) {
                result += '二、感情关键要点分析\n';
                
                // 提取要点1、2、3
                const points = data.感情关键要点分析;
                
                if (points.要点1_恋爱表现) {
                    result += `（一）恋爱表现——${points.要点1_恋爱表现.结论 || ''}\n`;
                    result += `${points.要点1_恋爱表现.分析内容 || ''}\n\n`;
                }
                
                if (points.要点2_婚姻关键期) {
                    result += `（二）婚姻状况——${points.要点2_婚姻关键期.结论 || ''}\n`;
                    result += `${points.要点2_婚姻关键期.分析内容 || ''}\n\n`;
                }
                
                if (points.要点3_婚配特质) {
                    result += `（三）伴侣匹配——${points.要点3_婚配特质.结论 || ''}\n`;
                    result += `${points.要点3_婚配特质.分析内容 || ''}\n\n`;
                }
                
                // 处理具体影响部分
                if (points.具体影响分析) {
                    result += '三、具体情况分析\n';
                    
                    const impacts = points.具体影响分析;
                    if (impacts['1_情感表达模式']) {
                        result += `1. 情感表达：${impacts['1_情感表达模式']}\n\n`;
                    }
                    
                    if (impacts['2_夫妻互动形态']) {
                        result += `2. 夫妻互动：${impacts['2_夫妻互动形态']}\n\n`;
                    }
                    
                    if (impacts['3_子女影响要素']) {
                        result += `3. 子女影响：${impacts['3_子女影响要素']}\n\n`;
                    }
                }
            }
            
            // 处理建议部分
            if (data.感情建议) {
                result += '四、建议与指导\n';
                const advice = data.感情建议;
                
                if (advice.优势发挥) {
                    result += '（一）优势发挥\n';
                    Object.entries(advice.优势发挥).forEach(([key, value]) => {
                        const num = key.match(/\d+/);
                        if (num) {
                            result += `${num[0]}. ${value}\n`;
                        }
                    });
                    result += '\n';
                }
                
                if (advice.短板补足) {
                    result += '（二）短板补足\n';
                    Object.entries(advice.短板补足).forEach(([key, value]) => {
                        const num = key.match(/\d+/);
                        if (num && key !== '感情总结') {
                            result += `${num[0]}. ${value}\n`;
                        }
                    });
                    result += '\n';
                }
                
                // 处理总结
                if (advice.短板补足 && advice.短板补足.感情总结) {
                    result += `五、感情总结\n${advice.短板补足.感情总结}`;
                }
            }
            
            return result;
        }
        
        // 提取所有字符串值并组合
        let extractedContent = '';
        for (const prop in data) {
            if (prop === 'format_template' || prop === '格式说明') continue;
            
            const value = data[prop];
            if (typeof value === 'string' && value.length > 20) {
                extractedContent += value + '\n\n';
            } else if (typeof value === 'object') {
                const nestedContent = extractNestedContent(value, key);
                if (nestedContent) {
                    extractedContent += nestedContent + '\n\n';
                }
            }
        }
        
        return extractedContent.trim();
    }
    
    // 其他类型，转为字符串
    return String(data);
}

/**
 * 显示分析结果
 * @param {Object} data - 分析结果数据
 */
function displayResult(data) {
    // 保存当前数据到实例变量
    this.currentData = data;
    
    // 显示分析内容状态
    if (window.showAnalysisContent && typeof window.showAnalysisContent === 'function') {
        window.showAnalysisContent();
    }
    
    const contentContainer = document.getElementById('analysisContent');
    
    // 生成八字信息部分
    let baziInfoHtml = generateBaziInfoSection.call(this);
    
    let analysisHtml = '';
    let analysisData = {};
    
    // 收集所有分析数据
    if (data.llm_analysis && data.llm_analysis.results) {
        const llmResults = data.llm_analysis.results;
        Object.keys(llmResults).forEach(key => {
            const content = llmResults[key];
            if (content) {
                // 使用深度解析函数处理内容，然后再应用过滤函数
                const processedContent = extractNestedContent(content, key);
                analysisData[key] = { content: filterPromptWords(processedContent, key) };
            }
        });
    } else if (data.result && data.result.llm_analysis && data.result.llm_analysis.results) {
        const llmResults = data.result.llm_analysis.results;
        Object.keys(llmResults).forEach(key => {
            const content = llmResults[key];
            if (content) {
                // 使用深度解析函数处理内容，然后再应用过滤函数
                const processedContent = extractNestedContent(content, key);
                analysisData[key] = { content: filterPromptWords(processedContent, key) };
            }
        });
    } else if (data.result && data.result.analysis) {
        const analysis = data.result.analysis;
        // 遍历分析的各个维度
        Object.keys(analysis).forEach(key => {
            const dimension = analysis[key];
            if (dimension && typeof dimension === 'object') {
                // 如果维度对象包含content字段，应用过滤
                if (dimension.content && typeof dimension.content === 'string') {
                    dimension.content = filterPromptWords(dimension.content, key);
                }
                // 如果维度对象包含analysis字段，应用过滤
                if (dimension.analysis && typeof dimension.analysis === 'string') {
                    dimension.analysis = filterPromptWords(dimension.analysis, key);
                }
                // 如果维度对象包含description字段，应用过滤
                if (dimension.description && typeof dimension.description === 'string') {
                    dimension.description = filterPromptWords(dimension.description, key);
                }
                analysisData[key] = dimension;
            }
        });
    } else if (data.analysis) {
        // 处理直接的analysis结构
        Object.keys(data.analysis).forEach(key => {
            const dimension = data.analysis[key];
            if (dimension && typeof dimension === 'object') {
                // 如果维度对象包含content字段，应用过滤
                if (dimension.content && typeof dimension.content === 'string') {
                    dimension.content = filterPromptWords(dimension.content, key);
                }
                // 如果维度对象包含analysis字段，应用过滤
                if (dimension.analysis && typeof dimension.analysis === 'string') {
                    dimension.analysis = filterPromptWords(dimension.analysis, key);
                }
                // 如果维度对象包含description字段，应用过滤
                if (dimension.description && typeof dimension.description === 'string') {
                    dimension.description = filterPromptWords(dimension.description, key);
                }
                analysisData[key] = dimension;
            }
        });
    } else {
        // 处理直接包含分析维度的JSON结构（如LLM分析结果）
        Object.keys(data).forEach(key => {
            const dimension = data[key];
            if (dimension && typeof dimension === 'string' && dimension.length > 50) {
                // 将字符串内容包装成对象格式，并应用过滤
                analysisData[key] = { content: filterPromptWords(dimension, key) };
            }
        });
    }
    
    // 按指定顺序排序分析维度
    const sortedKeys = sortAnalysisDimensions(Object.keys(analysisData));
    
    // 按排序后的顺序生成HTML
    sortedKeys.forEach(key => {
        analysisHtml += generateDimensionSection(key, analysisData[key]);
    });
    
    // 如果没有找到任何分析内容，显示提示信息
    if (!analysisHtml) {
        analysisHtml = `
            <div class="detailed-dimension-section">
                <div class="detailed-dimension-header active">
                    <div class="detailed-dimension-title">
                        <span class="detailed-dimension-icon">⚠️</span>
                        暂无分析内容
                    </div>
                </div>
                <div class="detailed-dimension-content active">
                    <div class="detailed-dimension-text">分析结果正在生成中，请稍后刷新页面查看。</div>
                </div>
            </div>
        `;
    }
    
    contentContainer.innerHTML = baziInfoHtml + analysisHtml;

    // 添加展开/收起功能
    initializeToggleFeatures.call(this);

    // 应用样式增强
    setTimeout(() => {
        if (window.applyStyleEnhancements && typeof window.applyStyleEnhancements === 'function') {
            window.applyStyleEnhancements();
        }
    }, 100);
}

/**
 * 生成分析维度部分
 * @param {string} key - 维度键名
 * @param {Object} dimension - 维度数据
 * @returns {string} 维度HTML
 */
function generateDimensionSection(key, dimension) {
    const dimensionNames = {
        'personality': '性格特质',
        'career': '事业发展',
        'wealth': '财运分析',
        'relationship': '感情婚姻',
        'health': '健康状况',
        'education': '学业文化',
        'family': '家庭关系',
        'social': '社交人际',
        'spiritual': '精神修养',
        'fortune': '运势走向',
        'strength': '旺衰分析',
        'pattern': '格局分析',
        // 添加中文维度名称映射
        '性格': '性格特质',
        '事业': '事业发展',
        '财运': '财运分析',
        '感情': '感情婚姻',
        '健康': '健康状况',
        '学业': '学业文化',
        '家庭': '家庭关系',
        '人际': '社交人际',
        '修养': '精神修养',
        '运势': '运势走向',
        '旺衰': '旺衰分析',
        '格局': '格局分析',
        // 流年运势
        '2022运势': '2022运势',
        '2023运势': '2023运势',
        '2024运势': '2024运势',
        '2025运势': '2025运势'
    };
    
    const dimensionIcons = {
        'personality': '👤',
        'career': '💼',
        'wealth': '💰',
        'relationship': '💕',
        'health': '🏥',
        'education': '📚',
        'family': '👨‍👩‍👧‍👦',
        'social': '🤝',
        'spiritual': '🧘',
        'fortune': '☯',
        'strength': '⚖️',
        'pattern': '🎯',
        // 添加中文维度图标映射
        '性格': '👤',
        '事业': '💼',
        '财运': '💰',
        '感情': '💕',
        '健康': '🏥',
        '学业': '📚',
        '家庭': '👨‍👩‍👧‍👦',
        '人际': '🤝',
        '修养': '🧘',
        '运势': '☯',
        '旺衰': '⚖️',
        '格局': '🎯',
        // 流年运势图标
        '2022运势': '📅',
        '2023运势': '📅',
        '2024运势': '📅',
        '2025运势': '📅'
    };
    
    const title = dimensionNames[key] || key;
    const icon = dimensionIcons[key] || '📋';
    
    // 处理内容，检查是否是JSON字符串格式
    let content = '';
    
    if (typeof dimension === 'string') {
        // 如果维度直接是字符串，尝试使用深度解析函数处理
        content = extractNestedContent(dimension, key);
    } else if (dimension.analysis || dimension.content || dimension.description) {
        content = dimension.analysis || dimension.content || dimension.description || JSON.stringify(dimension, null, 2);
    } else {
        // 对于对象类型，也使用深度解析函数
        content = extractNestedContent(dimension, key);
    }
    
    // 应用过滤函数移除提示词
    content = filterPromptWords(content, key);
    
    // 使用更安全的方法转换文本为HTML
    let contentHtml = '';
    if (content) {
        // 预处理内容，修复可能存在的文本分割问题
        content = content.replace(/(性格|健康|感情|学业|职业|运势)(\s+)(总论|特点|建议|分析)/g, '$1$3');
        
        // 修复中文标题格式（确保"一、二、三、"等格式后有空格和内容）
        content = content.replace(/^([一二三四五六七八九十]+、)(?!\s)/gm, '$1 ');
        
        // 修复中文括号标题格式（确保"（一）（二）（三）"等格式后有空格和内容）
        content = content.replace(/^(（[一二三四五六七八九十]+）)(?!\s)/gm, '$1 ');
        
        // 特殊处理段落开头文本
        // 确保"一、性格总论"或"一、健康总论"等标题完整
        content = content.replace(/^((?:[一二三四五六七八九十]+、|（[一二三四五六七八九十]+）)\s*)(性格|健康|感情|学业|职业|运势)\s*(总论|分析|特点|建议)(?:\s*)(——|：|\n)/gm, 
            (match, prefix, topic, category, suffix) => {
                return `${prefix}${topic}${category}${suffix}`;
            }
        );
        
        // 合并可能被错误分割的内容
        content = content.replace(/([^\n])(\n)(?!\n)([^（\n一二三四五六七八九十])/g, '$1 $3');
        
        // 将内容按段落拆分，但保留标题与内容的连续性
        const paragraphs = [];
        const lines = content.split('\n');
        let currentParagraph = '';
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (line === '') {
                // 空行表示段落结束
                if (currentParagraph) {
                    paragraphs.push(currentParagraph);
                    currentParagraph = '';
                }
            } else if (/^[一二三四五六七八九十]+、/.test(line) || 
                      /^（[一二三四五六七八九十]+）/.test(line) ||
                      /^\d+\./.test(line)) {
                // 如果是标题格式，先保存当前段落，再开始新段落
                if (currentParagraph) {
                    paragraphs.push(currentParagraph);
                }
                currentParagraph = line;
            } else {
                // 常规内容行，添加到当前段落
                if (currentParagraph) {
                    currentParagraph += ' ' + line;
                } else {
                    currentParagraph = line;
                }
            }
        }
        
        // 添加最后一个段落
        if (currentParagraph) {
            paragraphs.push(currentParagraph);
        }
        
        // 为每个段落创建<p>标签
        contentHtml = paragraphs.map(paragraph => {
            if (!paragraph.trim()) return '';
            return `<p>${paragraph.trim()}</p>`;
        }).join('\n');
    } else {
        contentHtml = '<p>暂无分析内容</p>';
    }
    
    return `
        <div class="detailed-dimension-section">
            <div class="detailed-dimension-header active">
                <div class="detailed-dimension-title">
                    <span class="detailed-dimension-icon">${icon}</span>
                    ${title}
                </div>
                <div class="detailed-dimension-toggle">
                    <span class="toggle-icon">▼</span>
                </div>
            </div>
            <div class="detailed-dimension-content active">
                <div class="detailed-dimension-text">${contentHtml}</div>
            </div>
        </div>
    `;
}

/**
 * 排序分析维度
 * @param {Array} keys - 维度键名数组
 * @returns {Array} 排序后的键名数组
 */
function sortAnalysisDimensions(keys) {
    // 定义维度优先级顺序：性格、旺衰、格局在前，流年在最后
    const priorityOrder = {
        '性格': 1,
        'personality': 1,
        '旺衰': 2,
        'strength': 2,
        '格局': 3,
        'pattern': 3,
        '学历': 4,
        'education': 4,
        '职业': 5,
        'career': 5,
        '感情': 6,
        'relationship': 6,
        '疾病': 7,
        'health': 7,
        '财运': 8,
        'wealth': 8,
        '运势': 9,
        'fortune': 9,
        // 流年相关的放在最后
         '2025运势': 103
    };
    
    return keys.sort((a, b) => {
        const priorityA = priorityOrder[a] || 50; // 未定义的维度放在中间
        const priorityB = priorityOrder[b] || 50;
        return priorityA - priorityB;
    });
}

/**
 * 更新生成时间
 */
function updateGenerateTime() {
    const timeElement = document.getElementById('generateTime');
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    timeElement.textContent = `生成时间: ${timeString}`;
}

/**
 * 显示错误信息
 * @param {string} message - 错误消息
 * @param {string} details - 错误详情
 */
function showError(message, details) {
    if (window.showErrorState && typeof window.showErrorState === 'function') {
        window.showErrorState(message, details);
    } else {
        // 兼容旧版本的错误显示
        const contentContainer = document.getElementById('analysisContent');
        if (contentContainer) {
            contentContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #e53e3e;">
                    <h3>❌ ${message}</h3>
                    ${details ? `<p style="margin-top: 16px; color: #718096;">${details}</p>` : ''}
                    <p style="margin-top: 16px; color: #718096;">请返回重新进行分析</p>
                    <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                        <button onclick="window.location.href='index.html'" style="padding: 10px 20px; background: #48bb78; color: white; border: none; border-radius: 6px; cursor: pointer;">重新分析</button>
                        <button onclick="window.history.back()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">返回</button>
                    </div>
                </div>
            `;
        }
    }
}

/**
 * 显示分析进度
 * @param {Object} progressData - 进度数据
 */
function showAnalysisProgress(progressData) {
    const contentContainer = document.getElementById('analysisContent');
    if (contentContainer) {
        const progress = progressData.progress || 0;
        const elapsedTime = progressData.elapsed_time_formatted || '计算中...';
        const llmProgress = progressData.llm_progress || {};
        
        let progressDetails = '';
        if (llmProgress.current_step) {
            progressDetails = `<p style="margin-top: 10px; color: #4a5568;">当前步骤: ${llmProgress.current_step}</p>`;
        }
        
        contentContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #2d3748;">
                <h3>🔄 正在分析中...</h3>
                <div style="margin: 20px auto; width: 300px; background: #e2e8f0; border-radius: 10px; overflow: hidden;">
                    <div style="width: ${progress}%; height: 20px; background: linear-gradient(90deg, #48bb78, #38a169); transition: width 0.3s ease;"></div>
                </div>
                <p style="margin-top: 10px; font-size: 18px; font-weight: bold; color: #2b6cb0;">${progress}%</p>
                <p style="margin-top: 10px; color: #4a5568;">已用时间: ${elapsedTime}</p>
                ${progressDetails}
                <p style="margin-top: 20px; color: #718096; font-size: 14px;">请耐心等待，分析完成后会自动显示结果</p>
                <div style="margin-top: 20px;">
                    <button onclick="window.location.reload()" style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer; margin-right: 10px;">刷新页面</button>
                    <button onclick="window.location.href='result.html'" style="padding: 8px 16px; background: #718096; color: white; border: none; border-radius: 6px; cursor: pointer;">返回列表</button>
                </div>
            </div>
        `;
    }
}

/**
 * 显示空状态
 */
function showEmptyState() {
    if (window.showEmptyState && typeof window.showEmptyState === 'function') {
        window.showEmptyState();
    } else {
        // 兼容旧版本的空状态显示
        const contentContainer = document.getElementById('analysisContent');
        if (contentContainer) {
            contentContainer.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #718096;">
                    <h3>📋 暂无分析结果</h3>
                    <p style="margin-top: 16px;">请先进行八字分析</p>
                    <div style="margin-top: 20px;">
                        <button onclick="window.location.href='index.html'" style="padding: 12px 24px; background: #48bb78; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">开始分析</button>
                    </div>
                </div>
            `;
        }
    }
}

/**
 * 递归处理JSON对象或数组中的每个字段，应用filterPromptWords
 * @param {object|array|string} content - 要处理的内容
 * @returns {object|array|string} - 处理后的内容
 */
function processContent(content) {
    // 如果是字符串，直接过滤
    if (typeof content === 'string') {
        return filterPromptWords(content, '');
    }
    
    // 如果是数组，处理每个元素
    if (Array.isArray(content)) {
        return content.map(item => processContent(item));
    }
    
    // 如果是对象，处理每个属性
    if (typeof content === 'object' && content !== null) {
        const result = {};
        for (const key in content) {
            result[key] = processContent(content[key]);
        }
        return result;
    }
    
    // 其他类型直接返回
    return content;
}

/**
 * 处理分析结果文本
 * @param {string} text 原始分析结果文本
 * @param {string} dimension 维度名称
 * @returns {string} 处理后的HTML
 */
function processAnalysisText(text, dimension) {
    if (!text) return '<p>暂无分析结果</p>';
    
    // 处理可能的JSON格式
    let content = text;
    
    // 尝试提取嵌套的JSON内容
    const extractedContent = extractNestedContent(text, dimension);
    
    // 如果提取出了对象，则根据维度进行处理
    if (typeof extractedContent === 'object') {
        if (dimension === '感情') {
            // 感情维度处理
            content = extractedContent.总论 || 
                     (extractedContent.感情总论 ? extractedContent.感情总论 : 
                     (extractedContent.感情 ? extractedContent.感情 : text));
        } else {
            // 其他维度，选择一个非空字段或原始文本
            const keys = Object.keys(extractedContent);
            content = '';
            for (const key of keys) {
                if (extractedContent[key] && typeof extractedContent[key] === 'string') {
                    content += extractedContent[key] + '\n\n';
                }
            }
            
            if (!content.trim()) {
                content = text;
            }
        }
    }
    
    // 过滤提示词
    content = filterPromptWords(content, dimension);
    
    // 将文本中的换行转换为HTML
    const htmlContent = content
        .replace(/\n\n/g, '</p><p>')  // 两个换行转为新段落
        .replace(/\n/g, '<br>');      // 单个换行转为<br>
    
    return `<p>${htmlContent}</p>`;
}