#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
八字分析器模块
处理八字数据并调用LLM进行分析
"""

import json
import os
import logging
import threading
import time
from datetime import datetime
from bazi_summary import get_bazi_info, set_bazi_data
import traceback
# 导入LLMapi模块中的API_URL和API_HEADERS
from LLMapi import call_api, API_URL, API_HEADERS
from timeout_config import get_timeout

logger = logging.getLogger(__name__)

class BaziAnalyzer:
    """八字分析器"""
    
    def __init__(self, personality_only=False, bazi_data=None, summary=None):
        """初始化八字分析器
        
        Args:
            personality_only: 是否只分析性格维度
            bazi_data: 八字数据
            summary: 摘要文本，优先使用
        """
        self.personality_only = personality_only
        self.bazi_data = bazi_data
        self.summary = summary
        self.output_path = None
        self.analysis_results = {}
        self.lock = threading.Lock()
    
    def is_valid_data(self):
        """验证八字数据是否有效"""
        return self.bazi_data is not None or self.summary is not None
    
    def get_available_dimensions(self):
        """获取可用的分析维度"""
        if self.personality_only:
            return ["性格"]
        else:
            # 返回所有可用维度
            return ["日主强弱", "健康", "2025运势", "性格", "格局", "学业", "职业", "感情"]
    
    def _get_dimension_filename(self, dimension):
        """获取维度对应的提示词文件名
        
        Args:
            dimension: 维度名称
            
        Returns:
            str: 对应的文件名
        """
        try:
            # 从index.json加载维度到文件的映射
            index_file = os.path.join(os.path.dirname(__file__), "prompts", "index.json")
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    if "prompts_index" in index_data and dimension in index_data["prompts_index"]:
                        return index_data["prompts_index"][dimension]
            
            # 如果无法从索引文件中找到，使用默认映射
            dimension_filename_map = {
                "日主强弱": "strength.txt",
                "健康": "health.txt",
                "2025运势": "fortune_2025.txt",
                "性格": "personality.txt",
                "格局": "pattern.json",
                "学业": "education.txt",
                "职业": "career.txt",
                "感情": "relationship.txt"
            }
            return dimension_filename_map.get(dimension, "common.txt")
        except Exception as e:
            logger.error(f"获取维度 {dimension} 的提示词文件名时出错: {str(e)}")
            return "common.txt"  # 出错时使用通用模板
    
    def analyze_dimension(self, dimension):
        """分析特定维度
        
        Args:
            dimension: 分析维度
            
        Returns:
            str: 分析结果
        """
        try:
            print(f"🧠 开始分析维度: {dimension}", flush=True)
            
            # 优先使用摘要文本，如果没有则使用原始八字数据
            bazi_info_text = ""
            if self.summary:
                # 直接使用summary文本
                print(f"📄 使用预生成的摘要文本进行分析", flush=True)
                bazi_info_text = self.summary
            else:
                # 获取线程局部的八字数据副本
                local_bazi_data = None
                with self.lock:
                    if self.bazi_data:
                        # 创建深拷贝，避免多线程共享问题
                        import copy
                        local_bazi_data = copy.deepcopy(self.bazi_data)
                
                # 确保先设置八字数据，再获取结构化的八字信息
                if local_bazi_data:
                    set_bazi_data(local_bazi_data)
                else:
                    print(f"⚠️ 维度 {dimension} 缺少八字数据，可能导致分析错误", flush=True)
                    logger.warning(f"维度 {dimension} 缺少八字数据，可能导致分析错误")
                
                # 获取结构化的八字信息
                bazi_info_text = get_bazi_info()
            
            # 获取维度对应的提示词文件名
            dimension_filename = self._get_dimension_filename(dimension)
            
            # 从prompts目录加载分析模板
            prompt_file = os.path.join(os.path.dirname(__file__), "prompts", dimension_filename)
            if not os.path.exists(prompt_file):
                # 如果没有特定维度的模板，使用通用模板
                prompt_file = os.path.join(os.path.dirname(__file__), "prompts", "common.json")
                print(f"⚠️ 未找到维度 {dimension} 的提示词模板，使用通用模板", flush=True)
            else:
                print(f"📝 维度 {dimension} 使用提示词模板: {dimension_filename}", flush=True)
            
            # 加载维度约束和格式模板（使用纯文本文件）
            format_template_file = os.path.join(os.path.dirname(__file__), "prompts", "format_template.txt")
            format_constraints_file = os.path.join(os.path.dirname(__file__), "prompts", "format_constraints.txt")
            dimension_constraints_file = os.path.join(os.path.dirname(__file__), "prompts", "dimension_constraints.txt")
            
            # 线程安全地加载文件
            try:
                # 从文件加载提示词模板
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    if prompt_file.endswith('.json'):
                        prompt_template = json.load(f)
                    else:
                        # 如果不是JSON文件，直接读取文本内容作为纯文本提示词
                        prompt_template = f.read()
            except Exception as e:
                logger.error(f"加载提示词模板 {dimension_filename} 时出错: {str(e)}")
                prompt_template = f"请分析{dimension}。"
                print(f"⚠️ 加载提示词模板出错，使用默认模板: {str(e)}", flush=True)
                
            format_template = ""
            format_constraints = ""
            dimension_constraints = {}
            
            # 尝试加载格式模板（纯文本）
            if os.path.exists(format_template_file):
                try:
                    with open(format_template_file, 'r', encoding='utf-8') as f:
                        format_template = f.read()
                    print(f"📋 加载格式模板: format_template.txt", flush=True)
                except Exception as e:
                    print(f"⚠️ 加载格式模板时出错: {str(e)}", flush=True)
            
            # 尝试加载格式约束（纯文本）
            if os.path.exists(format_constraints_file):
                try:
                    with open(format_constraints_file, 'r', encoding='utf-8') as f:
                        format_constraints = f.read()
                    print(f"🔍 加载格式约束: format_constraints.txt", flush=True)
                except Exception as e:
                    print(f"⚠️ 加载格式约束时出错: {str(e)}", flush=True)
            
            # 尝试加载维度约束
            if os.path.exists(dimension_constraints_file):
                try:
                    with open(dimension_constraints_file, 'r', encoding='utf-8') as f:
                        if dimension_constraints_file.endswith('.json'):
                            dimension_constraints = json.load(f)
                        else:
                            # 如果是纯文本文件，读取内容存储为constraints字段
                            dimension_constraints = {
                                "dimension_constraints": {
                                    "concise_template": f.read(),
                                    "prohibitions": {
                                        dimension: f"不涉及其他维度内容，专注于{dimension}分析"
                                    }
                                }
                            }
                    print(f"🔒 加载维度约束: {os.path.basename(dimension_constraints_file)}", flush=True)
                except Exception as e:
                    print(f"⚠️ 加载维度约束时出错: {str(e)}", flush=True)
            
            # 构建简洁的提示词，避免重复内容
            prompt = f"请根据以下八字信息：\n\n{bazi_info_text}\n\n"
            
            # 添加维度约束
            if dimension_constraints and "dimension_constraints" in dimension_constraints:
                constraints = dimension_constraints["dimension_constraints"]
                
                # 获取该维度的禁止内容
                if "prohibitions" in constraints and dimension in constraints["prohibitions"]:
                    prohibition = constraints["prohibitions"][dimension]
                else:
                    prohibition = constraints["prohibitions"].get("性格", "不涉及其他维度内容")
                
                # 使用简洁模板
                if "concise_template" in constraints:
                    template = constraints["concise_template"]
                    constraint_text = ""
                    
                    # 如果模板中有占位符，替换它们
                    if "{dimension}" in template and "{prohibition}" in template:
                        # 同时替换两个占位符
                        constraint_text = template.replace("{dimension}", dimension).replace("{prohibition}", prohibition)
                    elif "{dimension}" in template:
                        # 只替换维度占位符
                        constraint_text = template.replace("{dimension}", dimension)
                    else:
                        # 如果模板是纯文本没有占位符，直接使用
                        constraint_text = template
                    
                    prompt += f"{constraint_text}\n\n"
            
            # 添加分析维度说明
            prompt += f"请对【{dimension}】维度进行分析：\n\n"
            
            # 添加格式要求（纯文本）
            if format_template:
                # 替换模板中的{dimension}占位符
                template_text = format_template.replace("{dimension}", dimension)
                prompt += "【输出格式】：\n" + template_text + "\n\n"
            
            # 添加格式约束（纯文本）
            if format_constraints:
                prompt += "【格式约束】：\n" + format_constraints + "\n\n"
            
            # 添加分析要求，根据实际键名选择
            prompt += "【分析要求】：\n"
            
            # 添加调试日志
            print(f"📄 维度 {dimension} 提示词模板类型: {type(prompt_template)}", flush=True)
            if isinstance(prompt_template, str):
                print(f"📄 纯文本提示词: {prompt_template[:30]}...", flush=True)
            elif isinstance(prompt_template, dict):
                print(f"📄 JSON提示词键: {list(prompt_template.keys())}", flush=True)
            
            # 处理提示词模板
            if isinstance(prompt_template, dict):
                # 处理JSON格式的提示词
                matched_key = None
                for key in prompt_template:
                    if key == dimension or key == "日主强弱" or key == "旺衰":
                        matched_key = key
                        break
                
                if not matched_key:
                    # 如果没有找到匹配的键，使用第一个键
                    matched_key = list(prompt_template.keys())[0]
                
                print(f"📄 使用JSON提示词键: {matched_key}", flush=True)
                
                # 检查值是否是字符串，如果是则直接使用
                if isinstance(prompt_template[matched_key], str):
                    prompt += prompt_template[matched_key]
                else:
                    # 如果值是对象或数组，转换为JSON字符串（旧的处理方式）
                    prompt += json.dumps(prompt_template[matched_key], ensure_ascii=False)
            else:
                # 如果是纯文本格式，直接使用
                prompt += prompt_template
            
            # 调用LLM API进行分析
            
            # 构建API请求数据
            api_data = {
                "model": "deepseek/deepseek-r1-0528:free",
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            # 记录提示词到日志
            thread_id = threading.get_ident()
            self._log_prompt(f"{dimension}_{thread_id}", prompt)
            
            # 使用更健壮的call_api函数
            api_result = call_api(API_URL, API_HEADERS, json.dumps(api_data))
            
            # 处理结果
            if "error" in api_result:
                error_msg = api_result["error"]
                print(f"❌ 维度 {dimension} API调用返回错误: {error_msg}", flush=True)
                return f"分析失败: {error_msg}"
            
            if "choices" in api_result and len(api_result["choices"]) > 0:
                analysis_content = api_result["choices"][0]["message"]["content"]
                
                # 清理内容中的代码块标记
                analysis_content = self._clean_result_format(analysis_content)
                
                # 保存结果
                with self.lock:
                    self.analysis_results[dimension] = analysis_content
                
                print(f"✅ 维度 {dimension} 分析完成", flush=True)
                return analysis_content
            else:
                error_msg = "API返回格式无效或为空"
                print(f"❌ 维度 {dimension} {error_msg}", flush=True)
                return f"分析失败: {error_msg}"
            
        except Exception as e:
            # 处理非API调用相关的异常（如提示词构建、结果处理等）
            error_msg = f"分析维度 {dimension} 时出错: {str(e)}"
            print(f"❌ {error_msg}", flush=True)
            logger.error(error_msg)
            logger.error(traceback.format_exc())  # 记录完整堆栈跟踪
            return f"分析出错: {str(e)}"
    
    def batch_analyze_with_queue(self, dimensions, concurrency=10, progress_callback=None):
        """批量分析多个维度
        
        Args:
            dimensions: 维度列表
            concurrency: 并发数
            progress_callback: 进度回调函数
            
        Returns:
            dict: 分析结果
        """
        results = {}
        total = len(dimensions)
        completed = 0
        
        # 创建并发控制的信号量
        semaphore = threading.Semaphore(concurrency)
        # 为结果创建锁
        results_lock = self.lock
        # 为completed计数器创建锁
        completed_lock = threading.Lock()
        # 为已创建的线程列表创建锁
        threads_lock = threading.Lock()
        
        # 创建线程池
        threads = []
        active_threads = []
        
        # 定义线程函数
        def worker(dim):
            # 在函数开始就声明nonlocal变量
            nonlocal completed
            
            try:
                # 获取信号量，控制并发数
                semaphore.acquire()
                try:
                    # 将当前线程添加到活动线程列表
                    with threads_lock:
                        active_threads.append(threading.current_thread())
                    
                    # 执行分析
                    result = self.analyze_dimension(dim)
                    
                    # 安全地更新结果和计数
                    with results_lock:
                        results[dim] = result
                    
                    with completed_lock:
                        completed += 1
                        if progress_callback:
                            progress_callback(completed, total, dim)
                            
                finally:
                    # 释放信号量，不管是否成功
                    semaphore.release()
                    # 从活动线程列表中移除当前线程
                    with threads_lock:
                        if threading.current_thread() in active_threads:
                            active_threads.remove(threading.current_thread())
            except Exception as e:
                # 在异常情况下仍然安全地更新结果
                with results_lock:
                    results[dim] = f"分析出错: {str(e)}"
                
                with completed_lock:
                    completed += 1
                    if progress_callback:
                        progress_callback(completed, total, dim)
                
                # 记录错误
                print(f"❌ 维度 {dim} 分析线程出错: {str(e)}", flush=True)
                logger.error(f"维度 {dim} 分析线程出错: {str(e)}")
        
        # 创建并启动所有线程
        for dim in dimensions:
            thread = threading.Thread(target=worker, args=(dim,), name=f"AnalyzeThread-{dim}")
            thread.daemon = True  # 设置为守护线程，主线程结束时会被强制终止
            with threads_lock:
                threads.append(thread)
            thread.start()
        
        # 使用统一的超时配置
        timeout_per_dimension = get_timeout('analysis_per_dimension')
        max_wait_time = get_timeout('analysis_total')  # 最大等待时间
        
        try:
            # 等待所有线程完成或超时
            start_time = time.time()
            while threads:
                # 复制线程列表以避免在迭代过程中修改它
                with threads_lock:
                    current_threads = threads.copy()
                
                # 移除已完成的线程
                for thread in current_threads:
                    if not thread.is_alive():
                        with threads_lock:
                            if thread in threads:
                                threads.remove(thread)
                
                # 检查是否已超时
                elapsed_time = time.time() - start_time
                if elapsed_time > max_wait_time:
                    print(f"⚠️ 分析超时，已等待 {elapsed_time:.1f} 秒", flush=True)
                    break
                
                # 短暂睡眠以减少CPU使用率
                time.sleep(0.5)
            
            # 如果仍有活跃线程但已超时，记录警告
            with threads_lock:
                if active_threads:
                    active_dims = [t.name.replace("AnalyzeThread-", "") for t in active_threads]
                    print(f"⚠️ 以下维度分析未完成: {', '.join(active_dims)}", flush=True)
                    logger.warning(f"以下维度分析未完成: {', '.join(active_dims)}")
        
        except KeyboardInterrupt:
            print("⚠️ 用户中断分析过程", flush=True)
            logger.warning("用户中断分析过程")
        
        finally:
            # 确保所有已完成的结果都被保存
            if self.output_path:
                try:
                    with open(self.output_path, 'w', encoding='utf-8') as f:
                        json.dump(results, f, ensure_ascii=False, indent=2)
                        print(f"✅ 分析结果已保存到: {self.output_path}", flush=True)
                except Exception as e:
                    print(f"❌ 保存结果到文件时出错: {str(e)}", flush=True)
                    logger.error(f"保存结果到文件时出错: {str(e)}")
            else:
                print("⚠️ 未设置输出路径，分析结果未保存到文件", flush=True)
        
        return results
    
    def _log_prompt(self, dimension, prompt):
        """记录提示词到日志文件"""
        try:
            log_dir = os.path.join(os.path.dirname(__file__), "llm_logs")
            os.makedirs(log_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
            log_file = os.path.join(log_dir, f"llm_prompt_{timestamp}.json")
            
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "model": "deepseek/deepseek-r1-0528:free",
                "dimension": dimension,
                "prompt_length": len(prompt),
                "prompt_content": prompt
            }
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"记录提示词到日志时出错: {str(e)}")
    
    def _clean_result_format(self, content):
        """清理LLM返回结果中的格式问题
        
        Args:
            content: LLM返回的原始内容
            
        Returns:
            str: 清理后的内容
        """
        if content is None:
            return ""
            
        # 首先尝试解析整个内容是否直接是JSON格式
        try:
            parsed_content = json.loads(content)
            
            # 处理JSON中的format_template结构
            if isinstance(parsed_content, dict):
                # 如果包含format_template字段
                if "format_template" in parsed_content:
                    if isinstance(parsed_content["format_template"], dict) and "template" in parsed_content["format_template"]:
                        return parsed_content["format_template"]["template"]
                
                # 如果只有一个键，直接返回其值
                if len(parsed_content) == 1:
                    key = list(parsed_content.keys())[0]
                    return parsed_content[key]
        except json.JSONDecodeError:
            pass  # 不是JSON格式，继续后面的处理
            
        # 去除可能的代码块标记
        # 处理 ```json ... ``` 格式
        if content.startswith('```json') and '```' in content[6:]:
            start_idx = content.find('{', content.find('```json'))
            end_idx = content.rfind('}', 0, content.rfind('```'))
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                try:
                    # 提取JSON部分
                    json_content = content[start_idx:end_idx+1]
                    parsed_json = json.loads(json_content)
                    # 如果JSON中有format_template字段，提取template字段的值作为内容
                    if "format_template" in parsed_json:
                        if isinstance(parsed_json["format_template"], dict) and "template" in parsed_json["format_template"]:
                            return parsed_json["format_template"]["template"]
                    # 如果JSON中只有一个键且值是该维度的内容，直接返回值部分
                    if len(parsed_json) == 1:
                        key = list(parsed_json.keys())[0]
                        return parsed_json[key]
                    return content
                except json.JSONDecodeError:
                    pass  # 解析失败，使用原内容继续处理
                    
        # 处理 ``` ... ``` 格式（无语言指定）
        elif content.startswith('```') and content.endswith('```'):
            content = content[3:-3].strip()
            
            # 尝试解析去除代码块后的内容是否为JSON
            try:
                parsed_content = json.loads(content)
                if isinstance(parsed_content, dict):
                    # 如果包含format_template字段
                    if "format_template" in parsed_content:
                        if isinstance(parsed_content["format_template"], dict) and "template" in parsed_content["format_template"]:
                            return parsed_content["format_template"]["template"]
                    
                    # 如果只有一个键，直接返回其值
                    if len(parsed_content) == 1:
                        key = list(parsed_content.keys())[0]
                        return parsed_content[key]
            except json.JSONDecodeError:
                pass  # 不是JSON格式，继续处理
            
        # 去除多余的注释行
        lines = content.split('\n')
        cleaned_lines = [line for line in lines if not line.strip().startswith('//')]
        content = '\n'.join(cleaned_lines)
        
        # 导入正则表达式模块
        import re
        
        # 1. 清除所有Markdown标题标记
        content = re.sub(r'^#+\s+', '', content, flags=re.MULTILINE)
        
        # 2. 清除粗体、斜体标记
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 清除粗体 **text**
        content = re.sub(r'\*(.*?)\*', r'\1', content)      # 清除斜体 *text*
        content = re.sub(r'__(.*?)__', r'\1', content)      # 清除粗体 __text__
        content = re.sub(r'_(.*?)_', r'\1', content)        # 清除斜体 _text_
        
        # 3. 清除列表标记
        content = re.sub(r'^\s*[-*+•]\s+', '', content, flags=re.MULTILINE)  # 无序列表
        content = re.sub(r'^\s*\d+\.\s+', '', content, flags=re.MULTILINE)   # 有序列表
        
        # 4. 清除引用标记
        content = re.sub(r'^\s*>\s+', '', content, flags=re.MULTILINE)
        
        # 5. 清除代码段标记 (已经在上面处理了一些情况，这里补充处理内联代码)
        content = re.sub(r'`([^`]+)`', r'\1', content)
        
        # 6. 清除水平线
        content = re.sub(r'^\s*[-*_]{3,}\s*$', '', content, flags=re.MULTILINE)
        
        # 7. 清除链接标记
        content = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', content)
        
        # 8. 清除图片标记
        content = re.sub(r'!\[(.*?)\]\(.*?\)', r'\1', content)
        
        # 9. 清除HTML标签
        content = re.sub(r'<[^>]+>', '', content)
        
        # 10. 清除表格标记
        content = re.sub(r'^\|.*\|$', '', content, flags=re.MULTILINE)
        content = re.sub(r'^[-|:]+$', '', content, flags=re.MULTILINE)
        
        # 11. 处理连续多个换行为单个换行
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # 12. 清除markdown风格的注释
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
        
        # 13. 清除任何剩余的特殊标记字符序列
        content = re.sub(r'[\\`*_#{}\[\]()>#+\-.!](\s*[\\`*_#{}\[\]()>#+\-.!])+', ' ', content)
        
        # 14. 清除多余空格
        content = re.sub(r' {2,}', ' ', content)
        content = content.strip()
        
        # 15. 最后一次尝试解析内容中可能的JSON格式(处理前面步骤可能清理出的JSON格式)
        try:
            # 检查内容是否是JSON格式
            parsed_content = json.loads(content)
            
            # 如果是JSON格式，检查是否包含特定格式的字段
            if isinstance(parsed_content, dict):
                # 处理format_template格式
                if "format_template" in parsed_content:
                    if isinstance(parsed_content["format_template"], dict) and "template" in parsed_content["format_template"]:
                        return parsed_content["format_template"]["template"]
                
                # 如果只有一个键，直接返回其值
                if len(parsed_content) == 1:
                    key = list(parsed_content.keys())[0]
                    return parsed_content[key]
        except json.JSONDecodeError:
            # 不是JSON格式，保持原样
            pass
            
        return content 