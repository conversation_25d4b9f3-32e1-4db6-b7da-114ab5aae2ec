import os
import sys
import time

# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 检查环境
def is_server_environment():
    """检测是否运行在服务器环境中"""
    server_paths = [
        '/www.wuladehuanxiang.com',
        '/wwwroot/www.wuladehuanxiang.com',
        'C:\\wwwroot\\www.wuladehuanxiang.com'
    ]
    
    for path in server_paths:
        if os.path.exists(path):
            return True
    
    # 检查环境变量
    return os.environ.get('SERVER_ENV') == 'production'

# 如果是服务器环境，可以设置环境变量
if is_server_environment():
    os.environ['SERVER_ENV'] = 'production'
    print("检测到服务器环境，设置SERVER_ENV=production")

# 直接从当前目录导入
try:
    from app import app
    from config_manager import get_config
except Exception as e:
    print(f"导入模块失败: {str(e)}")
    print("尝试安装依赖...")
    
    # 尝试安装依赖
    try:
        import subprocess
        requirements_path = os.path.join(current_dir, 'requirements.txt')
        if os.path.exists(requirements_path):
            subprocess.call([sys.executable, '-m', 'pip', 'install', '-r', requirements_path])
            print("依赖安装完成，重新导入模块...")
            from app import app
            from config_manager import get_config
        else:
            print(f"找不到requirements.txt文件: {requirements_path}")
            sys.exit(1)
    except Exception as install_error:
        print(f"安装依赖失败: {str(install_error)}")
        sys.exit(1)

if __name__ == '__main__':
    try:
        config = get_config()
        port = config.get_server_port()
        host = config.get_server_host()
        
        print("=" * 50)
        print("正在启动八字服务后端...")
        print(f"服务地址: http://{host}:{port}")
        if is_server_environment():
            print("运行环境: 服务器")
        else:
            print("运行环境: 本地开发")
        print("按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动服务
        app.run(host=host, port=port, debug=config.is_debug_mode())
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务时出错: {str(e)}")
        
        # 如果是服务器环境，发生错误时尝试自动重启
        if is_server_environment():
            print("服务器环境下出错，5秒后尝试重新启动...")
            time.sleep(5)
            
            try:
                # 重新启动服务
                config = get_config()
                port = config.get_server_port()
                host = config.get_server_host()
                print("尝试再次启动服务...")
                app.run(host=host, port=port, debug=False)  # 错误后重启使用非调试模式
            except Exception as restart_error:
                print(f"重新启动服务失败: {str(restart_error)}")