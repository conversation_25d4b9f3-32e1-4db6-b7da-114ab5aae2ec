# 项目文件结构

## 项目概述
这是一个八字分析系统，包含前端界面和后端API服务，支持用户输入生辰八字信息并获取详细的命理分析报告。

## 目录结构

```
project/
├── .cursor/                          # Cursor编辑器配置
│   └── rules/
│       ├── file_split_rules.mdc      # 文件拆分规则
│       └── project.mdc               # 项目配置
├── .trae/                            # Trae AI配置
│   └── rules/
│       └── project_rules.md          # 项目规则文档（本文件）
├── CONFIG_README.md                  # 配置说明文档
├── analysis_results/                 # 前端分析结果存储目录 (目前为空)
├── article.html                      # 文章页面
├── articles/                         # 文章数据
│   ├── articles.json                 # 文章列表
│   ├── articles_fixed.json           # 修复后的文章列表
│   ├── categories.json               # 文章分类
│   └── popular-articles.json         # 热门文章
├── backend/                          # 后端服务目录
│   ├── LLMapi.py                     # LLM API接口
│   ├── __pycache__/                  # Python缓存文件
│   │   ├── LLMapi.cpython-313.pyc
│   │   ├── app.cpython-313.pyc
│   │   ├── bazi_analyzer.cpython-313.pyc
│   │   ├── bazi_service.cpython-313.pyc
│   │   ├── bazi_summary.cpython-313.pyc
│   │   ├── card_manager.cpython-313.pyc
│   │   ├── config_api.cpython-313.pyc
│   │   ├── config_manager.cpython-313.pyc
│   │   ├── request_manager.cpython-313.pyc
│   │   ├── start_server.cpython-313.pyc
│   │   └── url_generator.cpython-313.pyc
│   ├── add_cards.py                  # 卡密添加工具
│   ├── analysis_results/             # 后端LLM分析结果存储
│   │   ├── llm_analysis_1899腊初一_子_男.json
│   │   ├── llm_analysis_1900三初三_子_男.json
│   │   ├── llm_analysis_1990四廿一_子_男.json
│   │   ├── llm_analysis_1990四廿一_巳_女.json
│   │   ├── llm_analysis_2002五廿七_子_男.json
│   │   └── llm_analysis_2011正廿四_子_女.json
│   ├── app.py                        # Flask主应用
│   ├── app_debug.py                  # Flask调试版本
│   ├── bazi_analyzer.py              # 八字分析核心模块
│   ├── bazi_results.json             # 八字分析结果 (后端内部使用)
│   ├── bazi_service.py               # 八字服务模块
│   ├── bazi_summary.py               # 八字摘要生成
│   ├── card_manager.py               # 卡密管理
│   ├── cards.json                    # 卡密数据 (后端内部使用)
│   ├── cards.json.backup             # 卡密数据备份
│   ├── config.json                   # 后端配置文件
│   ├── config_api.py                 # 配置API
│   ├── config_manager.py             # 配置管理器
│   ├── debug_api.py                  # API调试接口
│   ├── debug_api_data.py             # API调试数据工具
│   ├── debug_card_manager.py         # 卡密管理调试工具
│   ├── debug_results.py              # 结果调试工具
│   ├── fix_cards.py                  # 卡密修复工具
│   ├── llm_logs/                     # LLM请求日志 (后端)
│   │   ├── app.log                   # 应用日志
│   │   └── llm_request_*.json        # LLM请求记录 (多个文件)
│   ├── llm_prompt_example.txt        # LLM提示词示例
│   ├── prompts/                      # 提示词模板
│   │   ├── career.json               # 事业运势提示词
│   │   ├── education.json            # 学业运势提示词
│   │   ├── fortune_2022.json         # 2022年运势提示词
│   │   ├── fortune_2023.json         # 2023年运势提示词
│   │   ├── fortune_2024.json         # 2024年运势提示词
│   │   ├── fortune_2025.json         # 2025年运势提示词
│   │   ├── health.json               # 健康运势提示词
│   │   ├── index.json                # 主页提示词
│   │   ├── pattern.json              # 格局分析提示词
│   │   ├── personality.json          # 性格分析提示词
│   │   ├── relationship.json         # 感情运势提示词
│   │   └── strength.json             # 五行强弱提示词
│   ├── raw_bazi_data_20250528_053711.json  # 原始八字数据
│   ├── request_manager.py            # 请求管理器
│   ├── requirements.txt              # Python依赖包
│   ├── start_server.py               # 服务启动脚本
│   ├── summaries/                    # 分析摘要 (后端)
│   │   └── bazi_summary_*.txt        # 多个摘要文件
│   ├── summaries_backup_1748424734.txt  # 摘要备份文件
│   └── url_generator.py              # URL生成器
├── bazi_results.json                 # 八字分析结果 (根目录)
├── blog.html                         # 博客页面
├── card_generator.html               # 卡密生成页面
├── check_json.py                     # JSON检查工具
├── check_target_card.py              # 目标卡密检查工具
├── config.html                       # 配置页面
├── css/                              # 前端CSS样式目录
│   ├── article.css                   # 文章页面样式
│   ├── blog.css                      # 博客页面样式
│   ├── card_generator.css            # 卡密生成页面样式
│   ├── config.css                    # 配置页面样式
│   ├── debug.css                     # 调试页面样式
│   ├── detailed_result.css           # 详细结果页面样式
│   ├── index.css                     # 主页样式
│   ├── result.css                    # 结果页面样式
│   └── up.css                        # 分析请求页面样式
├── debug_api_call.py                 # API调用调试工具
├── debug_card_validation.py          # 卡密验证调试工具
├── debug_server_validate.py          # 服务器验证调试工具
├── debug_validate.log                # 验证调试日志
├── design_prompts.md                 # 设计提示文档
├── detailed_result.html              # 详细结果页面
├── favicon.ico                       # 网站图标
├── final_fix.py                      # 最终修复脚本
├── final_review_gate.py              # 最终审查脚本
├── fix_401_error.py                  # 401错误修复脚本
├── fix_json.py                       # JSON修复脚本
├── index.html                        # 主页面
├── js/                               # 前端JavaScript模块
│   ├── README.md                     # JS模块说明文档
│   ├── analyzer.js                   # 分析逻辑模块
│   ├── api.js                        # API调用模块
│   ├── config.js                     # 配置管理模块
│   ├── detailed_result.js            # 详细结果页面逻辑
│   ├── main.js                       # 主应用逻辑
│   ├── ui.js                         # UI交互模块
│   └── utils.js                      # 工具函数模块
├── llm_logs/                         # 前端LLM日志 (根目录)
│   └── app.log                       # 应用日志
├── pic/                              # 图片资源
│   └── 我的微信号.jpg                # 微信二维码
├── project.md                        # 项目说明文档
├── result.html                       # 结果页面
├── script.js.backup                  # 旧版JS文件备份
├── simple_fix.py                     # 简单修复脚本
├── styles.css                        # 全局基础样式
├── summaries/                        # 前端摘要存储 (根目录)
│   └── bazi_summary_*.txt            # 多个摘要文件
├── test_api.ps1                      # API测试脚本 (PowerShell)
├── test_api_simple.ps1               # 简单API测试脚本 (PowerShell)
├── test_server_card_manager.py       # 服务器卡密管理测试
├── test_server_instance.py           # 服务器实例测试
└── up.html                           # 分析请求页面
```

## 核心模块说明

### 前端模块
#### HTML页面
- **index.html**: 用户输入界面，收集生辰八字信息
- **result.html**: 分析结果列表页面
- **detailed_result.html**: 详细分析报告页面
- **up.html**: 分析请求提交页面
- **config.html**: 系统配置页面
- **article.html**: 文章展示页面
- **blog.html**: 博客列表页面
- **card_generator.html**: 卡密生成页面

#### 文档和资源
- **project.md**: 项目说明文档
- **design_prompts.md**: 设计提示文档
- **favicon.ico**: 网站图标
- **script.js.backup**: 旧版JS文件备份
- **js/**: 前端JavaScript模块目录
  - **main.js**: 主应用逻辑，处理用户输入、表单提交、与后端API交互、调用其他JS模块进行分析和UI更新
  - **api.js**: 封装与后端Flask API的通信，如获取分析结果、配置、卡密验证等
  - **ui.js**: 负责动态更新页面元素，如显示结果、进度条、错误信息、加载状态等
  - **utils.js**: 提供通用的辅助函数，如日期格式化、数据校验、DOM操作等
  - **config.js**: 管理前端配置信息，如API端点、默认参数等
  - **analyzer.js**: (若有前端处理能力) 可能包含部分轻量级的八字排盘逻辑或数据预处理
  - **detailed_result.js**: `detailed_result.html` 页面的特定交互逻辑和数据展示
  - **README.md**: `js/` 模块的说明文档
- **css/**: 前端CSS样式目录，存放各个HTML页面对应的独立CSS文件
  - **styles.css**: (位于根目录) 全局基础样式、变量定义、通用布局等
  - **article.css**: `article.html` 页面的特定样式
  - **blog.css**: `blog.html` 页面的特定样式
  - **config.css**: `config.html` 页面的特定样式
  - **debug.css**: `debug.html` 页面的特定样式
  - **detailed_result.css**: `detailed_result.html` 页面的特定样式
  - **index.css**: `index.html` 页面的特定样式
  - **result.css**: `result.html` 页面的特定样式
  - **up.css**: `up.html` 页面的特定样式

### 后端模块
- **app.py**: Flask主应用，提供API服务
- **app_debug.py**: Flask调试版本，包含详细日志输出
- **bazi_analyzer.py**: 八字分析核心算法
- **bazi_service.py**: 八字服务封装
- **bazi_summary.py**: 八字摘要生成模块
- **LLMapi.py**: 大语言模型API接口
- **card_manager.py**: 卡密验证和管理
- **config_manager.py**: 配置文件管理
- **config_api.py**: 配置相关API接口
- **request_manager.py**: 请求处理和管理
- **url_generator.py**: URL生成工具
- **start_server.py**: 服务器启动脚本

#### 调试和开发工具
- **debug_api.py**: API调试接口
- **debug_api_data.py**: API调试数据工具
- **debug_card_manager.py**: 卡密管理调试工具
- **debug_results.py**: 结果调试工具
- **add_cards.py**: 卡密添加工具
- **fix_cards.py**: 卡密修复工具

### 数据存储
- **analysis_results/**: 分析结果JSON文件存储
- **llm_logs/**: LLM请求和响应日志
- **cards.json**: 卡密数据库
- **config.json**: 系统配置参数

### 测试和调试模块
#### 后端测试
- **test_server_card_manager.py**: 服务器卡密管理测试
- **test_server_instance.py**: 服务器实例测试

#### 前端测试脚本
- **test_api.ps1**: API测试脚本 (PowerShell)
- **test_api_simple.ps1**: 简单API测试脚本 (PowerShell)

#### 调试工具
- **debug_api_call.py**: API调用调试工具
- **debug_card_validation.py**: 卡密验证调试工具
- **debug_server_validate.py**: 服务器验证调试工具
- **debug_validate.log**: 验证调试日志

#### 修复脚本
- **check_json.py**: JSON检查工具
- **check_target_card.py**: 目标卡密检查工具
- **fix_json.py**: JSON修复脚本
- **fix_401_error.py**: 401错误修复脚本
- **simple_fix.py**: 简单修复脚本
- **final_fix.py**: 最终修复脚本
- **final_review_gate.py**: 最终审查脚本

## 主要功能
1. **八字分析**: 根据用户输入的生辰八字进行命理分析
2. **卡密验证**: 支持卡密系统，控制访问权限
3. **结果管理**: 分析结果的存储、检索和展示
4. **配置管理**: 系统参数的动态配置
5. **日志记录**: 详细的操作和错误日志
6. **测试覆盖**: 完整的测试用例覆盖

## 技术栈
- **前端**: HTML5, CSS3, JavaScript
- **后端**: Python Flask
- **数据存储**: JSON文件
- **AI服务**: 大语言模型API集成
- **测试**: Python unittest框架


端口默认是5000

## AI代码处理规则

### 代码拆分和模块化原则
为了确保AI能够高效处理代码，避免一次性读写过多内容导致性能问题，请遵循以下规则：

#### 1. 文件大小限制
- **单个文件最大行数**: 不超过500行
- **单次读取限制**: 每次最多读取200行代码
- **单次写入限制**: 每次最多写入300行代码
- **函数/类大小**: 单个函数不超过50行，单个类不超过200行

#### 2. 模块化拆分策略
- **功能分离**: 将不同功能拆分到独立文件中
- **配置分离**: 配置相关代码独立成config模块
- **工具函数分离**: 通用工具函数独立成utils模块
- **API接口分离**: API相关代码独立成api模块
- **UI逻辑分离**: 前端UI逻辑独立成ui模块

#### 3. 文件命名规范
- **配置文件**: `config.js`, `config.py`
- **工具文件**: `utils.js`, `utils.py`
- **API文件**: `api.js`, `api.py`
- **UI文件**: `ui.js`, `ui.py`
- **主入口文件**: `main.js`, `main.py`
- **分析器文件**: `analyzer.js`, `analyzer.py`

#### 4. AI处理指导原则
- **分批处理**: 大文件必须分批次处理，每次处理一个逻辑模块
- **依赖管理**: 明确模块间依赖关系，确保引用顺序正确
- **增量修改**: 优先使用增量修改而非全文重写
- **备份机制**: 重大修改前必须备份原文件
- **测试验证**: 每次拆分后必须验证功能完整性

#### 5. 代码组织结构
```
project/
├── js/                    # 前端JavaScript模块
│   ├── config.js         # 配置管理
│   ├── utils.js          # 工具函数
│   ├── ui.js             # UI交互
│   ├── api.js            # API调用
│   ├── analyzer.js       # 分析逻辑
│   ├── main.js           # 主入口
│   └── README.md         # 模块说明
├── backend/
│   ├── modules/          # 后端模块
│   │   ├── config/       # 配置模块
│   │   ├── utils/        # 工具模块
│   │   ├── api/          # API模块
│   │   ├── analyzer/     # 分析模块
│   │   └── services/     # 服务模块
│   └── ...
```

#### 6. 重构指导
- **识别重复代码**: 提取公共函数到utils模块
- **分离关注点**: 将不同职责的代码分离到不同文件
- **减少耦合**: 通过接口和配置减少模块间直接依赖
- **提高内聚**: 相关功能组织在同一模块内

#### 7. AI操作限制
- **禁止一次性处理超过1000行的文件**
- **禁止在单次操作中修改超过5个文件**
- **必须使用分段读取查看大文件**
- **必须使用增量编辑而非全文替换**
- **必须在拆分前分析代码结构和依赖关系**

这些规则确保AI能够高效、安全地处理代码，避免因文件过大导致的性能问题和错误。
