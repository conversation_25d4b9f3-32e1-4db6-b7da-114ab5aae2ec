#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from card_manager import CardManager

print("=== 调试CardManager ===")

# 1. 直接读取JSON文件
cards_file = 'cards.json'
print(f"1. 直接读取JSON文件: {cards_file}")
if os.path.exists(cards_file):
    with open(cards_file, 'r', encoding='utf-8') as f:
        direct_data = json.load(f)
    print(f"   直接读取到 {len(direct_data)} 个卡密")
    print(f"   前5个卡密: {list(direct_data.keys())[:5]}")
else:
    print(f"   文件不存在: {cards_file}")

# 2. 使用CardManager
print("\n2. 使用CardManager:")
try:
    cm = CardManager()
    print(f"   CardManager加载了 {len(cm.cards_data)} 个卡密")
    print(f"   前5个卡密: {list(cm.cards_data.keys())[:5]}")
    print(f"   卡密文件路径: {cm.cards_file}")
except Exception as e:
    print(f"   CardManager初始化失败: {e}")

# 3. 检查文件路径
print("\n3. 检查文件路径:")
current_dir = os.path.dirname(os.path.abspath(__file__))
full_path = os.path.join(current_dir, cards_file)
print(f"   当前目录: {current_dir}")
print(f"   完整路径: {full_path}")
print(f"   文件存在: {os.path.exists(full_path)}")

if os.path.exists(full_path):
    with open(full_path, 'r', encoding='utf-8') as f:
        full_path_data = json.load(f)
    print(f"   完整路径读取到 {len(full_path_data)} 个卡密")