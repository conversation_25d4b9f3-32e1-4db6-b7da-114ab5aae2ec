#!/usr/bin/env python3
"""
全面测试API端点和路由
"""

from app import app
import json

def test_all_api_endpoints():
    with app.test_client() as client:
        print("=== 全面测试API端点 ===")
        
        # 测试结果统计
        results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
        
        # 定义测试用例
        test_cases = [
            # 健康检查
            {'method': 'GET', 'url': '/api/health', 'expected_status': 200, 'name': '健康检查'},
            
            # 配置相关
            {'method': 'GET', 'url': '/api/config/get', 'expected_status': 200, 'name': '获取配置'},
            {'method': 'GET', 'url': '/api/config/dimensions', 'expected_status': 200, 'name': '获取维度配置'},
            
            # 卡密相关
            {'method': 'GET', 'url': '/api/cards', 'expected_status': 200, 'name': '获取卡密列表'},
            {'method': 'GET', 'url': '/api/cards/json', 'expected_status': 200, 'name': '获取卡密JSON'},
            
            # 报告相关
            {'method': 'GET', 'url': '/api/reports/list', 'expected_status': 200, 'name': '获取报告列表'},
            
            # 分析状态（需要有效ID）
            {'method': 'GET', 'url': '/api/analysis/status/test_id', 'expected_status': [200, 404], 'name': '查询分析状态'},
            {'method': 'GET', 'url': '/api/analysis/llm-progress/test_id', 'expected_status': [200, 404], 'name': '查询LLM进度'},
            
            # Webhook端点
            {'method': 'GET', 'url': '/webhook/check-status', 'expected_status': 200, 'name': 'Webhook状态检查'},
        ]
        
        # 执行测试
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}...")
            
            try:
                if test_case['method'] == 'GET':
                    response = client.get(test_case['url'])
                elif test_case['method'] == 'POST':
                    response = client.post(test_case['url'], 
                                         data=test_case.get('data', '{}'),
                                         content_type='application/json')
                
                status_code = response.status_code
                expected = test_case['expected_status']
                
                # 检查状态码
                if isinstance(expected, list):
                    status_ok = status_code in expected
                else:
                    status_ok = status_code == expected
                
                if status_ok:
                    print(f"   ✅ {test_case['method']} {test_case['url']} - Status: {status_code}")
                    results['passed'] += 1
                    
                    # 尝试解析JSON响应
                    try:
                        json_data = response.get_json()
                        if json_data:
                            print(f"   📄 响应包含JSON数据: {len(str(json_data))} 字符")
                    except:
                        print(f"   📄 响应为非JSON格式")
                        
                else:
                    print(f"   ❌ {test_case['method']} {test_case['url']} - Status: {status_code} (期望: {expected})")
                    results['failed'] += 1
                    results['errors'].append(f"{test_case['name']}: 状态码 {status_code}")
                    
            except Exception as e:
                print(f"   💥 {test_case['method']} {test_case['url']} - 异常: {str(e)}")
                results['failed'] += 1
                results['errors'].append(f"{test_case['name']}: 异常 {str(e)}")
        
        # 输出测试结果
        print(f"\n=== 测试结果统计 ===")
        print(f"✅ 通过: {results['passed']}")
        print(f"❌ 失败: {results['failed']}")
        print(f"📊 总计: {results['passed'] + results['failed']}")
        
        if results['errors']:
            print(f"\n❌ 失败详情:")
            for error in results['errors']:
                print(f"   - {error}")
        
        return results['failed'] == 0

if __name__ == '__main__':
    try:
        success = test_all_api_endpoints()
        print(f"\n{'✅ 所有API端点测试通过' if success else '❌ 部分API端点测试失败'}")
    except Exception as e:
        print(f"\n💥 API端点测试出现异常: {e}")
        import traceback
        traceback.print_exc()
