<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - 八字分析系统</title>
    <link rel="stylesheet" href="css/config.css">
</head>

<body>
    <!-- 密码保护层 -->
    <div id="passwordProtection" style="display: flex; justify-content: center; align-items: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;">
            <h2 style="color: #333; margin-bottom: 20px;">🔒 访问受限</h2>
            <p style="color: #666; margin-bottom: 20px;">此页面需要密码验证</p>
            <p style="color: #999; font-size: 14px;">页面将自动弹出密码输入框</p>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div id="mainContent" style="display: none;">
        <div class="container">
            <div class="header">
                <h1>🔧 配置管理</h1>
                <p>八字分析系统配置中心</p>
                <div class="nav-links">
                    <a href="/" class="nav-link">首页</a>
                    <a href="/card-generator" class="nav-link">卡密生成器</a>
                </div>
            </div>

            <div class="content">
            <div id="status" class="status"></div>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载配置...</p>
            </div>

            <!-- 分析配置 -->
            <div class="section">
                <h2>📊 分析配置</h2>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="personalityOnly"> 只分析性格维度（节省API额度）
                    </label>
                </div>
                <div class="form-group">
                    <label>启用的分析维度：</label>
                    <div id="dimensionsCheckbox" class="checkbox-group">
                        <!-- 动态生成 -->
                    </div>
                </div>
                <button class="btn" onclick="updateAnalysisConfig()">更新分析配置</button>
                <button class="btn btn-secondary" onclick="togglePersonalityOnly()">切换性格分析模式</button>
            </div>

            <!-- API配置 -->
            <div class="section">
                <h2>🔗 API配置</h2>
                <div class="form-group">
                    <label for="apiUrl">API地址：</label>
                    <input type="text" id="apiUrl" placeholder="https://api.example.com/v1/chat/completions">
                </div>
                <div class="form-group">
                    <label for="modelName">模型名称：</label>
                    <input type="text" id="modelName" placeholder="gpt-3.5-turbo">
                </div>
                <div class="form-group">
                    <label for="apiKey">API密钥：</label>
                    <input type="password" id="apiKey" placeholder="sk-...">
                </div>
                <div class="form-group">
                    <label for="maxRetries">最大重试次数：</label>
                    <input type="number" id="maxRetries" min="1" max="10" value="3">
                </div>
                <div class="form-group">
                    <label for="retryDelay">重试间隔（秒）：</label>
                    <input type="number" id="retryDelay" min="1" max="60" value="10">
                </div>
                <button class="btn" onclick="updateApiConfig()">更新API配置</button>
            </div>

            <!-- 卡密管理 -->
            <div class="section">
                <h2>🎫 卡密管理</h2>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="cardValidationEnabled"> 启用卡密验证
                    </label>
                </div>
                <div class="form-group">
                    <label for="newCard">添加新卡密：</label>
                    <input type="text" id="newCard" placeholder="输入新卡密">
                    <button class="btn" onclick="addCard()">添加</button>
                </div>
                <div class="form-group">
                    <label>当前有效卡密：</label>
                    <div id="cardList" class="card-list">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 服务器配置 -->
            <div class="section">
                <h2>🖥️ 服务器配置</h2>
                <div class="form-group">
                    <label for="serverHost">服务器地址：</label>
                    <input type="text" id="serverHost" value="0.0.0.0">
                </div>
                <div class="form-group">
                    <label for="serverPort">服务器端口：</label>
                    <input type="number" id="serverPort" min="1" max="65535" value="5000">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="debugMode"> 调试模式
                    </label>
                </div>
                <button class="btn" onclick="updateServerConfig()">更新服务器配置</button>
            </div>

            <!-- 配置操作 -->
            <div class="section">
                <h2>💾 配置操作</h2>
                <button class="btn btn-success" onclick="saveConfig()">保存配置到文件</button>
                <button class="btn btn-secondary" onclick="reloadConfig()">重新加载配置</button>
                <button class="btn btn-secondary" onclick="loadConfig()">刷新页面配置</button>
            </div>

            <!-- 当前配置显示 -->
            <div class="section">
                <h2>📋 当前配置</h2>
                <div id="configDisplay" class="config-display">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 根据当前环境确定API基础URL
        const getBaseUrl = () => {
            const hostname = window.location.hostname;
            const port = window.location.port;
            
            // 开发环境使用完整URL（包含端口）
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return `http://${hostname}:${port || 5000}/api/config`;
            }
            
            // 生产环境仅使用相对路径
            return '/api/config';
        };
        
        const API_BASE = getBaseUrl();
        console.log('使用API基础URL:', API_BASE);

        // 显示状态消息
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        // 显示加载状态
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 加载配置
        async function loadConfig() {
            showLoading(true);
            try {
                const response = await fetch(`${API_BASE}/get`);
                const data = await response.json();
                
                if (data.success) {
                    populateForm(data.config);
                    updateConfigDisplay(data.config);
                    showStatus('配置加载成功');
                } else {
                    showStatus(`加载配置失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`加载配置失败: ${error.message}`, true);
            } finally {
                showLoading(false);
            }
        }

        // 填充表单
        function populateForm(config) {
            // API配置
            document.getElementById('apiUrl').value = config.api?.url || '';
            document.getElementById('modelName').value = config.api?.model_name || '';
            document.getElementById('apiKey').value = config.api?.headers?.Authorization?.replace('Bearer ', '') || '';
            document.getElementById('maxRetries').value = config.api?.max_retries || 3;
            document.getElementById('retryDelay').value = config.api?.retry_delay || 10;

            // 分析配置
            document.getElementById('personalityOnly').checked = config.analysis?.personality_only || false;
            
            // 生成维度复选框
            const dimensionsContainer = document.getElementById('dimensionsCheckbox');
            dimensionsContainer.innerHTML = '';
            const availableDimensions = config.analysis?.available_dimensions || [];
            const enabledDimensions = config.analysis?.enabled_dimensions || [];
            
            availableDimensions.forEach(dimension => {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="dim_${dimension}" value="${dimension}" 
                           ${enabledDimensions.includes(dimension) ? 'checked' : ''}>
                    <label for="dim_${dimension}">${dimension}</label>
                `;
                dimensionsContainer.appendChild(div);
            });

            // 卡密配置
            document.getElementById('cardValidationEnabled').checked = config.card_validation?.enabled || false;
            updateCardList(config.card_validation?.valid_cards || []);

            // 服务器配置
            document.getElementById('serverHost').value = config.server?.host || '0.0.0.0';
            document.getElementById('serverPort').value = config.server?.port || 5000;
            document.getElementById('debugMode').checked = config.server?.debug || false;
        }

        // 更新配置显示
        function updateConfigDisplay(config) {
            document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
        }

        // 更新卡密列表
        function updateCardList(cards) {
            const cardList = document.getElementById('cardList');
            cardList.innerHTML = '';
            
            cards.forEach(card => {
                const div = document.createElement('div');
                div.className = 'card-item';
                div.innerHTML = `
                    <span>${card}</span>
                    <button class="remove-btn" onclick="removeCard('${card}')">删除</button>
                `;
                cardList.appendChild(div);
            });
        }

        // 更新分析配置
        async function updateAnalysisConfig() {
            const enabledDimensions = [];
            document.querySelectorAll('#dimensionsCheckbox input[type="checkbox"]:checked').forEach(cb => {
                enabledDimensions.push(cb.value);
            });

            try {
                const response = await fetch(`${API_BASE}/dimensions`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ dimensions: enabledDimensions })
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                    loadConfig(); // 重新加载配置
                } else {
                    showStatus(`更新失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`更新失败: ${error.message}`, true);
            }
        }

        // 切换性格分析模式
        async function togglePersonalityOnly() {
            try {
                const response = await fetch(`${API_BASE}/toggle-personality-only`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                    loadConfig(); // 重新加载配置
                } else {
                    showStatus(`切换失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`切换失败: ${error.message}`, true);
            }
        }

        // 更新API配置
        async function updateApiConfig() {
            const apiKey = document.getElementById('apiKey').value;
            const updates = [
                { key: 'api.url', value: document.getElementById('apiUrl').value },
                { key: 'api.model_name', value: document.getElementById('modelName').value },
                { key: 'api.max_retries', value: parseInt(document.getElementById('maxRetries').value) },
                { key: 'api.retry_delay', value: parseInt(document.getElementById('retryDelay').value) }
            ];

            if (apiKey) {
                updates.push({ key: 'api.headers.Authorization', value: `Bearer ${apiKey}` });
            }

            try {
                for (const update of updates) {
                    const response = await fetch(`${API_BASE}/set`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(update)
                    });
                    
                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.error);
                    }
                }
                
                showStatus('API配置更新成功');
                loadConfig();
            } catch (error) {
                showStatus(`API配置更新失败: ${error.message}`, true);
            }
        }

        // 更新服务器配置
        async function updateServerConfig() {
            const updates = [
                { key: 'server.host', value: document.getElementById('serverHost').value },
                { key: 'server.port', value: parseInt(document.getElementById('serverPort').value) },
                { key: 'server.debug', value: document.getElementById('debugMode').checked }
            ];

            try {
                for (const update of updates) {
                    const response = await fetch(`${API_BASE}/set`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(update)
                    });
                    
                    const data = await response.json();
                    if (!data.success) {
                        throw new Error(data.error);
                    }
                }
                
                showStatus('服务器配置更新成功（重启后生效）');
                loadConfig();
            } catch (error) {
                showStatus(`服务器配置更新失败: ${error.message}`, true);
            }
        }

        // 添加卡密
        async function addCard() {
            const card = document.getElementById('newCard').value.trim();
            if (!card) {
                showStatus('请输入卡密', true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/cards/add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ card })
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                    document.getElementById('newCard').value = '';
                    updateCardList(data.valid_cards);
                } else {
                    showStatus(`添加失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`添加失败: ${error.message}`, true);
            }
        }

        // 移除卡密
        async function removeCard(card) {
            try {
                const response = await fetch(`${API_BASE}/cards/remove`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ card })
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                    updateCardList(data.valid_cards);
                } else {
                    showStatus(`移除失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`移除失败: ${error.message}`, true);
            }
        }

        // 保存配置
        async function saveConfig() {
            try {
                const response = await fetch(`${API_BASE}/save`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                } else {
                    showStatus(`保存失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`保存失败: ${error.message}`, true);
            }
        }

        // 重新加载配置
        async function reloadConfig() {
            try {
                const response = await fetch(`${API_BASE}/reload`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                if (data.success) {
                    showStatus(data.message);
                    loadConfig();
                } else {
                    showStatus(`重新加载失败: ${data.error}`, true);
                }
            } catch (error) {
                showStatus(`重新加载失败: ${error.message}`, true);
            }
        }

        // 密码验证
        function checkPassword() {
            const password = prompt('请输入访问密码：');
            if (password === '88888888') {
                document.getElementById('passwordProtection').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                loadConfig();
            } else if (password !== null) {
                alert('密码错误！');
                checkPassword();
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkPassword();
        });
    </script>
</body>
</html>