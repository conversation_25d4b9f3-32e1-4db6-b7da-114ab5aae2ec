/**
 * 标题格式化模块
 * 自动识别和格式化标题层级，添加图标和样式
 */

class TitleFormatter {
    constructor() {
        // 标题层级配置
        this.titleConfig = {
            h1: {
                icon: '📊',
                prefix: '',
                style: 'main-title'
            },
            h2: {
                icon: '🔹',
                prefix: '',
                style: 'section-title'
            },
            h3: {
                icon: '▶',
                prefix: '',
                style: 'subsection-title'
            },
            h4: {
                icon: '●',
                prefix: '',
                style: 'item-title'
            },
            h5: {
                icon: '◆',
                prefix: '',
                style: 'sub-item-title'
            },
            h6: {
                icon: '▸',
                prefix: '',
                style: 'detail-title'
            }
        };
        
        // 特殊标题映射
        this.specialTitles = {
            // 主要章节
            '日主强弱': { icon: '⚖️', color: '#6366f1' },
            '性格分析': { icon: '🧠', color: '#8b5cf6' },
            '事业运势': { icon: '💼', color: '#059669' },
            '财运分析': { icon: '💰', color: '#f59e0b' },
            '感情婚姻': { icon: '💕', color: '#ec4899' },
            '健康状况': { icon: '🏥', color: '#ef4444' },
            '学业文昌': { icon: '📚', color: '#3b82f6' },
            '大运分析': { icon: '🔄', color: '#06b6d4' },
            '流年运势': { icon: '📅', color: '#7c3aed' },
            '格局分析': { icon: '🎯', color: '#dc2626' },
            
            // 子章节
            '基本信息': { icon: '📋', color: '#6b7280' },
            '五行分析': { icon: '🌟', color: '#10b981' },
            '十神分析': { icon: '🎭', color: '#8b5cf6' },
            '用神喜忌': { icon: '⚡', color: '#f59e0b' },
            '调候用神': { icon: '🌡️', color: '#06b6d4' },
            
            // 运势相关
            '近期运势': { icon: '📈', color: '#10b981' },
            '中期展望': { icon: '🔮', color: '#6366f1' },
            '长期趋势': { icon: '🎯', color: '#7c3aed' },
            
            // 建议类
            '改善建议': { icon: '💡', color: '#f59e0b' },
            '注意事项': { icon: '⚠️', color: '#ef4444' },
            '开运方法': { icon: '🍀', color: '#10b981' },
            '风水调理': { icon: '🏠', color: '#8b5cf6' },
            
            // 时间相关
            '本年运势': { icon: '🗓️', color: '#6366f1' },
            '下年展望': { icon: '🔜', color: '#8b5cf6' },
            '月运分析': { icon: '🌙', color: '#06b6d4' },
            
            // 方面分析
            '优势特点': { icon: '✨', color: '#10b981' },
            '潜在问题': { icon: '🔍', color: '#ef4444' },
            '发展方向': { icon: '🎯', color: '#059669' },
            '改进空间': { icon: '📈', color: '#f59e0b' }
        };
    }
    
    /**
     * 格式化标题
     * @param {Element} element - 标题元素
     */
    formatTitle(element) {
        if (!element || !element.tagName) return;
        
        const tagName = element.tagName.toLowerCase();
        const config = this.titleConfig[tagName];
        
        if (!config) return;
        
        const originalText = element.textContent.trim();
        const specialConfig = this.specialTitles[originalText];
        
        // 清空原内容
        element.innerHTML = '';
        
        // 创建标题容器
        const titleContainer = document.createElement('div');
        titleContainer.className = `title-container ${config.style}`;
        
        // 添加图标
        const iconSpan = document.createElement('span');
        iconSpan.className = 'title-icon';
        iconSpan.textContent = specialConfig ? specialConfig.icon : config.icon;
        
        // 添加文本
        const textSpan = document.createElement('span');
        textSpan.className = 'title-text';
        textSpan.textContent = originalText;
        
        // 如果是特殊标题，添加特殊样式
        if (specialConfig) {
            titleContainer.style.setProperty('--title-color', specialConfig.color);
            titleContainer.classList.add('special-title');
        }
        
        // 添加层级指示器
        if (tagName !== 'h1') {
            const levelIndicator = document.createElement('span');
            levelIndicator.className = 'title-level';
            levelIndicator.textContent = this.getTitleLevel(tagName);
            titleContainer.appendChild(levelIndicator);
        }
        
        titleContainer.appendChild(iconSpan);
        titleContainer.appendChild(textSpan);
        
        // 添加锚点链接
        const anchor = this.createAnchor(originalText);
        if (anchor) {
            element.id = anchor;
            
            const linkSpan = document.createElement('span');
            linkSpan.className = 'title-anchor';
            linkSpan.innerHTML = '🔗';
            linkSpan.title = '复制链接';
            linkSpan.onclick = () => this.copyAnchorLink(anchor);
            titleContainer.appendChild(linkSpan);
        }
        
        element.appendChild(titleContainer);
        
        // 添加标题后的装饰线
        if (tagName === 'h1' || tagName === 'h2') {
            const decorLine = document.createElement('div');
            decorLine.className = `title-decoration ${tagName}-decoration`;
            element.appendChild(decorLine);
        }
    }
    
    /**
     * 获取标题层级标识
     * @param {string} tagName - 标签名
     * @returns {string} - 层级标识
     */
    getTitleLevel(tagName) {
        const levels = {
            'h2': '§',
            'h3': '¶',
            'h4': '•',
            'h5': '‣',
            'h6': '▪'
        };
        return levels[tagName] || '';
    }
    
    /**
     * 创建锚点ID
     * @param {string} text - 标题文本
     * @returns {string} - 锚点ID
     */
    createAnchor(text) {
        return text
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '')
            .toLowerCase();
    }
    
    /**
     * 复制锚点链接
     * @param {string} anchor - 锚点ID
     */
    copyAnchorLink(anchor) {
        const url = `${window.location.origin}${window.location.pathname}#${anchor}`;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('链接已复制到剪贴板');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showToast('链接已复制到剪贴板');
        }
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     */
    showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast-message';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    /**
     * 处理所有标题
     * @param {string} selector - 容器选择器
     */
    formatAllTitles(selector = '.detailed-dimension-text') {
        const containers = document.querySelectorAll(selector);
        
        containers.forEach(container => {
            const titles = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            titles.forEach(title => this.formatTitle(title));
        });
        
        console.log(`已格式化 ${containers.length} 个容器中的标题`);
    }
    
    /**
     * 生成目录
     * @param {string} selector - 容器选择器
     * @returns {string} - 目录HTML
     */
    generateTOC(selector = '.detailed-dimension-text') {
        const containers = document.querySelectorAll(selector);
        let tocHTML = '<div class="table-of-contents"><h3>📑 目录</h3><ul>';
        
        containers.forEach(container => {
            const titles = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
            
            titles.forEach(title => {
                const level = parseInt(title.tagName.charAt(1));
                const text = title.textContent.trim();
                const anchor = title.id || this.createAnchor(text);
                const indent = '  '.repeat(level - 1);
                
                if (!title.id) {
                    title.id = anchor;
                }
                
                tocHTML += `${indent}<li class="toc-level-${level}">
                    <a href="#${anchor}" class="toc-link">${text}</a>
                </li>`;
            });
        });
        
        tocHTML += '</ul></div>';
        return tocHTML;
    }
}

// 创建全局实例
window.titleFormatter = new TitleFormatter();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TitleFormatter;
}
