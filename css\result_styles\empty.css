/* 空状态 */
.empty-state {
    color: var(--text-primary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 24px;
    opacity: 0.6;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.empty-message {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 32px;
    max-width: 400px;
    line-height: 1.6;
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.start-analysis-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    min-width: 100px;
    background: var(--primary-gradient);
    color: white;
}

.start-analysis-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .empty-icon {
        font-size: 3.5rem;
        margin-bottom: 16px;
    }
    
    .empty-title {
        font-size: 1.4rem;
        text-align: center;
    }
    
    .empty-message {
        font-size: 15px;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 20px;
    }
    
    .empty-actions {
        flex-direction: column;
        gap: 12px;
        width: 100%;
        max-width: 280px;
    }
    
    .start-analysis-btn {
        width: 100%;
        min-height: 48px;
        font-size: 16px;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .empty-icon {
        font-size: 3rem;
    }
    
    .empty-title {
        font-size: 1.2rem;
    }
    
    .empty-message {
        font-size: 14px;
    }
    
    .empty-actions {
        max-width: 240px;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .empty-title {
        color: var(--text-primary);
    }
    
    .empty-message {
        color: var(--text-secondary);
    }
} 