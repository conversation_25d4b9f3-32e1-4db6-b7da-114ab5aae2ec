#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：获取问真八字API的完整原始数据
"""

import requests
import json
from datetime import datetime

def get_raw_bazi_data():
    """获取原始八字数据"""
    # 测试用的八字数据：1990年5月21日11时30分，男性
    url = "https://bzapi2.iwzbz.com/getbasebz7.php?d=1990-5-21%2011:30:0&s=1&today=undefined-NaN-NaN%20NaN:NaN:00&vip=0&userguid=&yzs=0"
    
    print("=" * 60)
    print("问真八字API原始数据获取")
    print("=" * 60)
    print(f"请求URL: {url}")
    print(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        response = requests.get(url, timeout=10)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("\n" + "=" * 60)
            print("API返回的所有字段：")
            print("=" * 60)
            
            # 显示所有顶级字段
            for key in sorted(data.keys()):
                value = data[key]
                value_type = type(value).__name__
                
                if isinstance(value, (list, dict)):
                    if isinstance(value, list):
                        length = len(value)
                        sample = value[:3] if length > 3 else value
                        print(f"{key:15} ({value_type}[{length}]): {sample}{'...' if length > 3 else ''}")
                    else:
                        print(f"{key:15} ({value_type}): {dict(list(value.items())[:3])}{'...' if len(value) > 3 else ''}")
                else:
                    print(f"{key:15} ({value_type}): {value}")
            
            print("\n" + "=" * 60)
            print("重要字段详细内容：")
            print("=" * 60)
            
            # 显示重要字段的详细内容
            important_fields = ['bz', 'tdbz', 'ss', 'ny', 'cg', 'cgss', 'dayun', 'xiaoyun', 
                              'szshensha', 'minggong', 'shenggong', 'taixi', 'taiyuan']
            
            for field in important_fields:
                if field in data:
                    print(f"\n【{field}】:")
                    value = data[field]
                    if isinstance(value, list):
                        for i, item in enumerate(value[:10]):  # 只显示前10个
                            print(f"  [{i}]: {item}")
                        if len(value) > 10:
                            print(f"  ... 还有 {len(value) - 10} 个项目")
                    else:
                        print(f"  {value}")
            
            # 保存完整数据到文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"raw_bazi_data_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 完整数据已保存到: {filename}")
            print(f"📊 数据总大小: {len(json.dumps(data))} 字符")
            
            return data
            
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return None

if __name__ == "__main__":
    get_raw_bazi_data()