/**
 * @file 八字分析结果查看器
 * @description 模块化重构入口文件，导入所有模块
 * @file-group detailed_result
 */

// 导入主类
import { DetailedResultViewer } from './detailed_result/detailed_result_core.js';

// 导出全局函数
import { toggleSection, editTitle, downloadReport, exportSeparateFiles } from './detailed_result/detailed_result_utils.js';

// 向全局暴露所需函数
window.toggleSection = toggleSection;
window.editTitle = editTitle;
window.downloadReport = downloadReport;
window.exportSeparateFiles = exportSeparateFiles;
console.log('全局函数已绑定:', {
    toggleSection: typeof window.toggleSection === 'function',
    editTitle: typeof window.editTitle === 'function',
    downloadReport: typeof window.downloadReport === 'function',
    exportSeparateFiles: typeof window.exportSeparateFiles === 'function'
});

// 添加DOMContentLoaded事件监听
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化八字分析结果查看器...');

    // 实例化DetailedResultViewer
    try {
        window.detailedResultViewer = new DetailedResultViewer();
        console.log('八字分析结果查看器初始化成功');

        // 创建样式增强应用函数
        window.applyStyleEnhancements = function() {
            console.log('应用样式增强...');

            // 应用标题格式化
            if (window.titleFormatter) {
                window.titleFormatter.formatAllTitles('.detailed-dimension-text');
                console.log('标题格式化应用完成');
            } else {
                console.warn('标题格式化器未找到');
            }



            // 生成目录（可选）
            if (window.titleFormatter && document.querySelector('.detailed-dimension-text h1, .detailed-dimension-text h2')) {
                const tocHTML = window.titleFormatter.generateTOC('.detailed-dimension-text');
                const firstSection = document.querySelector('.detailed-dimension-section');
                if (firstSection && tocHTML) {
                    const tocContainer = document.createElement('div');
                    tocContainer.innerHTML = tocHTML;
                    firstSection.parentNode.insertBefore(tocContainer.firstChild, firstSection);
                    console.log('目录生成完成');
                }
            }
        };

        // 定期检查内容是否已加载并应用样式
        const checkAndApplyStyles = () => {
            const contentElements = document.querySelectorAll('.detailed-dimension-text');
            if (contentElements.length > 0) {
                // 检查是否有实际内容
                const hasContent = Array.from(contentElements).some(el =>
                    el.textContent.trim().length > 0
                );

                if (hasContent) {
                    console.log('检测到内容已加载，应用样式增强');
                    setTimeout(() => {
                        window.applyStyleEnhancements();
                    }, 500);
                    return true; // 停止检查
                }
            }
            return false; // 继续检查
        };

        // 立即检查一次
        if (!checkAndApplyStyles()) {
            // 如果内容还没加载，设置定期检查
            const checkInterval = setInterval(() => {
                if (checkAndApplyStyles()) {
                    clearInterval(checkInterval);
                }
            }, 1000);

            // 设置最大检查时间（30秒）
            setTimeout(() => {
                clearInterval(checkInterval);
                console.log('样式增强检查超时');
            }, 30000);
        }

    } catch (error) {
        console.error('八字分析结果查看器初始化失败:', error);
        if (window.showErrorState) {
            window.showErrorState('系统初始化失败', error.message || '请刷新页面重试');
        }
    }
});
