/**
 * 详细分析结果页面辅助功能模块
 * 包含PDF下载、调试、状态切换等功能
 */

// 全局API配置
window.apiBaseUrl = window.location.hostname === '***********' ? 'http://***********:5000' : '';

// 备用下载函数
function backupDownloadReport() {
    console.log('备用下载函数被调用');
    try {
        const title = document.getElementById('resultTitle').textContent || '八字分析报告';
        const dateStr = new Date().toISOString().slice(0, 10);
        
        // 创建一个临时容器，用于生成PDF
        const container = document.createElement('div');
        container.style.padding = '20px';
        container.style.fontFamily = 'Arial, sans-serif';
        
        // 在生成PDF前，确保所有流年部分都被展开
        const fortuneYearSections = document.querySelectorAll('.detailed-dimension-section');
        fortuneYearSections.forEach(section => {
            const header = section.querySelector('.detailed-dimension-header');
            const content = section.querySelector('.detailed-dimension-content');
            const title = section.querySelector('.detailed-dimension-title');
            
            // 检查是否为流年部分（通过标题文本判断）
            if (title && title.textContent && 
                (title.textContent.includes('运势') || title.textContent.includes('流年'))) {
                console.log('找到流年部分，强制展开:', title.textContent);
                // 强制添加active类
                if (header) header.classList.add('active');
                if (content) content.classList.add('active');
                // 更新展开/收起图标
                const toggleIcon = section.querySelector('.detailed-toggle-icon');
                if (toggleIcon) toggleIcon.textContent = '▼';
            }
        });
        
        // 复制分析内容
        const analysisContent = document.getElementById('analysisContent');
        if (analysisContent) {
            // 添加内联样式，确保PDF美观
            const pdfStyles = `
                <style>
                    body { font-family: 'Arial', 'Microsoft YaHei', sans-serif; }
                    h1 { color: #4a00e0; text-align: center; margin-bottom: 20px; font-size: 24px; }
                    h2 { color: #4a00e0; margin-top: 20px; margin-bottom: 10px; font-size: 20px; }
                    h3 { color: #4a00e0; margin-top: 15px; margin-bottom: 8px; font-size: 18px; }
                    p { margin-bottom: 10px; line-height: 1.6; }
                    .section { margin-bottom: 20px; padding: 15px; border-radius: 8px; background-color: #f9f9f9; }
                    .section-title { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 10px 15px; border-radius: 5px; margin-bottom: 15px; }
                    .bazi-info { background-color: #f0f8ff; padding: 15px; border-left: 4px solid #4a00e0; margin-bottom: 20px; border-radius: 5px; }
                    .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
                    table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                    table, th, td { border: 1px solid #ddd; }
                    th, td { padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    /* 确保所有内容都显示，不受折叠状态影响 */
                    .detailed-dimension-content, .section-content {
                        display: block !important;
                        visibility: visible !important;
                        height: auto !important;
                        opacity: 1 !important;
                        overflow: visible !important;
                    }
                </style>
            `;
            
            // 获取八字信息部分
            const baziInfoElement = document.querySelector('.bazi-info-section');
            let baziInfoHtml = '';
            if (baziInfoElement) {
                baziInfoHtml = `
                    <div class="bazi-info">
                        ${baziInfoElement.innerHTML}
                    </div>
                `;
            }
            
            // 获取分析维度部分
            let sectionsHtml = '';
            const sections = document.querySelectorAll('.detailed-dimension-section');
            sections.forEach(section => {
                const title = section.querySelector('.detailed-dimension-title');
                const content = section.querySelector('.detailed-dimension-text');
                
                if (title && content) {
                    // 确保流年内容被展开并显示
                    const isFortuneYear = title.textContent && 
                        (title.textContent.includes('运势') || title.textContent.includes('流年'));
                    
                    sectionsHtml += `
                        <div class="section ${isFortuneYear ? 'fortune-year-section' : ''}">
                            <div class="section-title">${title.innerHTML}</div>
                            <div class="section-content" style="display:block !important;">${content.innerHTML}</div>
                        </div>
                    `;
                }
            });
            
            // 如果没有找到分析维度，直接使用clonedContent的内容
            if (!sectionsHtml) {
                sectionsHtml = analysisContent.innerHTML;
            }
            
            container.innerHTML = `
                ${pdfStyles}
                <h1>${title}</h1>
                <p style="text-align: center; color: #666; margin-bottom: 30px;">生成时间: ${new Date().toLocaleString()}</p>
                ${baziInfoHtml}
                ${sectionsHtml}
                <div class="footer">
                    <p>八字分析系统 - 专业命理分析报告</p>
                    <p>生成日期: ${new Date().toLocaleDateString()}</p>
                </div>
            `;
        } else {
            alert('无法找到报告内容，请确保页面已完全加载');
            return;
        }
        
        // 显示加载提示
        const downloadBtn = document.querySelector('.detailed-download-btn');
        const originalText = downloadBtn.innerHTML;
        downloadBtn.innerHTML = '<span class="download-icon">⏳</span><span class="btn-text">生成PDF中...</span>';
        downloadBtn.disabled = true;
        
        // 配置PDF选项
        const opt = {
            margin: [15, 15],
            filename: `八字分析报告_${title.replace(/[^\w\s-]/g, '')}_${dateStr}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { 
                scale: 2, 
                useCORS: true,
                letterRendering: true,
                allowTaint: true,
                logging: true,
                dpi: 300,
                scrollX: 0,
                scrollY: 0
            },
            jsPDF: { 
                unit: 'mm', 
                format: [297, 9999], // 使用超长的高度来避免分页
                orientation: 'portrait',
                compress: true,
                precision: 16
            },
            // 避免所有自动分页
            pagebreak: { mode: 'avoid-all' }
        };
        
        // 生成PDF
        html2pdf().from(container).set(opt).save().then(() => {
            console.log('PDF生成完成');
            
            // 恢复按钮状态
            setTimeout(() => {
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            }, 1000);
        }).catch(error => {
            console.error('PDF生成失败:', error);
            alert('PDF生成失败: ' + error.message);
            
            // 恢复按钮状态
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        });
        
    } catch (error) {
        console.error('备用下载失败:', error);
        alert('下载失败: ' + error.message);
    }
}

// 调试函数
function debugReport() {
    console.log('调试函数被调用');
    try {
        const analysisContent = document.getElementById('analysisContent');
        console.log('分析内容元素:', analysisContent);
        if (analysisContent) {
            console.log('分析内容显示状态:', analysisContent.style.display);
            console.log('分析内容HTML长度:', analysisContent.innerHTML.length);
            console.log('分析内容子元素数量:', analysisContent.children.length);
        }
        
        const sections = document.querySelectorAll('.dimension-section');
        console.log('维度部分数量:', sections.length);
        sections.forEach((section, index) => {
            console.log(`维度部分 ${index + 1} 类名:`, section.className);
        });
        
        alert('调试信息已在控制台输出，请按F12查看');
    } catch (error) {
        console.error('调试失败:', error);
        alert('调试失败: ' + error.message);
    }
}

// 暴露给全局
window.backupDownloadReport = backupDownloadReport;
window.debugReport = debugReport; 