/* CSS变量定义 */
:root {
    /* 主色调 - 使用更柔和的渐变色 */
    --primary-color: #5d59d1;
    --primary-dark: #4844b8;
    --primary-light: #a8b4f5;
    --primary-gradient: linear-gradient(135deg, #5d59d1 0%, #8362e6 100%);
    
    /* 背景色 - 使用更温和的灰色调 */
    --background-light: #f8f9fd;
    
    /* 文本颜色 - 使用更柔和的文本颜色 */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #718096;
    --text-muted: #a0aec0;
    
    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-color-light: #edf2f7;
    
    /* 阴影效果 - 更柔和的阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.07), 0 4px 6px rgba(0, 0, 0, 0.05);
    
    /* 圆角半径 */
    --radius-small: 6px;
    --radius-medium: 8px;
    --radius-large: 12px;
    
    /* 动画过渡 */
    --transition-normal: all 0.3s ease;
    
    /* 字体设置 */
    --font-family-primary: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --line-height-base: 1.6;
    
    /* 五行颜色 - 更和谐的配色 */
    --wuxing-jin-color: #d4af37;  /* 金 - 更柔和的金色 */
    --wuxing-mu-color: #38a169;   /* 木 - 更柔和的绿色 */
    --wuxing-shui-color: #3182ce; /* 水 - 更柔和的蓝色 */
    --wuxing-huo-color: #e53e3e;  /* 火 - 更柔和的红色 */
    --wuxing-tu-color: #b7791f;   /* 土 - 更柔和的棕色 */
    --wuxing-kong-color: #a0aec0; /* 空 - 柔和的灰色 */
    
    /* 卡片和容器背景 */
    --bg-card: #ffffff;
    --bg-primary: var(--background-light);
    --bg-secondary: #f7fafc;
    --bg-overlay: rgba(255, 255, 255, 0.85);
}

/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-family: var(--font-family-primary);
    font-size: 16px;
    color: var(--text-primary);
    background-color: var(--background-light);
    line-height: var(--line-height-base);
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
}

/* 通用容器 */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
}

/* 通用按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 24px;
    border-radius: var(--radius-medium);
    font-weight: 500;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(93, 89, 209, 0.2);
}

.btn-secondary {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-light);
}

.btn-secondary:hover {
    background-color: #f9fafb;
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

/* 通用卡片 */
.card {
    background-color: var(--bg-card);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

/* 通用间距 */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

/* 通用文本样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-light { color: var(--text-light); }
.text-muted { color: var(--text-muted); }

/* 通用Flex布局 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }

/* 通用响应式隐藏 */
@media (max-width: 768px) {
    .hidden-mobile { display: none; }
}

@media (min-width: 769px) {
    .hidden-desktop { display: none; }
}

/* 通用过渡效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 详细结果页面样式 - 优化版本 */
.detailed-result-page {
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: var(--line-height-base);
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 加载状态 */
.detailed-result-page.loading {
    overflow: hidden;
}

.detailed-result-page.loading::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    animation: detailed-loading-bar 2s ease-in-out infinite;
    z-index: 9999;
}

@keyframes detailed-loading-bar {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .detailed-result-page *,
    .detailed-result-page *::before,
    .detailed-result-page *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* 键盘导航支持 */
.detailed-dimension-header:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
} 