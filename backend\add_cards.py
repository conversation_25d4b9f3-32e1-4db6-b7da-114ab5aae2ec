#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
八字分析系统 - 卡密批量添加工具
"""

import sys
import os
from datetime import datetime, timedelta
from card_manager import CardManager

def add_cards_from_list(card_list, expire_days=30, max_usage=100):
    """从列表批量添加卡密"""
    card_manager = CardManager()
    
    print(f"开始添加 {len(card_list)} 张卡密...")
    
    for card_key in card_list:
        # 检查卡密是否已存在
        if card_key in card_manager.cards_data:
            print(f"卡密 {card_key} 已存在，跳过")
            continue
            
        # 添加卡密
        card_manager.add_card(card_key, expire_days, max_usage)
        print(f"已添加卡密: {card_key}")
    
    print(f"添加完成，共添加 {len(card_list)} 张卡密")

def add_cards_from_file(file_path, expire_days=30, max_usage=100):
    """从文件批量添加卡密"""
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            card_list = [line.strip() for line in f if line.strip()]
        
        add_cards_from_list(card_list, expire_days, max_usage)
    except Exception as e:
        print(f"添加卡密时出错: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("1. 从文件添加: python add_cards.py -f 卡密文件.txt [过期天数] [使用次数]")
        print("2. 直接添加: python add_cards.py 卡密1 卡密2 卡密3... [过期天数] [使用次数]")
        return
    
    expire_days = 30  # 默认30天过期
    max_usage = 100   # 默认最多使用100次
    
    # 处理可选参数
    args = sys.argv[1:]
    if args and args[-1].isdigit() and args[-2].isdigit():
        max_usage = int(args[-1])
        expire_days = int(args[-2])
        args = args[:-2]
    elif args and args[-1].isdigit():
        max_usage = int(args[-1])
        args = args[:-1]
    
    # 从文件添加
    if args[0] == '-f' and len(args) >= 2:
        file_path = args[1]
        add_cards_from_file(file_path, expire_days, max_usage)
    # 直接添加
    else:
        add_cards_from_list(args, expire_days, max_usage)

if __name__ == "__main__":
    main() 