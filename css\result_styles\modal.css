/* 卡密输入模态框样式 */
.card-key-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.card-key-modal.show {
    opacity: 1;
    visibility: visible;
}

.card-key-modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-large);
    padding: 40px;
    max-width: 480px;
    width: 90%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color-light);
    transform: translateY(-50px) scale(0.9);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-key-modal.show .card-key-modal-content {
    transform: translateY(0) scale(1);
}

.card-key-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.card-key-modal-header {
    text-align: center;
    margin-bottom: 32px;
}

.card-key-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.card-key-modal-title::before {
    content: '🔐';
    font-size: 1.2em;
}

.card-key-modal-subtitle {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin: 0;
}

.card-key-input-group {
    margin-bottom: 24px;
}

.card-key-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid var(--border-color-light);
    border-radius: var(--radius-medium);
    font-size: 1rem;
    background: var(--bg-input);
    color: var(--text-primary);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.card-key-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    background: white;
}

.card-key-input::placeholder {
    color: var(--text-muted);
}

.card-key-status {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: var(--radius-medium);
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.card-key-status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.card-key-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.card-key-status.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.card-key-modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.card-key-modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.card-key-modal-btn.primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

.card-key-modal-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-key-modal-btn.primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.card-key-modal-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color-light);
}

.card-key-modal-btn.secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .card-key-modal-content {
        padding: 24px 20px;
        margin: 20px;
        width: calc(100% - 40px);
        max-width: none;
    }
    
    .card-key-modal h3 {
        font-size: 1.3rem;
        margin-bottom: 16px;
    }
    
    .card-key-input {
        padding: 12px 16px;
        font-size: 1rem;
        margin-bottom: 16px;
    }
    
    .modal-buttons {
        flex-direction: column;
        gap: 12px;
    }
    
    .modal-buttons .btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .card-key-modal-content {
        padding: 20px 16px;
        margin: 16px;
    }
} 