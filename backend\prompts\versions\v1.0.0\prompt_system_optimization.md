# 提示词系统优化方案

## 一、架构优化

### 1.1 统一文件格式
建议统一使用JSON格式，支持更好的结构化和扩展性：

```json
{
  "version": "1.0.0",
  "dimension": "性格",
  "metadata": {
    "author": "system",
    "created_at": "2025-01-30",
    "description": "性格分析提示词模板"
  },
  "base_template": "common_personality",
  "system_prompt": {
    "role": "你是一位专业的八字命理分析师",
    "constraints": [
      "必须严格按照十神理论进行分析",
      "禁止使用五行理论",
      "只能根据八字中实际出现的十神进行分析"
    ]
  },
  "analysis_template": {
    "structure": [
      {
        "section": "总论",
        "title": "## 一、{dimension}总论",
        "content": "**核心特点：** {核心结论}\n\n{总体分析内容}"
      },
      {
        "section": "详细分析", 
        "title": "## 二、十神{dimension}特质分析",
        "subsections": [
          {
            "title": "### （一）外在表现（天干十神）",
            "prompt": "严格根据天干中出现的十神，分析此人的外在{dimension}表现"
          },
          {
            "title": "### （二）内在特质（地支十神）", 
            "prompt": "严格根据地支藏干中出现的十神，分析此人的内在{dimension}特质"
          }
        ]
      }
    ]
  },
  "output_format": {
    "format": "markdown",
    "max_length": 800,
    "tone": "professional",
    "person": "third_person"
  }
}
```

### 1.2 层次化提示词架构

```
prompts/
├── base/                    # 基础模板
│   ├── system_prompts.json  # 系统级提示词
│   ├── common_templates.json # 通用模板
│   └── constraints.json     # 全局约束
├── dimensions/              # 维度特定提示词
│   ├── personality.json     # 性格分析
│   ├── career.json         # 职业分析
│   ├── health.json         # 健康分析
│   └── ...
├── formats/                 # 输出格式模板
│   ├── markdown.json       # Markdown格式
│   ├── structured.json     # 结构化格式
│   └── summary.json        # 摘要格式
└── versions/               # 版本管理
    ├── v1.0/
    ├── v1.1/
    └── current -> v1.1/
```

### 1.3 提示词管理器类设计

```python
class PromptManager:
    def __init__(self, version="current"):
        self.version = version
        self.cache = {}
        self.base_prompts = self._load_base_prompts()
    
    def get_prompt(self, dimension, bazi_info, **kwargs):
        """获取完整的分析提示词"""
        # 1. 加载维度特定提示词
        # 2. 继承基础模板
        # 3. 应用约束和格式
        # 4. 组装最终提示词
        pass
    
    def validate_prompt(self, prompt_config):
        """验证提示词配置的有效性"""
        pass
    
    def update_prompt(self, dimension, new_config):
        """更新提示词配置"""
        pass
```

## 二、内容质量优化

### 2.1 提示词结构标准化
- **明确的角色定义**：系统角色、专业背景、分析原则
- **清晰的任务描述**：分析目标、输出要求、质量标准
- **具体的约束条件**：禁止事项、必须遵守的规则
- **标准化的输出格式**：结构化的Markdown模板

### 2.2 逻辑链条优化
当前提示词逻辑：
```
八字信息 → 维度约束 → 分析要求 → 格式要求
```

优化后的逻辑链条：
```
系统角色设定 → 八字信息解析 → 十神理论应用 → 维度聚焦分析 → 结构化输出
```

### 2.3 指令明确性提升
- 使用具体的动词：分析、识别、评估、建议
- 避免模糊表述：将"请分析"改为"请根据天干十神配置，具体分析..."
- 增加示例：提供正确和错误的分析示例

## 三、管理系统改进

### 3.1 版本控制机制
```python
class PromptVersionManager:
    def create_version(self, version_name, base_version=None):
        """创建新版本"""
        pass
    
    def rollback_version(self, version_name):
        """回滚到指定版本"""
        pass
    
    def compare_versions(self, v1, v2):
        """比较版本差异"""
        pass
```

### 3.2 动态加载和缓存
```python
class PromptCache:
    def __init__(self, cache_size=100, ttl=3600):
        self.cache = LRUCache(cache_size)
        self.ttl = ttl
    
    def get_cached_prompt(self, key):
        """获取缓存的提示词"""
        pass
    
    def invalidate_cache(self, pattern=None):
        """清除缓存"""
        pass
```

### 3.3 A/B测试支持
```python
class PromptABTester:
    def create_experiment(self, name, variants):
        """创建A/B测试实验"""
        pass
    
    def get_variant(self, user_id, experiment_name):
        """获取用户对应的变体"""
        pass
    
    def record_result(self, user_id, experiment_name, result):
        """记录测试结果"""
        pass
```

## 四、效果评估机制

### 4.1 质量指标定义
- **准确性**：分析结果与十神理论的符合度
- **完整性**：是否覆盖了所有必要的分析维度
- **一致性**：同样的八字数据产生一致的分析结果
- **可读性**：输出格式的规范性和易读性

### 4.2 自动化评估
```python
class PromptEvaluator:
    def evaluate_accuracy(self, prompt_result, expected_shishen):
        """评估十神使用的准确性"""
        pass
    
    def evaluate_completeness(self, prompt_result, required_sections):
        """评估分析完整性"""
        pass
    
    def evaluate_consistency(self, results_list):
        """评估结果一致性"""
        pass
```

### 4.3 反馈收集机制
- 用户满意度评分
- 专家评审结果
- 系统性能指标（响应时间、成功率）
- 错误日志分析

## 五、实施建议

### 5.1 分阶段实施
1. **第一阶段**：统一文件格式，建立基础架构
2. **第二阶段**：实现提示词管理器和缓存机制
3. **第三阶段**：添加版本控制和A/B测试功能
4. **第四阶段**：建立评估和反馈机制

### 5.2 向后兼容
- 保留现有提示词文件作为备份
- 提供格式转换工具
- 渐进式迁移，确保系统稳定性

### 5.3 监控和维护
- 建立提示词性能监控
- 定期评估和优化
- 建立提示词更新流程

## 六、迁移指南

### 6.1 现有系统分析
当前系统使用的是混合格式的提示词文件：
- 文本文件（.txt）：如 `personality.txt`, `career.txt`
- JSON文件：如 `pattern.json`（虽然索引中提到，但实际可能不存在）
- 索引文件：`index.json` 管理文件映射

### 6.2 迁移步骤

#### 步骤1：备份现有系统
```bash
# 创建备份目录
mkdir -p backend/prompts/backup_$(date +%Y%m%d)
# 备份所有提示词文件
cp -r backend/prompts/*.txt backend/prompts/*.json backend/prompts/backup_$(date +%Y%m%d)/
```

#### 步骤2：安装新组件
```python
# 将新的组件文件放置到backend目录
# - prompt_manager.py
# - prompt_evaluator.py
# - prompt_version_manager.py
# - optimized_bazi_analyzer.py
```

#### 步骤3：创建版本管理结构
```bash
mkdir -p backend/prompts/versions/v1.0
mkdir -p backend/prompts/base
mkdir -p backend/prompts/formats
```

#### 步骤4：转换现有提示词
使用转换脚本将现有的.txt文件转换为标准JSON格式：

```python
# 示例转换脚本
def convert_txt_to_json(txt_file, dimension):
    with open(txt_file, 'r', encoding='utf-8') as f:
        content = f.read()

    json_config = {
        "version": "1.0.0",
        "dimension": dimension,
        "metadata": {
            "converted_from": txt_file,
            "converted_at": datetime.now().isoformat()
        },
        "content": content,
        "format": "text"
    }

    json_file = txt_file.replace('.txt', '.json')
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_config, f, ensure_ascii=False, indent=2)
```

#### 步骤5：渐进式替换
1. 首先在测试环境中使用 `OptimizedBaziAnalyzer`
2. 对比新旧系统的输出质量
3. 逐步在生产环境中启用新系统

#### 步骤6：配置评估和监控
```python
# 在现有的bazi_analyzer.py中添加评估功能
from prompt_evaluator import PromptEvaluator

class BaziAnalyzer:
    def __init__(self):
        self.evaluator = PromptEvaluator()

    def analyze_dimension(self, dimension):
        # 现有分析逻辑...
        result = self.existing_analysis_logic(dimension)

        # 添加质量评估
        evaluation = self.evaluator.evaluate_analysis_result(
            result, self.bazi_data, dimension
        )

        # 记录评估结果用于优化
        self.log_evaluation(dimension, evaluation)

        return result
```

### 6.3 兼容性处理

#### 保持API兼容性
```python
# 在现有的app.py中添加兼容层
from optimized_bazi_analyzer import OptimizedBaziAnalyzer

class CompatibilityLayer:
    def __init__(self):
        self.old_analyzer = BaziAnalyzer()  # 现有分析器
        self.new_analyzer = OptimizedBaziAnalyzer()  # 新分析器
        self.use_new_system = False  # 开关控制

    def analyze_dimension(self, dimension, **kwargs):
        if self.use_new_system:
            return self.new_analyzer.analyze_dimension(dimension, **kwargs)
        else:
            return self.old_analyzer.analyze_dimension(dimension)
```

#### 配置开关
```json
{
  "prompt_system": {
    "use_optimized_system": false,
    "enable_evaluation": true,
    "enable_ab_testing": false,
    "fallback_to_old_system": true
  }
}
```

### 6.4 测试和验证

#### 功能测试
```bash
# 运行测试脚本
python backend/test_optimized_prompt_system.py
```

#### 质量对比测试
```python
# 对比新旧系统的输出质量
def compare_systems():
    old_analyzer = BaziAnalyzer()
    new_analyzer = OptimizedBaziAnalyzer()

    test_cases = load_test_cases()

    for test_case in test_cases:
        old_result = old_analyzer.analyze_dimension("性格")
        new_result = new_analyzer.analyze_dimension("性格")

        # 比较结果质量
        compare_quality(old_result, new_result)
```

### 6.5 监控和回滚

#### 性能监控
- 监控分析质量评分变化
- 监控API响应时间
- 监控错误率

#### 回滚机制
```python
# 如果新系统出现问题，快速回滚
def emergency_rollback():
    config = load_config()
    config["prompt_system"]["use_optimized_system"] = False
    config["prompt_system"]["fallback_to_old_system"] = True
    save_config(config)

    # 重启相关服务
    restart_services()
```

### 6.6 培训和文档

#### 开发团队培训
- 新系统架构介绍
- 提示词管理最佳实践
- 版本控制和A/B测试使用方法

#### 文档更新
- 更新API文档
- 更新部署文档
- 创建故障排除指南

## 七、预期收益

### 7.1 质量提升
- 提示词使用准确性提升20-30%
- 输出格式规范性提升40-50%
- 分析逻辑一致性提升25-35%

### 7.2 效率提升
- 提示词管理效率提升60%
- 版本迭代速度提升50%
- 问题定位和修复时间减少40%

### 7.3 可维护性提升
- 代码可维护性显著改善
- 新功能开发速度提升
- 系统稳定性增强
