/**
 * 报告摘要生成
 * 处理报告摘要的提取和显示
 */

// 性别标准化函数
HistoryReportsManager.prototype.normalizeGender = function(genderValue) {
    if (typeof genderValue === 'number') {
        return genderValue === 1 ? '男' : '女';
    } else if (typeof genderValue === 'string') {
        if (genderValue === '1' || genderValue.toLowerCase() === 'male' || genderValue === '男') {
            return '男';
        } else if (genderValue === '0' || genderValue.toLowerCase() === 'female' || genderValue === '女') {
            return '女';
        }
    }
    return '';
};

// 从多个数据源提取性别信息
HistoryReportsManager.prototype.extractGender = function(data, resultData, requestData, basicInfo, bz) {
    const sources = [data, resultData, requestData, basicInfo, bz];
    for (const source of sources) {
        if (source && source.gender !== undefined) {
            const normalized = this.normalizeGender(source.gender);
            if (normalized) return normalized;
        }
    }
    return '';
};

// 从多个数据源提取出生日期信息
HistoryReportsManager.prototype.extractBirthDate = function(data, resultData, requestData, basicInfo, bz) {
    const sources = [data, resultData, requestData, basicInfo, bz];
    for (const source of sources) {
        if (source && source.birthDate) {
            return source.birthDate;
        }
    }
    return '';
};

// 生成报告摘要
HistoryReportsManager.prototype.generateReportSummary = function(data) {
    if (!data) {
        return '<div class="summary-placeholder">暂无摘要数据</div>';
    }
    
    try {
        // 生成报告摘要
        
        // 如果data包含resultData，优先使用resultData
        let reportData = data;
        if (data.resultData) {
            reportData = data.resultData;
        } else if (data.data) {
            reportData = data.data;
        }
        
        // 首先尝试从原始请求数据中获取出生年月日和性别
        let originalRequestData = null;
        
        // 检查各种可能的路径
        if (reportData.requestData) {
            originalRequestData = reportData.requestData;
            // 从reportData.requestData获取原始请求数据
        } else if (data.requestData) {
            originalRequestData = data.requestData;
            // 从data.requestData获取原始请求数据
        } else if (data.data && data.data.requestData) {
            originalRequestData = data.data.requestData;
            // 从data.data.requestData获取原始请求数据
        } else if (reportData.resultData && reportData.resultData.requestData) {
            originalRequestData = reportData.resultData.requestData;
            // 从reportData.resultData.requestData获取原始请求数据
        }
        
        // 如果找到原始请求数据，优先使用
        if (originalRequestData) {
            // 找到原始请求数据
            let summaryParts = [];
            
            // 提取出生日期
            const year = originalRequestData.year;
            const month = originalRequestData.month;
            const day = originalRequestData.day;
            
            if (year && month && day) {
                summaryParts.push(`出生日期: ${year}年${month}月${day}日`);
            }
            
            // 提取性别
            if (originalRequestData.gender) {
                let gender = originalRequestData.gender;
                let genderText = '';
                
                if (typeof gender === 'string') {
                    if (gender === '1' || gender.toLowerCase() === 'male' || gender === '男') {
                        genderText = '男';
                    } else if (gender === '2' || gender.toLowerCase() === 'female' || gender === '女') {
                        genderText = '女';
                    } else {
                        genderText = gender;
                    }
                } else if (gender === 1) {
                    genderText = '男';
                } else if (gender === 2) {
                    genderText = '女';
                }
                
                if (genderText) {
                    summaryParts.push(`性别: ${genderText}`);
                }
            }
            
            if (summaryParts.length > 0) {
                return `<div class="summary-info">${summaryParts.join(' | ')}</div>`;
            }
        }
        
        // 检查是否有summary字段
        if (reportData.summary) {
            // 找到summary字段
            
            if (typeof reportData.summary === 'string') {
                // 如果summary是字符串，直接返回摘要的前100个字符
                const summaryText = reportData.summary.substring(0, 100);
                return `<div class="summary-text">${summaryText}${reportData.summary.length > 100 ? '...' : ''}</div>`;
            } else if (typeof reportData.summary === 'object') {
                // 如果summary是对象，查找关键点或内容
                // summary对象字段
                
                if (reportData.summary.key_points && Array.isArray(reportData.summary.key_points)) {
                    // 显示前三个关键点
                    const points = reportData.summary.key_points.slice(0, 3);
                    return points.map(point => `<div class="summary-point">• ${point}</div>`).join('');
                } else if (reportData.summary.content) {
                    // 显示content字段的摘要
                    return `<div class="summary-text">${reportData.summary.content.substring(0, 100)}...</div>`;
                }
            }
        }
        
        // 尝试从basicInfo中提取信息
        if (reportData.basicInfo) {
            // 从basicInfo提取信息
            const info = reportData.basicInfo;
            let summaryParts = [];
            
            if (info.name) {
                summaryParts.push(`姓名: ${info.name}`);
            }
            
            if (info.gender || info.sex) {
                const gender = info.gender || info.sex;
                let genderText = '';
                if (typeof gender === 'string') {
                    if (gender.toLowerCase() === 'male' || gender === '男' || gender === '1') {
                        genderText = '男';
                    } else if (gender.toLowerCase() === 'female' || gender === '女' || gender === '2') {
                        genderText = '女';
                    } else {
                        genderText = gender;
                    }
                } else if (gender === 1) {
                    genderText = '男';
                } else if (gender === 2) {
                    genderText = '女';
                }
                
                if (genderText) {
                    summaryParts.push(`性别: ${genderText}`);
                }
            }
            
            if (info.birthDate || info.birth_date) {
                summaryParts.push(`出生日期: ${info.birthDate || info.birth_date}`);
            }
            
            if (summaryParts.length > 0) {
                return `<div class="summary-info">${summaryParts.join(' | ')}</div>`;
            }
        }
        
        // 尝试直接从顶层属性获取信息
        let topLevelSummary = [];
        
        if (data.birthDate || data.birth_date) {
            topLevelSummary.push(`出生日期: ${data.birthDate || data.birth_date}`);
        }
        
        if (data.gender || data.sex) {
            const gender = data.gender || data.sex;
            let genderText = '';
            
            if (typeof gender === 'string') {
                if (gender.toLowerCase() === 'male' || gender === '男' || gender === '1') {
                    genderText = '男';
                } else if (gender.toLowerCase() === 'female' || gender === '女' || gender === '2') {
                    genderText = '女';
                } else {
                    genderText = gender;
                }
            } else if (gender === 1) {
                genderText = '男';
            } else if (gender === 2) {
                genderText = '女';
            }
            
            if (genderText) {
                topLevelSummary.push(`性别: ${genderText}`);
            }
        }
        
        if (topLevelSummary.length > 0) {
            return `<div class="summary-info">${topLevelSummary.join(' | ')}</div>`;
        }
        
        // 尝试从bz信息中提取关键信息 - 调整到较低优先级
        if (reportData.bz) {
            // 从bz字段提取信息
            let bzSummary = '';
            
            // 添加出生时间和性别信息
            let birthInfo = '';
            
            // 首先尝试从bz[8]字段提取农历出生时间
            const birthTimeString = reportData.bz["8"] || reportData.bz[8];
            if (birthTimeString && typeof birthTimeString === 'string') {
                // 从bz[8]提取农历时间
                // 直接使用农历时间字符串，例如："1933年二月十一 子时"
                birthInfo = birthTimeString;
            } else if (originalRequestData && originalRequestData.year && originalRequestData.month && originalRequestData.day) {
                const hour = originalRequestData.hour || 0;
                let timeText = `${originalRequestData.year}年${originalRequestData.month}月${originalRequestData.day}日`;
                
                // 如果有小时信息，添加时辰
                if (hour || hour === 0) {
                    // 转换小时为时辰
                    const timeNames = ['子时(23:00-01:00)', '丑时(01:00-03:00)', '寅时(03:00-05:00)', 
                                     '卯时(05:00-07:00)', '辰时(07:00-09:00)', '巳时(09:00-11:00)', 
                                     '午时(11:00-13:00)', '未时(13:00-15:00)', '申时(15:00-17:00)', 
                                     '酉时(17:00-19:00)', '戌时(19:00-21:00)', '亥时(21:00-23:00)'];
                    
                    // 计算时辰索引 (0-11)
                    const timeIndex = Math.floor((hour % 24) / 2);
                    timeText += ` ${timeNames[timeIndex]}`;
                }
                
                birthInfo += timeText;
            } else if (data.birthDate || data.birth_date || (reportData.basicInfo && (reportData.basicInfo.birthDate || reportData.basicInfo.birth_date))) {
                // 使用已存在的出生日期字段
                const birthDate = data.birthDate || data.birth_date || 
                                 (reportData.basicInfo && (reportData.basicInfo.birthDate || reportData.basicInfo.birth_date));
                birthInfo += birthDate;
            }
            
            // 提取性别信息 - 使用公共函数
            let gender = '';
            
            // 首先尝试从reportData.sex字段提取性别（这是八字API返回的标准字段）
            if (reportData.sex !== undefined) {
                // 从reportData.sex提取性别
                gender = this.normalizeGender(reportData.sex);
            } else {
                // 使用提取函数从多个数据源获取性别
                const sources = {
                    originalRequestData: originalRequestData,
                    data: data,
                    reportDataBasicInfo: reportData.basicInfo
                };
                
                if (originalRequestData && originalRequestData.gender) {
                    gender = this.normalizeGender(originalRequestData.gender);
                } else {
                    gender = this.extractGender(data, reportData, null, reportData.basicInfo, null);
                }
            }
            
            // 添加出生信息和性别到摘要
            if (birthInfo || gender) {
                let summaryParts = [];
                if (birthInfo) {
                    summaryParts.push(birthInfo);
                }
                if (gender) {
                    summaryParts.push(`性别: ${gender}`);
                }
                
                if (summaryParts.length > 0) {
                    return `<div class="summary-info">${summaryParts.join(' | ')}</div>`;
                }
            }
        }
        
        // 如果都没有找到合适的信息，尝试构建基本的出生时间和性别信息
        let fallbackSummary = [];
        
        // 优先从originalRequestData提取年月日信息
        if (originalRequestData && originalRequestData.year && originalRequestData.month && originalRequestData.day) {
            const birthDateFromRequest = `${originalRequestData.year}年${originalRequestData.month}月${originalRequestData.day}日`;
            fallbackSummary.push(`出生时间: ${birthDateFromRequest}`);
        } else {
            // 使用提取函数从多个数据源获取出生时间
            const birthInfo = this.extractBirthDate(data, reportData, null, data.data && data.data.basicInfo, null);
            
            if (birthInfo) {
                fallbackSummary.push(`出生时间: ${birthInfo}`);
            }
        }
        
        // 优先从originalRequestData提取性别信息 - 使用公共函数
        let genderInfo = '';
        if (originalRequestData && originalRequestData.gender) {
            genderInfo = this.normalizeGender(originalRequestData.gender);
        } else {
            // 使用提取函数从多个数据源获取性别
            genderInfo = this.extractGender(data, reportData, null, data.data && data.data.basicInfo, null);
        }
        
        if (genderInfo) {
            fallbackSummary.push(`性别: ${genderInfo}`);
        }
        
        // 如果找到了出生时间或性别信息，显示它们
        if (fallbackSummary.length > 0) {
            return `<div class="summary-info">${fallbackSummary.join(' | ')}</div>`;
        }
        
        // 如果完全没有找到任何信息，尝试显示报告ID或时间戳
        let fallbackInfo = '';
        if (data.id) {
            fallbackInfo = `报告ID: ${data.id}`;
        } else if (data.timestamp) {
            const date = new Date(data.timestamp);
            fallbackInfo = `创建时间: ${date.toLocaleDateString()}`;
        } else if (data.completedTime) {
            fallbackInfo = `完成时间: ${data.completedTime}`;
        }
        
        if (fallbackInfo) {
            return `<div class="summary-info">${fallbackInfo}</div>`;
        }
        
        return `<div class="summary-placeholder">暂无基本信息</div>`;
    } catch (error) {
        // 生成报告摘要时出错
        return `<div class="summary-error">无法生成摘要</div>`;
    }
};

// 调试函数已移除 - 生产环境不需要