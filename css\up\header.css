/* 页面头部样式 */
.page-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.header-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    display: inline-block;
    animation: pulse 2s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    position: relative;
}

.page-header h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--hero-gradient);
    border-radius: 2px;
}

.header-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 400;
    opacity: 0.8;
    letter-spacing: 0.5px;
} 