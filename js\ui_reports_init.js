/**
 * 报告页面初始化
 * 处理报告页面的初始化逻辑
 */

// 初始化模块日志记录器
const initLogger = window.BaziLogger ? window.BaziLogger.forModule('ReportsInit') : {
    debug: console.log,
    info: console.log,
    warn: console.warn,
    error: console.error
};

// 验证分析是否还在进行
async function verifyAnalysisStillRunning(analysis) {
    try {
        const apiUrl = window.getApiBaseUrl ? window.getApiBaseUrl() : 'http://localhost:5000';
        const webhookUrl = `${apiUrl}/api/webhook/status/${analysis.requestId}`;

        initLogger.debug('验证分析状态，请求URL:', webhookUrl);

        const response = await fetch(webhookUrl);
        if (!response.ok) {
            initLogger.warn('状态验证请求失败:', response.status);
            return false;
        }

        const statusData = await response.json();
        initLogger.debug('获取到的状态数据:', statusData);

        // 检查是否已完成
        if (statusData.completed === true) {
            initLogger.info('分析已完成，不需要继续轮询');
            return false;
        }

        // 检查是否有有效的进行中状态
        const isRunning = statusData.status === 'processing' ||
                         statusData.status === 'llm_analyzing' ||
                         statusData.status === 'queued' ||
                         (statusData.llm_progress && statusData.llm_progress.stage === 'analyzing');

        initLogger.info('分析状态验证结果:', isRunning ? '仍在进行' : '已停止');
        return isRunning;

    } catch (error) {
        initLogger.error('验证分析状态失败:', error);
        // 如果验证失败，为了安全起见，假设分析已停止
        return false;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    initLogger.info('页面加载完成，开始初始化...');
    await initializeReportPage();
});

// 初始化报告页面
async function initializeReportPage() {
    initLogger.info('开始初始化报告页面...');
    
    // 确保全局变量正确定义
    if (!window.hasOwnProperty('currentCardKey')) {
        window.currentCardKey = '';
    }
    
    // 检查是否需要刷新报告列表（由saveAnalysisResult或startPollingAnalysis设置）
    const needRefreshReports = localStorage.getItem('needRefreshReports');
    if (needRefreshReports === 'true') {
        initLogger.info('检测到需要刷新报告列表的标志...');
        // 清除标志
        localStorage.removeItem('needRefreshReports');
        // 清除所有报告缓存
        const cacheKeys = Object.keys(sessionStorage).filter(key => key.startsWith('reports_cache_'));
        cacheKeys.forEach(key => sessionStorage.removeItem(key));
        initLogger.debug('已清除所有报告缓存，强制重新获取数据');
    }
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const requestId = urlParams.get('requestId');
    let cardKey = urlParams.get('cardKey');
    
    // 检查本地存储中的多卡密缓存
    const validatedCards = getValidatedCards();
    const cacheExpiry = 24 * 60 * 60 * 1000; // 24小时过期
    
    // 如果URL中没有卡密，尝试使用最近验证的卡密
    if (!cardKey && validatedCards.length > 0) {
        // 使用最近验证的卡密
        const latestCard = validatedCards[0];
        if ((Date.now() - latestCard.timestamp) < cacheExpiry) {
            initLogger.info('使用最近缓存的卡密:', latestCard.cardKey);
            cardKey = latestCard.cardKey;
            
            // 更新URL中的卡密参数
            const newUrl = new URL(window.location);
            newUrl.searchParams.set('cardKey', cardKey);
            if (requestId) {
                newUrl.searchParams.set('requestId', requestId);
            }
            window.history.replaceState({}, '', newUrl.toString());
        }
    }
    
    // 如果没有卡密，要求用户输入
    if (!cardKey) {
        try {
            cardKey = await showCardKeyModal();
            
            // 缓存验证成功的卡密
            addValidatedCard(cardKey);
            
            // 更新URL
            const newUrl = new URL(window.location);
            newUrl.searchParams.set('cardKey', cardKey);
            if (requestId) {
                newUrl.searchParams.set('requestId', requestId);
            }
            window.history.replaceState({}, '', newUrl.toString());
        } catch (error) {
            // 用户取消输入或其他错误
            initLogger.info('用户取消输入卡密');
            window.location.href = 'index.html';
            return;
        }
    } else {
        // 验证URL中的卡密
        const cachedCard = validatedCards.find(card => card.cardKey === cardKey);
        const isCardCached = cachedCard && (Date.now() - cachedCard.timestamp) < cacheExpiry;
        
        // 检查是否来自up.html的跳转（通过referrer判断）
        const isFromUpPage = document.referrer && document.referrer.includes('up.html');
        
        if (!isCardCached) {
            if (isFromUpPage) {
                // 如果来自up.html，直接信任卡密并缓存（因为up.html已经验证过了）
                initLogger.info('来自up.html的卡密，直接信任并缓存:', cardKey);
                addValidatedCard(cardKey);
            } else {
                // 其他情况需要验证卡密
                initLogger.info('验证新的卡密...');
                const isValid = await validateCardKey(cardKey);
                if (!isValid) {
                    // 显示错误提示并要求重新输入
                    try {
                        cardKey = await showCardKeyModal();
                        
                        // 缓存验证成功的卡密
                        addValidatedCard(cardKey);
                        
                        // 更新URL
                        const newUrl = new URL(window.location);
                        newUrl.searchParams.set('cardKey', cardKey);
                        if (requestId) {
                            newUrl.searchParams.set('requestId', requestId);
                        }
                        window.history.replaceState({}, '', newUrl.toString());
                    } catch (error) {
                        initLogger.info('用户取消输入卡密');
                        window.location.href = 'index.html';
                        return;
                    }
                } else {
                    // 缓存验证成功的卡密
                    addValidatedCard(cardKey);
                }
            }
        } else {
            initLogger.info('使用已缓存的有效卡密:', cardKey);
        }
    }
    
    // 存储卡密供后续API调用使用
    window.currentCardKey = cardKey;
    initLogger.debug('设置全局卡密变量:', window.currentCardKey);
    
    // 创建报告管理器实例
    window.reportsManager = new HistoryReportsManager();

    // 初始化进度显示状态（隐藏未开始的进度信息）
    if (window.reportsManager.initializeProgressDisplay) {
        window.reportsManager.initializeProgressDisplay();
    }

    // 调试localStorage数据
    if (window.reportsManager.debugLocalStorageReports) {
        window.reportsManager.debugLocalStorageReports();
    }
    
    // 先显示骨架屏
    const reportsContainer = document.getElementById('reportsList');
    if (reportsContainer) {
        window.reportUIHelper.showSkeletonScreen(reportsContainer);
    }
    
    // 从URL参数和本地存储确定分析状态
    let analysis = null;
    let needPolling = false;
    
    // 只有当URL中同时包含requestId和cardKey时才开始分析
    if (requestId && cardKey) {
        initLogger.info('检测到URL中包含完整的分析参数');
        
        // 创建分析信息对象
        analysis = {
            id: requestId,
            type: 'request',
            startTime: Date.now(),
            cardKey: cardKey,
            requestId: requestId
        };
        
        // 保存到localStorage，确保下次也能检测到
        localStorage.setItem('ongoingAnalysis', JSON.stringify(analysis));
        initLogger.debug('已保存分析信息到localStorage:', analysis);
        
        needPolling = true;
    } else {
        // 如果URL中没有参数，检查localStorage中是否有正在进行的分析
        const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
        if (ongoingAnalysis) {
            try {
                analysis = JSON.parse(ongoingAnalysis);
                initLogger.info('从localStorage检测到进行中的分析:', analysis);

                // 检查是否超过30分钟，如果是则认为已失效
                if (Date.now() - analysis.startTime < 30 * 60 * 1000) {
                    initLogger.info('检测到有效期内的分析，验证是否真的还在进行...');

                    // 验证分析是否真的还在进行
                    const isStillRunning = await verifyAnalysisStillRunning(analysis);
                    if (isStillRunning) {
                        initLogger.info('验证通过，分析确实还在进行，恢复进度显示');
                        needPolling = true;
                    } else {
                        initLogger.info('验证失败，分析已完成或不存在，清除状态');
                        localStorage.removeItem('ongoingAnalysis');
                        analysis = null;
                    }
                } else {
                    initLogger.info('检测到的分析已超过30分钟，视为已失效');
                    localStorage.removeItem('ongoingAnalysis');
                    analysis = null;
                }
            } catch (error) {
                initLogger.error('解析进行中分析记录失败:', error);
                localStorage.removeItem('ongoingAnalysis');
                analysis = null;
            }
        }
    }
    
    // 只获取一次报告数据，避免重复请求
    initLogger.info('获取报告数据...');
    const result = await window.reportsManager.fetchHistoryReports(1);
    initLogger.debug('获取到的报告数据:', result);
    
    // 根据分析状态选择显示方式
    if (analysis) {
        // 显示分析状态和历史报告
        await displayAnalysisAndReports(analysis, result);
        
        // 如果需要轮询，启动轮询
        if (needPolling) {
            window.reportsManager.startPollingAnalysis(analysis);
        }
    } else {
        // 直接显示报告列表
        await displayReportsOnly(result);
    }
}