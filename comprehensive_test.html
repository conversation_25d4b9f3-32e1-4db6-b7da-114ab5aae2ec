<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合问题诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 综合问题诊断工具</h1>
        
        <div class="section">
            <h3>🎯 快速诊断</h3>
            <button onclick="runFullDiagnosis()">运行完整诊断</button>
            <button onclick="clearAllData()">清理所有缓存</button>
            <button onclick="testNewRequest()">测试新请求</button>
            <div id="quickStatus"></div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>📡 API 测试</h3>
                <button onclick="testAPI()">测试 API 响应</button>
                <pre id="apiResult">等待测试...</pre>
            </div>
            
            <div class="section">
                <h3>💾 缓存状态</h3>
                <button onclick="checkCache()">检查缓存</button>
                <pre id="cacheResult">等待检查...</pre>
            </div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>🔧 前端逻辑测试</h3>
                <button onclick="testFrontendLogic()">测试前端逻辑</button>
                <pre id="frontendResult">等待测试...</pre>
            </div>
            
            <div class="section">
                <h3>📊 数据流分析</h3>
                <button onclick="analyzeDataFlow()">分析数据流</button>
                <pre id="dataFlowResult">等待分析...</pre>
            </div>
        </div>
        
        <div class="section">
            <h3>🎯 问题汇总</h3>
            <div id="problemSummary">
                <div class="status info">
                    <strong>已知问题列表：</strong>
                    <ol>
                        <li>数据传递错误（localStorage缓存问题）</li>
                        <li>前端显示"正在加载分析结果..."</li>
                        <li>JavaScript语法错误（export问题）</li>
                        <li>用户希望立即显示八字排盘</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function runFullDiagnosis() {
            showStatus('quickStatus', '🔍 正在运行完整诊断...', 'info');
            
            const results = [];
            
            // 1. 检查JavaScript错误
            results.push('=== JavaScript错误检查 ===');
            try {
                // 检查关键函数是否存在
                const functions = [
                    'generateBaziInfoSection',
                    'initializeToggleFeatures',
                    'toggleSection',
                    'downloadReport'
                ];
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        results.push(`✅ ${funcName}: 存在`);
                    } else {
                        results.push(`❌ ${funcName}: 不存在`);
                    }
                });
            } catch (error) {
                results.push(`❌ JavaScript检查失败: ${error.message}`);
            }
            
            // 2. 检查API状态
            results.push('\n=== API状态检查 ===');
            try {
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const data = await response.json();
                results.push(`✅ API响应状态: ${response.status}`);
                results.push(`📊 completed: ${data.completed}`);
                results.push(`📊 processing: ${data.processing}`);
                results.push(`📊 有result.data: ${!!(data.result && data.result.data)}`);
            } catch (error) {
                results.push(`❌ API检查失败: ${error.message}`);
            }
            
            // 3. 检查缓存状态
            results.push('\n=== 缓存状态检查 ===');
            results.push(`📦 localStorage项目数: ${localStorage.length}`);
            results.push(`📦 sessionStorage项目数: ${sessionStorage.length}`);
            
            const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
            if (ongoingAnalysis) {
                try {
                    const analysis = JSON.parse(ongoingAnalysis);
                    results.push(`⚠️ 发现旧的分析缓存: ${analysis.cardKey}`);
                    if (analysis.formData) {
                        results.push(`   - 缓存的日期: ${analysis.formData.year}-${analysis.formData.month}-${analysis.formData.day}`);
                    }
                } catch (e) {
                    results.push(`❌ 分析缓存解析失败`);
                }
            } else {
                results.push(`✅ 无旧的分析缓存`);
            }
            
            // 4. 前端逻辑检查
            results.push('\n=== 前端逻辑检查 ===');
            try {
                // 模拟渐进式加载器逻辑
                const testData = {
                    completed: true,
                    processing: false,
                    result: {
                        data: {
                            bz: { '8': '测试数据' }
                        }
                    }
                };
                
                const isProcessing = testData.processing === true || (testData.completed === false && testData.status === 'processing');
                const isCompleted = testData.completed === true;
                const hasData = testData.result && testData.result.data;
                
                results.push(`🔧 处理中条件: ${isProcessing}`);
                results.push(`🔧 完成条件: ${isCompleted}`);
                results.push(`🔧 有数据条件: ${hasData}`);
                
                if (isCompleted && hasData) {
                    results.push(`✅ 逻辑正确: 应该显示完整数据`);
                } else {
                    results.push(`❌ 逻辑异常: 条件不匹配`);
                }
            } catch (error) {
                results.push(`❌ 前端逻辑检查失败: ${error.message}`);
            }
            
            showStatus('quickStatus', `<pre>${results.join('\n')}</pre>`, 'info');
        }
        
        async function testAPI() {
            try {
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const data = await response.json();
                document.getElementById('apiResult').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiResult').textContent = `错误: ${error.message}`;
            }
        }
        
        function checkCache() {
            const cache = {
                localStorage: {},
                sessionStorage: {},
                summary: {
                    localCount: localStorage.length,
                    sessionCount: sessionStorage.length
                }
            };
            
            // 检查关键缓存项
            const keyItems = ['ongoingAnalysis', 'baziReports', 'validatedCards', 'latestBaziResult'];
            keyItems.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    try {
                        cache.localStorage[key] = JSON.parse(value);
                    } catch (e) {
                        cache.localStorage[key] = value;
                    }
                }
            });
            
            document.getElementById('cacheResult').textContent = JSON.stringify(cache, null, 2);
        }
        
        async function testFrontendLogic() {
            const results = [];
            
            try {
                // 获取真实API数据
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const result = await response.json();
                
                results.push('=== 前端逻辑测试 ===');
                results.push(`原始数据: ${JSON.stringify(result, null, 2)}`);
                results.push('\n=== 条件判断 ===');
                
                // 模拟 progressive_loader.js 的逻辑
                if (result.processing === true || (result.completed === false && result.status === 'processing')) {
                    results.push('⏳ 匹配处理中条件');
                    if (result.result && result.result.data) {
                        results.push('✅ 有基础数据 -> 应该显示排盘');
                    } else {
                        results.push('❌ 无基础数据 -> 显示等待');
                    }
                } else if (result.completed === true) {
                    results.push('✅ 匹配完成条件');
                    if (result.result && result.result.data) {
                        results.push('✅ 有完整数据 -> 应该显示完整结果');
                    } else {
                        results.push('❌ 无数据 -> 异常状态');
                    }
                } else {
                    results.push('❓ 未知状态');
                }
                
            } catch (error) {
                results.push(`❌ 测试失败: ${error.message}`);
            }
            
            document.getElementById('frontendResult').textContent = results.join('\n');
        }
        
        function analyzeDataFlow() {
            const analysis = [
                '=== 数据流分析 ===',
                '1. 用户输入 -> 前端表单',
                '2. 前端表单 -> getFormData()',
                '3. getFormData() -> localStorage缓存检查',
                '4. 数据 -> sendBaziRequest()',
                '5. 后端处理 -> API响应',
                '6. API响应 -> progressive_loader.js',
                '7. progressive_loader -> 条件判断',
                '8. 条件判断 -> 显示结果',
                '',
                '=== 可能的问题点 ===',
                '❓ localStorage缓存干扰',
                '❓ 前端条件判断错误',
                '❓ API数据结构不匹配',
                '❓ JavaScript语法错误阻断执行'
            ];
            
            document.getElementById('dataFlowResult').textContent = analysis.join('\n');
        }
        
        function clearAllData() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showStatus('quickStatus', '✅ 所有缓存数据已清理', 'success');
                
                setTimeout(() => {
                    if (confirm('缓存已清理，是否刷新页面？')) {
                        window.location.reload();
                    }
                }, 2000);
            } catch (error) {
                showStatus('quickStatus', `❌ 清理失败: ${error.message}`, 'error');
            }
        }
        
        async function testNewRequest() {
            showStatus('quickStatus', '🧪 测试新请求流程...', 'info');
            
            // 模拟新的表单数据
            const newFormData = {
                cardKey: 'Xs1MR9iVx9RNZOk6',
                year: '2004',
                month: '1',
                day: '30',
                hour: '申时',
                gender: '0',
                inputMode: 'solar'
            };
            
            try {
                // 清理旧缓存
                localStorage.removeItem('ongoingAnalysis');
                
                // 模拟发送请求
                const response = await fetch('/webhook/bazi-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(newFormData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('quickStatus', `✅ 新请求提交成功！请求ID: ${result.requestId}`, 'success');
                } else {
                    showStatus('quickStatus', `❌ 新请求失败: ${result.error}`, 'error');
                }
                
            } catch (error) {
                showStatus('quickStatus', `❌ 测试新请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动运行诊断
        window.addEventListener('load', () => {
            setTimeout(runFullDiagnosis, 1000);
        });
    </script>
</body>
</html>
