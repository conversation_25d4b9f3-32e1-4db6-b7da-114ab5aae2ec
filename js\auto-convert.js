/**
 * 自动阳历转换功能
 * 当用户完成所有出生信息输入后，自动触发阳历到农历的转换
 */

document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本加载完成
    setTimeout(initAutoConvert, 2000);
});

/**
 * 初始化自动转换功能
 */
function initAutoConvert() {
    // 阳历输入模式下的年月日时字段
    const yearInput = document.getElementById('yearInput');
    const monthInput = document.getElementById('monthInput');
    const dayInput = document.getElementById('dayInput');
    const hourInput = document.getElementById('hourInput');
    
    if (!yearInput || !monthInput || !dayInput || !hourInput) {
        console.log('自动转换功能：无法找到必要的输入字段');
        return;
    }
    
    // 确保所有字段都有name属性（解决不可聚焦问题）
    ensureNameAttributes();
    
    // 为每个字段添加change事件监听
    const checkAndConvert = () => {
        // 检查所有字段是否已填写
        if (yearInput.value && monthInput.value && dayInput.value && hourInput.value) {
            console.log('所有出生信息已填写完成，自动进行阳历转换');
            
            // 如果baziIntegration对象存在，自动调用其performLunarConvert方法
            if (window.baziIntegration && typeof window.baziIntegration.performLunarConvert === 'function') {
                try {
                    // 尝试调用农历转换函数，使用静默模式
                    window.baziIntegration.performLunarConvert(true);
                    console.log('自动阳历转换已触发（静默模式）');
                } catch (error) {
                    console.error('自动阳历转换失败:', error);
                }
            } else {
                // 如果baziIntegration对象不存在或方法不可用，等待并重试
                console.log('等待八字集成模块加载完成...');
                setTimeout(() => {
                    if (window.baziIntegration && typeof window.baziIntegration.performLunarConvert === 'function') {
                        try {
                            window.baziIntegration.performLunarConvert(true);
                            console.log('延迟自动阳历转换已触发（静默模式）');
                        } catch (error) {
                            console.error('延迟自动阳历转换失败:', error);
                        }
                    } else {
                        console.error('无法找到八字集成模块，自动转换功能不可用');
                    }
                }, 1000);
            }
        }
    };
    
    // 为年月日输入添加事件监听
    yearInput.addEventListener('change', checkAndConvert);
    monthInput.addEventListener('change', checkAndConvert);
    dayInput.addEventListener('change', checkAndConvert);
    
    // 为时辰下拉框添加特殊处理
    hourInput.addEventListener('change', checkAndConvert);
    // 增加选择事件监听，确保选择后立即触发
    hourInput.addEventListener('input', checkAndConvert);
    
    console.log('自动阳历转换功能已初始化');
}

/**
 * 确保表单输入字段都有name属性
 */
function ensureNameAttributes() {
    // 为主要输入字段添加name属性（如果尚未设置）
    const inputFields = [
        { id: 'yearInput', name: 'year' },
        { id: 'monthInput', name: 'month' },
        { id: 'dayInput', name: 'day' },
        { id: 'hourInput', name: 'hour' },
        { id: 'genderInput', name: 'gender' },
        { id: 'cardKeyInput', name: 'cardKey' }
    ];
    
    inputFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (element && !element.hasAttribute('name')) {
            element.setAttribute('name', field.name);
            console.log(`为 ${field.id} 添加 name 属性: ${field.name}`);
        }
    });
} 