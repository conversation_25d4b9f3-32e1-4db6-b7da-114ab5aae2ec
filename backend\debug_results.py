#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from bazi_service import BaziService
from request_manager import RequestManager

# 创建服务实例
bazi_service = BaziService()
request_manager = RequestManager()

print("=== 调试 BaziService Results ===")
print(f"Results 字典大小: {len(bazi_service.results)}")
print(f"Processing Status 字典大小: {len(bazi_service.processing_status)}")

print("\n=== Results 内容 ===")
for key, value in bazi_service.results.items():
    print(f"Key: {key}")
    if isinstance(value, dict):
        print(f"  - success: {value.get('success')}")
        print(f"  - processed_time: {value.get('processed_time')}")
        print(f"  - card_key: {value.get('card_key')}")
        print(f"  - request_id: {value.get('request_id')}")
    print()

print("\n=== Processing Status 内容 ===")
for key, value in bazi_service.processing_status.items():
    print(f"Key: {key}")
    print(f"Value: {value}")
    print()

print("\n=== 检查特定请求ID ===")
request_id = "H1EQXT7L-CED8E8U8-QQFC4O5N-2LHQ1UE3_1748459986_0bd01de0"
card_key = "H1EQXT7L-CED8E8U8-QQFC4O5N-2LHQ1UE3"

print(f"请求ID '{request_id}' 是否在 results 中: {request_id in bazi_service.results}")
print(f"请求ID '{request_id}' 是否在 processing_status 中: {request_id in bazi_service.processing_status}")

# 检查request_manager中的数据
latest_request = request_manager.get_latest_request_by_card(card_key)
print(f"\n卡密 '{card_key}' 的最新请求: {latest_request}")

# 尝试获取结果
result = bazi_service.get_result(request_id)
print(f"\n通过请求ID获取结果: {result is not None}")

result_by_card = bazi_service.get_result(card_key)
print(f"通过卡密获取结果: {result_by_card is not None}")