<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试API前端响应</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API 前端响应测试</h1>
        
        <div class="section">
            <h3>📡 API 调用测试</h3>
            <button onclick="testAPI()">测试 API 调用</button>
            <button onclick="testProgressiveLoader()">测试渐进式加载器</button>
            <div id="status"></div>
        </div>
        
        <div class="section">
            <h3>📊 API 响应数据</h3>
            <pre id="apiResponse">点击"测试 API 调用"按钮查看响应数据</pre>
        </div>
        
        <div class="section">
            <h3>🔧 渐进式加载器测试</h3>
            <pre id="loaderTest">点击"测试渐进式加载器"按钮查看测试结果</pre>
        </div>
        
        <div class="section">
            <h3>🎯 数据结构分析</h3>
            <pre id="dataAnalysis">等待数据...</pre>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function testAPI() {
            showStatus('正在调用 API...', 'info');
            
            try {
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const data = await response.json();
                
                // 显示原始响应
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                
                // 分析数据结构
                analyzeData(data);
                
                showStatus(`API 调用成功 (状态码: ${response.status})`, 'success');
                
            } catch (error) {
                showStatus(`API 调用失败: ${error.message}`, 'error');
                document.getElementById('apiResponse').textContent = `错误: ${error.message}`;
            }
        }
        
        function analyzeData(data) {
            const analysis = [];
            
            analysis.push('=== 数据结构分析 ===');
            analysis.push(`数据类型: ${typeof data}`);
            analysis.push(`是否为对象: ${typeof data === 'object' && data !== null}`);
            
            if (data) {
                analysis.push('\n=== 顶级属性 ===');
                Object.keys(data).forEach(key => {
                    const value = data[key];
                    analysis.push(`${key}: ${typeof value} ${Array.isArray(value) ? '(数组)' : ''}`);
                });
                
                analysis.push('\n=== 关键字段检查 ===');
                analysis.push(`completed: ${data.completed} (类型: ${typeof data.completed})`);
                analysis.push(`processing: ${data.processing} (类型: ${typeof data.processing})`);
                analysis.push(`status: ${data.status} (类型: ${typeof data.status})`);
                analysis.push(`result 存在: ${!!data.result}`);
                
                if (data.result) {
                    analysis.push(`result.data 存在: ${!!(data.result && data.result.data)}`);
                    if (data.result.data) {
                        analysis.push(`result.data.bz 存在: ${!!(data.result.data.bz)}`);
                    }
                }
                
                analysis.push('\n=== 渐进式加载器条件检查 ===');
                
                // 检查处理中条件
                const isProcessing = data.processing === true || (data.completed === false && data.status === 'processing');
                analysis.push(`处理中条件: ${isProcessing}`);
                
                // 检查完成条件
                const isCompleted = data.completed === true;
                analysis.push(`完成条件: ${isCompleted}`);
                
                // 检查数据存在条件
                const hasData = data.result && data.result.data;
                analysis.push(`有数据条件: ${hasData}`);
                
                analysis.push('\n=== 预期行为 ===');
                if (isCompleted && hasData) {
                    analysis.push('✅ 应该显示完整八字数据');
                } else if (isProcessing && hasData) {
                    analysis.push('🔄 应该显示基础数据 + 处理中状态');
                } else if (isProcessing) {
                    analysis.push('⏳ 应该显示等待界面');
                } else {
                    analysis.push('❓ 未知状态，可能有问题');
                }
            }
            
            document.getElementById('dataAnalysis').textContent = analysis.join('\n');
        }
        
        async function testProgressiveLoader() {
            showStatus('测试渐进式加载器...', 'info');
            
            try {
                // 模拟渐进式加载器的逻辑
                const response = await fetch('/api/get_result/Xs1MR9iVx9RNZOk6');
                const result = await response.json();
                
                const testResults = [];
                testResults.push('=== 渐进式加载器测试 ===');
                testResults.push(`原始数据: ${JSON.stringify(result, null, 2)}`);
                testResults.push('\n=== 条件判断测试 ===');
                
                // 模拟 loadBasicData 方法的逻辑
                testResults.push('🔍 响应结构检查:');
                testResults.push(`  - result存在: ${!!result.result}`);
                testResults.push(`  - result.data存在: ${!!(result.result && result.result.data)}`);
                testResults.push(`  - processing状态: ${result.processing}`);
                testResults.push(`  - completed状态: ${result.completed}`);
                
                // 检查处理中状态
                if (result.processing === true || (result.completed === false && result.status === 'processing')) {
                    testResults.push('\n⏳ 检测到处理中状态');
                    
                    if (result.result && result.result.data) {
                        testResults.push('✅ 找到基础八字数据，应该先显示排盘');
                        testResults.push('返回值: result.result');
                    } else {
                        testResults.push('⏳ 无基础数据，应该显示等待界面');
                        testResults.push('返回值: null');
                    }
                } else if (result.completed === true) {
                    testResults.push('\n✅ 检测到处理已完成');
                    
                    if (result.result && result.result.data) {
                        testResults.push('✅ 找到完整八字数据');
                        testResults.push('返回值: result.result');
                    } else {
                        testResults.push('⚠️ 处理完成但没有找到数据');
                    }
                } else {
                    testResults.push('\n❓ 未知状态');
                }
                
                document.getElementById('loaderTest').textContent = testResults.join('\n');
                showStatus('渐进式加载器测试完成', 'success');
                
            } catch (error) {
                showStatus(`渐进式加载器测试失败: ${error.message}`, 'error');
                document.getElementById('loaderTest').textContent = `错误: ${error.message}`;
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            testAPI();
        });
    </script>
</body>
</html>
