#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理所有缓存文件
"""

import os
import shutil
import json
import glob

def clean_cache_files():
    """清理所有缓存文件"""
    print("🧹 开始清理缓存文件")
    print("=" * 60)
    
    cleaned_items = []
    
    # 1. 清理Python缓存
    print("🐍 清理Python缓存...")
    pycache_dirs = glob.glob("**/__pycache__", recursive=True)
    for pycache_dir in pycache_dirs:
        try:
            shutil.rmtree(pycache_dir)
            cleaned_items.append(f"Python缓存: {pycache_dir}")
            print(f"  ✅ 删除: {pycache_dir}")
        except Exception as e:
            print(f"  ❌ 删除失败: {pycache_dir} - {e}")
    
    # 2. 清理八字分析结果
    print("\n🔮 清理八字分析结果...")
    analysis_dir = "backend/analysis_results"
    if os.path.exists(analysis_dir):
        try:
            files = os.listdir(analysis_dir)
            for file in files:
                file_path = os.path.join(analysis_dir, file)
                os.remove(file_path)
                cleaned_items.append(f"分析结果: {file}")
                print(f"  ✅ 删除: {file}")
        except Exception as e:
            print(f"  ❌ 清理分析结果失败: {e}")
    
    # 3. 清理摘要文件
    print("\n📄 清理摘要文件...")
    summaries_dir = "backend/summaries"
    if os.path.exists(summaries_dir):
        try:
            files = os.listdir(summaries_dir)
            for file in files:
                file_path = os.path.join(summaries_dir, file)
                os.remove(file_path)
                cleaned_items.append(f"摘要文件: {file}")
                print(f"  ✅ 删除: {file}")
        except Exception as e:
            print(f"  ❌ 清理摘要文件失败: {e}")
    
    # 4. 清理LLM日志
    print("\n🤖 清理LLM日志...")
    llm_logs_dir = "backend/llm_logs"
    if os.path.exists(llm_logs_dir):
        try:
            files = os.listdir(llm_logs_dir)
            for file in files:
                if file.endswith(('.json', '.txt')) and file != 'app.log':  # 保留app.log
                    file_path = os.path.join(llm_logs_dir, file)
                    os.remove(file_path)
                    cleaned_items.append(f"LLM日志: {file}")
                    print(f"  ✅ 删除: {file}")
        except Exception as e:
            print(f"  ❌ 清理LLM日志失败: {e}")
    
    # 5. 清理八字结果缓存
    print("\n💾 清理八字结果缓存...")
    bazi_results_file = "backend/bazi_results.json"
    if os.path.exists(bazi_results_file):
        try:
            # 备份当前结果
            backup_file = f"backend/bazi_results_backup_{int(time.time())}.json"
            shutil.copy2(bazi_results_file, backup_file)
            print(f"  📋 备份到: {backup_file}")
            
            # 清空结果文件
            with open(bazi_results_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            
            cleaned_items.append(f"八字结果缓存: {bazi_results_file}")
            print(f"  ✅ 清空: {bazi_results_file}")
        except Exception as e:
            print(f"  ❌ 清理八字结果缓存失败: {e}")
    
    # 6. 清理临时文件
    print("\n🗂️ 清理临时文件...")
    temp_patterns = [
        "*.tmp",
        "*.temp",
        "*_temp.json",
        "test_*.html",
        "debug_*.html",
        "verify_*.py",
        "test_*.py"
    ]
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                cleaned_items.append(f"临时文件: {file}")
                print(f"  ✅ 删除: {file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {e}")
    
    return cleaned_items

def reset_server_state():
    """重置服务器状态"""
    print("\n🔄 重置服务器状态...")
    
    # 这里可以添加重置服务器状态的逻辑
    # 比如清理内存中的缓存等
    print("  💡 建议重启服务器以完全清理内存缓存")

def main():
    """主函数"""
    import time
    
    print("🧹 缓存清理工具")
    print("=" * 60)
    
    # 确认清理
    print("⚠️ 此操作将清理以下内容:")
    print("  - Python __pycache__ 目录")
    print("  - 八字分析结果文件")
    print("  - 摘要文件")
    print("  - LLM日志文件")
    print("  - 八字结果缓存")
    print("  - 临时测试文件")
    
    confirm = input("\n是否继续清理？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 清理已取消")
        return
    
    # 执行清理
    cleaned_items = clean_cache_files()
    
    # 重置服务器状态
    reset_server_state()
    
    # 总结
    print(f"\n📊 清理完成")
    print("=" * 60)
    print(f"✅ 共清理了 {len(cleaned_items)} 个项目")
    
    if cleaned_items:
        print("\n📋 清理详情:")
        for item in cleaned_items[:10]:  # 只显示前10个
            print(f"  - {item}")
        
        if len(cleaned_items) > 10:
            print(f"  ... 还有 {len(cleaned_items) - 10} 个项目")
    
    print(f"\n💡 建议:")
    print("1. 重启服务器以完全清理内存缓存")
    print("2. 重新测试八字分析功能")
    print("3. 检查是否还有重复生成的问题")
    
    print(f"\n🔗 重启服务器命令:")
    print("python start_prompt_manager.py web --no-browser")

if __name__ == "__main__":
    main()
