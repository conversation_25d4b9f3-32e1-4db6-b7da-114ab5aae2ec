// 配置文件

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
function getApiBaseUrl() {
    // 检查是否在本地开发环境
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // 统一使用localhost，避免跨域问题
        return 'http://localhost:5000';
    }
    
    // 服务器环境，检查当前访问的主机
    const currentHost = window.location.hostname;
    const currentPort = window.location.port;
    const currentProtocol = window.location.protocol;
    
    // 如果是通过IP访问，使用相同的IP和端口5000
    if (currentHost && currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
        return `${currentProtocol}//${currentHost}:5000`;
    }
    
    // 默认使用相对路径
    return '';
}

// 导出函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { getApiBaseUrl };
}