/**
 * @file-group up_css
 * @related-files 
 * - variables.css (变量定义)
 * - reset.css (基础样式重置)
 * - navigation.css (导航组件)
 * - form.css (表单样式)
 * - buttons.css (按钮样式)
 * - progress.css (进度条样式)
 * - header.css (页面头部)
 * - decorations.css (装饰效果)
 * - dark-mode.css (深色模式)
 * - responsive.css (响应式设计)
 */

/* 导入所有分拆的CSS文件 */
@import '../fonts.css';
@import 'variables.css';
@import 'reset.css';
@import 'navigation.css';
@import 'form.css';
@import 'buttons.css';
@import 'progress.css';
@import 'header.css';
@import 'decorations.css';
@import 'dark-mode.css';
@import 'responsive.css';

/* 导入八字相关样式 */
@import '../bazi-integration.css';
@import '../lunar-mobile-dark.css';
@import '../bazi-reverse-mobile-dark.css';