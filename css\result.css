/* 结果页面特定样式 */
.nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 20px;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color-light);
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.nav-link:hover {
    transform: translateX(-5px) translateY(-2px);
    color: var(--accent-color);
    background: white;
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.nav-link::before {
    content: '🏠';
    font-size: 1.1em;
    transition: transform 0.3s ease;
}

.nav-link:hover::before {
    transform: scale(1.2) rotate(5deg);
}

/* 操作按钮区域 */
.action-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    justify-content: center;
    margin: 32px 0;
    padding: 24px;
    background: var(--card-gradient);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
}

.action-buttons .btn {
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

/* 卡密输入模态框样式 */
.card-key-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.card-key-modal.show {
    opacity: 1;
    visibility: visible;
}

.card-key-modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: 40px;
    max-width: 480px;
    width: 90%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color-light);
    transform: translateY(-50px) scale(0.9);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-key-modal.show .card-key-modal-content {
    transform: translateY(0) scale(1);
}

.card-key-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--hero-gradient);
}

.card-key-modal-header {
    text-align: center;
    margin-bottom: 32px;
}

.card-key-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.card-key-modal-title::before {
    content: '🔐';
    font-size: 1.2em;
}

.card-key-modal-subtitle {
    color: var(--text-secondary);
    font-size: 0.95rem;
    margin: 0;
}

.card-key-input-group {
    margin-bottom: 24px;
}

.card-key-input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid var(--border-color-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--bg-input);
    color: var(--text-primary);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.card-key-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
    background: white;
}

.card-key-input::placeholder {
    color: var(--text-muted);
}

.card-key-status {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.card-key-status.success {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.card-key-status.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.card-key-status.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.card-key-modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.card-key-modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.card-key-modal-btn.primary {
    background: var(--hero-gradient);
    color: white;
    box-shadow: var(--shadow-sm);
}

.card-key-modal-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-key-modal-btn.primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.card-key-modal-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color-light);
}

.card-key-modal-btn.secondary:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* 报告列表样式 */
.report-list {
    display: grid;
    gap: 20px;
    margin-top: 32px;
}

.report-item {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.report-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--hero-gradient);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.report-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
}

.report-item:hover::before {
    transform: scaleY(1);
}

.report-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color-light);
}

.report-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.report-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: var(--success-gradient);
    color: white;
}

.status-processing {
    background: var(--warning-gradient);
    color: white;
    animation: var(--pulse-animation);
}

/* 加载状态 */
.loading-container {
    text-align: center;
    padding: 60px 20px;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 16px;
        padding-top: 20px;
    }
    
    .nav-link {
        padding: 10px 16px;
        margin-bottom: 16px;
        font-size: 0.9rem;
        border-radius: 8px;
    }
    
    .header {
        margin-bottom: 24px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.8rem;
        margin-bottom: 8px;
    }
    
    .subtitle {
        font-size: 0.9rem;
        margin-bottom: 16px;
    }
    
    .actions-section {
        flex-direction: column;
        gap: 16px;
        margin: 20px 0;
        padding: 16px;
    }
    
    .action-btn {
        width: 100%;
        padding: 14px 20px;
        font-size: 0.95rem;
        min-width: auto;
        justify-content: center;
    }
    
    .card-filter {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }
    
    .card-filter-input {
        width: 100%;
        padding: 12px 16px;
        font-size: 1rem;
        border-radius: 8px;
    }
    
    .reports-container {
        margin: 16px 0;
    }
    
    .report-card {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;
    }
    
    .report-title {
        font-size: 1.1rem;
        line-height: 1.4;
    }
    
    .report-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        font-size: 0.85rem;
    }
    
    .report-actions {
        flex-direction: column;
        gap: 8px;
        margin-top: 12px;
    }
    
    .report-actions .btn {
        width: 100%;
        padding: 10px 16px;
        font-size: 0.9rem;
        justify-content: center;
    }
    
    .card-key-modal-content {
        padding: 24px 20px;
        margin: 20px;
        width: calc(100% - 40px);
        max-width: none;
    }
    
    .card-key-modal h3 {
        font-size: 1.3rem;
        margin-bottom: 16px;
    }
    
    .card-key-input {
        padding: 12px 16px;
        font-size: 1rem;
        margin-bottom: 16px;
    }
    
    .modal-buttons {
        flex-direction: column;
        gap: 12px;
    }
    
    .modal-buttons .btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 1rem;
    }
    
    .loading-container {
        padding: 40px 20px;
        margin: 16px 0;
    }
    
    .loading-text {
        font-size: 1rem;
    }
    
    .footer {
        margin-top: 40px;
        padding: 20px 16px;
        text-align: center;
    }
    
    .footer p {
        font-size: 0.85rem;
        line-height: 1.5;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 12px;
        padding-top: 16px;
    }
    
    .header h1 {
        font-size: 1.6rem;
    }
    
    .subtitle {
        font-size: 0.85rem;
    }
    
    .action-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
    
    .report-card {
        padding: 14px;
        margin-bottom: 12px;
    }
    
    .report-title {
        font-size: 1rem;
    }
    
    .report-meta {
        font-size: 0.8rem;
    }
    
    .card-key-modal-content {
        padding: 20px 16px;
        margin: 16px;
    }
    
    .loading-container {
        padding: 30px 16px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .action-btn,
    .report-actions .btn,
    .nav-link {
        min-height: 44px;
        touch-action: manipulation;
    }
    
    .card-filter-input {
        min-height: 44px;
    }
    
    .report-card {
        touch-action: manipulation;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #0f172a, #1e293b);
        color: #f8fafc;
    }
    
    .header {
        background: rgba(30, 41, 59, 0.9);
        border-bottom-color: #334155;
    }
    
    .header-content {
        color: #f8fafc;
    }
    
    .site-title {
        color: #f8fafc;
    }
    
    /* 全局强制覆盖白色背景 */
    [style*="background: white"],
    [style*="background: #fff"],
    [style*="background: #ffffff"],
    [style*="background-color: white"],
    [style*="background-color: #fff"],
    [style*="background-color: #ffffff"],
    [style*="background:#fff"],
    [style*="background:#ffffff"],
    [style*="background-color:#fff"],
    [style*="background-color:#ffffff"],
    div[style*="background"],
    span[style*="background"],
    p[style*="background"] {
        background: rgba(15, 23, 42, 0.8) !important;
        background-color: rgba(15, 23, 42, 0.8) !important;
        color: #f1f5f9 !important;
    }
    
    /* 全局强制覆盖白色文本颜色 - 排除特定元素 */
    [style*="color: white"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color: #fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color: #ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color:#fff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn),
    [style*="color:#ffffff"]:not(.force-complete-btn):not(.title-text):not(.title-icon):not(.title-level):not(.item-title *):not(.detailed-action-btn):not(.special-title *):not(.number):not(.term):not(.action-btn) {
        color: #0f172a !important;
        background-color: #f1f5f9 !important;
        padding: 0 4px !important;
        border-radius: 4px !important;
    }

    /* 确保特定元素的白色文字不被覆盖 */
    .force-complete-btn,
    .item-title:hover .title-text,
    .item-title:hover .title-icon,
    .item-title:hover .title-level,
    .item-title:hover *,
    .detailed-action-btn,
    .special-title *,
    .number,
    .term {
        color: white !important;
        background-color: transparent !important;
        padding: initial !important;
        -webkit-text-fill-color: white !important;
        text-fill-color: white !important;
    }

    /* 确保 action-btn.secondary 按钮样式不被覆盖 */
    .action-btn.secondary {
        background: white !important;
        color: var(--primary-color) !important;
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }

    .action-btn.secondary:hover {
        background: rgba(102, 126, 234, 0.05) !important;
        color: var(--primary-color) !important;
        -webkit-text-fill-color: var(--primary-color) !important;
        text-fill-color: var(--primary-color) !important;
    }
    
    .card-key-modal-btn.secondary {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
        border-color: var(--border-color);
    }
    
    .card-key-modal-btn.secondary:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
    }
    
    /* 加载和错误消息深色模式 */
    .loading-container,
    .error-container {
        background: var(--bg-card);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .loading-text,
    .error-text {
        color: var(--text-secondary);
    }
    
    /* 页脚深色模式 */
    .footer {
        background: var(--bg-secondary);
        border-top-color: var(--border-color);
        color: var(--text-secondary);
    }
}

/* 手机端深色模式特殊优化 */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    .report-card {
        background: var(--bg-card);
        border: 2px solid var(--border-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }
    
    .report-card:hover,
    .report-card:active {
        background: var(--bg-tertiary);
        border-color: var(--border-color-dark);
        transform: none; /* 移动端不使用transform效果 */
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
    }
    
    .report-title {
        color: var(--text-primary) !important;
        font-weight: 700;
    }
    
    .report-source {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        font-weight: 600;
    }
    
    .report-date {
        color: var(--text-secondary);
        font-weight: 600;
    }
    
    .report-actions .btn {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border: 2px solid var(--border-color);
        font-weight: 600;
    }
    
    .report-actions .view-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }
    
    .report-actions .delete-btn {
        background: var(--bg-tertiary);
        color: #ff6b6b;
        border-color: #ff6b6b;
    }
    
    .report-actions .delete-btn:hover,
    .report-actions .delete-btn:active {
        background: #ef4444;
        color: white;
    }
}