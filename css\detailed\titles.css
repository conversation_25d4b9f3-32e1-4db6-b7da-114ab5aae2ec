/* 增强的标题格式化样式 */

/* 标题容器基础样式 */
.title-container {
    display: flex;
    align-items: center;
    gap: 0.5em;
    position: relative;
    transition: all 0.3s ease;
    margin: 0.5em 0;
    padding: 0.3em 0;
}

.title-container:hover {
    transform: translateX(4px);
}

/* 标题图标样式 */
.title-icon {
    font-size: 1em;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.8em;
    height: 1.8em;
    border-radius: 6px;
    background: rgba(99, 102, 241, 0.1);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

/* 标题文本样式 */
.title-text {
    flex: 1;
    font-weight: inherit;
    color: inherit;
    margin: 0;
}

/* 标题层级指示器 */
.title-level {
    font-size: 0.7em;
    color: #6b7280;
    font-weight: 400;
    margin-right: 0.3em;
    opacity: 0.7;
}

/* 锚点链接样式 */
.title-anchor {
    opacity: 0;
    font-size: 0.8em;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.3s ease;
    padding: 0.3em;
    border-radius: 4px;
    margin-left: auto;
}

.title-container:hover .title-anchor {
    opacity: 1;
}

.title-anchor:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    transform: scale(1.1);
}

/* 不同层级标题的样式 */

/* 主标题样式 */
.main-title {
    font-size: 2rem;
    font-weight: 800;
    margin: 1.5em 0 1em 0;
}

.main-title .title-icon {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
    font-size: 1.2em;
    width: 2em;
    height: 2em;
}

.main-title .title-text {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 章节标题样式 */
.section-title {
    font-size: 1.6rem;
    font-weight: 700;
    margin: 1.3em 0 0.8em 0;
}

.section-title .title-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
    color: white;
    box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

.section-title .title-text {
    color: #1e40af;
}

/* 子章节标题样式 */
.subsection-title {
    font-size: 1.35rem;
    font-weight: 600;
    margin: 1.1em 0 0.6em 0;
}

.subsection-title .title-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.subsection-title .title-text {
    color: #059669;
}

/* 项目标题样式 */
.item-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 1em 0 0.5em 0;
}

.item-title .title-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
}

.item-title .title-text {
    color: #7c3aed;
}

/* 修复悬停时的颜色冲突 */
.item-title:hover {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
    transform: translateX(4px);
}

.item-title:hover .title-text,
.item-title:hover .title-icon,
.item-title:hover .title-level {
    color: white !important;
}

/* 子项目标题样式 */
.sub-item-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.8em 0 0.4em 0;
}

.sub-item-title .title-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.sub-item-title .title-text {
    color: #dc2626;
}

/* 详细标题样式 */
.detail-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0.6em 0 0.3em 0;
}

.detail-title .title-icon {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(107, 114, 128, 0.3);
    width: 1.5em;
    height: 1.5em;
}

.detail-title .title-text {
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.95em;
}

/* 特殊标题样式 */
.special-title .title-icon {
    background: var(--title-color, #6366f1) !important;
    color: white !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
}

.special-title .title-text {
    color: var(--title-color, #6366f1) !important;
    font-weight: 700;
}

/* 标题装饰线 */
.title-decoration {
    height: 2px;
    margin: 0.5em 0 1em 0;
    border-radius: 1px;
    opacity: 0.8;
}

.h1-decoration {
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899, transparent);
    height: 3px;
}

.h2-decoration {
    background: linear-gradient(90deg, #3b82f6, #06b6d4, transparent);
    height: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title-container {
        gap: 0.3em;
    }
    
    .title-icon {
        width: 1.5em;
        height: 1.5em;
        font-size: 0.9em;
    }
    
    .main-title {
        font-size: 1.6rem;
    }
    
    .section-title {
        font-size: 1.4rem;
    }
    
    .subsection-title {
        font-size: 1.2rem;
    }
    
    .item-title {
        font-size: 1.1rem;
    }
    
    .sub-item-title {
        font-size: 1rem;
    }
    
    .detail-title {
        font-size: 0.95rem;
    }
    
    .title-anchor {
        display: none; /* 在移动设备上隐藏锚点链接 */
    }
}

/* 打印样式 */
@media print {
    .title-container {
        break-after: avoid;
    }
    
    .title-icon {
        background: none !important;
        color: #333 !important;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }
    
    .title-anchor {
        display: none !important;
    }
    
    .title-decoration {
        background: #ccc !important;
        height: 1px !important;
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(10px);
    }
}

/* Toast消息样式 */
.toast-message {
    animation: slideIn 0.3s ease;
}
