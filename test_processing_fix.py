#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正在处理请求的修复效果
"""

import requests
import time

def test_processing_request():
    """测试正在处理的请求是否能正确返回"""
    print("🎯 测试正在处理请求的修复效果")
    print("=" * 60)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(5)
    
    # 发送一个新请求
    print("\n1. 发送新的八字分析请求")
    print("-" * 40)
    
    payload = {
        "cardKey": "wdd",
        "year": "1992",
        "month": "8",
        "day": "15",
        "hour": "申时",
        "gender": "1"
    }
    
    print(f"📤 发送请求: {payload}")
    
    response = requests.post('http://localhost:5000/webhook/bazi-analysis', json=payload)
    if response.status_code == 200:
        result = response.json()
        request_id = result.get('requestId')
        print(f"✅ 请求成功，ID: {request_id}")
    else:
        print(f"❌ 请求失败: {response.status_code}")
        return False
    
    # 立即查询结果（应该返回处理中状态）
    print("\n2. 立即查询结果（应该显示处理中）")
    print("-" * 40)
    
    result_response = requests.get('http://localhost:5000/api/get_result/wdd')
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        
        print(f"📊 响应数据:")
        print(f"   完成状态: {result_data.get('completed', 'N/A')}")
        print(f"   处理中: {result_data.get('processing', 'N/A')}")
        print(f"   请求ID: {result_data.get('request_id', 'N/A')}")
        print(f"   状态: {result_data.get('status', 'N/A')}")
        
        if result_data.get('processing') == True:
            print("✅ 成功！API正确返回了处理中状态")
            
            # 等待处理完成
            print("\n3. 等待处理完成...")
            print("-" * 40)
            time.sleep(15)
            
            # 再次查询结果
            final_response = requests.get('http://localhost:5000/api/get_result/wdd')
            if final_response.status_code == 200:
                final_data = final_response.json()
                
                if final_data.get('completed'):
                    result_obj = final_data.get('result', {})
                    bz = result_obj.get('data', {}).get('bz', {})
                    
                    if bz:
                        birth_info = bz.get('8', '未知')
                        print(f"✅ 处理完成！")
                        print(f"   生辰: {birth_info}")
                        
                        # 检查是否是我们刚才提交的时间
                        if '1992' in birth_info and ('七月' in birth_info or '八月' in birth_info):
                            print("✅ 返回了正确的1992年数据")
                            return True
                        else:
                            print(f"❌ 返回了错误的数据: {birth_info}")
                            print("   应该是1992年七月或八月的数据")
                            return False
                    else:
                        print("❌ 没有八字数据")
                        return False
                else:
                    print("⏳ 仍在处理中...")
                    return False
            else:
                print(f"❌ 最终查询失败: {final_response.status_code}")
                return False
        else:
            print("❌ API没有返回处理中状态")
            print(f"   实际返回: {result_data}")
            return False
    else:
        print(f"❌ 查询失败: {result_response.status_code}")
        return False

def main():
    """主函数"""
    print("🎯 正在处理请求修复测试")
    print("=" * 60)
    
    success = test_processing_request()
    
    if success:
        print("\n🎉 修复成功！")
        print("💡 API现在能够:")
        print("  - 正确识别正在处理中的请求")
        print("  - 返回处理中状态而不是旧数据")
        print("  - 处理完成后返回最新结果")
        print("🔗 你现在可以在 http://localhost:5000/ 测试不同的日期时间")
    else:
        print("\n⚠️ 还需要进一步修复")
        print("💡 请检查服务器日志以了解具体问题")

if __name__ == "__main__":
    main()
