#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示词效果评估器
用于评估和优化提示词的质量和效果
"""

import json
import re
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime
import hashlib

logger = logging.getLogger(__name__)

class PromptEvaluator:
    """提示词效果评估器"""
    
    def __init__(self):
        """初始化评估器"""
        # 十神列表
        self.shishen_list = [
            "正官", "七杀", "正财", "偏财", "正印", "偏印", 
            "比肩", "劫财", "食神", "伤官"
        ]
        
        # 禁用的五行理论术语
        self.forbidden_wuxing_terms = [
            "金木水火土", "五行相生", "五行相克", "旺衰", "生克制化",
            "金旺", "木旺", "水旺", "火旺", "土旺",
            "金弱", "木弱", "水弱", "火弱", "土弱"
        ]
        
        # Markdown格式检查规则
        self.markdown_rules = [
            (r'^##\s+', "一级标题格式"),
            (r'^###\s+', "二级标题格式"), 
            (r'\*\*[^*]+\*\*', "粗体强调格式"),
            (r'^\d+\.\s+', "数字列表格式")
        ]
    
    def evaluate_analysis_result(self, 
                                analysis_result: str, 
                                bazi_info: Dict[str, Any],
                                dimension: str) -> Dict[str, Any]:
        """评估分析结果的整体质量
        
        Args:
            analysis_result: LLM生成的分析结果
            bazi_info: 八字信息
            dimension: 分析维度
            
        Returns:
            Dict: 评估结果
        """
        evaluation = {
            "timestamp": datetime.now().isoformat(),
            "dimension": dimension,
            "overall_score": 0.0,
            "detailed_scores": {},
            "issues": [],
            "suggestions": []
        }
        
        try:
            # 1. 十神使用准确性评估 (权重40%)
            shishen_score, shishen_issues = self._evaluate_shishen_accuracy(
                analysis_result, bazi_info
            )
            evaluation["detailed_scores"]["shishen_accuracy"] = shishen_score
            evaluation["issues"].extend(shishen_issues)
            
            # 2. 逻辑一致性评估 (权重30%)
            logic_score, logic_issues = self._evaluate_logic_consistency(analysis_result)
            evaluation["detailed_scores"]["logic_consistency"] = logic_score
            evaluation["issues"].extend(logic_issues)
            
            # 3. 格式规范性评估 (权重20%)
            format_score, format_issues = self._evaluate_format_compliance(analysis_result)
            evaluation["detailed_scores"]["format_compliance"] = format_score
            evaluation["issues"].extend(format_issues)
            
            # 4. 内容完整性评估 (权重10%)
            completeness_score, completeness_issues = self._evaluate_content_completeness(
                analysis_result, dimension
            )
            evaluation["detailed_scores"]["content_completeness"] = completeness_score
            evaluation["issues"].extend(completeness_issues)
            
            # 计算总分
            evaluation["overall_score"] = (
                shishen_score * 0.4 +
                logic_score * 0.3 +
                format_score * 0.2 +
                completeness_score * 0.1
            )
            
            # 生成改进建议
            evaluation["suggestions"] = self._generate_suggestions(evaluation)
            
        except Exception as e:
            logger.error(f"评估分析结果时出错: {str(e)}")
            evaluation["error"] = str(e)
        
        return evaluation
    
    def _evaluate_shishen_accuracy(self, 
                                  analysis_result: str, 
                                  bazi_info: Dict[str, Any]) -> Tuple[float, List[str]]:
        """评估十神使用的准确性"""
        issues = []
        score = 1.0
        
        try:
            # 提取八字中实际存在的十神
            actual_shishen = set()
            if "天干十神" in bazi_info:
                actual_shishen.update(bazi_info["天干十神"])
            if "地支藏干十神" in bazi_info:
                for branch_shishen in bazi_info["地支藏干十神"]:
                    if isinstance(branch_shishen, list):
                        actual_shishen.update(branch_shishen)
                    else:
                        actual_shishen.add(branch_shishen)
            
            # 检查分析结果中提到的十神
            mentioned_shishen = set()
            for shishen in self.shishen_list:
                if shishen in analysis_result:
                    mentioned_shishen.add(shishen)
            
            # 检查是否使用了不存在的十神
            invalid_shishen = mentioned_shishen - actual_shishen
            if invalid_shishen:
                score -= 0.3 * len(invalid_shishen) / len(self.shishen_list)
                issues.append(f"使用了八字中不存在的十神: {', '.join(invalid_shishen)}")
            
            # 检查是否遗漏了重要的十神
            missing_shishen = actual_shishen - mentioned_shishen
            if missing_shishen and len(missing_shishen) > len(actual_shishen) * 0.5:
                score -= 0.2
                issues.append(f"遗漏了重要的十神分析: {', '.join(missing_shishen)}")
            
            # 检查是否使用了五行理论
            for forbidden_term in self.forbidden_wuxing_terms:
                if forbidden_term in analysis_result:
                    score -= 0.4
                    issues.append(f"违规使用五行理论术语: {forbidden_term}")
                    break
            
        except Exception as e:
            logger.error(f"评估十神准确性时出错: {str(e)}")
            score = 0.5
            issues.append(f"十神准确性评估出错: {str(e)}")
        
        return max(0.0, score), issues
    
    def _evaluate_logic_consistency(self, analysis_result: str) -> Tuple[float, List[str]]:
        """评估逻辑一致性"""
        issues = []
        score = 1.0
        
        try:
            # 检查是否有自相矛盾的描述
            contradictions = [
                (["内向", "外向"], "性格内外向矛盾"),
                (["谨慎", "冲动"], "行为风格矛盾"),
                (["保守", "激进"], "处事态度矛盾")
            ]
            
            for contradiction_terms, description in contradictions:
                if all(term in analysis_result for term in contradiction_terms):
                    score -= 0.2
                    issues.append(f"存在逻辑矛盾: {description}")
            
            # 检查段落之间的逻辑连贯性
            sections = re.split(r'##\s+', analysis_result)
            if len(sections) < 3:
                score -= 0.3
                issues.append("分析结构不完整，缺少必要的分析段落")
            
            # 检查是否有空洞的分析
            empty_sections = 0
            for section in sections:
                if len(section.strip()) < 50:
                    empty_sections += 1
            
            if empty_sections > 0:
                score -= 0.1 * empty_sections
                issues.append(f"存在{empty_sections}个内容过于简单的分析段落")
            
        except Exception as e:
            logger.error(f"评估逻辑一致性时出错: {str(e)}")
            score = 0.5
            issues.append(f"逻辑一致性评估出错: {str(e)}")
        
        return max(0.0, score), issues
    
    def _evaluate_format_compliance(self, analysis_result: str) -> Tuple[float, List[str]]:
        """评估格式规范性"""
        issues = []
        score = 1.0
        
        try:
            # 检查Markdown格式
            format_checks = {
                "一级标题": r'^##\s+.+$',
                "二级标题": r'^###\s+.+$',
                "粗体强调": r'\*\*[^*]+\*\*',
                "段落分隔": r'\n\s*\n'
            }
            
            for format_name, pattern in format_checks.items():
                if not re.search(pattern, analysis_result, re.MULTILINE):
                    score -= 0.15
                    issues.append(f"缺少{format_name}格式")
            
            # 检查是否使用了第三人称
            first_person_patterns = [r'你的', r'你是', r'你会', r'你在']
            for pattern in first_person_patterns:
                if re.search(pattern, analysis_result):
                    score -= 0.2
                    issues.append("使用了第一人称，应该使用第三人称描述")
                    break
            
            # 检查长度是否合适
            word_count = len(analysis_result)
            if word_count < 300:
                score -= 0.3
                issues.append(f"分析内容过短({word_count}字)，建议增加详细分析")
            elif word_count > 1500:
                score -= 0.1
                issues.append(f"分析内容过长({word_count}字)，建议精简表达")
            
        except Exception as e:
            logger.error(f"评估格式规范性时出错: {str(e)}")
            score = 0.5
            issues.append(f"格式规范性评估出错: {str(e)}")
        
        return max(0.0, score), issues
    
    def _evaluate_content_completeness(self, 
                                     analysis_result: str, 
                                     dimension: str) -> Tuple[float, List[str]]:
        """评估内容完整性"""
        issues = []
        score = 1.0
        
        try:
            # 定义各维度必需的分析要素
            required_elements = {
                "性格": ["外在表现", "内在特质", "人际交往", "处事风格"],
                "职业": ["职业特质", "工作表现", "职业方向", "发展建议"],
                "感情": ["感情态度", "恋爱模式", "婚姻观念", "感情建议"],
                "健康": ["健康状况", "体质特点", "养生建议", "注意事项"]
            }
            
            if dimension in required_elements:
                missing_elements = []
                for element in required_elements[dimension]:
                    if element not in analysis_result:
                        missing_elements.append(element)
                
                if missing_elements:
                    score -= 0.2 * len(missing_elements) / len(required_elements[dimension])
                    issues.append(f"缺少必要的分析要素: {', '.join(missing_elements)}")
            
            # 检查是否有建议和指导部分
            if "建议" not in analysis_result and "指导" not in analysis_result:
                score -= 0.3
                issues.append("缺少实用的建议和指导内容")
            
        except Exception as e:
            logger.error(f"评估内容完整性时出错: {str(e)}")
            score = 0.5
            issues.append(f"内容完整性评估出错: {str(e)}")
        
        return max(0.0, score), issues
    
    def _generate_suggestions(self, evaluation: Dict[str, Any]) -> List[str]:
        """根据评估结果生成改进建议"""
        suggestions = []
        
        # 根据分数生成建议
        if evaluation["detailed_scores"].get("shishen_accuracy", 1.0) < 0.7:
            suggestions.append("建议严格检查十神使用的准确性，确保只使用八字中实际存在的十神")
        
        if evaluation["detailed_scores"].get("logic_consistency", 1.0) < 0.7:
            suggestions.append("建议加强分析逻辑的一致性，避免自相矛盾的描述")
        
        if evaluation["detailed_scores"].get("format_compliance", 1.0) < 0.7:
            suggestions.append("建议改进输出格式，确保符合Markdown规范和第三人称描述要求")
        
        if evaluation["detailed_scores"].get("content_completeness", 1.0) < 0.7:
            suggestions.append("建议补充完整的分析内容，包括必要的分析要素和实用建议")
        
        if evaluation["overall_score"] < 0.6:
            suggestions.append("整体质量需要显著改进，建议重新优化提示词模板")
        
        return suggestions
    
    def batch_evaluate(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量评估多个分析结果"""
        batch_evaluation = {
            "total_count": len(results),
            "average_score": 0.0,
            "score_distribution": {},
            "common_issues": {},
            "recommendations": []
        }
        
        if not results:
            return batch_evaluation
        
        total_score = 0.0
        all_issues = []
        
        for result in results:
            if "overall_score" in result:
                total_score += result["overall_score"]
            if "issues" in result:
                all_issues.extend(result["issues"])
        
        batch_evaluation["average_score"] = total_score / len(results)
        
        # 统计常见问题
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        batch_evaluation["common_issues"] = dict(
            sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        )
        
        # 生成批量建议
        if batch_evaluation["average_score"] < 0.7:
            batch_evaluation["recommendations"].append("整体质量偏低，建议全面优化提示词系统")
        
        return batch_evaluation
