/* 加载状态 */
.loading-container {
    text-align: center;
    padding: 60px 20px;
    background: var(--bg-card);
    border-radius: var(--radius-large);
    border: 1px solid var(--border-color-light);
    box-shadow: var(--shadow-md);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

/* 状态页面样式 */
.loading-state,
.error-state,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 32px;
    text-align: center;
    min-height: 400px;
}

/* 加载进度条 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: var(--border-color-light);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .loading-container {
        padding: 40px 20px;
        margin: 16px 0;
    }
    
    .loading-text {
        font-size: 1rem;
    }
    
    .loading-state, 
    .error-state, 
    .empty-state {
        padding: 30px 20px;
        min-height: 300px;
    }
}

@media (max-width: 480px) {
    .loading-container {
        padding: 30px 16px;
    }
    
    .loading-state, 
    .error-state, 
    .empty-state {
        min-height: 250px;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    .loading-container {
        background: var(--bg-card);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .loading-text {
        color: var(--text-secondary);
    }
    
    .loading-progress {
        background: var(--border-color);
    }
} 