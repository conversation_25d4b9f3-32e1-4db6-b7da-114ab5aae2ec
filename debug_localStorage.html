<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试 localStorage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #005a87;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 localStorage 调试工具</h1>
        
        <div class="section">
            <h3>🎯 正在进行的分析 (ongoingAnalysis)</h3>
            <pre id="ongoingAnalysis"></pre>
            <button onclick="clearOngoingAnalysis()" class="clear-btn">清除正在进行的分析</button>
        </div>
        
        <div class="section">
            <h3>📊 八字报告 (baziReports)</h3>
            <pre id="baziReports"></pre>
            <button onclick="clearBaziReports()" class="clear-btn">清除八字报告</button>
        </div>
        
        <div class="section">
            <h3>🔑 已验证卡密 (validatedCards)</h3>
            <pre id="validatedCards"></pre>
            <button onclick="clearValidatedCards()" class="clear-btn">清除已验证卡密</button>
        </div>
        
        <div class="section">
            <h3>🗂️ 所有 localStorage 数据</h3>
            <pre id="allLocalStorage"></pre>
            <button onclick="refreshData()">刷新数据</button>
            <button onclick="clearAllLocalStorage()" class="clear-btn">清除所有数据</button>
        </div>
    </div>

    <script>
        function displayData() {
            // 显示正在进行的分析
            const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
            document.getElementById('ongoingAnalysis').textContent = ongoingAnalysis ? 
                JSON.stringify(JSON.parse(ongoingAnalysis), null, 2) : '无数据';
            
            // 显示八字报告
            const baziReports = localStorage.getItem('baziReports');
            document.getElementById('baziReports').textContent = baziReports ? 
                JSON.stringify(JSON.parse(baziReports), null, 2) : '无数据';
            
            // 显示已验证卡密
            const validatedCards = localStorage.getItem('validatedCards');
            document.getElementById('validatedCards').textContent = validatedCards ? 
                JSON.stringify(JSON.parse(validatedCards), null, 2) : '无数据';
            
            // 显示所有localStorage数据
            const allData = {};
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                try {
                    allData[key] = JSON.parse(value);
                } catch (e) {
                    allData[key] = value;
                }
            }
            document.getElementById('allLocalStorage').textContent = JSON.stringify(allData, null, 2);
        }
        
        function clearOngoingAnalysis() {
            localStorage.removeItem('ongoingAnalysis');
            alert('已清除正在进行的分析数据');
            displayData();
        }
        
        function clearBaziReports() {
            localStorage.removeItem('baziReports');
            localStorage.removeItem('latestBaziResult');
            alert('已清除八字报告数据');
            displayData();
        }
        
        function clearValidatedCards() {
            localStorage.removeItem('validatedCards');
            alert('已清除已验证卡密数据');
            displayData();
        }
        
        function clearAllLocalStorage() {
            if (confirm('确定要清除所有 localStorage 数据吗？这将删除所有缓存的分析结果和设置。')) {
                localStorage.clear();
                alert('已清除所有 localStorage 数据');
                displayData();
            }
        }
        
        function refreshData() {
            displayData();
        }
        
        // 页面加载时显示数据
        displayData();
    </script>
</body>
</html>
