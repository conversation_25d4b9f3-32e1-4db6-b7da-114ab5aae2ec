# 八字分析项目设计提示词

## 项目概述
这是一个基于Flask后端和原生JavaScript前端的八字分析系统，提供用户输入生辰八字信息并获得AI分析结果的服务。

## 页面设计提示词

### 1. 整体设计风格
```
设计一个简洁现代的八字分析页面，要求：
- 采用中式传统色彩搭配（深蓝、金色、白色）
- 布局简洁清晰，突出核心功能
- 响应式设计，支持移动端访问
- 减少视觉干扰，专注用户体验
```

### 2. 表单设计优化
```
优化八字输入表单，包含以下元素：
- 年份输入框（1900-2100年范围）
- 月份输入框（1-12月）
- 日期输入框（1-31日）
- 时辰选择下拉框（十二时辰，显示具体时间范围）
- 性别选择（1代表男性，0代表女性）
- 卡密输入框
- 提交按钮

表单验证要求：
- 所有字段必填验证
- 数值范围验证
- 实时反馈用户输入状态
```

### 3. 交互体验优化
```
改进页面交互体验：
- 添加加载状态指示器
- 实现分析进度显示
- 优化错误提示信息
- 添加重试机制
- 结果展示区域美化
```

### 4. 视觉元素设计
```
设计符合八字主题的视觉元素：
- 标题使用传统书法风格字体
- 添加subtle的中式装饰边框
- 使用渐变背景增加层次感
- 按钮采用圆角设计，悬停效果
- 结果区域使用卡片式布局
```

## 功能优化提示词

### 1. 前端性能优化
```
优化前端性能和用户体验：
- 减少CSS文件大小，移除冗余样式
- 优化JavaScript代码，减少重复逻辑
- 实现表单数据本地缓存
- 添加网络状态检测
- 优化移动端触摸体验
```

### 2. 后端API优化
```
改进后端服务性能：
- 优化八字分析算法效率
- 实现结果缓存机制
- 添加请求频率限制
- 改进错误处理和日志记录
- 优化数据库查询性能
```

### 3. 数据处理优化
```
优化数据处理流程：
- 统一前后端数据格式（性别：1=男，0=女）
- 实现数据验证中间件
- 添加数据清洗和格式化
- 优化时辰转换逻辑
- 改进错误数据处理
```

## 用户体验提示词

### 1. 界面友好性
```
提升界面用户友好性：
- 添加输入提示和示例
- 实现智能默认值填充
- 优化表单布局和间距
- 添加键盘快捷键支持
- 实现无障碍访问支持
```

### 2. 反馈机制
```
完善用户反馈机制：
- 实时显示分析进度
- 提供详细的错误信息
- 添加成功状态提示
- 实现分析结果预览
- 添加用户操作指导
```

### 3. 移动端适配
```
优化移动端用户体验：
- 调整表单元素大小适配触摸操作
- 优化页面滚动和导航
- 实现手势操作支持
- 优化移动端加载速度
- 适配不同屏幕尺寸
```

## 技术实现提示词

### 1. 前端技术栈
```
使用以下技术优化前端：
- 原生JavaScript（避免引入重型框架）
- CSS3动画和过渡效果
- Flexbox/Grid布局
- 媒体查询响应式设计
- Web API（Fetch、LocalStorage等）
```

### 2. 后端技术栈
```
后端技术优化方向：
- Flask框架优化配置
- 异步处理长时间任务
- 数据库连接池优化
- 缓存策略实现
- API接口标准化
```

### 3. 部署和维护
```
项目部署和维护建议：
- 实现自动化部署流程
- 添加健康检查接口
- 配置日志轮转和监控
- 实现数据备份策略
- 添加性能监控工具
```

## 具体实现示例

### 1. 页面布局改进
```html
<!-- 建议的HTML结构 -->
<div class="container">
    <header class="page-header">
        <h1 class="title">八字分析</h1>
        <p class="subtitle">传统命理，现代解读</p>
    </header>
    
    <main class="main-content">
        <form class="bazi-form">
            <!-- 表单内容 -->
        </form>
        
        <section class="result-section">
            <!-- 结果展示 -->
        </section>
    </main>
</div>
```

### 2. CSS样式优化
```css
/* 建议的CSS样式结构 */
:root {
    --primary-color: #1a365d;
    --accent-color: #d69e2e;
    --background-color: #f7fafc;
    --text-color: #2d3748;
    --border-radius: 8px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.bazi-form {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}
```

### 3. JavaScript交互优化
```javascript
// 建议的JavaScript结构
class BaziAnalyzer {
    constructor() {
        this.form = document.getElementById('baziForm');
        this.resultContainer = document.getElementById('result');
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadCachedData();
    }
    
    bindEvents() {
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        // 处理表单提交
    }
}

new BaziAnalyzer();
```

## 使用说明

1. **设计阶段**：参考页面设计提示词进行UI/UX设计
2. **开发阶段**：按照功能优化提示词改进代码
3. **测试阶段**：根据用户体验提示词进行测试
4. **部署阶段**：遵循技术实现提示词进行部署

## 注意事项

- 保持代码简洁，每个文件不超过300行
- 确保前后端数据格式一致
- 重视用户体验和页面性能
- 遵循响应式设计原则
- 注重代码可维护性和扩展性