<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密生成器 - 八字分析系统</title>
    <link rel="stylesheet" href="css/card_generator.css">
    <style>
        /* 批量选择菜单样式 */
        .batch-selection-menu {
            position: absolute;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            min-width: 150px;
        }
        
        .batch-selection-menu div {
            padding: 8px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .batch-selection-menu div:hover {
            background-color: #f5f5f5;
        }
        
        .batch-selection-menu div.divider {
            border-top: 1px solid #eee;
            margin: 5px 0;
        }
        
        /* 按钮组样式 */
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        /* 表格选中行样式 */
        #systemCardTable tr.selected {
            background-color: #f0f7ff;
        }
        
        /* 删除按钮样式 */
        .delete-btn {
            background-color: #ff4d4f;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 3px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s;
        }
        
        .delete-btn:hover {
            background-color: #ff7875;
        }
        
        /* 复选框样式 */
        .system-card-checkbox {
            cursor: pointer;
            width: 16px;
            height: 16px;
        }
        
        /* 表格悬停效果 */
        #systemCardTable tbody tr:hover {
            background-color: #f9f9f9;
        }
        
        /* 卡密状态样式 */
        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        
        .status-badge.api-valid {
            background-color: #52c41a;  /* 绿色 */
        }
        
        .status-badge.valid {
            background-color: #1890ff;  /* 蓝色 */
        }
        
        .status-badge.invalid {
            background-color: #ff4d4f;  /* 红色 */
        }
        
        /* 卡密过期样式 */
        tr.expired td {
            color: #999;
        }
        
        /* 卡密用完样式 */
        tr.used-up td {
            color: #999;
            text-decoration: line-through;
        }
        
        /* 按钮样式 */
        .btn-warning {
            background-color: #faad14;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-warning:hover {
            background-color: #ffc53d;
        }
        
        /* 系统信息提示 */
        .system-info-alert {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            border-radius: 4px;
            padding: 12px 15px;
            margin-bottom: 15px;
            color: #876800;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .system-info-alert strong {
            color: #614700;
        }

        @keyframes highlight-row {
            0% { background-color: #fffae6; }
            70% { background-color: #fffae6; }
            100% { background-color: transparent; }
        }
    </style>
</head>
<body>
    <!-- 密码保护层 -->
    <div id="passwordProtection" style="display: flex; justify-content: center; align-items: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center;">
            <h2 style="color: #333; margin-bottom: 20px;">🔒 访问受限</h2>
            <p style="color: #666; margin-bottom: 20px;">此页面需要密码验证</p>
            <p style="color: #999; font-size: 14px;">页面将自动弹出密码输入框</p>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div id="mainContent" style="display: none;">
        <div class="container">
            <div class="header">
                <h1>🎫 卡密生成器</h1>
                <p>八字分析系统卡密管理中心</p>
                <div class="nav-links">
                    <a href="/" class="nav-link">首页</a>
                    <a href="/config" class="nav-link">配置管理</a>
                </div>
            </div>

            <div class="content">
                <div id="status" class="status"></div>
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>正在处理...</p>
                </div>

                <!-- 卡密生成配置 -->
                <div class="section">
                    <h2>⚙️ 生成配置</h2>
                    <div class="form-group">
                        <label for="cardCount">生成数量：</label>
                        <input type="number" id="cardCount" min="1" max="1000" value="10">
                    </div>
                    <div class="form-group">
                        <label for="cardLength">卡密长度：</label>
                        <input type="number" id="cardLength" min="6" max="32" value="16">
                    </div>
                    <div class="form-group">
                        <label for="cardPrefix">卡密前缀（可选）：</label>
                        <input type="text" id="cardPrefix" placeholder="例如：BAZI-">
                    </div>
                    <div class="form-group">
                        <label for="cardFormat">卡密格式：</label>
                        <select id="cardFormat">
                            <option value="random">随机字符（数字+字母）</option>
                            <option value="numbers">仅数字</option>
                            <option value="letters">仅字母</option>
                            <option value="uuid">UUID格式</option>
                            <option value="segments">分段格式（XXXX-XXXX-XXXX-XXXX）</option>
                        </select>
                    </div>
                </div>

                <!-- 卡密有效期配置 -->
                <div class="section">
                    <h2>🕒 有效期配置</h2>
                    <div class="form-group">
                        <label for="expireDays">有效天数：</label>
                        <input type="number" id="expireDays" min="1" max="3650" value="30">
                    </div>
                    <div class="form-group">
                        <label for="maxUsage">最大使用次数：</label>
                        <input type="number" id="maxUsage" min="1" max="10000" value="100">
                    </div>
                </div>

                <!-- 卡密生成操作 -->
                <div class="section">
                    <h2>🚀 生成操作</h2>
                    <button class="btn btn-primary" onclick="generateCards()">生成卡密</button>
                    <button class="btn btn-success" onclick="saveCardsToSystem()">保存到系统</button>
                    <button class="btn btn-secondary" onclick="exportCards()">导出卡密</button>
                    <button class="btn btn-secondary" onclick="copyCards()">复制卡密</button>
                    <button class="btn btn-danger" onclick="clearCards()">清空列表</button>
                </div>

                <!-- 已生成的卡密 -->
                <div class="section">
                    <h2>📋 已生成卡密</h2>
                    <div class="card-stats">
                        <div class="stat-item">
                            <span class="stat-label">总数量</span>
                            <span class="stat-value" id="totalCards">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">已保存</span>
                            <span class="stat-value" id="savedCards">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">未保存</span>
                            <span class="stat-value" id="unsavedCards">0</span>
                        </div>
                    </div>
                    <div class="card-list-container">
                        <table id="cardTable" class="card-table">
                            <thead>
                                <tr>
                                    <th width="5%"><input type="checkbox" id="selectAll" onclick="toggleSelectAll()"></th>
                                    <th width="5%">序号</th>
                                    <th width="50%">卡密</th>
                                    <th width="15%">有效期</th>
                                    <th width="15%">使用次数</th>
                                    <th width="10%">状态</th>
                                </tr>
                            </thead>
                            <tbody id="cardTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 系统中的卡密 -->
                <div class="section">
                    <h2>💾 系统中的卡密</h2>
                    <div class="system-info-alert">
                        <strong>系统说明：</strong> 本页面显示的卡密数据来自两个来源：API接口和后端cards.json文件。删除操作只会通过API接口执行，不会直接修改cards.json文件。因此，删除操作在刷新页面后可能不会保留，需要管理员手动更新后端文件。
                    </div>
                    <div class="button-group">
                        <button class="btn btn-secondary" onclick="loadSystemCards(true)">刷新列表</button>
                        <button class="btn btn-warning" onclick="forceRefreshCards()">强制刷新</button>
                        <button id="selectBatchBtn" class="btn btn-secondary">批量选择</button>
                        <button id="batchDeleteBtn" class="btn btn-danger" onclick="batchDeleteSystemCards()">批量删除</button>
                    </div>
                    <div class="system-card-list-container">
                        <table id="systemCardTable" class="card-table">
                            <thead>
                                <tr>
                                    <th class="select-all-th" width="5%"><input type="checkbox" id="selectAllSystem" onclick="toggleSelectAllSystem()"></th>
                                    <th width="5%">序号</th>
                                    <th width="40%">卡密</th>
                                    <th width="15%">创建时间</th>
                                    <th width="15%">过期时间</th>
                                    <th width="10%">使用情况</th>
                                    <th width="5%">状态</th>
                                    <th width="5%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="systemCardTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 根据当前环境确定API基础URL
        const getBaseUrl = () => {
            const hostname = window.location.hostname;
            const port = window.location.port;
            
            // 开发环境使用完整URL（包含端口）
            if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '***********') {
                return `http://${hostname}:${port || 5000}/api`;
            }
            
            // 生产环境仅使用相对路径
            return '/api';
        };
        
        const API_BASE = getBaseUrl();
        console.log('使用API基础URL:', API_BASE);

        // 生成的卡密列表
        let generatedCards = [];
        let savedCardKeys = new Set();
        
        // 获取卡密详情的辅助函数
        async function fetchCardDetails(forceRefresh = false) {
            try {
                // 强制刷新时清除缓存
                if (forceRefresh) {
                    console.log('强制刷新卡密数据，清除缓存');
                    localStorage.removeItem('cardDetailsCache');
                }
                
                // 检查是否最近添加了新卡密
                const cacheTime = localStorage.getItem('lastAddCardTime');
                if (cacheTime && (Date.now() - parseInt(cacheTime) < 60000)) {
                    // 如果1分钟内添加过新卡密，强制刷新
                    console.log('检测到1分钟内添加了新卡密，强制刷新');
                    forceRefresh = true;
                    localStorage.removeItem('cardDetailsCache');
                }
                
                // 如果不是强制刷新，尝试从缓存中获取（缓存时间减少为1分钟）
                if (!forceRefresh) {
                    const cache = localStorage.getItem('cardDetailsCache');
                    if (cache) {
                        try {
                            const cacheData = JSON.parse(cache);
                            const cacheTime = cacheData.timestamp;
                            // 缓存有效期减少为1分钟
                            if (Date.now() - cacheTime < 60000) {
                                console.log('使用缓存的卡密详情数据');
                                return cacheData.data;
                            } else {
                                console.log('缓存已过期，重新获取数据');
                            }
                        } catch (e) {
                            console.error('解析缓存数据失败:', e);
                        }
                    }
                }
                
                // 添加随机时间戳防止缓存
                const timestamp = Date.now();
                const cacheBuster = `?t=${timestamp}`;
                
                // 尝试多种路径获取卡密详情
                const paths = [
                    `${API_BASE}/cards/json${cacheBuster}`,  // 首选新API
                    `/backend/cards.json${cacheBuster}`,
                    `${API_BASE}/cards/details${cacheBuster}`,
                    `${API_BASE}/cards${cacheBuster}`
                ];
                
                for (const path of paths) {
                    try {
                        console.log(`尝试从 ${path} 获取卡密详情`);
                        const response = await fetch(path, { 
                            method: 'GET',
                            cache: 'no-store',  // 禁用缓存
                            headers: { 
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache',
                                'Expires': '0'
                            }
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            console.log(`成功从 ${path} 获取卡密详情，包含 ${Object.keys(data).length} 个卡密`);
                            
                            // 缓存数据，但缓存时间减少
                            try {
                                localStorage.setItem('cardDetailsCache', JSON.stringify({
                                    timestamp: Date.now(),
                                    data: data
                                }));
                            } catch (e) {
                                console.error('缓存卡密详情数据失败:', e);
                            }
                            
                            return data;
                        }
                    } catch (err) {
                        console.log(`从 ${path} 获取卡密详情失败:`, err);
                    }
                }
                
                console.error('所有路径都无法获取卡密详情');
                return null;
            } catch (error) {
                console.error('获取卡密详情失败:', error);
                return null;
            }
        }

        // 显示状态消息
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        // 显示加载状态
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 生成随机字符串
        function generateRandomString(length, type = 'random') {
            let chars = '';
            switch (type) {
                case 'numbers':
                    chars = '0123456789';
                    break;
                case 'letters':
                    chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
                    break;
                case 'random':
                default:
                    chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                    break;
            }
            
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // 生成UUID格式
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // 生成分段格式
        function generateSegments(length) {
            const segmentLength = 4;
            const segments = Math.ceil(length / segmentLength);
            let result = '';
            
            for (let i = 0; i < segments; i++) {
                result += generateRandomString(Math.min(segmentLength, length - i * segmentLength), 'random');
                if (i < segments - 1) {
                    result += '-';
                }
            }
            
            return result;
        }

        // 生成卡密
        function generateCards() {
            const count = parseInt(document.getElementById('cardCount').value);
            const length = parseInt(document.getElementById('cardLength').value);
            const prefix = document.getElementById('cardPrefix').value;
            const format = document.getElementById('cardFormat').value;
            const expireDays = parseInt(document.getElementById('expireDays').value);
            const maxUsage = parseInt(document.getElementById('maxUsage').value);
            
            if (isNaN(count) || count < 1 || count > 1000) {
                showStatus('请输入有效的生成数量（1-1000）', true);
                return;
            }
            
            if (isNaN(length) || length < 6 || length > 32) {
                showStatus('请输入有效的卡密长度（6-32）', true);
                return;
            }
            
            if (isNaN(expireDays) || expireDays < 1) {
                showStatus('请输入有效的有效天数', true);
                return;
            }
            
            if (isNaN(maxUsage) || maxUsage < 1) {
                showStatus('请输入有效的最大使用次数', true);
                return;
            }
            
            // 生成卡密
            const newCards = [];
            const existingCardSet = new Set(generatedCards.map(item => item.card));
            let attempts = 0;
            const maxAttempts = count * 3; // 最多尝试次数，避免无限循环
            
            showLoading(true);
            
            while (newCards.length < count && attempts < maxAttempts) {
                attempts++;
                let card;
                
                switch (format) {
                    case 'numbers':
                        card = generateRandomString(length, 'numbers');
                        break;
                    case 'letters':
                        card = generateRandomString(length, 'letters');
                        break;
                    case 'uuid':
                        card = generateUUID();
                        break;
                    case 'segments':
                        card = generateSegments(length);
                        break;
                    case 'random':
                    default:
                        card = generateRandomString(length, 'random');
                        break;
                }
                
                // 添加前缀
                if (prefix) {
                    card = prefix + card;
                }
                
                // 检查是否重复
                if (!existingCardSet.has(card) && !savedCardKeys.has(card)) {
                    newCards.push({
                        card,
                        expireDays,
                        maxUsage,
                        saved: false
                    });
                    existingCardSet.add(card);
                }
            }
            
            // 添加到已生成列表
            generatedCards = [...generatedCards, ...newCards];
            updateCardTable();
            showLoading(false);
            
            if (newCards.length < count) {
                showStatus(`已生成 ${newCards.length} 个卡密，部分卡密因重复被跳过`);
            } else {
                showStatus(`已生成 ${count} 个卡密`);
            }
        }

        // 更新卡密表格
        function updateCardTable() {
            const tbody = document.getElementById('cardTableBody');
            tbody.innerHTML = '';
            
            generatedCards.forEach((item, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="card-checkbox" data-index="${index}"></td>
                    <td>${index + 1}</td>
                    <td>${item.card}</td>
                    <td>${item.expireDays} 天</td>
                    <td>${item.maxUsage} 次</td>
                    <td>${item.saved ? '<span class="saved-badge">已保存</span>' : '<span class="unsaved-badge">未保存</span>'}</td>
                `;
                tbody.appendChild(row);
            });
            
            // 更新统计信息
            document.getElementById('totalCards').textContent = generatedCards.length;
            document.getElementById('savedCards').textContent = generatedCards.filter(c => c.saved).length;
            document.getElementById('unsavedCards').textContent = generatedCards.filter(c => !c.saved).length;
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.card-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 获取选中的卡密
        function getSelectedCards() {
            const selected = [];
            const checkboxes = document.querySelectorAll('.card-checkbox:checked');
            
            checkboxes.forEach(checkbox => {
                const index = parseInt(checkbox.getAttribute('data-index'));
                if (!isNaN(index) && index >= 0 && index < generatedCards.length) {
                    selected.push(generatedCards[index]);
                }
            });
            
            return selected;
        }

        // 保存卡密到系统
        async function saveCardsToSystem() {
            const selected = getSelectedCards();
            
            if (selected.length === 0) {
                showStatus('请选择要保存的卡密', true);
                return;
            }
            
            showLoading(true);
            
            try {
                // 获取已有卡密列表
                const existingResponse = await fetch(`${API_BASE}/config/cards`);
                const existingData = await existingResponse.json();
                
                // 适配不同的API响应格式
                let existingCards = [];
                if (existingData.valid_cards) {
                    // 如果API返回的是卡密数组
                    existingCards = existingData.valid_cards;
                } else if (typeof existingData === 'object' && !Array.isArray(existingData)) {
                    // 如果API返回的是卡密对象
                    existingCards = Object.keys(existingData);
                }
                
                let successCount = 0;
                let errorCount = 0;
                let addedCards = [];
                
                // 逐个添加卡密
                for (const item of selected) {
                    // 检查是否已存在
                    if (existingCards.includes(item.card) || savedCardKeys.has(item.card)) {
                        errorCount++;
                        continue;
                    }
                    
                    try {
                        // 使用后端的卡密管理API
                        const cardData = {
                            card: item.card,
                            expire_days: item.expireDays,
                            max_usage: item.maxUsage
                        };
                        
                        console.log('添加卡密数据:', cardData);
                        console.log('API URL:', `${API_BASE}/config/cards/add`);
                        
                        const response = await fetch(`${API_BASE}/config/cards/add`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(cardData)
                        });
                        
                        const data = await response.json();
                        console.log('API响应:', data);
                        
                        if (data.success) {
                            item.saved = true;
                            savedCardKeys.add(item.card);
                            successCount++;
                            addedCards.push(item.card);
                            
                            // 记录新卡密添加时间
                            localStorage.setItem('lastAddCardTime', Date.now().toString());
                            
                            // 如果API返回了最新的卡密列表，更新existingCards
                            if (data.valid_cards && Array.isArray(data.valid_cards)) {
                                existingCards = data.valid_cards;
                            }
                        } else {
                            errorCount++;
                            console.error('API错误:', data.error);
                        }
                    } catch (error) {
                        console.error(`添加卡密 ${item.card} 失败:`, error);
                        errorCount++;
                    }
                }
                
                // 更新表格显示
                updateCardTable();
                
                // 重置全选复选框
                document.getElementById('selectAll').checked = false;
                
                // 清除所有缓存
                localStorage.removeItem('cardDetailsCache');
                
                // 强制重新加载后端配置，确保前后端数据一致
                try {
                    console.log('强制重新加载后端配置');
                    await fetch(`${API_BASE}/config/reload`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache, no-store, must-revalidate'
                        }
                    });
                } catch (error) {
                    console.error('重新加载配置失败:', error);
                }
                
                // 直接获取最新的卡密数据
                const timestamp = Date.now();
                console.log(`获取最新卡密数据: ${API_BASE}/cards/json?t=${timestamp}`);
                
                try {
                    const response = await fetch(`${API_BASE}/cards/json?t=${timestamp}`, {
                        method: 'GET',
                        cache: 'no-store',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        console.log('最新卡密数据:', data);
                        console.log('卡密列表:', Object.keys(data));
                        
                        // 检查新添加的卡密是否在返回的数据中
                        const returnedCards = Object.keys(data);
                        const missingCards = addedCards.filter(card => !returnedCards.includes(card));
                        
                        if (missingCards.length > 0) {
                            console.warn('以下新添加的卡密未在返回数据中找到:', missingCards);
                        } else {
                            console.log('所有新添加的卡密都已在返回数据中找到');
                        }
                        
                        updateSystemCardTable(data);
                        
                        // 高亮显示新添加的卡密
                        setTimeout(() => {
                            highlightNewCards(addedCards);
                        }, 500);
                        
                        showStatus(`已刷新 ${Object.keys(data).length} 个卡密，新添加 ${successCount} 个`);
                    } else {
                        console.error('获取最新卡密数据失败:', response.status);
                        throw new Error(`获取最新卡密数据失败: ${response.status}`);
                    }
                } catch (error) {
                    console.error('获取最新卡密数据出错:', error);
                    showStatus('获取最新卡密数据失败，请手动刷新', true);
                }
                
                if (errorCount === 0) {
                    showStatus(`成功保存 ${successCount} 个卡密`);
                } else {
                    showStatus(`成功保存 ${successCount} 个卡密，${errorCount} 个失败`, true);
                }
            } catch (error) {
                console.error('保存卡密时出错:', error);
                showStatus('保存卡密失败', true);
            } finally {
                showLoading(false);
            }
        }
        
        // 高亮显示新添加的卡密
        function highlightNewCards(cardList) {
            if (!cardList || cardList.length === 0) return;
            
            const rows = document.querySelectorAll('#systemCardTableBody tr');
            
            rows.forEach(row => {
                const cardCell = row.querySelector('td:nth-child(3)');
                if (!cardCell) return;
                
                const cardCode = cardCell.querySelector('code');
                if (!cardCode) return;
                
                const cardText = cardCode.textContent;
                
                if (cardList.includes(cardText)) {
                    // 添加高亮样式
                    row.style.animation = 'highlight-row 3s';
                    row.style.backgroundColor = '#fffae6';
                    
                    // 3秒后恢复
                    setTimeout(() => {
                        row.style.backgroundColor = '';
                    }, 3000);
                }
            });
        }

        // 导出卡密
        function exportCards() {
            const selected = getSelectedCards();
            
            if (selected.length === 0) {
                showStatus('请选择要导出的卡密', true);
                return;
            }
            
            // 创建CSV内容
            let csvContent = 'card,expire_days,max_usage,saved\n';
            selected.forEach(item => {
                csvContent += `${item.card},${item.expireDays},${item.maxUsage},${item.saved}\n`;
            });
            
            console.log('导出卡密数据:', csvContent);
            
            try {
                // 创建下载链接
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', `卡密_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showStatus(`已导出 ${selected.length} 个卡密`);
            } catch (error) {
                console.error('导出卡密失败:', error);
                showStatus('导出卡密失败', true);
            }
        }

        // 复制卡密
        function copyCards() {
            const selected = getSelectedCards();
            
            if (selected.length === 0) {
                showStatus('请选择要复制的卡密', true);
                return;
            }
            
            // 创建纯文本内容
            const textContent = selected.map(item => item.card).join('\n');
            
            // 检查是否支持现代剪贴板API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                // 使用现代剪贴板API
                navigator.clipboard.writeText(textContent)
                    .then(() => {
                        showStatus(`已复制 ${selected.length} 个卡密到剪贴板`);
                    })
                    .catch(err => {
                        console.error('现代剪贴板API复制失败:', err);
                        // 降级到传统方法
                        fallbackCopyToClipboard(textContent, selected.length);
                    });
            } else {
                // 使用传统方法
                fallbackCopyToClipboard(textContent, selected.length);
            }
        }
        
        // 传统剪贴板复制方法（兼容性更好）
        function fallbackCopyToClipboard(text, count) {
            try {
                // 创建临时文本区域
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                
                // 选择并复制
                textArea.focus();
                textArea.select();
                
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                
                if (successful) {
                    showStatus(`已复制 ${count} 个卡密到剪贴板`);
                } else {
                    throw new Error('execCommand复制失败');
                }
            } catch (err) {
                console.error('传统剪贴板复制失败:', err);
                // 最后的降级方案：显示文本让用户手动复制
                showCopyModal(text, count);
            }
        }
        
        // 显示复制模态框（最后的降级方案）
        function showCopyModal(text, count) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 20px;
                border-radius: 8px;
                max-width: 80%;
                max-height: 80%;
                overflow: auto;
            `;
            
            content.innerHTML = `
                <h3>请手动复制以下卡密 (${count}个)</h3>
                <textarea readonly style="width: 100%; height: 200px; font-family: monospace;">${text}</textarea>
                <div style="margin-top: 10px; text-align: right;">
                    <button onclick="this.closest('.modal').remove()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
                </div>
            `;
            
            modal.className = 'modal';
            modal.appendChild(content);
            document.body.appendChild(modal);
            
            // 自动选择文本
            const textarea = content.querySelector('textarea');
            textarea.focus();
            textarea.select();
            
            showStatus('请手动复制上方文本框中的内容', true);
        }

        // 清空卡密列表
        function clearCards() {
            if (generatedCards.length === 0) {
                showStatus('列表已经是空的');
                return;
            }
            
            if (confirm('确定要清空列表吗？此操作不可撤销。')) {
                generatedCards = [];
                updateCardTable();
                // 重置全选复选框
                document.getElementById('selectAll').checked = false;
                showStatus('已清空卡密列表');
            }
        }

        // 加载系统中的卡密
        async function loadSystemCards(forceRefresh = false) {
            showLoading(true);
            
            try {
                // 首先尝试从/api/cards/json获取完整卡密数据
                try {
                    // 添加防缓存参数
                    const cacheBuster = forceRefresh ? `?t=${Date.now()}` : '';
                    console.log('尝试从API获取完整卡密数据，URL:', `${API_BASE}/cards/json${cacheBuster}`);
                    
                    const response = await fetch(`${API_BASE}/cards/json${cacheBuster}`, {
                        cache: forceRefresh ? 'no-store' : 'default',
                        headers: forceRefresh ? { 'Cache-Control': 'no-cache, no-store, must-revalidate' } : {}
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        console.log('完整卡密数据包含', Object.keys(data).length, '个卡密');
                        
                        if (data && typeof data === 'object' && !Array.isArray(data)) {
                            updateSystemCardTable(data);
                            showStatus('已加载系统卡密');
                            showLoading(false);
                            return;
                        }
                    }
                } catch (error) {
                    console.error('获取完整卡密数据失败:', error);
                }
                
                // 如果直接获取失败，尝试获取有效卡密列表和详细信息
                const cacheBuster = forceRefresh ? `?t=${Date.now()}` : '';
                console.log('加载系统卡密，API URL:', `${API_BASE}/config/cards${cacheBuster}`);
                
                const response = await fetch(`${API_BASE}/config/cards${cacheBuster}`, {
                    cache: forceRefresh ? 'no-store' : 'default',
                    headers: forceRefresh ? { 'Cache-Control': 'no-cache, no-store, must-revalidate' } : {}
                });
                
                const data = await response.json();
                console.log('系统卡密API响应:', data);
                
                // 保存API返回的有效卡密列表
                if (data.success && data.valid_cards && Array.isArray(data.valid_cards)) {
                    const statusElem = document.getElementById('status');
                    statusElem.setAttribute('data-valid-cards', JSON.stringify(data.valid_cards));
                    console.log('保存API返回的有效卡密列表:', data.valid_cards);
                }
                
                // 检查返回的数据格式，适配不同的API响应结构
                let systemCards = {};
                
                // 直接从cards.json文件获取所有卡密详情
                const detailsData = await fetchCardDetails(forceRefresh);
                if (detailsData) {
                    console.log('获取到详细卡密信息:', detailsData);
                    // 使用所有从cards.json获取到的卡密
                    systemCards = detailsData;
                    
                    // 如果API返回了valid_cards，可以用它来标记哪些卡密是有效的
                    if (data.success && data.valid_cards && Array.isArray(data.valid_cards)) {
                        console.log('API返回的有效卡密列表:', data.valid_cards);
                        // 可以在这里做一些额外处理，比如标记哪些卡密是API认为有效的
                    }
                } else {
                    // 如果无法获取详细信息，则使用API返回的数据
                    if (data.success && data.valid_cards && Array.isArray(data.valid_cards)) {
                        // 格式：{ success: true, valid_cards: [...] }
                        console.log('检测到valid_cards数组格式，开始构建卡密详情');
                        
                        // 构建基本卡密信息
                        data.valid_cards.forEach(card => {
                            systemCards[card] = {
                                valid: true,
                                created_time: new Date().toISOString(),
                                expire_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                                usage_count: 0,
                                max_usage: 100
                            };
                        });
                    } else if (data.success && data.data) {
                        // 标准格式：{ success: true, data: {...} }
                        systemCards = data.data;
                    } else if (typeof data === 'object' && !Array.isArray(data)) {
                        // 直接返回卡密对象：{ "card1": {...}, "card2": {...} }
                        systemCards = data;
                    } else {
                        throw new Error('无效的卡密数据格式');
                    }
                }
                
                console.log('最终卡密数据包含', Object.keys(systemCards).length, '个卡密');
                updateSystemCardTable(systemCards);
                showStatus('已加载系统卡密');
            } catch (error) {
                console.error('加载系统卡密时出错:', error);
                showStatus('加载系统卡密失败', true);
            } finally {
                showLoading(false);
            }
        }

        // 更新系统卡密表格
        function updateSystemCardTable(systemCards) {
            const tbody = document.getElementById('systemCardTableBody');
            tbody.innerHTML = '';
            
            // 更新卡密数量显示
            const cardCountElem = document.getElementById('systemCardCount');
            if (cardCountElem) {
                cardCountElem.textContent = Object.keys(systemCards).length;
            } else {
                // 如果不存在卡密数量元素，创建一个
                const cardTableHeaders = document.querySelectorAll('.section h2');
                let systemCardHeader = null;
                
                // 查找包含"系统卡密"文本的h2元素
                for (const header of cardTableHeaders) {
                    if (header.textContent.includes('系统卡密')) {
                        systemCardHeader = header;
                        break;
                    }
                }
                
                if (systemCardHeader) {
                    systemCardHeader.innerHTML = `系统卡密 <span id="systemCardCount" style="font-size: 0.8em; color: #666;">(${Object.keys(systemCards).length})</span>`;
                }
            }
            
            // 如果没有卡密数据，显示提示信息
            if (Object.keys(systemCards).length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `<td colspan="8" style="text-align: center; padding: 20px;">暂无卡密数据</td>`;
                tbody.appendChild(emptyRow);
                return;
            }
            
            // 获取API认为有效的卡密列表
            let validCardsFromAPI = [];
            try {
                const apiResponse = document.querySelector('#status').getAttribute('data-valid-cards');
                if (apiResponse) {
                    validCardsFromAPI = JSON.parse(apiResponse);
                }
            } catch (e) {
                console.error('解析API有效卡密列表失败:', e);
            }
            
            const now = new Date();
            let index = 1;
            for (const [card, info] of Object.entries(systemCards)) {
                const row = document.createElement('tr');
                
                try {
                    // 格式化日期
                    const createDate = info.created_time ? new Date(info.created_time).toLocaleString() : '未知';
                    const expireDate = info.expire_time ? new Date(info.expire_time) : null;
                    const expireDateStr = expireDate ? expireDate.toLocaleString() : '未知';
                    
                    // 使用情况
                    const usageCount = info.usage_count || 0;
                    const maxUsage = info.max_usage || Infinity;
                    const usageText = `${usageCount}/${maxUsage === Infinity ? '∞' : maxUsage}`;
                    
                    // 判断卡密是否有效
                    const isValidFromAPI = validCardsFromAPI.includes(card);
                    const isValid = info.valid !== false;
                    const statusClass = isValidFromAPI ? 'api-valid' : (isValid ? 'valid' : 'invalid');
                    const statusText = isValidFromAPI ? 'API有效' : (isValid ? '有效' : '无效');
                    
                    // 判断卡密是否过期或用完
                    const isExpired = expireDate && expireDate < now;
                    const isUsedUp = usageCount >= maxUsage;
                    
                    if (isExpired) {
                        row.classList.add('expired');
                    }
                    
                    if (isUsedUp) {
                        row.classList.add('used-up');
                    }
                    
                    row.innerHTML = `
                        <td><input type="checkbox" class="system-card-checkbox" data-card="${card}"></td>
                        <td>${index++}</td>
                        <td><code>${card}</code></td>
                        <td>${createDate}</td>
                        <td>${expireDateStr}</td>
                        <td>${usageText}</td>
                        <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                        <td>
                            <button class="delete-btn" onclick="removeSystemCard('${card}')">删除</button>
                        </td>
                    `;
                    
                    tbody.appendChild(row);
                } catch (e) {
                    console.error(`处理卡密 ${card} 时出错:`, e);
                }
            }
        }

        // 显示批量选择选项
        function showBatchSelectionOptions() {
            // 创建一个简单的弹出菜单
            const menu = document.createElement('div');
            menu.className = 'batch-selection-menu';
            
            // 获取按钮位置
            const btn = document.getElementById('selectBatchBtn');
            const rect = btn.getBoundingClientRect();
            menu.style.top = (rect.bottom + window.scrollY) + 'px';
            menu.style.left = (rect.left + window.scrollX) + 'px';
            
            // 添加选项
            menu.innerHTML = `
                <div onclick="selectByRange(1, 10)">选择前10个</div>
                <div onclick="selectByRange(11, 20)">选择11-20个</div>
                <div onclick="selectByRange(21, 30)">选择21-30个</div>
                <div class="divider"></div>
                <div onclick="selectExpired()">选择已过期卡密</div>
                <div onclick="selectUsed()">选择已用完卡密</div>
                <div onclick="selectUnused()">选择未使用卡密</div>
                <div class="divider"></div>
                <div onclick="selectAll()">全部选择</div>
                <div onclick="selectNone()">取消选择</div>
                <div onclick="selectInvert()">反选</div>
            `;
            
            // 添加到文档
            document.body.appendChild(menu);
            
            // 点击其他地方关闭菜单
            const closeMenu = (e) => {
                if (!menu.contains(e.target) && e.target !== btn) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            };
            
            // 延迟添加事件监听器，避免立即触发
            setTimeout(() => {
                document.addEventListener('click', closeMenu);
            }, 100);
        }
        
        // 全选
        function selectAll() {
            const checkboxes = document.querySelectorAll('.system-card-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            
            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('selectAllSystem');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = true;
            }
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 取消全选
        function selectNone() {
            const checkboxes = document.querySelectorAll('.system-card-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 更新全选复选框状态
            const selectAllCheckbox = document.getElementById('selectAllSystem');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
            }
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 反选
        function selectInvert() {
            const checkboxes = document.querySelectorAll('.system-card-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = !checkbox.checked;
            });
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 选择未使用的卡密
        function selectUnused() {
            const rows = document.querySelectorAll('#systemCardTableBody tr');
            
            rows.forEach(row => {
                const checkbox = row.querySelector('.system-card-checkbox');
                if (!checkbox) return;
                
                const usageCell = row.cells[5]; // 使用情况列
                if (!usageCell) return;
                
                try {
                    const usageText = usageCell.textContent;
                    const [used, max] = usageText.split('/').map(n => parseInt(n.trim()));
                    checkbox.checked = (used === 0);
                } catch (e) {
                    console.error('解析使用情况失败:', e);
                }
            });
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 按范围选择卡密
        function selectByRange(start, end) {
            const checkboxes = document.querySelectorAll('.system-card-checkbox');
            checkboxes.forEach((checkbox, index) => {
                checkbox.checked = (index + 1 >= start && index + 1 <= end);
            });
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 选择已过期的卡密
        function selectExpired() {
            const now = new Date();
            const rows = document.querySelectorAll('#systemCardTableBody tr');
            
            rows.forEach(row => {
                const checkbox = row.querySelector('.system-card-checkbox');
                if (!checkbox) return;
                
                const expireDateCell = row.cells[4]; // 过期时间列
                if (!expireDateCell) return;
                
                try {
                    const expireDate = new Date(expireDateCell.textContent);
                    checkbox.checked = (expireDate < now);
                } catch (e) {
                    console.error('解析过期时间失败:', e);
                }
            });
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }
        
        // 选择已用完的卡密
        function selectUsed() {
            const rows = document.querySelectorAll('#systemCardTableBody tr');
            
            rows.forEach(row => {
                const checkbox = row.querySelector('.system-card-checkbox');
                if (!checkbox) return;
                
                const usageCell = row.cells[5]; // 使用情况列
                if (!usageCell) return;
                
                try {
                    const usageText = usageCell.textContent;
                    const [used, max] = usageText.split('/').map(n => parseInt(n.trim()));
                    checkbox.checked = (used >= max);
                } catch (e) {
                    console.error('解析使用情况失败:', e);
                }
            });
            
            // 关闭菜单
            const menu = document.querySelector('.batch-selection-menu');
            if (menu) menu.remove();
        }

        // 全选/取消全选系统卡密
        function toggleSelectAllSystem() {
            const selectAll = document.getElementById('selectAllSystem');
            const checkboxes = document.querySelectorAll('.system-card-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 批量删除系统卡密
        async function batchDeleteSystemCards() {
            const selectedCheckboxes = document.querySelectorAll('.system-card-checkbox:checked');
            const selectedCards = Array.from(selectedCheckboxes).map(cb => cb.getAttribute('data-card'));
            
            if (selectedCards.length === 0) {
                showStatus('请选择要删除的卡密', true);
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${selectedCards.length} 个卡密吗？\n\n注意：删除操作将会彻底从系统中移除这些卡密。`)) {
                return;
            }
            
            showLoading(true);
            
            try {
                console.log('批量删除卡密，API URL:', `${API_BASE}/cards/batch-remove`);
                console.log('删除卡密数据:', { cards: selectedCards });
                
                const response = await fetch(`${API_BASE}/cards/batch-remove`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cards: selectedCards })
                });
                
                const data = await response.json();
                console.log('批量删除卡密API响应:', data);
                
                if (data.success) {
                    // 从界面中移除选中的卡密行
                    selectedCards.forEach(card => {
                        savedCardKeys.delete(card);
                        
                        // 直接从DOM中移除该行
                        const checkbox = document.querySelector(`.system-card-checkbox[data-card="${card}"]`);
                        if (checkbox && checkbox.closest('tr')) {
                            checkbox.closest('tr').remove();
                        }
                    });
                    
                    // 重置全选复选框
                    document.getElementById('selectAllSystem').checked = false;
                    
                    // 强制刷新卡密数据
                    const detailsData = await fetchCardDetails(true); // 强制刷新
                    if (detailsData) {
                        // 更新表格显示
                        updateSystemCardTable(detailsData);
                    }
                    
                    showStatus(`成功删除 ${data.success_count || selectedCards.length} 个卡密${data.failed_count ? '，失败 '+data.failed_count+' 个' : ''}`);
                } else {
                    showStatus(`批量删除失败: ${data.error}`, true);
                }
            } catch (error) {
                console.error('批量删除卡密时出错:', error);
                showStatus('批量删除卡密失败', true);
                
                // 如果出错，刷新卡密列表
                await loadSystemCards();
            } finally {
                showLoading(false);
            }
        }

        // 删除系统中的卡密
        async function removeSystemCard(card) {
            if (!confirm(`确定要删除卡密 ${card} 吗？\n\n注意：删除操作将会彻底从系统中移除该卡密。`)) {
                return;
            }
            
            showLoading(true);
            
            try {
                console.log('删除卡密，API URL:', `${API_BASE}/cards/remove`);
                console.log('删除卡密数据:', { card });
                
                const response = await fetch(`${API_BASE}/cards/remove`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ card })
                });
                
                const data = await response.json();
                console.log('删除卡密API响应:', data);
                
                if (data.success) {
                    showStatus(`卡密 ${card} 已成功删除`);
                    savedCardKeys.delete(card);
                    
                    // 直接从DOM中移除该行
                    const checkbox = document.querySelector(`.system-card-checkbox[data-card="${card}"]`);
                    if (checkbox && checkbox.closest('tr')) {
                        checkbox.closest('tr').remove();
                    }
                    
                    // 尝试直接删除cards.json文件中的卡密
                    try {
                        const detailsData = await fetchCardDetails(true); // 强制刷新
                        if (detailsData) {
                            // 更新表格显示
                            updateSystemCardTable(detailsData);
                        }
                    } catch (error) {
                        console.error('更新卡密数据失败:', error);
                    }
                } else {
                    showStatus(`删除失败: ${data.error}`, true);
                }
            } catch (error) {
                console.error('删除卡密时出错:', error);
                showStatus('删除卡密失败', true);
            } finally {
                showLoading(false);
            }
        }

        // 密码验证
        function checkPassword() {
            const password = prompt('请输入访问密码：');
            if (password === '88888888') {
                document.getElementById('passwordProtection').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                loadSystemCards();
            } else if (password !== null) {
                alert('密码错误！');
                checkPassword();
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkPassword();
            
            // 绑定批量选择按钮事件
            const selectBatchBtn = document.getElementById('selectBatchBtn');
            if (selectBatchBtn) {
                selectBatchBtn.addEventListener('click', showBatchSelectionOptions);
            }
        });

        // 强制刷新卡密列表
        async function forceRefreshCards() {
            showStatus('正在强制刷新卡密数据...');
            
            // 清除所有缓存
            localStorage.removeItem('cardDetailsCache');
            localStorage.removeItem('lastAddCardTime');
            
            try {
                // 直接请求最新数据
                showLoading(true);
                
                // 先尝试刷新后端配置
                try {
                    console.log('强制刷新后端卡密数据');
                    const reloadResponse = await fetch(`${API_BASE}/config/reload`, {
                        method: 'POST',
                        cache: 'no-store',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'Expires': '0'
                        }
                    });
                    
                    if (reloadResponse.ok) {
                        console.log('后端配置已重新加载');
                    }
                } catch (err) {
                    console.error('刷新后端配置失败:', err);
                }
                
                // 获取最新数据
                const timestamp = Date.now();
                
                // 直接请求卡密API获取数据
                console.log(`尝试从API获取卡密数据: ${API_BASE}/cards/json?t=${timestamp}`);
                const response = await fetch(`${API_BASE}/cards/json?t=${timestamp}`, {
                    method: 'GET',
                    cache: 'no-store',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('完整卡密数据:', data);
                    console.log('卡密列表:', Object.keys(data));
                    console.log('卡密数量:', Object.keys(data).length);
                    
                    // 更新表格
                    updateSystemCardTable(data);
                    showStatus(`已刷新 ${Object.keys(data).length} 个卡密`);
                    return;
                } else {
                    console.error('从API获取卡密数据失败:', response.status);
                    throw new Error(`获取卡密数据失败: ${response.status}`);
                }
            } catch (error) {
                console.error('强制刷新卡密失败:', error);
                showStatus('强制刷新失败，请稍后再试', true);
            } finally {
                showLoading(false);
            }
        }
    </script>
</body>
</html>