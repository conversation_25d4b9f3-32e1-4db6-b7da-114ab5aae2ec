#!/usr/bin/env python3
"""
简化的Flask应用 - 快速修复404问题
"""

import os
import sys
from datetime import datetime
from flask import Flask, send_file, jsonify
from flask_cors import CORS

# 获取项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir.endswith('backend'):
    project_dir = os.path.dirname(script_dir)
else:
    project_dir = script_dir

# 切换到项目根目录
os.chdir(project_dir)
print(f"工作目录: {os.getcwd()}")
print(f"项目目录: {project_dir}")

# 创建Flask应用
app = Flask(__name__, static_folder=project_dir, static_url_path='/static')
CORS(app)

@app.route('/')
def index():
    """主页"""
    print("访问根路径")
    
    # 查找HTML文件
    html_files = ['index.html', 'up.html']
    for filename in html_files:
        file_path = os.path.join(project_dir, filename)
        print(f"检查文件: {file_path}")
        if os.path.exists(file_path):
            print(f"找到文件: {file_path}")
            return send_file(file_path)
    
    # 如果找不到，返回简单页面
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>八字分析系统</title>
        <meta charset="UTF-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .container {{ max-width: 800px; margin: 0 auto; }}
            .status {{ background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; }}
            .links {{ margin: 20px 0; }}
            .links a {{ display: inline-block; margin: 10px; padding: 10px 20px; 
                       background: #007cba; color: white; text-decoration: none; border-radius: 3px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 八字分析系统</h1>
            <div class="status">
                <h3>✅ Flask服务器运行正常</h3>
                <p>当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>项目目录: {project_dir}</p>
                <p>工作目录: {os.getcwd()}</p>
            </div>
            <div class="links">
                <a href="/test">测试页面</a>
                <a href="/api/health">健康检查</a>
                <a href="/static/up.html">分析页面</a>
                <a href="/static/index.html">主页</a>
            </div>
            <h3>📁 项目文件:</h3>
            <ul>
                {''.join([f'<li>{f}</li>' for f in os.listdir(project_dir) if f.endswith('.html')])}
            </ul>
        </div>
    </body>
    </html>
    """

@app.route('/test')
def test():
    """测试页面"""
    return """
    <h1>🎉 测试页面</h1>
    <p>Flask服务器运行正常！</p>
    <ul>
        <li><a href="/">返回主页</a></li>
        <li><a href="/api/health">健康检查</a></li>
    </ul>
    """

@app.route('/api/health')
def health():
    """健康检查"""
    return jsonify({
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "message": "Flask服务器运行正常"
    })

@app.route('/up.html')
def up_html():
    """分析页面"""
    file_path = os.path.join(project_dir, 'up.html')
    if os.path.exists(file_path):
        return send_file(file_path)
    return "找不到up.html文件", 404

@app.route('/index.html')
def index_html():
    """主页HTML"""
    file_path = os.path.join(project_dir, 'index.html')
    if os.path.exists(file_path):
        return send_file(file_path)
    return "找不到index.html文件", 404

# 导入原始应用的其他路由
try:
    sys.path.insert(0, os.path.join(project_dir, 'backend'))
    
    # 导入配置和其他模块
    from config_manager import ConfigManager
    from card_manager import CardManager
    from bazi_service import BaziService
    
    config = ConfigManager()
    card_manager = CardManager()
    bazi_service = BaziService()
    
    @app.route('/webhook/bazi-analysis', methods=['POST', 'OPTIONS'])
    def bazi_analysis():
        """八字分析接口"""
        if request.method == 'OPTIONS':
            return '', 200
        
        try:
            from flask import request
            data = request.get_json()
            
            # 验证卡密
            card_code = data.get('cardCode')
            if not card_manager.validate_card(card_code):
                return jsonify({"error": "无效的卡密"}), 401
            
            # 执行分析
            result = bazi_service.analyze(data)
            return jsonify(result)
            
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    
    @app.route('/webhook/check-status', methods=['GET'])
    def check_status():
        """检查分析状态"""
        try:
            from flask import request
            request_id = request.args.get('request_id')
            status = bazi_service.get_status(request_id)
            return jsonify(status)
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    
    print("✅ 成功导入原始应用的路由")
    
except Exception as e:
    print(f"⚠️ 导入原始路由失败: {e}")

if __name__ == '__main__':
    print("🚀 启动简化Flask服务器")
    print(f"监听地址: 0.0.0.0:5000")
    print(f"项目目录: {project_dir}")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
