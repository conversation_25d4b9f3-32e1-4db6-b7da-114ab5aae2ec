#!/usr/bin/env python3
import sys
print("Starting test...", flush=True)
sys.stdout.flush()

try:
    from app import app
    print("App imported successfully", flush=True)
    
    with app.test_client() as client:
        print("Testing health endpoint...", flush=True)
        response = client.get('/api/health')
        print(f"Health check status: {response.status_code}", flush=True)
        
        if response.status_code == 200:
            print("✅ Health check passed", flush=True)
        else:
            print("❌ Health check failed", flush=True)
            
except Exception as e:
    print(f"Error: {e}", flush=True)
    import traceback
    traceback.print_exc()

print("Test completed", flush=True)
