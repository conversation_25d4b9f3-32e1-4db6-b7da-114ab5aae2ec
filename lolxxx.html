<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="八字分析系统管理后台">
    <meta name="theme-color" content="#4a00e0">
    <title>系统管理后台</title>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* 登录界面样式 */
        .login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }
        
        .login-box h2 {
            margin: 0 0 30px 0;
            color: #333;
            font-size: 2em;
        }
        
        .login-box input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
            transition: border-color 0.3s ease;
        }
        
        .login-box input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-box button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .login-box button:hover {
            transform: translateY(-2px);
        }
        
        .login-error {
            color: #dc3545;
            margin-top: 15px;
            font-size: 14px;
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
            display: none;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .admin-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .admin-nav {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex-wrap: wrap;
        }
        
        .admin-nav-item {
            flex: 1;
            min-width: 150px;
            padding: 15px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .admin-nav-item.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .admin-nav-item:hover:not(.active) {
            background: rgba(255,255,255,0.5);
        }
        
        .admin-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .admin-section {
            display: none;
        }
        
        .admin-section.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .stat-card p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-action {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }
        
        .quick-action:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        .quick-action .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        
        .quick-action .title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .quick-action .desc {
            font-size: 0.9em;
            color: #666;
        }
        
        .system-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .system-info h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .info-item .label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-item .value {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }
        
        /* 报告列表样式 */
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .report-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .report-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        .report-card .report-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .report-card .report-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        .report-card .report-preview {
            color: #888;
            font-size: 0.85em;
            line-height: 1.4;
            max-height: 60px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .report-card .report-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .admin-container {
                padding: 10px;
            }
            
            .admin-header {
                padding: 20px;
            }
            
            .admin-header h1 {
                font-size: 2em;
            }
            
            .admin-nav {
                flex-direction: column;
            }
            
            .admin-nav-item {
                margin-bottom: 5px;
            }
            
            .admin-content {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
            
            .reports-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div id="login-overlay" class="login-overlay">
        <div class="login-box">
            <h2>🔐 管理后台登录</h2>
            <input type="password" id="password-input" placeholder="请输入管理密码" maxlength="20">
            <button onclick="login()">登录</button>
            <div id="login-error" class="login-error" style="display: none;"></div>
        </div>
    </div>
    
    <!-- 管理后台主界面 -->
    <div id="admin-container" class="admin-container">
        <div class="admin-header">
            <button class="logout-btn" onclick="logout()">退出登录</button>
            <h1>⚙️ 系统管理后台</h1>
            <p>八字分析系统的统一管理和监控平台</p>
        </div>
        
        <nav class="admin-nav">
            <a href="#dashboard" class="admin-nav-item active" onclick="showSection('dashboard')">
                <span>📊</span> 仪表盘
            </a>
            <a href="#reports" class="admin-nav-item" onclick="showSection('reports')">
                <span>📋</span> 分析报告
            </a>
            <a href="#prompts" class="admin-nav-item" onclick="showSection('prompts')">
                <span>🧠</span> 提示词管理
            </a>
            <a href="#cards" class="admin-nav-item" onclick="showSection('cards')">
                <span>🎫</span> 卡密管理
            </a>
            <a href="#logs" class="admin-nav-item" onclick="showSection('logs')">
                <span>📋</span> 系统日志
            </a>
            <a href="#settings" class="admin-nav-item" onclick="showSection('settings')">
                <span>⚙️</span> 系统设置
            </a>
        </nav>
        
        <div class="admin-content">
            <!-- 仪表盘 -->
            <div id="dashboard-section" class="admin-section active">
                <h2>📊 系统仪表盘</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 id="total-analysis">0</h3>
                        <p>总分析次数</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="active-cards">0</h3>
                        <p>活跃卡密数</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="prompt-versions">0</h3>
                        <p>提示词版本</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="system-uptime">0h</h3>
                        <p>系统运行时间</p>
                    </div>
                </div>
                
                <div class="system-info">
                    <h3>系统信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="label">服务器状态</div>
                            <div class="value" id="server-status">加载中...</div>
                        </div>
                        <div class="info-item">
                            <div class="label">当前版本</div>
                            <div class="value" id="system-version">加载中...</div>
                        </div>
                        <div class="info-item">
                            <div class="label">数据库状态</div>
                            <div class="value" id="db-status">加载中...</div>
                        </div>
                        <div class="info-item">
                            <div class="label">最后更新</div>
                            <div class="value" id="last-update">加载中...</div>
                        </div>
                        <div class="info-item">
                            <div class="label">API状态</div>
                            <div class="value" id="api-status">检测中...</div>
                        </div>
                        <div class="info-item">
                            <div class="label">缓存命中率</div>
                            <div class="value" id="cache-hit-rate">计算中...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分析报告 -->
            <div id="reports-section" class="admin-section">
                <h2>📋 分析报告管理</h2>
                
                <div class="alert alert-info">
                    <strong>提示：</strong> 这里显示系统中所有的八字分析报告，点击可查看详细内容。
                </div>
                
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="refreshReports()">🔄 刷新报告</button>
                    <button class="btn btn-secondary" onclick="exportReports()">📤 导出报告</button>
                    <span style="margin-left: 20px; color: #666;">
                        共 <span id="reports-count">0</span> 份报告
                    </span>
                </div>
                
                <div id="reports-list" class="reports-grid">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        正在加载报告列表...
                    </div>
                </div>
            </div>
            
            <!-- 其他部分保持不变 -->
            <div id="prompts-section" class="admin-section">
                <h2>🧠 提示词管理</h2>
                <div class="alert alert-info">
                    <strong>提示：</strong> 提示词管理功能已集成到系统中。
                </div>
                <div class="quick-actions">
                    <a href="prompt_manager.html" class="quick-action" target="_blank">
                        <span class="icon">🌐</span>
                        <div class="title">Web管理界面</div>
                        <div class="desc">在新窗口中打开完整的提示词管理界面</div>
                    </a>
                </div>
            </div>
            
            <div id="cards-section" class="admin-section">
                <h2>🎫 卡密管理</h2>
                <p>卡密管理功能正在开发中...</p>
            </div>
            
            <div id="logs-section" class="admin-section">
                <h2>📋 系统日志</h2>
                <p>系统日志功能正在开发中...</p>
            </div>
            
            <div id="settings-section" class="admin-section">
                <h2>⚙️ 系统设置</h2>
                <p>系统设置功能正在开发中...</p>
            </div>
        </div>
    </div>
    
    <script src="js/lolxxx.js"></script>
</body>
</html>
