/**
 * 系统管理后台 JavaScript
 * 处理管理界面的交互和数据展示
 */

class AdminDashboard {
    constructor() {
        this.apiBase = '/api';
        this.promptApiBase = '/api/prompt';
        this.updateInterval = 30000; // 30秒更新一次
        this.init();
    }
    
    init() {
        this.loadDashboardData();
        this.loadPromptStatus();
        this.startAutoUpdate();
    }
    
    // API调用封装
    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(endpoint, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.error || '请求失败');
            }
            
            return result;
        } catch (error) {
            console.error('API调用错误:', error);
            throw error;
        }
    }
    
    // 加载仪表盘数据
    async loadDashboardData() {
        try {
            // 从API获取真实数据
            const dashboardData = await this.apiCall(`${this.promptApiBase}/dashboard`);

            // 更新统计数据
            const stats = dashboardData.stats;
            document.getElementById('total-analysis').textContent = stats.total_analysis || 0;
            document.getElementById('active-cards').textContent = stats.active_cards || 0;
            document.getElementById('prompt-versions').textContent = stats.prompt_versions || 0;
            document.getElementById('system-uptime').textContent = stats.system_uptime || '0h';

            // 更新系统信息
            const systemInfo = dashboardData.system_info;
            document.getElementById('server-status').textContent = systemInfo.server_status || '运行中';
            document.getElementById('system-version').textContent = systemInfo.system_version || 'v2.0.0';
            document.getElementById('db-status').textContent = systemInfo.db_status || '正常';
            document.getElementById('last-update').textContent = systemInfo.last_update || new Date().toLocaleTimeString();

            // 更新API状态
            document.getElementById('api-status').textContent = '正常';
            document.getElementById('api-status').style.color = '#28a745';

            // 更新缓存命中率
            const promptInfo = dashboardData.prompt_info;
            let cacheHitRate = '不可用';
            if (promptInfo.cache_hit_rate && promptInfo.cache_hit_rate !== 'N/A') {
                try {
                    const cacheStr = promptInfo.cache_hit_rate;
                    const hitsMatch = cacheStr.match(/hits=(\d+)/);
                    const missesMatch = cacheStr.match(/misses=(\d+)/);

                    if (hitsMatch && missesMatch) {
                        const hits = parseInt(hitsMatch[1]);
                        const misses = parseInt(missesMatch[1]);
                        const total = hits + misses;
                        if (total > 0) {
                            const hitRate = ((hits / total) * 100).toFixed(1);
                            cacheHitRate = `${hitRate}%`;
                        } else {
                            cacheHitRate = '0%';
                        }
                    }
                } catch (e) {
                    console.log('解析缓存命中率失败:', e);
                }
            }
            document.getElementById('cache-hit-rate').textContent = cacheHitRate;

            // 如果有内存和磁盘使用信息，也更新显示
            if (systemInfo.memory_usage && systemInfo.memory_usage !== 'N/A') {
                this.updateSystemResource('memory', systemInfo.memory_usage);
            }
            if (systemInfo.disk_usage && systemInfo.disk_usage !== 'N/A') {
                this.updateSystemResource('disk', systemInfo.disk_usage);
            }

        } catch (error) {
            console.error('加载仪表盘数据失败:', error);
            // 如果API调用失败，显示默认值和错误状态
            document.getElementById('total-analysis').textContent = '0';
            document.getElementById('active-cards').textContent = '0';
            document.getElementById('prompt-versions').textContent = '0';
            document.getElementById('system-uptime').textContent = '0h';
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();

            // 显示API错误状态
            document.getElementById('api-status').textContent = 'API错误';
            document.getElementById('api-status').style.color = '#dc3545';
            document.getElementById('cache-hit-rate').textContent = '不可用';

            // 显示错误提示
            this.showAlert('error', 'API连接失败，显示的可能是缓存数据');
        }
    }

    // 更新系统资源显示
    updateSystemResource(type, usage) {
        // 如果页面上有资源使用显示区域，可以在这里更新
        // 这个方法为将来扩展预留
        console.log(`${type} usage: ${usage}`);
    }
    
    // 加载提示词系统状态
    async loadPromptStatus() {
        try {
            // 获取仪表盘数据中的提示词信息
            const dashboardData = await this.apiCall(`${this.promptApiBase}/dashboard`);
            const promptInfo = dashboardData.prompt_info;

            document.getElementById('prompt-version').textContent = promptInfo.current_version || 'current';
            document.getElementById('available-dimensions').textContent = promptInfo.available_dimensions || '0';
            document.getElementById('cache-status').textContent = `${promptInfo.cache_size || 0} 项`;

            // 尝试解析缓存命中率
            let cacheHitRate = '不可用';
            if (promptInfo.cache_hit_rate && promptInfo.cache_hit_rate !== 'N/A') {
                try {
                    // 解析类似 "CacheInfo(hits=0, misses=2, maxsize=100, currsize=2)" 的字符串
                    const cacheStr = promptInfo.cache_hit_rate;
                    const hitsMatch = cacheStr.match(/hits=(\d+)/);
                    const missesMatch = cacheStr.match(/misses=(\d+)/);

                    if (hitsMatch && missesMatch) {
                        const hits = parseInt(hitsMatch[1]);
                        const misses = parseInt(missesMatch[1]);
                        const total = hits + misses;
                        if (total > 0) {
                            const hitRate = ((hits / total) * 100).toFixed(1);
                            cacheHitRate = `${hitRate}%`;
                        }
                    }
                } catch (e) {
                    console.log('解析缓存命中率失败:', e);
                }
            }

            // 更新最后评估时间（这里可以根据实际需求调整）
            document.getElementById('last-evaluation').textContent = this.getLastEvaluationTime();

        } catch (error) {
            console.error('加载提示词状态失败:', error);
            // 如果API不可用，显示默认信息
            document.getElementById('prompt-version').textContent = 'current';
            document.getElementById('available-dimensions').textContent = '0';
            document.getElementById('cache-status').textContent = '不可用';
            document.getElementById('last-evaluation').textContent = '不可用';
        }
    }

    // 获取最后评估时间（模拟）
    getLastEvaluationTime() {
        // 这里可以从localStorage或其他地方获取真实的最后评估时间
        const lastEval = localStorage.getItem('lastEvaluationTime');
        if (lastEval) {
            const evalTime = new Date(lastEval);
            const now = new Date();
            const diffMinutes = Math.floor((now - evalTime) / (1000 * 60));

            if (diffMinutes < 60) {
                return `${diffMinutes}分钟前`;
            } else if (diffMinutes < 1440) {
                return `${Math.floor(diffMinutes / 60)}小时前`;
            } else {
                return `${Math.floor(diffMinutes / 1440)}天前`;
            }
        }
        return '未评估';
    }
    
    // 开始自动更新
    startAutoUpdate() {
        setInterval(() => {
            this.loadDashboardData();
            this.loadPromptStatus();
        }, this.updateInterval);
    }
    
    // 显示提示信息
    showAlert(type, message, containerId = null) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `<strong>${type === 'error' ? '错误' : type === 'success' ? '成功' : '提示'}：</strong> ${message}`;
        
        const container = containerId ? document.getElementById(containerId) : document.querySelector('.admin-section.active');
        container.insertBefore(alertDiv, container.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 全局变量
let adminDashboard;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    adminDashboard = new AdminDashboard();
});

// 切换管理面板
function showSection(sectionName) {
    // 隐藏所有section
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 移除所有导航项的active状态
    document.querySelectorAll('.admin-nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示选中的section
    document.getElementById(`${sectionName}-section`).classList.add('active');
    
    // 设置选中的导航项为active
    event.target.classList.add('active');
    
    // 根据section执行特定的加载逻辑
    switch(sectionName) {
        case 'dashboard':
            adminDashboard.loadDashboardData();
            break;
        case 'prompts':
            adminDashboard.loadPromptStatus();
            break;
        case 'logs':
            refreshLogs();
            break;
    }
}

// 提示词管理相关功能
function launchDesktopGUI() {
    adminDashboard.showAlert('info', '正在启动桌面管理工具...', 'prompts-section');
    
    // 尝试通过API启动桌面GUI
    fetch('/api/prompt/launch-gui', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminDashboard.showAlert('success', '桌面管理工具已启动', 'prompts-section');
            } else {
                adminDashboard.showAlert('error', '启动失败：' + (data.error || '未知错误'), 'prompts-section');
            }
        })
        .catch(error => {
            adminDashboard.showAlert('error', '无法启动桌面工具，请手动运行 python backend/prompt_gui.py', 'prompts-section');
        });
}

async function checkPromptQuality() {
    adminDashboard.showAlert('info', '正在检查提示词质量...', 'prompts-section');

    try {
        const result = await adminDashboard.apiCall(`${adminDashboard.promptApiBase}/evaluation`, 'POST', {
            dimension: '性格',
            bazi_data: '测试数据'
        });

        const score = Math.round(result.overall_score * 100);
        const message = `质量检查完成，整体评分：${score}%`;

        // 保存评估时间
        localStorage.setItem('lastEvaluationTime', new Date().toISOString());

        if (score >= 80) {
            adminDashboard.showAlert('success', message, 'prompts-section');
        } else if (score >= 60) {
            adminDashboard.showAlert('warning', message + '，建议优化', 'prompts-section');
        } else {
            adminDashboard.showAlert('error', message + '，需要立即优化', 'prompts-section');
        }

        // 更新提示词状态显示
        adminDashboard.loadPromptStatus();

    } catch (error) {
        adminDashboard.showAlert('error', '质量检查失败：' + error.message, 'prompts-section');
    }
}

function backupPrompts() {
    adminDashboard.showAlert('info', '正在备份提示词...', 'prompts-section');
    
    // 创建备份
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `prompts_backup_${timestamp}`;
    
    fetch(`${adminDashboard.promptApiBase}/backup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: backupName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            adminDashboard.showAlert('success', `备份已创建：${backupName}`, 'prompts-section');
        } else {
            adminDashboard.showAlert('error', '备份失败：' + (data.error || '未知错误'), 'prompts-section');
        }
    })
    .catch(error => {
        adminDashboard.showAlert('error', '备份失败：' + error.message, 'prompts-section');
    });
}

// 系统日志相关功能
function refreshLogs() {
    const logViewer = document.getElementById('log-viewer');
    
    // 模拟日志刷新
    const currentTime = new Date().toLocaleString();
    const newLogLine = document.createElement('div');
    newLogLine.className = 'log-line log-info';
    newLogLine.textContent = `[${currentTime}] INFO: 日志已刷新`;
    
    logViewer.appendChild(newLogLine);
    logViewer.scrollTop = logViewer.scrollHeight;
    
    adminDashboard.showAlert('success', '日志已刷新', 'logs-section');
}

function downloadLogs() {
    // 创建下载链接
    const logContent = document.getElementById('log-viewer').textContent;
    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `system_logs_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    adminDashboard.showAlert('success', '日志文件已下载', 'logs-section');
}

function clearLogs() {
    if (confirm('确定要清空所有日志吗？此操作不可恢复！')) {
        document.getElementById('log-viewer').innerHTML = '';
        adminDashboard.showAlert('warning', '日志已清空', 'logs-section');
    }
}

// 系统管理功能
function exportData() {
    adminDashboard.showAlert('info', '正在导出数据...');
    
    // 模拟数据导出
    setTimeout(() => {
        const data = {
            exportTime: new Date().toISOString(),
            totalAnalysis: document.getElementById('total-analysis').textContent,
            activeCards: document.getElementById('active-cards').textContent,
            systemInfo: {
                version: document.getElementById('system-version').textContent,
                uptime: document.getElementById('system-uptime').textContent
            }
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `system_data_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        adminDashboard.showAlert('success', '数据导出完成');
    }, 2000);
}

function restartSystem() {
    if (confirm('确定要重启系统吗？这将中断所有正在进行的操作。')) {
        adminDashboard.showAlert('warning', '正在重启系统...', 'settings-section');
        
        fetch('/api/system/restart', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    adminDashboard.showAlert('success', '系统重启命令已发送', 'settings-section');
                } else {
                    adminDashboard.showAlert('error', '重启失败：' + (data.error || '未知错误'), 'settings-section');
                }
            })
            .catch(error => {
                adminDashboard.showAlert('error', '重启请求失败：' + error.message, 'settings-section');
            });
    }
}

function updateSystem() {
    adminDashboard.showAlert('info', '正在检查系统更新...', 'settings-section');
    
    // 模拟更新检查
    setTimeout(() => {
        adminDashboard.showAlert('info', '当前已是最新版本', 'settings-section');
    }, 2000);
}

function systemBackup() {
    if (confirm('确定要创建系统备份吗？这可能需要几分钟时间。')) {
        adminDashboard.showAlert('info', '正在创建系统备份...', 'settings-section');
        
        // 模拟备份过程
        setTimeout(() => {
            const backupName = `system_backup_${new Date().toISOString().split('T')[0]}`;
            adminDashboard.showAlert('success', `系统备份已创建：${backupName}`, 'settings-section');
        }, 5000);
    }
}

// 导出到全局作用域
window.showSection = showSection;
window.launchDesktopGUI = launchDesktopGUI;
window.checkPromptQuality = checkPromptQuality;
window.backupPrompts = backupPrompts;
window.refreshLogs = refreshLogs;
window.downloadLogs = downloadLogs;
window.clearLogs = clearLogs;
window.exportData = exportData;
window.restartSystem = restartSystem;
window.updateSystem = updateSystem;
window.systemBackup = systemBackup;
