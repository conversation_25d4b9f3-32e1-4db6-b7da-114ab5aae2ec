/**
 * 分析服务
 * 处理八字分析请求、状态查询和结果处理
 */

// 保存分析结果到localStorage
function saveAnalysisResult(analysis, resultData) {
    try {
        console.log('保存分析结果，analysis:', analysis);
        console.log('结果数据:', resultData);
        
        // 获取现有的报告列表
        const existingReports = JSON.parse(localStorage.getItem('baziReports') || '[]');
        
        // 检查是否已经存在相同的分析结果（防止重复保存）
        const isDuplicate = existingReports.some(report => {
            return report.cardKey === analysis.cardKey &&
                   report.name === analysis.name &&
                   (report.birthDate || report.birth_date) === (analysis.birthDate || analysis.birth_date) &&
                   report.birthTime === analysis.birthTime &&
                   Math.abs(report.timestamp - Date.now()) < 60000; // 1分钟内的重复
        });
        
        if (isDuplicate) {
            console.log('检测到重复的分析结果，跳过保存');
            return;
        }
        
        // 提取生日和性别信息
        let birthDate = analysis.birthDate || analysis.birth_date;
        let gender = analysis.gender;
        
        // 尝试从结果数据中提取
        if (resultData) {
            // 检查顶层属性
            if (!birthDate) {
                birthDate = resultData.birthDate || resultData.birth_date;
            }
            if (!gender) {
                gender = resultData.gender;
            }
            
            // 检查 basicInfo
            if (resultData.basicInfo) {
                if (!birthDate) {
                    birthDate = resultData.basicInfo.birthDate || resultData.basicInfo.birth_date;
                }
                if (!gender) {
                    gender = resultData.basicInfo.gender;
                }
            }
            
            // 检查 bz
            if (!birthDate && resultData.bz && resultData.bz['8']) {
                birthDate = resultData.bz['8'];
            }
        }
        
        console.log('提取到的信息 - birthDate:', birthDate, 'gender:', gender);
        
        // 从localStorage获取原始请求数据
        let originalRequestData = null;
        try {
            const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
            if (ongoingAnalysis) {
                const analysisInfo = JSON.parse(ongoingAnalysis);
                if (analysisInfo.formData) {
                    originalRequestData = {
                        year: analysisInfo.formData.year,
                        month: analysisInfo.formData.month,
                        day: analysisInfo.formData.day,
                        hour: analysisInfo.formData.hour,
                        gender: analysisInfo.formData.gender,
                        name: analysisInfo.formData.name,
                        location: analysisInfo.formData.location,
                        cardKey: analysisInfo.formData.cardKey
                    };
                    console.log('从ongoingAnalysis获取到原始请求数据:', originalRequestData);
                }
            }
        } catch (e) {
            console.warn('获取原始请求数据失败:', e);
        }
        
        // 创建新的报告对象
        const newReport = {
            id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            cardKey: analysis.cardKey,
            name: analysis.name,
            gender: gender,
            birthDate: birthDate,
            birthTime: analysis.birthTime,
            location: analysis.location,
            completedTime: new Date().toLocaleString(),
            timestamp: Date.now(),
            resultData: resultData,
            analysisData: analysis,
            // 保存包含原始请求数据的data结构
            data: {
                requestData: originalRequestData,
                resultData: resultData,
                basicInfo: {
                    name: analysis.name,
                    birthDate: birthDate,
                    gender: gender,
                    birthTime: analysis.birthTime,
                    location: analysis.location
                }
            }
        };
        
        console.log('新报告对象:', newReport);
        
        // 添加到报告列表开头（最新的在前面）
        existingReports.unshift(newReport);
        
        // 保存到localStorage
        localStorage.setItem('baziReports', JSON.stringify(existingReports));
        
        // 同时保存为最新结果（用于向后兼容）
        localStorage.setItem('latestBaziResult', JSON.stringify(newReport));
        
        console.log('分析结果已保存到localStorage:', newReport.id);
        
        // 自动刷新报告列表显示 - 新增功能
        if (typeof refreshReports === 'function') {
            console.log('自动刷新报告列表...');
            setTimeout(refreshReports, 500); // 延迟500毫秒刷新，确保数据已完全保存
        } else {
            console.log('refreshReports函数不可用，无法自动刷新');
            // 如果在详情页面，可能需要重定向到报告列表页面
            if (window.location.pathname.includes('detailed_result.html')) {
                const resultPageUrl = 'result.html';
                console.log('在详情页面，将在3秒后重定向到报告列表页面...');
                setTimeout(() => {
                    window.location.href = resultPageUrl;
                }, 3000);
            }
        }
        
    } catch (error) {
        console.error('保存分析结果失败:', error);
    }
}

// 强制完成分析
function forceCompleteAnalysis() {
    if (confirm('确定要手动标记此分析为已完成状态吗？\n如果分析实际仍在进行中，此操作可能导致结果不完整。')) {
        // 从localStorage获取当前分析
        const ongoingAnalysis = localStorage.getItem('ongoingAnalysis');
        if (ongoingAnalysis) {
            try {
                // 标记为已完成
                const analysis = JSON.parse(ongoingAnalysis);
                const completed = {
                    ...analysis,
                    completedTime: new Date().toLocaleString(),
                    timestamp: Date.now(),
                    forcedComplete: true
                };
                localStorage.setItem('completedAnalysis', JSON.stringify(completed));
            } catch (error) {
                console.error('解析分析状态失败:', error);
            }
        }
        
        // 清除正在进行的分析
        localStorage.removeItem('ongoingAnalysis');
        
        // 清除URL参数
        if (window.location.search.includes('requestId') || 
            window.location.search.includes('cardKey')) {
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);
        }
        
        // 刷新报告列表
        refreshReports();
    }
}

// 切换显示调试信息
function toggleDebugInfo() {
    const debugInfo = document.getElementById('debugInfo');
    const debugBtn = document.querySelector('.debug-btn');
    
    if (debugInfo) {
        if (debugInfo.style.display === 'none') {
            debugInfo.style.display = 'block';
            debugBtn.textContent = '隐藏调试信息 🔧';
        } else {
            debugInfo.style.display = 'none';
            debugBtn.textContent = '显示调试信息 🔧';
        }
    }
}