/**
 * 提示消息样式
 * 为临时显示的消息提供样式支持
 */

/* 基础toast样式 */
.toast-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 6px;
    z-index: 10000;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    font-size: 14px;
    font-weight: 500;
    max-width: 80%;
    text-align: center;
    
    /* 动画 */
    animation: toast-slide-down 0.3s ease-out;
    
    /* 使用CSS变量，便于主题切换 */
    background-color: var(--card-bg-color, #fff);
    color: var(--text-color, #333);
    border: 1px solid var(--border-color, #eee);
}

/* 成功消息样式 */
.toast-message.success {
    background-color: var(--success-color-light, #e6f7e6);
    color: var(--success-color-dark, #2e7d32);
    border-color: var(--success-color, #4caf50);
}

/* 错误消息样式 */
.toast-message.error {
    background-color: var(--error-color-light, #ffebee);
    color: var(--error-color-dark, #c62828);
    border-color: var(--error-color, #f44336);
}

/* 警告消息样式 */
.toast-message.warning {
    background-color: var(--warning-color-light, #fff8e1);
    color: var(--warning-color-dark, #ff8f00);
    border-color: var(--warning-color, #ffc107);
}

/* 信息消息样式 */
.toast-message.info {
    background-color: var(--info-color-light, #e3f2fd);
    color: var(--info-color-dark, #0277bd);
    border-color: var(--info-color, #2196f3);
}

/* 淡出动画 */
.toast-fade-out {
    opacity: 0;
    transition: opacity 0.5s ease-out;
}

/* 滑入动画 */
@keyframes toast-slide-down {
    from {
        transform: translate(-50%, -20px);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

/* 响应式调整 */
@media (max-width: 600px) {
    .toast-message {
        width: 90%;
        font-size: 13px;
        padding: 8px 15px;
    }
} 