/* 布局相关样式 */
.detailed-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detailed-header {
    background: var(--bg-card); /* 使用全局卡片背景色 */
    border-radius: var(--border-radius-lg);
    padding: 8px 32px;
    margin-bottom: 12px;
    margin-top: 4px;
    box-shadow: var(--shadow-lg); /* 调整阴影 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    position: relative;
    overflow: hidden;
}

.detailed-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.detailed-header h1 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.03em;
    line-height: 1.2;
    max-width: 70%;
}

.detailed-header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 按钮通用样式 */
.detailed-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
    min-width: 120px;
    border: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    color: white !important; /* 所有按钮文字统一为白色 */
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: white !important;
    text-fill-color: white !important;
}

.detailed-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 编辑标题按钮样式 */
.detailed-edit-btn {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 6px -1px rgba(75, 85, 99, 0.3);
}

.detailed-edit-btn:hover {
    box-shadow: 0 6px 12px rgba(75, 85, 99, 0.4);
}

/* 下载PDF按钮样式 */
.detailed-download-btn {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.3);
}

.detailed-download-btn:hover {
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.4);
}

/* 导出源文件按钮样式 */
.detailed-export-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.detailed-export-btn:hover {
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
}

/* 导出长图按钮样式 */
.detailed-image-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
}

.detailed-image-btn:hover {
    box-shadow: 0 6px 12px rgba(245, 158, 11, 0.4);
}

/* 返回主页按钮样式 */
.detailed-back-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #d946ef 100%);
    box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.3);
}

.detailed-back-btn:hover {
    box-shadow: 0 6px 12px rgba(139, 92, 246, 0.4);
}

/* 共用图标样式 */
.detailed-action-btn span {
    font-size: 16px;
}

.detailed-result-container {
    padding: 32px;
    background: var(--bg-card, #ffffff);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin: 20px 0;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color-light, #f3f4f6);
}

.detailed-result-header {
    padding: 0 0 32px 0;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    margin-bottom: 32px;
}

.detailed-result-header h2 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary, #111827);
    margin: 0 0 16px 0;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.detailed-result-meta {
    display: flex;
    align-items: center;
    gap: 24px;
    color: var(--text-light, #6b7280);
    font-size: 15px;
}

.detailed-result-meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.detailed-result-meta-item i {
    color: var(--primary-color, #6366f1);
    font-size: 18px;
}

.detailed-analysis-content {
    padding: 40px 16px;
    font-size: 16px;
    line-height: 1.7;
    color: var(--text-primary, #111827);
}

/* 详细结果维度样式 */
.detailed-dimension-section {
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 24px;
    background: var(--bg-card, #ffffff);
    transition: all 0.3s ease;
}

.detailed-dimension-section:hover {
    border-color: var(--primary-color, #6366f1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.detailed-dimension-header {
    padding: 20px 24px;
    background: var(--bg-card, #ffffff);
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.detailed-dimension-header:hover {
    background: var(--background-light, #f8fafc);
}

.detailed-dimension-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary, #111827);
    display: flex;
    align-items: center;
    gap: 16px;
}

.detailed-dimension-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-size: 18px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(99, 102, 241, 0.2);
}

.detailed-dimension-arrow {
    transition: transform 0.3s ease;
    color: var(--text-light, #6b7280);
    font-size: 20px;
}

.detailed-dimension-header.active .detailed-dimension-arrow {
    transform: rotate(180deg);
    color: var(--primary-color, #6366f1);
}

.detailed-dimension-content {
    display: none;
    background: var(--bg-card, #ffffff);
    overflow: hidden;
    transition: all 0.3s ease;
}

.detailed-dimension-content.active {
    display: block;
    padding: 24px 32px;
}

/* 详细文本分析内容样式 - 增强版 */
.detailed-dimension-text h1,
.detailed-dimension-text h2,
.detailed-dimension-text h3,
.detailed-dimension-text h4,
.detailed-dimension-text h5,
.detailed-dimension-text h6 {
    color: var(--text-primary, #111827);
    font-weight: 700;
    margin: 2em 0 1em 0;
    line-height: 1.3;
    position: relative;
    scroll-margin-top: 80px; /* 锚点滚动偏移 */
}

/* 一级标题 - 主要章节 */
.detailed-dimension-text h1 {
    font-size: 2rem;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 3px solid transparent;
    border-image: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899) 1;
    padding-bottom: 0.5em;
    margin-top: 0;
    margin-bottom: 1.5em;
    position: relative;
}

.detailed-dimension-text h1::before {
    content: '📊';
    margin-right: 0.5em;
    font-size: 0.8em;
}

/* 二级标题 - 重要分类 */
.detailed-dimension-text h2 {
    font-size: 1.6rem;
    color: #1e40af;
    border-left: 4px solid #3b82f6;
    padding-left: 1em;
    padding-bottom: 0.3em;
    margin-left: -1em;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
    border-radius: 0 8px 8px 0;
}

.detailed-dimension-text h2::before {
    content: '🔹';
    margin-right: 0.5em;
    color: #3b82f6;
}

/* 三级标题 - 子分类 */
.detailed-dimension-text h3 {
    font-size: 1.35rem;
    color: #059669;
    position: relative;
    padding-left: 1.5em;
}

.detailed-dimension-text h3::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #10b981;
    font-size: 0.8em;
    top: 0.1em;
}

/* 四级标题 - 详细项目 */
.detailed-dimension-text h4 {
    font-size: 1.2rem;
    color: #7c3aed;
    font-weight: 600;
    position: relative;
    padding-left: 1.2em;
}

.detailed-dimension-text h4::before {
    content: '●';
    position: absolute;
    left: 0;
    color: #8b5cf6;
    font-size: 0.8em;
    top: 0.2em;
}

/* 五级标题 - 小节 */
.detailed-dimension-text h5 {
    font-size: 1.1rem;
    color: #dc2626;
    font-weight: 600;
    position: relative;
    padding-left: 1em;
}

.detailed-dimension-text h5::before {
    content: '◆';
    position: absolute;
    left: 0;
    color: #ef4444;
    font-size: 0.7em;
    top: 0.3em;
}

/* 六级标题 - 细节 */
.detailed-dimension-text h6 {
    font-size: 1rem;
    color: var(--text-secondary, #374151);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    padding-left: 0.8em;
}

.detailed-dimension-text h6::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: #6b7280;
    font-size: 0.8em;
    top: 0.1em;
}

/* 增强的标题容器样式 */
.title-container {
    display: flex;
    align-items: center;
    gap: 0.5em;
    position: relative;
    transition: all 0.3s ease;
}

.title-container:hover {
    transform: translateX(4px);
}

.title-icon {
    font-size: 0.9em;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5em;
    height: 1.5em;
    border-radius: 4px;
    background: rgba(99, 102, 241, 0.1);
    transition: all 0.3s ease;
}

.title-text {
    flex: 1;
    font-weight: inherit;
    color: inherit;
}

.title-level {
    font-size: 0.7em;
    color: #6b7280;
    font-weight: 400;
    margin-right: 0.3em;
}

.title-anchor {
    opacity: 0;
    font-size: 0.7em;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.3s ease;
    padding: 0.2em;
    border-radius: 3px;
}

.title-container:hover .title-anchor {
    opacity: 1;
}

.title-anchor:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

/* 特殊标题样式 */
.special-title .title-icon {
    background: var(--title-color, #6366f1);
    color: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: white !important;
    text-fill-color: white !important;
}

.special-title .title-text {
    color: var(--title-color, #6366f1);
}

/* 标题装饰线 */
.title-decoration {
    height: 2px;
    margin: 0.5em 0 1em 0;
    border-radius: 1px;
}

.h1-decoration {
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899, transparent);
}

.h2-decoration {
    background: linear-gradient(90deg, #3b82f6, #06b6d4, transparent);
    height: 1px;
}

/* 目录样式 */
.table-of-contents {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
    border-radius: 12px;
    padding: 1.5em;
    margin: 2em 0;
    position: sticky;
    top: 20px;
    z-index: 100;
}

.table-of-contents h3 {
    margin: 0 0 1em 0;
    color: #6366f1;
    font-size: 1.2rem;
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
    padding-bottom: 0.5em;
}

.table-of-contents ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.table-of-contents li {
    margin: 0.3em 0;
    padding: 0;
    background: none;
}

.toc-link {
    display: block;
    padding: 0.4em 0.8em;
    color: #374151;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.toc-link:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    transform: translateX(4px);
}

.toc-level-1 .toc-link {
    font-weight: 700;
    font-size: 1em;
    color: #1f2937;
}

.toc-level-2 .toc-link {
    font-weight: 600;
    padding-left: 1.5em;
}

.toc-level-3 .toc-link {
    padding-left: 2.5em;
    font-size: 0.85em;
}

.toc-level-4 .toc-link {
    padding-left: 3.5em;
    font-size: 0.8em;
    color: #6b7280;
}

.toc-level-5 .toc-link,
.toc-level-6 .toc-link {
    padding-left: 4.5em;
    font-size: 0.75em;
    color: #9ca3af;
}

/* Toast消息动画 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.detailed-dimension-text p {
    margin: 1.2em 0;
    line-height: 1.8;
    color: var(--text-primary, #374151);
    font-size: 1.05rem;
    text-align: justify;
}

/* 关键字和重要文本突出显示 */
.detailed-dimension-text strong {
    color: #1e40af;
    font-weight: 700;
    background: linear-gradient(120deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
    padding: 0.1em 0.3em;
    border-radius: 4px;
    border-left: 3px solid #3b82f6;
    margin: 0 0.1em;
    position: relative;
}

.detailed-dimension-text strong::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(120deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
    border-radius: 4px;
    z-index: -1;
}

/* 强调文本 */
.detailed-dimension-text em {
    color: #7c3aed;
    font-style: italic;
    font-weight: 500;
    background: linear-gradient(120deg, rgba(124, 58, 237, 0.08) 0%, rgba(196, 181, 253, 0.08) 100%);
    padding: 0.1em 0.2em;
    border-radius: 3px;
    position: relative;
}

/* 特殊关键词样式 */
.detailed-dimension-text strong[data-keyword],
.detailed-dimension-text .keyword {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: #92400e;
    padding: 0.2em 0.5em;
    border-radius: 6px;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
    border: none;
}

/* 数字和统计数据突出 */
.detailed-dimension-text .number,
.detailed-dimension-text strong:matches([0-9]) {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white !important;
    padding: 0.1em 0.4em;
    border-radius: 4px;
    font-weight: 700;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: white !important;
    text-fill-color: white !important;
}

/* 命理术语特殊样式 */
.detailed-dimension-text .term {
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
    color: white !important;
    padding: 0.15em 0.4em;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.95em;
    /* 防止深色模式CSS覆盖 */
    -webkit-text-fill-color: white !important;
    text-fill-color: white !important;
}

.detailed-dimension-text a {
    color: var(--primary-color, #6366f1);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.detailed-dimension-text a:hover {
    border-bottom-color: var(--primary-color, #6366f1);
}

/* 增强的列表样式 */
.detailed-dimension-text ul,
.detailed-dimension-text ol {
    margin: 1.5em 0;
    padding-left: 0;
    list-style: none;
}

.detailed-dimension-text ul li,
.detailed-dimension-text ol li {
    margin: 0.8em 0;
    line-height: 1.8;
    padding: 0.5em 0 0.5em 2.5em;
    position: relative;
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.03) 0%, transparent 100%);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.detailed-dimension-text ul li:hover,
.detailed-dimension-text ol li:hover {
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.08) 0%, transparent 100%);
    transform: translateX(4px);
}

/* 无序列表项目符号 */
.detailed-dimension-text ul li::before {
    content: '🔸';
    position: absolute;
    left: 0.8em;
    top: 0.5em;
    font-size: 0.9em;
    color: #6366f1;
}

/* 有序列表数字 */
.detailed-dimension-text ol {
    counter-reset: list-counter;
}

.detailed-dimension-text ol li {
    counter-increment: list-counter;
}

.detailed-dimension-text ol li::before {
    content: counter(list-counter);
    position: absolute;
    left: 0.5em;
    top: 0.5em;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    width: 1.5em;
    height: 1.5em;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

/* 增强的引用样式 */
.detailed-dimension-text blockquote {
    margin: 2em 0;
    padding: 1.5em 2em;
    border: none;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    border-left: 5px solid transparent;
    border-image: linear-gradient(135deg, #6366f1, #8b5cf6) 1;
    border-radius: 0 12px 12px 0;
    color: var(--text-primary, #374151);
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.7;
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.detailed-dimension-text blockquote::before {
    content: '"';
    position: absolute;
    top: -0.2em;
    left: 0.5em;
    font-size: 4em;
    color: rgba(99, 102, 241, 0.2);
    font-family: Georgia, serif;
    line-height: 1;
}

.detailed-dimension-text blockquote::after {
    content: '"';
    position: absolute;
    bottom: -0.5em;
    right: 0.5em;
    font-size: 4em;
    color: rgba(99, 102, 241, 0.2);
    font-family: Georgia, serif;
    line-height: 1;
}

.detailed-dimension-text hr {
    border: 0;
    border-top: 1px solid var(--border-color, #e5e7eb);
    margin: 2em 0;
}

.detailed-dimension-text code {
    font-family: monospace;
    background-color: var(--border-color-light, #f3f4f6);
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-size: 0.9em;
    color: var(--primary-color, #6366f1);
}

.detailed-dimension-text pre {
    background-color: var(--border-color-light, #f3f4f6);
    padding: 1em;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5em 0;
}

.detailed-dimension-text pre code {
    background-color: transparent;
    padding: 0;
    color: var(--text-primary, #111827);
}

.detailed-dimension-text table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.detailed-dimension-text th,
.detailed-dimension-text td {
    padding: 0.75em 1em;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    text-align: left;
}

.detailed-dimension-text th {
    background-color: var(--background-light, #f8fafc);
    font-weight: 700;
    color: var(--text-primary, #111827);
}

.detailed-dimension-text tr:last-child td {
    border-bottom: none;
}

.detailed-dimension-text tr:nth-child(even) {
    background-color: var(--border-color-light, #f3f4f6);
}

/* 详细分析信息网格 */
.detailed-bazi-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin: 32px 0;
}

.detailed-bazi-info-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color-light, #f3f4f6);
    transition: all 0.3s ease;
}

.detailed-bazi-info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-light, #a5b4fc);
}

.detailed-bazi-info-item strong {
    font-weight: 700;
    color: var(--text-primary, #111827);
    min-width: 120px;
    display: inline-block;
    margin-right: 16px;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state {
    padding: 60px 40px;
    text-align: center;
    background: var(--bg-card, #ffffff);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    margin: 20px 0;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 5px solid var(--primary-light, #a5b4fc);
    border-top-color: var(--primary-color, #6366f1);
    border-radius: 50%;
    animation: spinner 1s linear infinite;
    margin: 0 auto 24px auto;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

.loading-state h3,
.error-state h3,
.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary, #111827);
    margin-bottom: 16px;
}

.loading-state p,
.error-state p,
.empty-state p {
    color: var(--text-secondary, #374151);
    font-size: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.error-actions,
.empty-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 32px;
}

.error-actions button,
.empty-actions button {
    min-width: 180px;
}

.detailed-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
}

.detailed-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.detailed-modal {
    width: 90%;
    max-width: 600px;
    background: var(--bg-card, #ffffff);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    padding: 32px;
    transform: translateY(30px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
}

.detailed-overlay.active .detailed-modal {
    transform: translateY(0);
    opacity: 1;
}

.detailed-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.detailed-modal-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary, #111827);
    margin: 0;
}

.detailed-modal-close {
    background: none;
    border: none;
    color: var(--text-light, #6b7280);
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.detailed-modal-close:hover {
    color: var(--text-primary, #111827);
    transform: rotate(90deg);
}

.detailed-modal-content {
    margin-bottom: 24px;
}

.detailed-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
}

.detailed-modal-actions button {
    min-width: 120px;
}

/* 打印优化 */
@media print {
    .detailed-container {
        max-width: 100%;
        padding: 0;
        box-shadow: none;
        background: white;
    }
    
    .detailed-header-actions,
    .detailed-dimension-header {
        display: none;
    }
    
    .detailed-dimension-content {
        display: block !important;
        padding: 0 !important;
    }
    
    .detailed-dimension-section {
        border: none;
        margin-bottom: 32px;
        page-break-inside: avoid;
    }
    
    .detailed-result-header {
        padding: 0 0 16px 0;
        margin-bottom: 24px;
    }
    
    .detailed-result-container {
        padding: 0;
        box-shadow: none;
        border: none;
    }
    
    .detailed-bazi-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .detailed-bazi-info-item {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .detailed-dimension-text a {
        text-decoration: underline;
        color: #000;
    }
    
    /* 确保表格在打印时不会被截断 */
    .detailed-bazi-table,
    .detailed-dayun-table,
    .detailed-shensha-table {
        page-break-inside: avoid;
        font-size: 10pt;
    }
} 