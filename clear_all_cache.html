<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清理所有缓存</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
            width: 100%;
        }
        button:hover {
            background: #c82333;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 清理所有缓存数据</h1>
        
        <div class="info">
            <strong>说明：</strong>这个工具会清理所有可能导致数据混乱的缓存，包括：
            <ul>
                <li>localStorage 中的所有数据</li>
                <li>sessionStorage 中的所有数据</li>
                <li>浏览器缓存</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>🎯 一键清理所有缓存</h3>
            <button onclick="clearAllCache()">清理所有缓存数据</button>
            <div id="result"></div>
        </div>
        
        <div class="section">
            <h3>📊 当前缓存状态</h3>
            <button onclick="showCacheStatus()">查看缓存状态</button>
            <pre id="cacheStatus" style="background: #f8f8f8; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto;"></pre>
        </div>
    </div>

    <script>
        function clearAllCache() {
            const resultDiv = document.getElementById('result');
            
            try {
                // 清理 localStorage
                const localStorageCount = localStorage.length;
                localStorage.clear();
                
                // 清理 sessionStorage
                const sessionStorageCount = sessionStorage.length;
                sessionStorage.clear();
                
                // 显示结果
                resultDiv.innerHTML = `
                    <div class="success">
                        ✅ 缓存清理完成！<br>
                        - localStorage: 清理了 ${localStorageCount} 个项目<br>
                        - sessionStorage: 清理了 ${sessionStorageCount} 个项目<br>
                        <br>
                        <strong>建议：</strong>现在刷新页面或重新打开浏览器标签页。
                    </div>
                `;
                
                // 3秒后自动刷新页面
                setTimeout(() => {
                    if (confirm('缓存已清理完成，是否刷新页面？')) {
                        window.location.reload();
                    }
                }, 3000);
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px;">
                        ❌ 清理失败: ${error.message}
                    </div>
                `;
            }
        }
        
        function showCacheStatus() {
            const statusDiv = document.getElementById('cacheStatus');
            
            const status = {
                localStorage: {},
                sessionStorage: {},
                summary: {
                    localStorageCount: localStorage.length,
                    sessionStorageCount: sessionStorage.length
                }
            };
            
            // 收集 localStorage 数据
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                try {
                    status.localStorage[key] = JSON.parse(value);
                } catch (e) {
                    status.localStorage[key] = value;
                }
            }
            
            // 收集 sessionStorage 数据
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                try {
                    status.sessionStorage[key] = JSON.parse(value);
                } catch (e) {
                    status.sessionStorage[key] = value;
                }
            }
            
            statusDiv.textContent = JSON.stringify(status, null, 2);
        }
        
        // 页面加载时显示缓存状态
        window.addEventListener('load', showCacheStatus);
    </script>
</body>
</html>
