/**
 * 渐进式结果加载器
 * 实现先显示排盘结果，然后逐步加载AI分析的功能
 */

class ProgressiveLoader {
    constructor() {
        this.currentData = null;
        this.loadedDimensions = new Set();
        this.failedDimensions = new Set();
        this.retryCount = 0;
        this.maxRetries = 3;
        this.pollingInterval = null;
        this.isCompleted = false;
        this.dimensionOrder = [
            '性格', '事业', '感情', '健康', '运势', '格局', '学业'
        ];
    }

    /**
     * 开始渐进式加载
     * @param {string} cardKey - 卡密
     * @param {string} reportId - 报告ID
     */
    async startProgressiveLoad(cardKey, reportId) {
        console.log('🚀 [DEBUG] 开始渐进式加载:', { cardKey, reportId });
        console.log('🚀 [DEBUG] 当前时间:', new Date().toISOString());

        try {
            // 第一步：尝试获取基础数据（排盘结果）
            console.log('🔍 [DEBUG] 步骤1: 开始获取基础数据...');
            const basicData = await this.loadBasicData(cardKey, reportId);
            console.log('🔍 [DEBUG] 步骤1完成: basicData =', basicData);
            console.log('🔍 [DEBUG] basicData类型:', typeof basicData);
            console.log('🔍 [DEBUG] basicData是否为null:', basicData === null);
            console.log('🔍 [DEBUG] basicData是否为undefined:', basicData === undefined);

            if (basicData) {
                console.log('✅ [DEBUG] basicData存在，开始显示排盘结果...');
                console.log('📊 [DEBUG] basicData结构:', JSON.stringify(basicData, null, 2));

                // 显示排盘结果
                console.log('🎯 [DEBUG] 调用 displayBasicInfo...');
                this.displayBasicInfo(basicData);
                console.log('✅ [DEBUG] displayBasicInfo 调用完成');

                // 第二步：开始逐步加载AI分析
                console.log('🔄 [DEBUG] 开始逐步加载AI分析...');
                this.startProgressiveAnalysisLoad(cardKey, reportId);
            } else {
                // basicData为null可能表示正在处理中
                console.log('⏳ [DEBUG] basicData为null/undefined，可能正在处理中');
                console.log('⚠️ [DEBUG] 这可能表示API返回了null或者条件判断有问题');
                // showProcessingStatus已经在loadBasicData中调用了
                // 不显示错误，因为可能只是还在处理中
            }
        } catch (error) {
            console.error('❌ [DEBUG] 渐进式加载失败:', error);
            console.error('❌ [DEBUG] 错误堆栈:', error.stack);
            this.showError('加载失败', error.message);
        }
    }

    /**
     * 加载基础数据（排盘结果）
     * @param {string} cardKey - 卡密
     * @param {string} reportId - 报告ID
     * @returns {Promise<Object|null>} 基础数据
     */
    async loadBasicData(cardKey, reportId) {
        try {
            const apiUrl = window.getApiBaseUrl ? window.getApiBaseUrl() : 'http://localhost:5000';
            const url = `${apiUrl}/api/get_result/${cardKey}`;

            console.log('🔍 渐进式加载 - 请求基础数据');
            console.log('📋 卡密:', cardKey);
            console.log('🌐 API URL:', url);
            console.log('🔧 getApiBaseUrl函数:', typeof window.getApiBaseUrl);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            console.log('📡 响应状态:', response.status, response.statusText);
            console.log('📡 响应头:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 基础数据响应:', result);
                console.log('🔍 [DEBUG] 原始响应数据:', JSON.stringify(result, null, 2));
                console.log('🔍 响应结构检查:');
                console.log('  - result存在:', !!result.result);
                console.log('  - result.data存在:', !!(result.result && result.result.data));
                console.log('  - data存在（直接）:', !!result.data);
                console.log('  - processing状态:', result.processing);
                console.log('  - completed状态:', result.completed);

                // 检查是否正在处理中
                if (result.processing === true || (result.completed === false && result.status === 'processing')) {
                    console.log('⏳ 检测到处理中状态');

                    // 检查是否有基础八字数据（适配两种数据结构）
                    const baziData = (result.result && result.result.data) || result.data;
                    if (baziData) {
                        console.log('✅ 找到基础八字数据，先显示排盘');
                        console.log('📊 [DEBUG] 基础八字数据结构:', JSON.stringify(baziData, null, 2));

                        // 统一数据结构，确保返回的格式一致
                        const unifiedData = {
                            data: baziData
                        };

                        this.showProcessingWithBasicData(result.request_id || cardKey, unifiedData);
                        return unifiedData; // 返回统一格式的基础数据
                    } else {
                        console.log('⏳ 无基础数据，显示等待界面');
                        this.showProcessingStatus(result.request_id || cardKey);
                        return null; // 返回null表示还在处理中
                    }
                }

                // 检查是否已完成处理
                if (result.completed === true) {
                    console.log('✅ [DEBUG] 检测到处理已完成');
                    console.log('🔍 [DEBUG] result.result存在:', !!result.result);
                    console.log('🔍 [DEBUG] result.result.data存在:', !!(result.result && result.result.data));
                    console.log('🔍 [DEBUG] data存在（直接）:', !!result.data);

                    // 检查是否有完整数据（适配两种数据结构）
                    const completeData = (result.result && result.result.data) || result.data;
                    if (completeData) {
                        console.log('✅ [DEBUG] 找到完整八字数据');
                        console.log('📊 [DEBUG] 返回的数据结构:', JSON.stringify(completeData, null, 2));

                        // 统一数据结构
                        const unifiedData = {
                            data: completeData
                        };

                        console.log('🎯 [DEBUG] 即将返回统一格式的数据');
                        return unifiedData; // 返回统一格式的完整数据
                    } else {
                        console.log('⚠️ [DEBUG] 处理完成但没有找到数据');
                        console.log('🔍 [DEBUG] result结构:', JSON.stringify(result, null, 2));
                    }
                }

                // 检查是否有基础的八字数据
                // 支持多种可能的数据结构
                let baziData = null;

                if (result.result && result.result.data) {
                    // 标准结构：result.result.data
                    baziData = result.result;
                    console.log('✅ 找到基础八字数据（标准结构）');
                } else if (result.data) {
                    // 简化结构：result.data
                    baziData = result;
                    console.log('✅ 找到基础八字数据（简化结构）');
                } else if (result.success && result.data) {
                    // 直接结构：result本身就是数据
                    baziData = result;
                    console.log('✅ 找到基础八字数据（直接结构）');
                }

                if (baziData && baziData.data && baziData.data.bz) {
                    console.log('✅ 验证通过：找到八字排盘数据');
                    this.currentData = baziData;
                    return baziData;
                } else {
                    console.log('❌ 响应中没有找到有效的八字数据');
                    console.log('📊 完整响应结构:', JSON.stringify(result, null, 2));

                    // 尝试更宽松的检查
                    if (result.data && (result.data.sex !== undefined || result.data.bz)) {
                        console.log('🔄 尝试宽松检查：找到部分八字数据');
                        baziData = { data: result.data };
                        this.currentData = baziData;
                        return baziData;
                    }
                }
            } else {
                console.error('❌ API请求失败:', response.status, response.statusText);
                const errorText = await response.text();
                console.error('❌ 错误响应内容:', errorText);
            }

            return null;
        } catch (error) {
            console.error('❌ 加载基础数据异常:', error);
            console.error('❌ 错误详情:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
            return null;
        }
    }

    /**
     * 显示基础信息（排盘结果）
     * @param {Object} data - 基础数据
     */
    displayBasicInfo(data) {
        console.log('🎯 [DEBUG] === displayBasicInfo 开始 ===');
        console.log('🎯 [DEBUG] 接收到的数据:', data);
        console.log('🎯 [DEBUG] 数据类型:', typeof data);
        console.log('🎯 [DEBUG] 数据结构:', JSON.stringify(data, null, 2));

        // 隐藏所有状态元素
        console.log('🔧 [DEBUG] 开始隐藏状态元素...');
        const loadingState = document.getElementById('loadingState');
        const errorState = document.getElementById('errorState');
        const emptyState = document.getElementById('emptyState');

        console.log('🔧 [DEBUG] loadingState元素:', loadingState);
        console.log('🔧 [DEBUG] errorState元素:', errorState);
        console.log('🔧 [DEBUG] emptyState元素:', emptyState);

        if (loadingState) {
            loadingState.style.display = 'none';
            console.log('✅ [DEBUG] 已隐藏 loadingState');
        } else {
            console.log('⚠️ [DEBUG] loadingState 元素不存在');
        }
        if (errorState) {
            errorState.style.display = 'none';
            console.log('✅ [DEBUG] 已隐藏 errorState');
        } else {
            console.log('⚠️ [DEBUG] errorState 元素不存在');
        }
        if (emptyState) {
            emptyState.style.display = 'none';
            console.log('✅ [DEBUG] 已隐藏 emptyState');
        } else {
            console.log('⚠️ [DEBUG] emptyState 元素不存在');
        }

        // 显示分析内容容器
        console.log('🔧 [DEBUG] 开始显示分析内容容器...');
        const analysisContent = document.getElementById('analysisContent');
        console.log('🔧 [DEBUG] analysisContent元素:', analysisContent);
        if (analysisContent) {
            analysisContent.style.display = 'block';
            console.log('✅ [DEBUG] 已显示 analysisContent');
        } else {
            console.log('⚠️ [DEBUG] analysisContent 元素不存在');
        }

        // 使用原来的专业排盘格式
        let baziInfoHtml = '';

        // 检查是否有原来的八字信息生成函数
        console.log('🔧 [DEBUG] 检查 generateBaziInfoSection 函数...');
        console.log('🔧 [DEBUG] window.generateBaziInfoSection:', window.generateBaziInfoSection);
        console.log('🔧 [DEBUG] 函数类型:', typeof window.generateBaziInfoSection);

        if (window.generateBaziInfoSection && typeof window.generateBaziInfoSection === 'function') {
            console.log('✅ [DEBUG] 使用原来的专业排盘格式');

            // 确保 currentData 被正确设置
            console.log('🔧 [DEBUG] 设置 this.currentData...');
            this.currentData = data;
            console.log('✅ [DEBUG] this.currentData 已设置:', this.currentData);

            // 也设置到全局，以防函数调用时 this 不正确
            if (window.progressiveLoader) {
                window.progressiveLoader.currentData = data;
                console.log('✅ [DEBUG] window.progressiveLoader.currentData 已设置');
            }
            // 创建一个临时的上下文对象，模拟原来的this
            const tempContext = {
                currentData: data
            };
            baziInfoHtml = window.generateBaziInfoSection.call(tempContext);
        } else {
            console.log('⚠️ 未找到原来的排盘函数，使用简化版本');
            baziInfoHtml = this.generateBaziInfoHTML(data);
        }

        // 直接使用正常的结果显示方式，不创建特殊的渐进式容器
        if (analysisContent) {
            // 先显示基础的八字信息
            analysisContent.innerHTML = baziInfoHtml;

            // 保存当前数据，供后续AI分析更新使用
            this.currentData = data;
        }

        // 应用标题格式化
        if (window.titleFormatter) {
            setTimeout(() => {
                window.titleFormatter.formatAllTitles('#progressiveContainer');
            }, 100);
        }
    }

    /**
     * 生成八字基础信息HTML
     * @param {Object} data - 数据
     * @returns {string} HTML字符串
     */
    generateBaziInfoHTML(data) {
        if (!data || !data.data) {
            return '<div class="bazi-info-error">八字信息加载失败</div>';
        }

        const baziData = data.data;
        
        // 解析八字四柱
        let baziPillars = [];
        if (baziData.bz) {
            const bz = baziData.bz;
            if (typeof bz === 'object' && !Array.isArray(bz)) {
                // 字典格式：0246是天干，1357是地支
                for (let i = 0; i < 4; i++) {
                    const tg = bz[i * 2] || '';
                    const dz = bz[i * 2 + 1] || '';
                    baziPillars.push(tg + dz);
                }
            } else if (Array.isArray(bz)) {
                baziPillars = bz.slice(0, 4);
            }
        }

        const pillarNames = ['年柱', '月柱', '日柱', '时柱'];
        
        return `
            <div class="bazi-basic-info">
                <div class="bazi-header">
                    <h2>📊 八字排盘结果</h2>
                    <div class="bazi-meta">
                        <span>性别: ${baziData.sex === 1 ? '男' : '女'}</span>
                        <span>排盘时间: ${baziData.runtime || '未知'}</span>
                    </div>
                </div>
                
                <div class="bazi-pillars">
                    <h3>四柱八字</h3>
                    <div class="pillars-grid">
                        ${baziPillars.map((pillar, index) => `
                            <div class="pillar-item">
                                <div class="pillar-name">${pillarNames[index]}</div>
                                <div class="pillar-value">${pillar}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${this.generateShishenHTML(baziData)}
                ${this.generateDayunHTML(baziData)}
            </div>
        `;
    }

    /**
     * 生成十神信息HTML
     * @param {Object} baziData - 八字数据
     * @returns {string} HTML字符串
     */
    generateShishenHTML(baziData) {
        if (!baziData.ss || !Array.isArray(baziData.ss)) {
            return '';
        }

        const pillarNames = ['年干', '月干', '日干', '时干'];
        
        return `
            <div class="bazi-shishen">
                <h3>十神配置</h3>
                <div class="shishen-grid">
                    ${baziData.ss.map((shishen, index) => `
                        <div class="shishen-item">
                            <div class="shishen-position">${pillarNames[index]}</div>
                            <div class="shishen-value">${shishen}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 生成大运信息HTML
     * @param {Object} baziData - 八字数据
     * @returns {string} HTML字符串
     */
    generateDayunHTML(baziData) {
        if (!baziData.dayun || !Array.isArray(baziData.dayun)) {
            return '';
        }

        const qiyunsui = baziData.qiyunsui || '未知';
        
        return `
            <div class="bazi-dayun">
                <h3>大运信息</h3>
                <div class="dayun-meta">起运岁数: ${qiyunsui}岁</div>
                <div class="dayun-grid">
                    ${baziData.dayun.slice(0, 6).map((yun, index) => {
                        const startAge = parseInt(qiyunsui) + index * 10;
                        const endAge = startAge + 9;
                        return `
                            <div class="dayun-item">
                                <div class="dayun-period">${startAge}-${endAge}岁</div>
                                <div class="dayun-ganzhi">${yun}</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 生成维度占位符
     * @returns {string} HTML字符串
     */
    generateDimensionPlaceholders() {
        const dimensionNames = {
            '性格': '性格分析',
            '事业': '事业运势',
            '感情': '感情婚姻',
            '健康': '健康状况',
            '运势': '运势趋势',
            '格局': '格局分析',
            '学业': '学业文昌'
        };

        const dimensionIcons = {
            '性格': '🧠',
            '事业': '💼',
            '感情': '💕',
            '健康': '🏥',
            '运势': '🔮',
            '格局': '🎯',
            '学业': '📚'
        };

        return this.dimensionOrder.map(dim => `
            <div class="dimension-placeholder" id="dimension-${dim}">
                <div class="dimension-header">
                    <span class="dimension-icon">${dimensionIcons[dim] || '📋'}</span>
                    <span class="dimension-title">${dimensionNames[dim] || dim}</span>
                    <div class="dimension-status loading">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>
                </div>
                <div class="dimension-content" style="display: none;">
                    <!-- 内容将动态加载 -->
                </div>
            </div>
        `).join('');
    }

    /**
     * 开始逐步加载AI分析
     * @param {string} cardKey - 卡密
     * @param {string} reportId - 报告ID
     */
    startProgressiveAnalysisLoad(cardKey, reportId) {
        console.log('开始逐步加载AI分析');

        // 如果已经完成，不要启动新的轮询
        if (this.isCompleted) {
            console.log('✅ 分析已完成，跳过轮询启动');
            return;
        }

        // 设置轮询检查分析状态
        this.pollingInterval = setInterval(async () => {
            try {
                // 如果已经完成，停止轮询
                if (this.isCompleted) {
                    console.log('✅ 检测到分析已完成，停止轮询');
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                    return;
                }

                await this.checkAndUpdateAnalysis(cardKey, reportId);
            } catch (error) {
                console.error('检查分析状态失败:', error);
            }
        }, 3000); // 每3秒检查一次

        // 设置超时
        setTimeout(() => {
            if (this.pollingInterval && !this.isCompleted) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
                this.handleLoadTimeout();
            }
        }, 300000); // 5分钟超时
    }

    /**
     * 检查并更新分析结果
     * @param {string} cardKey - 卡密
     * @param {string} reportId - 报告ID
     */
    async checkAndUpdateAnalysis(cardKey, reportId) {
        try {
            const apiUrl = window.getApiBaseUrl ? window.getApiBaseUrl() : 'http://localhost:5000';
            const url = `${apiUrl}/api/get_result/${cardKey}`;

            const response = await fetch(url);
            if (!response.ok) {
                // 检查是否是可重试的错误
                if (response.status >= 500 || response.status === 0 || response.status === 429) {
                    console.log(`🔄 API请求失败 (${response.status})，将在下次轮询时重试`);
                    this.handleRetryableError(`HTTP ${response.status}`);
                    return;
                }
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('🔍 渐进式加载 - API响应:', result);

            // 检查多种可能的完成状态格式
            let isCompleted = false;
            let analysisData = null;

            if (result.completed && result.result) {
                // 格式1: { completed: true, result: {...} }
                isCompleted = true;
                analysisData = result.result;
                console.log('✅ 检测到格式1: completed + result');
            } else if (result.success && result.data && result.data.llm_analysis) {
                // 格式2: { success: true, data: { llm_analysis: {...} } }
                isCompleted = true;
                analysisData = result.data;
                console.log('✅ 检测到格式2: success + data.llm_analysis');
            } else if (result.result && result.result.data && result.result.data.llm_analysis) {
                // 格式3: { result: { data: { llm_analysis: {...} } } }
                isCompleted = true;
                analysisData = result.result.data;
                console.log('✅ 检测到格式3: result.data.llm_analysis');
            } else if (result.data && result.data.llm_analysis) {
                // 格式4: { data: { llm_analysis: {...} } }
                isCompleted = true;
                analysisData = result.data;
                console.log('✅ 检测到格式4: data.llm_analysis');
            }

            if (isCompleted && analysisData) {
                console.log('🎉 分析完成，更新所有维度');

                // 标记完成状态
                this.isCompleted = true;

                // 停止轮询
                if (this.pollingInterval) {
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                }

                this.updateAllDimensions(analysisData);
            } else {
                // 检查是否有部分结果可以显示
                console.log('⏳ 分析未完成，检查部分结果');
                this.updatePartialResults(result);
            }
        } catch (error) {
            console.error('检查分析状态失败:', error);
            this.handleAnalysisError(error);
        }
    }

    /**
     * 更新所有维度
     * @param {Object} result - 完整结果
     */
    updateAllDimensions(result) {
        console.log('更新所有维度:', result);

        // 标记渐进式加载已完成，避免重复初始化
        this.isCompleted = true;

        // 使用正常的结果显示方式，直接调用displayResult
        if (window.detailedResultViewer && window.detailedResultViewer.displayResult) {
            console.log('✅ 使用正常的displayResult函数显示完整结果');

            // 构建完整的数据对象
            const completeData = {
                ...this.currentData,
                ...result
            };

            // 直接调用正常的显示函数
            window.detailedResultViewer.displayResult(completeData);

            // 显示完成状态
            this.showCompletionStatus();
        } else {
            console.log('⚠️ 未找到正常的显示函数，使用备用方式');

            // 备用方式：手动追加AI分析内容
            const analysisContent = document.getElementById('analysisContent');
            if (analysisContent && result.llm_analysis && result.llm_analysis.analysis_results) {

                // 生成AI分析的HTML
                const aiAnalysisHtml = this.generateAIAnalysisHTML(result.llm_analysis.analysis_results);

                // 追加到现有内容
                analysisContent.innerHTML += aiAnalysisHtml;

                // 应用样式增强
                setTimeout(() => {
                    if (window.applyStyleEnhancements && typeof window.applyStyleEnhancements === 'function') {
                        window.applyStyleEnhancements();
                    }
                }, 100);
            }

            // 显示完成状态
            this.showCompletionStatus();
        }
    }

    /**
     * 更新部分结果
     * @param {Object} result - 部分结果
     */
    updatePartialResults(result) {
        console.log('检查部分结果:', result);

        // 更新进度状态
        const progressiveStatus = document.querySelector('.progressive-status');
        if (progressiveStatus) {
            progressiveStatus.textContent = '⏳ 正在加载分析结果...';
        }

        // 如果轮询了很长时间还没有结果，提供一个备用方案
        if (this.retryCount > 10) { // 30秒后
            console.log('⚠️ 长时间未获取到完整结果，尝试显示现有数据');

            // 尝试从任何可能的位置提取分析数据
            let fallbackData = null;

            if (result.result && result.result.data) {
                fallbackData = result.result.data;
            } else if (result.data) {
                fallbackData = result.data;
            } else if (result.result) {
                fallbackData = result.result;
            }

            if (fallbackData && (fallbackData.llm_analysis || fallbackData.bz)) {
                console.log('🔄 使用备用数据显示结果');
                this.updateAllDimensions(fallbackData);

                // 停止轮询
                if (this.pollingInterval) {
                    clearInterval(this.pollingInterval);
                    this.pollingInterval = null;
                }
            }
        }

        this.retryCount++;
    }

    /**
     * 处理可重试的错误
     * @param {string} errorMsg - 错误消息
     */
    handleRetryableError(errorMsg) {
        console.log(`⚠️ 遇到可重试错误: ${errorMsg}`);

        // 更新状态显示
        const progressiveStatus = document.querySelector('.progressive-status');
        if (progressiveStatus) {
            progressiveStatus.textContent = `⏳ 遇到临时问题，正在重试... (${this.retryCount + 1}/${this.maxRetries})`;
        }

        // 增加重试计数，但不停止轮询
        this.retryCount++;

        // 如果重试次数过多，显示警告但继续尝试
        if (this.retryCount > this.maxRetries * 2) {
            if (progressiveStatus) {
                progressiveStatus.innerHTML = `
                    ⚠️ 服务器响应较慢，请耐心等待...
                    <button class="retry-btn" onclick="window.progressiveLoader.retryAll()">🔄 强制重试</button>
                `;
            }
        }
    }

    /**
     * 生成AI分析HTML（使用正常样式）
     * @param {Object} analysisResults - 分析结果
     * @returns {string} HTML字符串
     */
    generateAIAnalysisHTML(analysisResults) {
        let html = '<div class="detailed-dimension-text">';

        // 添加AI分析标题
        html += '<h1>🤖 AI深度分析</h1>';

        // 为每个维度生成内容
        this.dimensionOrder.forEach(dimension => {
            if (analysisResults[dimension]) {
                const content = analysisResults[dimension];

                // 使用与正常结果相同的格式
                html += `
                    <div class="analysis-dimension">
                        <h2>${this.getDimensionTitle(dimension)}</h2>
                        <div class="dimension-content">
                            ${this.formatAnalysisContent(content)}
                        </div>
                    </div>
                `;
            }
        });

        html += '</div>';
        return html;
    }

    /**
     * 获取维度标题
     * @param {string} dimension - 维度名称
     * @returns {string} 格式化的标题
     */
    getDimensionTitle(dimension) {
        const titleMap = {
            '性格': '🧠 性格分析',
            '事业': '💼 事业运势',
            '感情': '💕 感情婚姻',
            '健康': '🏥 健康状况',
            '运势': '🔮 运势趋势',
            '格局': '🎯 格局分析',
            '学业': '📚 学业文昌'
        };
        return titleMap[dimension] || `📋 ${dimension}分析`;
    }

    /**
     * 格式化分析内容
     * @param {string} content - 原始内容
     * @returns {string} 格式化后的内容
     */
    formatAnalysisContent(content) {
        if (!content) return '';

        // 将换行符转换为HTML段落
        return content
            .split('\n\n')
            .filter(paragraph => paragraph.trim())
            .map(paragraph => `<p>${paragraph.trim()}</p>`)
            .join('');
    }

    /**
     * 更新维度内容
     * @param {string} dimension - 维度名称
     * @param {string} content - 内容
     */
    updateDimensionContent(dimension, content) {
        const dimensionElement = document.getElementById(`dimension-${dimension}`);
        if (!dimensionElement) return;

        const statusElement = dimensionElement.querySelector('.dimension-status');
        const contentElement = dimensionElement.querySelector('.dimension-content');

        if (statusElement) {
            statusElement.innerHTML = '<span class="completed">✅ 已完成</span>';
            statusElement.classList.remove('loading');
            statusElement.classList.add('completed');
        }

        if (contentElement) {
            // 处理内容格式
            const processedContent = this.processAnalysisContent(content);
            contentElement.innerHTML = processedContent;
            contentElement.style.display = 'block';
            
            // 添加展开/收起功能
            this.addToggleFunction(dimensionElement);
        }

        this.loadedDimensions.add(dimension);
        
        // 应用标题格式化
        if (window.titleFormatter) {
            setTimeout(() => {
                window.titleFormatter.formatAllTitles(`#dimension-${dimension}`);
            }, 100);
        }
    }

    /**
     * 处理分析内容
     * @param {string} content - 原始内容
     * @returns {string} 处理后的HTML
     */
    processAnalysisContent(content) {
        if (!content || typeof content !== 'string') {
            return '<p>暂无分析内容</p>';
        }

        // 检查是否包含错误信息
        if (content.includes('分析失败') || content.includes('Internal Server Error')) {
            return `
                <div class="analysis-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">分析遇到问题，正在重试...</div>
                    <button class="retry-btn" onclick="window.progressiveLoader.retryAll()">
                        🔄 重试
                    </button>
                </div>
            `;
        }

        // 将换行转换为HTML段落
        const paragraphs = content.split('\n\n').filter(p => p.trim());
        return paragraphs.map(p => `<p>${p.trim().replace(/\n/g, '<br>')}</p>`).join('');
    }

    /**
     * 添加展开/收起功能
     * @param {Element} dimensionElement - 维度元素
     */
    addToggleFunction(dimensionElement) {
        const header = dimensionElement.querySelector('.dimension-header');
        const content = dimensionElement.querySelector('.dimension-content');
        
        if (header && content) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const isVisible = content.style.display !== 'none';
                content.style.display = isVisible ? 'none' : 'block';
                
                // 添加展开/收起图标
                let toggleIcon = header.querySelector('.toggle-icon');
                if (!toggleIcon) {
                    toggleIcon = document.createElement('span');
                    toggleIcon.className = 'toggle-icon';
                    header.appendChild(toggleIcon);
                }
                toggleIcon.textContent = isVisible ? '▶' : '▼';
            });
        }
    }

    /**
     * 处理加载超时
     */
    handleLoadTimeout() {
        console.log('分析加载超时');
        
        const progressiveStatus = document.querySelector('.progressive-status');
        if (progressiveStatus) {
            progressiveStatus.innerHTML = `
                ⏰ 分析超时，部分结果可能未完成
                <button class="retry-btn" onclick="window.progressiveLoader.retryAll()">🔄 重试</button>
            `;
            progressiveStatus.classList.add('timeout');
        }
    }

    /**
     * 处理分析错误
     * @param {Error} error - 错误对象
     */
    handleAnalysisError(error) {
        console.error('分析错误:', error);
        
        this.retryCount++;
        if (this.retryCount < this.maxRetries) {
            console.log(`分析错误，${3}秒后重试 (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => {
                // 重试逻辑将在下次轮询中执行
            }, 3000);
        } else {
            // 超过最大重试次数
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
            }
            
            const progressiveStatus = document.querySelector('.progressive-status');
            if (progressiveStatus) {
                progressiveStatus.innerHTML = `
                    ❌ 分析失败，请重试
                    <button class="retry-btn" onclick="window.progressiveLoader.retryAll()">🔄 重试</button>
                `;
                progressiveStatus.classList.add('error');
            }
        }
    }

    /**
     * 显示完成状态
     */
    showCompletionStatus() {
        console.log('✅ 显示分析完成状态');

        // 更新状态显示
        const progressiveStatus = document.querySelector('.progressive-status');
        if (progressiveStatus) {
            progressiveStatus.innerHTML = '✅ 分析完成！所有维度已加载完毕';
            progressiveStatus.className = 'progressive-status completed';

            // 3秒后隐藏状态提示
            setTimeout(() => {
                if (progressiveStatus) {
                    progressiveStatus.style.display = 'none';
                }
            }, 3000);
        }
    }

    /**
     * 重试所有分析
     */
    retryAll() {
        console.log('重试所有分析');
        this.retryCount = 0;
        this.isCompleted = false;
        this.loadedDimensions.clear();
        this.failedDimensions.clear();

        // 重置状态
        const progressiveStatus = document.querySelector('.progressive-status');
        if (progressiveStatus) {
            progressiveStatus.textContent = '正在重新加载分析结果...';
            progressiveStatus.className = 'progressive-status';
            progressiveStatus.style.display = 'block';
        }

        // 重置所有维度状态
        this.dimensionOrder.forEach(dimension => {
            const dimensionElement = document.getElementById(`dimension-${dimension}`);
            if (dimensionElement) {
                const statusElement = dimensionElement.querySelector('.dimension-status');
                const contentElement = dimensionElement.querySelector('.dimension-content');

                if (statusElement) {
                    statusElement.innerHTML = '<div class="loading-spinner"></div><span>加载中...</span>';
                    statusElement.className = 'dimension-status loading';
                }

                if (contentElement) {
                    contentElement.style.display = 'none';
                    contentElement.innerHTML = '';
                }
            }
        });

        // 重新开始轮询
        if (this.currentData && this.currentData.cardKey) {
            this.startProgressiveAnalysisLoad(this.currentData.cardKey);
        }
    }

    /**
     * 显示带基础数据的处理中状态
     * @param {string} requestId - 请求ID
     * @param {Object} basicResult - 基础八字数据
     */
    showProcessingWithBasicData(requestId, basicResult) {
        console.log('🔮 显示带基础数据的处理中状态，请求ID:', requestId);

        // 在页面顶部添加处理中提示
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            // 在内容顶部插入处理中横幅
            const processingBanner = document.createElement('div');
            processingBanner.className = 'processing-banner';
            processingBanner.innerHTML = `
                <div class="processing-banner-content">
                    <div class="processing-banner-icon">🤖</div>
                    <div class="processing-banner-text">
                        <h3>AI智能分析进行中</h3>
                        <p>八字排盘已完成，正在生成详细的命理分析报告...</p>
                    </div>
                    <div class="processing-banner-progress">
                        <div class="loading-spinner-small"></div>
                    </div>
                </div>
            `;

            // 插入到内容顶部
            mainContent.insertBefore(processingBanner, mainContent.firstChild);
        }

        // 添加处理中横幅的样式
        this.addProcessingBannerStyles();

        // 开始轮询检查处理状态
        this.startProcessingPolling(requestId);
    }

    /**
     * 添加处理中横幅的样式
     */
    addProcessingBannerStyles() {
        if (document.getElementById('processing-banner-styles')) return;

        const style = document.createElement('style');
        style.id = 'processing-banner-styles';
        style.textContent = `
            .processing-banner {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 16px 20px;
                margin: -20px -20px 20px -20px;
                border-radius: 0 0 12px 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                position: relative;
                overflow: hidden;
            }

            .processing-banner::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                animation: shimmer 2s infinite;
            }

            .processing-banner-content {
                display: flex;
                align-items: center;
                gap: 16px;
                position: relative;
                z-index: 1;
            }

            .processing-banner-icon {
                font-size: 32px;
                animation: pulse 2s infinite;
            }

            .processing-banner-text h3 {
                margin: 0 0 4px 0;
                font-size: 18px;
                font-weight: 600;
            }

            .processing-banner-text p {
                margin: 0;
                font-size: 14px;
                opacity: 0.9;
            }

            .processing-banner-progress {
                margin-left: auto;
            }

            .loading-spinner-small {
                width: 24px;
                height: 24px;
                border: 2px solid rgba(255,255,255,0.3);
                border-top: 2px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 显示处理中状态
     * @param {string} requestId - 请求ID
     */
    showProcessingStatus(requestId) {
        console.log('🔄 显示处理中状态，请求ID:', requestId);

        // 显示处理中的界面
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="processing-container">
                    <div class="processing-header">
                        <h2>🔮 八字分析进行中</h2>
                        <p>正在为您生成详细的命理分析报告...</p>
                    </div>

                    <div class="processing-status">
                        <div class="loading-spinner-large"></div>
                        <div class="processing-steps">
                            <div class="step active">
                                <span class="step-icon">📊</span>
                                <span class="step-text">八字排盘计算</span>
                            </div>
                            <div class="step active">
                                <span class="step-icon">🤖</span>
                                <span class="step-text">AI智能分析</span>
                            </div>
                            <div class="step">
                                <span class="step-icon">📝</span>
                                <span class="step-text">生成详细报告</span>
                            </div>
                        </div>
                    </div>

                    <div class="processing-info">
                        <p>⏱️ 预计需要 30-60 秒</p>
                        <p>🔄 页面将自动刷新显示结果</p>
                        <p class="request-id">请求ID: ${requestId}</p>
                    </div>
                </div>
            `;
        }

        // 添加处理中的样式
        this.addProcessingStyles();

        // 开始轮询检查处理状态
        this.startProcessingPolling(requestId);
    }

    /**
     * 添加处理中状态的样式
     */
    addProcessingStyles() {
        if (document.getElementById('processing-styles')) return;

        const style = document.createElement('style');
        style.id = 'processing-styles';
        style.textContent = `
            .processing-container {
                text-align: center;
                padding: 40px 20px;
                max-width: 600px;
                margin: 0 auto;
            }

            .processing-header h2 {
                color: #2c3e50;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .processing-header p {
                color: #7f8c8d;
                font-size: 16px;
                margin-bottom: 30px;
            }

            .loading-spinner-large {
                width: 60px;
                height: 60px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 30px;
            }

            .processing-steps {
                display: flex;
                justify-content: center;
                gap: 30px;
                margin-bottom: 30px;
                flex-wrap: wrap;
            }

            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
                opacity: 0.5;
                transition: opacity 0.3s;
            }

            .step.active {
                opacity: 1;
            }

            .step-icon {
                font-size: 24px;
                width: 50px;
                height: 50px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #ecf0f1;
                border-radius: 50%;
            }

            .step.active .step-icon {
                background: #3498db;
                color: white;
            }

            .step-text {
                font-size: 14px;
                color: #2c3e50;
                font-weight: 500;
            }

            .processing-info {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #3498db;
            }

            .processing-info p {
                margin: 8px 0;
                color: #2c3e50;
            }

            .request-id {
                font-family: monospace;
                font-size: 12px;
                color: #7f8c8d !important;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 开始轮询检查处理状态
     * @param {string} requestId - 请求ID
     */
    startProcessingPolling(requestId) {
        console.log('🔄 开始轮询处理状态，请求ID:', requestId);

        // 清除之前的轮询
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
        }

        // 每5秒检查一次状态
        this.processingInterval = setInterval(async () => {
            try {
                const cardKey = requestId.split('_')[0]; // 从请求ID中提取卡密
                const apiUrl = window.getApiBaseUrl ? window.getApiBaseUrl() : 'http://localhost:5000';

                // 使用状态检查API而不是结果获取API
                const url = `${apiUrl}/webhook/check-status?cardKey=${cardKey}&requestId=${requestId}`;

                console.log('🔄 轮询检查状态:', url);

                const response = await fetch(url);
                if (response.ok) {
                    const result = await response.json();

                    console.log('📊 轮询响应:', result);

                    // 检查是否完成
                    if (result.completed === true) {
                        console.log('✅ 处理完成，重新加载页面');
                        clearInterval(this.processingInterval);

                        // 重新加载页面显示结果
                        window.location.reload();
                    } else if (result.processing !== true && result.status !== 'processing') {
                        // 如果不再是处理中状态，但也没有完成，可能出错了
                        console.log('⚠️ 处理状态异常:', result);

                        // 如果状态异常，也尝试重新加载页面
                        if (result.status === 'error' || result.status === 'failed') {
                            console.log('❌ 处理失败，重新加载页面');
                            clearInterval(this.processingInterval);
                            window.location.reload();
                        }
                    } else {
                        // 仍在处理中，显示进度信息
                        if (result.progress !== undefined) {
                            console.log(`⏳ 处理进度: ${result.progress}%`);
                        }
                    }
                }
            } catch (error) {
                console.error('❌ 轮询检查失败:', error);
            }
        }, 5000);

        // 设置最大轮询时间（5分钟）
        setTimeout(() => {
            if (this.processingInterval) {
                clearInterval(this.processingInterval);
                console.log('⏰ 轮询超时，停止检查');

                // 显示超时提示
                const container = document.querySelector('.processing-container');
                if (container) {
                    container.innerHTML += `
                        <div class="timeout-notice">
                            <p>⏰ 处理时间较长，请稍后手动刷新页面查看结果</p>
                            <button onclick="window.location.reload()" class="refresh-btn">🔄 刷新页面</button>
                        </div>
                    `;
                }
            }
        }, 300000); // 5分钟
    }

    /**
     * 显示错误
     * @param {string} title - 错误标题
     * @param {string} message - 错误消息
     */
    showError(title, message) {
        if (window.showErrorState && typeof window.showErrorState === 'function') {
            window.showErrorState(title, message);
        } else {
            console.error('显示错误:', title, message);
        }
    }
}

// 创建全局实例
window.progressiveLoader = new ProgressiveLoader();

// 添加调试函数
window.testProgressiveAPI = async function(cardKey) {
    try {
        const apiUrl = window.getApiBaseUrl ? window.getApiBaseUrl() : 'http://localhost:5000';
        const url = `${apiUrl}/api/get_result/${cardKey}`;

        console.log('🧪 测试API响应:', url);

        const response = await fetch(url);
        const result = await response.json();

        console.log('📊 API响应结果:', result);
        console.log('📊 响应结构分析:');
        console.log('  - result.completed:', result.completed);
        console.log('  - result.result:', !!result.result);
        console.log('  - result.success:', result.success);
        console.log('  - result.data:', !!result.data);
        console.log('  - result.data.llm_analysis:', !!(result.data && result.data.llm_analysis));
        console.log('  - result.result.data:', !!(result.result && result.result.data));
        console.log('  - result.result.data.llm_analysis:', !!(result.result && result.result.data && result.result.data.llm_analysis));

        return result;
    } catch (error) {
        console.error('❌ API测试失败:', error);
        return null;
    }
};

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressiveLoader;
}
